module("ChatSend", package.seeall)

--调用对应的接口，向结点端上报对应的信息
function execute(packetID, operateID, buffer)

	--AddressPreModel.DelGetWorker()
	--
	local cgmsg = msg_chat_pb.cgchatsend()
	local gcmsg = msg_chat_pb.gcchatsend()
	
	--cgmsg:ParseFromString(buffer)
	cgmsg:ParseFromString(buffer)
	--取到这个订单的消息，这个订单消息是在三方平台的数据库获取
	
	if cgmsg.channel == '' or cgmsg.pcorderid=='' or cgmsg.userid==0 then
		
		gcmsg.result, gcmsg.msg = -1,"发送失败"
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	
	
	local sqlCase = "select id,merchant_order_id,vendor_user_id,customer_user_id,type,channel from dy_customer_order  where id>0 and id="..cgmsg.pcorderid
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		
		gcmsg.result, gcmsg.msg = -2,"发送失败!"
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	
	end	
	local vendor_user_id = sqlData[3]
	local customer_user_id = sqlData[4]
	local merchant_order_id = sqlData[2]
	local chattype = g_chatDefine.chat_type['customer_to_merchant']
	
	if cgmsg.userid == tonumber(vendor_user_id) then
		chattype = g_chatDefine.chat_type['merchant_to_customer']
	else
		chattype = g_chatDefine.chat_type['customer_to_merchant']
	end
	
	local getData = ChatModel.InsertInto(sqlData[1],sqlData[2],sqlData[3],sqlData[4],sqlData[5],sqlData[6],chattype,cgmsg.msgtype,cgmsg.msg)
	
	--local 
	
	if getData ~= nil then
		
		gcmsg.cdata.timesec = getData.timesec
		gcmsg.cdata.chattype = getData.chattype
		gcmsg.cdata.msgtype = getData.msgtype
		gcmsg.cdata.msg = getData.msg
		gcmsg.cdata.pcorderid = cgmsg.pcorderid
		
		gcmsg.result, gcmsg.msg = 0,"发送成功"		
	else
		gcmsg.result, gcmsg.msg = 1,"发送失败"
	end
	
	if merchant_order_id == '0' or merchant_order_id == '' then
		--说明是otc的交易
		local gcrecv = msg_chat_pb.gcchatrecv()

		gcrecv.msg = "发送成功"
		gcrecv.pcorderid = cgmsg.pcorderid
		gcrecv.cdata.timesec = getData.timesec
		gcrecv.cdata.chattype = getData.chattype
		gcrecv.cdata.msgtype = getData.msgtype
		gcrecv.cdata.msg = getData.msg
		gcrecv.cdata.pcorderid = cgmsg.pcorderid		
		gcrecv.result = 0
	
		
		if cgmsg.userid == tonumber(vendor_user_id) then
			
			SendMessage( customer_user_id, PacketCode[4012].client, gcrecv:ByteSize(),gcrecv:SerializeToString())
		else
			SendMessage( vendor_user_id, PacketCode[4012].client, gcrecv:ByteSize(),gcrecv:SerializeToString())
		end
	else
		--这里是要推送给H端的
		local gcrecv = msg_chat_pb.gcchatrecv()
		gcrecv.msg = "发送成功"
		gcrecv.pcorderid = merchant_order_id
		gcrecv.cdata.timesec = getData.timesec
		gcrecv.cdata.chattype = getData.chattype
		gcrecv.cdata.msgtype = getData.msgtype
		gcrecv.cdata.msg = getData.msg
		gcrecv.cdata.pcorderid = cgmsg.pcorderid		
		gcrecv.result = 0
		gcrecv.target = merchant_order_id
		gcrecv.result = 0
		ChatModel.PushChatToUpdate("player",gcrecv)
	end
	
	
	return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
end