module("ChatCreate", package.seeall)

--
function execute(packetID, operateID, buffer)
	
	--创建的时候
	--AddressPreModel.DelGetWorker()
	local cgmsg = msg_chat_pb.cgchatcrate()
	local gcmsg = msg_chat_pb.gcchatcrate()
	
	--首先去三方支付平台，查看该订单存不存在
	cgmsg:ParseFromString(buffer)

	if cgmsg.channel == '' or cgmsg.pcorderid=='' or cgmsg.userid==0 then
		
		gcmsg.result, gcmsg.msg = -1,"参数类型错误"
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end


	
	local sqlCase = "select channel,price,money,status,pay_id,user_pay_id,amount,id,payee_account,payee_name,payee_bank,payee_band_addr,create_time,customer_user_id from dy_customer_order where id>0 and id="..cgmsg.pcorderid

	mysqlItem:executeQuery(sqlCase)

	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		
		gcmsg.result, gcmsg.msg = -1,"创建聊天室失败"
		return cgmsg.pcorderid,-1,gcmsg:ByteSize(),gcmsg:SerializeToString()
		
	end
	gcmsg.result = 0

	gcmsg.channel = sqlData[1]
	gcmsg.coinprice = sqlData[2]
	gcmsg.money = sqlData[3]
	gcmsg.state = tonumber(sqlData[4])
	gcmsg.timeout = g_chatDefine.time_out['start_pay']  

	gcmsg.payid = sqlData[5]
	
	
	local user_pay_id = sqlData[6]
	gcmsg.coinamount = sqlData[7]
	

	gcmsg.payaccount = sqlData[9]
	gcmsg.payusername = sqlData[10]
	gcmsg.paybankname = sqlData[11]
	gcmsg.paybankaddress = sqlData[12]
	--gcmsg.payurl = sqlData[21]

	if tonumber(sqlData[4]) == 1 then
		--只有未付款的才会有
		local timeMark = TimeUtils.GetTime() - TimeUtils.GetTime( sqlData[13] )
		gcmsg.timeout = 900 - timeMark 
		gcmsg.timeout = gcmsg.timeout < 0 and 0 or gcmsg.timeout
	else
		gcmsg.timeout = 0
	end
	

	
	sqlCase = "select timesec,chattype,msgtype,msg from dy_chat_data where otcorderid='"..cgmsg.pcorderid.."'"

	mysqlItem:executeQuery(sqlCase)
	
	for i = 1,100 do
		sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		local addItem = gcmsg.chatlist:add()
		addItem.timesec = tonumber(sqlData[1])
		addItem.chattype = tonumber(sqlData[2])
		addItem.msgtype = tonumber(sqlData[3])
		addItem.msg = sqlData[4]
		addItem.pcorderid = cgmsg.pcorderid
	end
	
	
	return cgmsg.pcorderid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
end