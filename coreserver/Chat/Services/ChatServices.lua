ChatServices = {}


function ChatServices.Init(userid)
	
	--通知挂卖单的人，有卖单，需要付款
	ChatServices.redis_remote = redisConnect.new(g_chain_redisInfo.host,g_chain_redisInfo.pass,g_chain_redisInfo.port)
	return true
end


function ChatServices.ServerLoop()
	
	--同步去取数据

	
	--暂定每10秒钟从结点服务器同步一次数据到中心服务器
	--local tm = TimeUtils.GetTableTime()
	--只有有长度的情况下，才会去整理
	--if math.mod(tm.sec,10) == 0 and SynchroChainDataModel.GetBlockChainTxListLen() > 0 then
		--processWork("SynchroChainWork", TimeUtils.GetTimeString())
	--end
	

	if ChatModel.ChatToUpdateLen("merchant")> 0 or ChatModel.StateToUpdateLen("merchant") > 0 then
		processWork("ChatUpdatePop", TimeUtils.GetTimeString())
	end
	
	return true
end


