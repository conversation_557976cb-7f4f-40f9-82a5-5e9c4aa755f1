module("ChatUpdatePop", package.seeall)

function work(buffer)

	local getLen = ChatModel.ChatToUpdateLen("merchant")
	
	for i = 1, getLen do
	
	
		local getData = redisItem:lpop( ChatModel.chat_to_update.."merchant", ChatModel.redis_index )
		if getData == nil then
			break
		end
		
		local gcmsg = msg_chat_pb.gcchatrecv()
		gcmsg:ParseFromString(getData)
		
		gcmsg.result = 0
		SendMessage( gcmsg.target, PacketCode[4012].client, gcmsg:ByteSize(),gcmsg:SerializeToString())

	end	
	
	
	getLen = ChatModel.StateToUpdateLen("merchant")

	
	for i = 1, getLen do
	
		local getData = redisItem:lpop( ChatModel.chat_to_player.."merchant", ChatModel.redis_index )
		if getData == nil then
			break
		end
		
		local gcmsg = msg_chat_pb.gcchatupdate()
		gcmsg.cdata:ParseFromString(getData)
		if true == ChatModel.Exist(gcmsg.target) then
			
			gcmsg.result = 0
			SendMessage( gcmsg.target, PacketCode[4003].client, gcmsg:ByteSize(),gcmsg:SerializeToString())
			
		end		
	end
	
	
	getLen = ChatModel.PayToUpdateLen("merchant")
	
	
	for i = 1, getLen do
		local getData = redisItem:lpop( ChatModel.pay_to_update.."merchant", ChatModel.redis_index )
		if getData == nil then
			break
		end
		
		local coinfo = CustomerOrderModel.GetCustomerOrderInfo(getData)
		
		if coinfo ~= nil then
			local uInfo = UserInfoModel.GetUserInfo( coinfo.customeruserid )
			VendorOrderService.PaidOrder(uInfo, coinfo)
		else 
			LogFile("PayToUpdate", getData)
		end
		
	end
	
	

end

