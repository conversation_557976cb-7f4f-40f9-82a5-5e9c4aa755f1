
NotifyModel = {}

NotifyModel.redis_index = "redis_notify"
NotifyModel.notice_notify_list = "notify_list"       			--公告列表


function NotifyModel.PushNotifyList(strData)
	
	
	
	--NoticeModel.LoadNoticeList(channel)
	--
	--
	redisItem:rpush(NotifyModel.notice_notify_list, strData, NotifyModel.redis_index)
	

end

function NotifyModel.GetNotifyLen()
	local getLen = redisItem:llen( NotifyModel.notice_notify_list, NotifyModel.redis_index )
	return getLen == nil and 0 or tonumber(getLen)
end



