RedisClearService = {}


function RedisClearService.Init()
	
	print("RedisClearService.Init")

end


function RedisClearService.UtilsLoop()

	if g_markTime.curr.hour == 5 and g_markTime.curr.min == 17 and g_markTime.curr.sec == 17 then
		processWork("RedisClearKey", "0")
	end	
	
	
	if g_markTime.curr.hour == 6 and g_markTime.curr.min == 17 and g_markTime.curr.sec == 17 then
		processWork("RedisClearHashKey", "0")
	end	
	
end