--清理正常key
module("RedisClearKey", package.seeall)

function work(buffer)
	
	local time = TimeUtils.GetTime()
	
	local keylist = redisItem:zrangebyscore(RedisClearModel.normal_key, "-inf", time, RedisClearModel.redis_index)
	if keylist == nil then
		return
	end
	
	for k,v in pairs(keylist) do
		
		-- 解析v
		local keyandindex = GameUtils.Split(v, "#")  
		
		redisItem:del(keyandindex[1], keyandindex[2])
		
	end
end




