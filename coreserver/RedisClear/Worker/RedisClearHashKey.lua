-- 清理hash 域值
module("RedisClearHashKey", package.seeall)

function work(buffer)
	local time = TimeUtils.GetTime()
	
	local keylist = redisItem:zrangebyscore(RedisClearModel.hash_field_key, "-inf", time, RedisClearModel.redis_index)
	if keylist == nil then
		return
	end
	
	for k,v in pairs(keylist) do
		
		-- 解析v
		local combinekey = GameUtils.Split(v, "#")  
		
		redisItem:hdel(combinekey[1], combinekey[2], combinekey[3])
	end
	
end




