
require("common.define.MarketDefine")
require("common.packet.packet_order")
require("common.st_order_pb")
require("common.msg_order_pb")
require("common.msg_order2_pb")

require("Market.Controller.HangOrder")
require("Market.Controller.CustomerOrderList")
require("Market.Controller.Deal")
require("Market.Controller.DealRecdList")
require("Market.Controller.Paid")
require("Market.Controller.Pass")
require("Market.Controller.OrderList")
require("Market.Controller.DisableOrder")
require("Market.Controller.CancelLeftCount")
require("Market.Controller.Cancel")
require("Market.Controller.WorkingList")
require("Market.Controller.TodayFlow")
require("Market.Controller.IncomeStat")
require("Market.Controller.ExpenseStat")
require("Market.Controller.QuickDeal")
require("Market.Controller.Appeal")
require("Market.Controller.Withdraw")
require("Market.Controller.WithdrawRecord")
require("Market.Controller.ExchangeRate")
require("Market.Controller.CheckAddr")
require("Market.Controller.TWalletRecharge")
require("Market.Controller.SubmitTXID")
require("Market.Controller.TWalletRecord")
require("Market.Controller.GetWithdrawalFee")
require("Market.Controller.ModifyProofUrl")
require("Market.Controller.IncomeExpenditureDetail")
require("Market.Controller.OrderActivation")
require("Market.Controller.SearchDealRecdList")

--[[

require("Market.Controller.AppealDetail")
require("Market.Controller.AppealReply")
require("Market.Controller.AppealList")

require("Market.Controller.Detail")


require("Market.Controller.ModifyOrder")

]]

require("Market.Model.CustomerOrderModel")
require("Market.Model.VendorOrderModel")

require("Market.Services.VendorOrderService")
require("Market.Services.CoinInfoService")


require("Market.Worker.CallbackOrderWorker")
require("Market.Worker.MatchOrderWoker")

g_redisIndex[VendorOrderModel.redis_index] = {index = g_redisInfo.redis_two, des = "redis_vendororder"}

