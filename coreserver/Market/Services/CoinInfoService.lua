
CoinInfoService = {}


function CoinInfoService.ServerLoop()
	--把全部订单取下来，检查订单的状态
	
	local dealList = CustomerOrderModel.GetCustomerOrderList()
	local NowTime = TimeUtils.GetTime()
	for _, dealid in pairs(dealList) do
		
		ThreadManager.DealLock(dealid)
		local dInfo = CustomerOrderModel.GetCustomerOrderInfo(dealid)
		if dInfo ~= nil then
			if dInfo.status == g_marketDefine.deal_status_wait then
				 --该订单开始，等待付款
				
				local timeOut = g_marketDefine.order_time_out
				if tonumber(dInfo.payidlist) == g_payCenterDefine.pay_id_list['bitpay'] then
					timeOut = g_marketDefine.cion_pay_order_time_out 
				end
				if ( timeOut + TimeUtils.GetTime(dInfo.updatetime)) <  NowTime  then
					--到时不付款就超时取消
					local uInfo = UserInfoModel.GetUserInfo(dInfo.customeruserid)
					if uInfo ~= nil then
						if tonumber(dInfo.payidlist) == 8401 then
							VendorOrderService.CanelOrderCionPay(uInfo, dInfo, true)
						else
							VendorOrderService.CanelOrder(uInfo, dInfo, true)
						end
						
					end
				end
				
			elseif dInfo.status == g_marketDefine.deal_status_pay then
				--订单已经付款，等待放行
				--[[
				if ( g_marketDefine.order_time_out + TimeUtils.GetTime(dInfo.paytime)) < NowTime  then
					--到时间不放行就改变状态
					CustomerOrderModel.UpdateStatus(dInfo, g_marketDefine.deal_status_timeout_frozen)
				end
				]]
			elseif dInfo.status == g_marketDefine.deal_status_appeal then
				--申诉中，在冻结的状态下，可以进行申诉
				
			elseif dInfo.status == g_marketDefine.deal_status_matching then
				--匹配中
		
			else
				--其他状态都都是订单已完成的
				CustomerOrderModel.delCustomerOrderList(dealid)
				CustomerOrderModel.delCustomerOrderInfo(dealid)
			end
		end
		ThreadManager.DealUnLock(dealid)
	end
	
	local dealList = CustomerOrderModel.GetCustomerWithdrawOrderList()
	local NowTime = TimeUtils.GetTime()
	for _, dealid in pairs(dealList) do
		
		ThreadManager.DealLock(dealid)
		local dInfo = CustomerOrderModel.GetCustomerOrderInfo(dealid)
		if dInfo ~= nil then
			if (dInfo.status == g_marketDefine.deal_status_wait_timeout or dInfo.status == g_marketDefine.deal_status_cancel)  and dInfo.iswait == 1 then
				if TimeUtils.GetTime(dInfo.canceltime) + g_marketDefine.order_time_rest <= NowTime then
					local voItem = VendorOrderModel.GetVendorOrderInfo(dInfo.vendororderid)
					if voItem ~= nil then
						voItem.unsoldordernum = voItem.unsoldordernum - 1
						voItem.unsoldorderamount = tostring(tonumber(voItem.unsoldorderamount) - tonumber(dInfo.amount))
						voItem.unsoldordermoney = tostring((tonumber(voItem.unsoldordermoney) or 0) - tonumber(dInfo.money))
						voItem.cancelnum = voItem.cancelnum + 1
						local sqlCase = "update dy_vendor_order set unsold_order_num=unsold_order_num-1, unsold_order_amount=unsold_order_amount-"
								..tonumber(dInfo.amount)..",unsold_order_money=unsold_order_money-"..tonumber(dInfo.money).." and cancel_order_num=cancel_order_num+1 where id="..voItem.hangid
						mysqlItem:execute(sqlCase)
						VendorOrderModel.SetVendorOrderInfo(voItem.hangid, voItem)	
					end
					local sqlCase = "update dy_customer_order set is_wait=0 where id="..dInfo.dealid
					mysqlItem:execute(sqlCase)
					CustomerOrderModel.delCustomerOrderList(dealid)
					CustomerOrderModel.delCustomerOrderInfo(dealid)
					CustomerOrderModel.delCustomerWithdrawOrderList(dealid)
				end
			end
		end
		ThreadManager.DealUnLock(dealid)
	end
	
	for i = 1, 10 do 
		local id = CustomerOrderModel.GetCallbackOrder()
		if id ~= nil then
			processWork("CallbackOrderWorker", id)
		else
			break
		end
	end
		
end

--获取erc20 中usdt对比人民币的汇价
function CoinInfoService.Erc20USDTPrice(channel, dealtype, currencyType, isUserPrice, userid)
	
	if isUserPrice == true then
		local sqlCase = "select * from dy_coin_info where coin_id="..currencyType.." and channel='"..userid.."'"
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch({})
		if sqlData ~= nil then
			if dealtype == g_marketDefine.hang_buy then
				--买价
				if tonumber(sqlData[21]) == 1 then
					return tonumber(sqlData[22])
				else
					return tonumber(sqlData[6]) + tonumber(sqlData[10])
				end
			elseif dealtype == g_marketDefine.hang_sell then  
				--卖价
				if tonumber(sqlData[23]) == 1 then
					return tonumber(sqlData[24])
				else
					return tonumber(sqlData[7]) + tonumber(sqlData[11])
				end
			elseif dealtype == g_marketDefine.hang_coin_pay then  
				--币支付
				if tonumber(sqlData[27]) == 1 then
					return tonumber(sqlData[28])
				else
					return tonumber(sqlData[8]) + tonumber(sqlData[26])
				end
			end
		end
	end
	
	local sqlCase = "select * from dy_coin_info where coin_id="..currencyType.." and channel='"..channel.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	
	if sqlData ~= nil then
		if dealtype == g_marketDefine.hang_buy then
			--买价
			if tonumber(sqlData[21]) == 1 then
					return tonumber(sqlData[22])
			else
				return tonumber(sqlData[6]) + tonumber(sqlData[10])
			end
		elseif dealtype == g_marketDefine.hang_sell then  
			--卖价
			if tonumber(sqlData[23]) == 1 then
				return tonumber(sqlData[24])
			else
				return tonumber(sqlData[7]) + tonumber(sqlData[11])
			end
		elseif dealtype == g_marketDefine.hang_coin_pay then  
			--币支付
			if tonumber(sqlData[27]) == 1 then
				return tonumber(sqlData[28])
			else
				return tonumber(sqlData[8]) + tonumber(sqlData[26])
			end
		end
	end
	
end


--获取erc20 中usdt对比人民币的汇价
function CoinInfoService.getListUSDTPrice(channel, dealtype, isUserPrice, userid, priceList)
	
	if isUserPrice == true then
		if priceList[userid] ~= nil then
			return priceList[tostring(userid)][dealtype]
		end
	end
	
	return priceList[channel][dealtype]
end

--获取erc20 中usdt对比人民币的汇价
function CoinInfoService.getOTCExchangePrice(priceType)
	
	local httpUrl = g_get_price_http
	local getData = HttpGet(httpUrl)
	if getData ~= "" and getData ~= nil then
		local s1 = string.sub(getData, 0, 1)
		if getData ~= nil and (s1 == '[' or s1 == '{') then
		getData = luajson.decode(getData)  
			if getData.code == 1 then
				if priceType == g_marketDefine.deal_buy then
					return tonumber(getData.data.inPrice)
				else
					return tonumber(getData.data.outPrice)
				end
			end
		end
	end
end




function CoinInfoService.orderTypeConversion(orderType, customeruserid,vendoruserid, userid)
	
	if userid == customeruserid then
		--用户是顾客
		if orderType == g_marketDefine.deal_buy then
			--买
			return g_marketDefine.deal_buy
			
		else
			--卖
			return g_marketDefine.deal_sell
		end
		
	else
		--用户是币商
		if orderType == g_marketDefine.deal_buy then
			--卖
			return g_marketDefine.deal_sell
			
			
		else
			--买 
			return g_marketDefine.deal_buy
		end
		
		
	end
	
end

function CoinInfoService.PassOrderCoinPay(userID, orderID, txID)
	
	local uInfo = UserInfoModel.GetUserInfo(userID)
	if uInfo == nil then
		return false
	end
	
	ThreadManager.DealLock(orderID)
	local coinfo = CustomerOrderModel.GetCustomerOrderInfo(orderID)
	if coinfo == nil then
		--挂单的信息不存在
		ThreadManager.DealUnLock(orderID)
		return false
	end
	
	VendorOrderService.PassOrderCoinPay(uInfo, coinfo, txID)
	
	ThreadManager.DealUnLock(orderID)
	return true
end

--获取提币价格
function CoinInfoService.withdrawCoinPrice(channel, currencyType, isUserPrice, userid)
	
	if isUserPrice == true then
		local sqlCase = "select * from dy_coin_info where coin_id="..currencyType.." and channel='"..userid.."'"
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch({})
		if sqlData ~= nil then
			if tonumber(sqlData[31]) == 1 then
				return tonumber(sqlData[32])
			else
				return tonumber(sqlData[6]) + tonumber(sqlData[30])
			end
		end
	end
	
	local sqlCase = "select * from dy_coin_info where coin_id="..currencyType.." and channel='"..channel.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	
	if sqlData ~= nil then
		if tonumber(sqlData[31]) == 1 then
			return tonumber(sqlData[32])
		else
			return tonumber(sqlData[6]) + tonumber(sqlData[30])
		end
	end
	
end