
VendorOrderService = {}

--获取erc20 中usdt对比人民币的汇价
function VendorOrderService.Init()
	
	--
	--这边初始化整个流程
	
	
end


function VendorOrderService.ServerLoop()
	--把全部订单取下来，检查订单的状态
	
	
	local hangList = VendorOrderModel.GetVendorOrderList(0, -1)
	for _, hangid in pairs(hangList) do
		ThreadManager.OrderLock(hangid)
		local hInfo = VendorOrderModel.GetVendorOrderInfo(hangid)
		
		if hInfo ~= nil then
			--检查对应的状态，同事发送消息通知
			if hInfo.autoswitch == 0 then
				local amount = tonumber(hInfo.amount)
				local dealorderamount = tonumber(hInfo.dealorderamount)
				local uInfo = UserInfoModel.GetUserInfo(hInfo.userid)
				if uInfo ~= nil then
					local price = hInfo.pricetype == 0 and tonumber(hInfo.price) or CoinInfoService.Erc20USDTPrice(uInfo.channel, hInfo.type, g_marketDefine.currency_type.USDT, true, uInfo.userid)
					local money = ((amount - dealorderamount) * price)
					if hInfo.dealmodel == 1 then
						money = tonumber(hInfo.predictmoney) - (dealorderamount * price)
					end
					local preAuthorizationMoney = tonumber(uInfo.preauthorization) or 0
					local preAuthorizationAmount = 0
					if preAuthorizationMoney > 0 then
						preAuthorizationAmount = tonumber(string.format("%.10f", tonumber(preAuthorizationMoney / price)))
					end	
					if (uInfo.dealcointype == 0 and  (money < tonumber(hInfo.minmoney)))
					or (uInfo.dealcointype == 1 and  ((tonumber(hInfo.predictmoney) - (tonumber(hInfo.dealordermoney) or 0)) < tonumber(hInfo.minmoney)))
					or (uInfo.dealcointype == 0 and ((tonumber(uInfo.ercusdtamount) - tonumber(uInfo.ercusdtlockamount) + preAuthorizationAmount) < (tonumber(hInfo.amount) - tonumber(hInfo.unsoldorderamount) - tonumber(hInfo.dealorderamount))))
					or (uInfo.dealcointype == 1 and ((tonumber(uInfo.ercfcamount) - tonumber(uInfo.ercfclockamount) + preAuthorizationMoney) < (tonumber(hInfo.predictmoney) - (tonumber(hInfo.unsoldordermoney) or 0) - (tonumber(hInfo.dealordermoney) or 0))))
					then
						if hInfo.isexternal == 0 or hInfo.isassigned == 1 then
							--剩下的额度小于最大限额就就失效
							hInfo.enablestatus = 0
							if (uInfo.dealcointype == 0 and ((tonumber(uInfo.ercusdtamount) - tonumber(uInfo.ercusdtlockamount)) < (tonumber(hInfo.amount) - tonumber(hInfo.unsoldorderamount) - tonumber(hInfo.dealorderamount))))
							or (uInfo.dealcointype == 1 and ((tonumber(uInfo.ercfcamount) - tonumber(uInfo.ercfclockamount)) < (tonumber(hInfo.predictmoney) - (tonumber(hInfo.unsoldordermoney) or 0) - (tonumber(hInfo.dealordermoney) or 0))))
							then
								hInfo.enablestatus = 4
							end
							local sqlCase = "update dy_vendor_order set enable_status="..hInfo.enablestatus.." where id="..hInfo.hangid
							mysqlItem:execute(sqlCase)	
							VendorOrderModel.DelVendOrorderInfo(hInfo.type, hInfo.userid, hInfo.hangid)
						end
					end
				end
			end
		end
		ThreadManager.OrderUnLock(hangid)
	end
end

function VendorOrderService.matchOrderLoop(currTime)
	
	local tTable = TimeUtils.GetTableTime(currTime)
	if tTable.sec == 30 or tTable.sec == 0 then
		--if VendorOrderModel.getMatchOrderState() == nil then
			processWork("MatchOrderWoker", 0)
		--end
	end
	
end

function VendorOrderService.DealOrder(uInfo, coinfo, voItem, dealtype, currencyCount, moneyCount, fromInfo, price, payType, tradeID)
	
	if voItem.userid == uInfo.userid then
		--自己的单
		return 1, "不能购买或出售自己的订单"
	end
	
	--检查建议类型匹不匹配
	if dealtype ~= ((voItem.type + 1) % 2) then
		return ReturnCode["deal_order_type_error"][1],ReturnCode["deal_order_type_error"][2]
	end
	
	--挂单的状态不对
	if voItem.enablestatus ~= 1 then
		return ReturnCode["deal_order_not_match"][1],ReturnCode["deal_order_not_match"][2]
	end
	
	--检查是否在限额之内
	if moneyCount < tonumber(voItem.minmoney) or moneyCount > tonumber(voItem.maxmoney) then
		return ReturnCode["hang_deal_err1"][1],ReturnCode["hang_deal_err1"][2]	
	end
	
	--获取挂单用户信息
	local vUserInfo = UserInfoModel.GetUserInfo(voItem.userid) 
	if vUserInfo == nil then
		return ReturnCode["human_user_not_exist"][1],"挂单的卖家不存着"	
	end
	
	local channelInfo = {}
	local sqlCase = "select channel,is_external from dy_channel_info"
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		local tmp = {}
		tmp['isExternal'] = tonumber(sqlData[2])
		channelInfo[sqlData[1]] = tmp
	end
	
	local payee = {}			--收款人
	local payer = {}			--付款人
	if dealtype == g_marketDefine.hang_buy then
		--顾客买币
		payee = vUserInfo
		payer = uInfo
	else
		--顾客卖币
		payee = uInfo
		payer = vUserInfo
	end
	
	
	--检查挂单的剩余数量是否足够
	if voItem.autoswitch == 0 then
		local sqlCase = "select unsold_order_amount,deal_order_amount,unsold_order_money,deal_order_money  from dy_vendor_order where id="..voItem.hangid
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			return 1,"挂单不存在"	
		end
		
		local unsoldorderamount = tonumber(sqlData[1])
		local dealorderamount = tonumber(sqlData[2])
		local unsoldordermoney = tonumber(sqlData[3])
		local dealordermoney = tonumber(sqlData[4])
		if payee.dealcointype == 0 then
			local AvailableCount = tonumber(voItem.amount) - unsoldorderamount - dealorderamount
			AvailableCount = tonumber(string.format("%.10f", tostring(AvailableCount)))
			currencyCount = tonumber(string.format("%.10f", tostring(currencyCount)))
			
			if AvailableCount < currencyCount then
				return ReturnCode["deal_order_not_enough"][1],ReturnCode["deal_order_not_enough"][2]
			end
		else
			local AvailableMoney = tonumber(voItem.predictmoney) - unsoldordermoney - dealordermoney
			AvailableMoney = tonumber(string.format("%.10f", tostring(AvailableMoney)))
			moneyCount = tonumber(string.format("%.10f", tostring(moneyCount)))
			if AvailableMoney < moneyCount then
				return ReturnCode["deal_order_not_enough"][1],ReturnCode["deal_order_not_enough"][2]
			end
		
		end
	end
	
	--获取检查收款人的收款方式
	local payTypeList = {}
	
	local dealType = 100
	local cInfo = UserInfoModel.GetUserInfo(uInfo.userid)
	if cInfo.usertype == 300 or cInfo.usertype == 301 then
		dealType = 200
	end
	
	local vInfo = UserInfoModel.GetUserInfo(voItem.userid)
	if vInfo.usertype == 300 or vInfo.usertype == 301 then
		dealType = 300
	end
	
	if channelInfo[payee.channel].isExternal == 0 then
		if dealType ~= 300 then
			payTypeList = UserPayModel.GetBankAndAliPayInfo(payee, payType, moneyCount) --
			--支付方式按最后的时间排序
			if #payTypeList > 0 then
				--查询玩家是否存在相同的金额的订单
				local orderList = {}
				local sqlCase = "select payee_account from dy_customer_order where money="..moneyCount.." and (status=1 or status=3 or status=9)"
						.." and ((type=0 and vendor_user_id="..payee.userid..") or (type=1 and customer_user_id="..payee.userid.."))"
				mysqlItem:executeQuery(sqlCase)
				while true do 
					local sqlData = mysqlItem:fetch()
					if sqlData == nil then 
						break;
					end
					orderList[sqlData] = 1
				end
				
				for i = #payTypeList, 1,-1  do 
					if orderList[payTypeList[i].account] == 1 then
						table.remove(payTypeList, i)
					end
				end
				
				table.sort(payTypeList, function(a,b)
					local aT = TimeUtils.GetTime((a.deallasttime == nil or a.deallasttime == "") and "2020-01-01 00:00:00" or a.deallasttime)
					local bT = TimeUtils.GetTime((b.deallasttime == nil or b.deallasttime == "") and "2020-01-01 00:00:00" or b.deallasttime)
					if aT < bT then
						return true
					else
						return false
					end
				end)
			end
		else
			local tmp = {}
			tmp["id"] = 0
			tmp["paytype"] = g_humanDefine.user_payList.bank
			tmp["account"] = string.text2html(voItem.payeeaccount)
			tmp["payee"] = string.text2html(voItem.payeename)
			tmp["qrcode"] = ""
			tmp["bankname"] = string.text2html(voItem.payeebank)
			tmp["bankaddr"] = string.text2html(voItem.payeebandaddr)
			tmp["singlelimit"] = voItem.maxmoney
			tmp["daylimit"] = voItem.maxmoney
			tmp["fourthpartyid"] = 0
			tmp["deallasttime"] = "2020-01-01 00:00:00"
			table.insert(payTypeList, tmp)
		end
	else
		local tmp = {}
		tmp["id"] = 0
		tmp["paytype"] = payType
		tmp["account"] = ""
		tmp["payee"] = ""
		tmp["qrcode"] = ""
		tmp["bankname"] = ""
		tmp["bankaddr"] = ""
		tmp["singlelimit"] = ""
		tmp["daylimit"] = ""
		tmp["fourthpartyid"] = 0
		tmp["deallasttime"] = "2020-01-01 00:00:00"
		table.insert(payTypeList, tmp)
	end
	
	if #payTypeList == 0 then
		return 1, "收款人未找到合适的收款方式"
	end
	local payInfo = payTypeList[1]
	coinfo.dealid = 0
	coinfo.type = dealtype
	coinfo.vendororderid = voItem.hangid
	coinfo.customeruserid = uInfo.userid
	coinfo.vendoruserid =voItem.userid
	coinfo.price = tostring(price)
	coinfo.amount = tostring(tonumber(string.format("%0.10f", currencyCount)))
	coinfo.money = tostring(moneyCount)
	coinfo.status = g_marketDefine.deal_status_wait  --进入等待
	coinfo.paytypelist = luajson.encode(payInfo)
	coinfo.payidlist = tostring(payType)
	coinfo.createtime = TimeUtils.GetTimeString()
	coinfo.updatetime = coinfo.createtime
	coinfo.paytime = ''
	coinfo.passtime = ''
	coinfo.channel = uInfo.channel
	coinfo.publicprice = tostring(CoinInfoService.Erc20USDTPrice(vInfo.channel,voItem.type, g_marketDefine.currency_type.USDT, true, vInfo.userid))
	coinfo.customerusernickname = uInfo.nickname
	coinfo.vendorusernickname = vUserInfo.nickname
	coinfo.withdrawtype = voItem.withdrawtype
	coinfo.isexternal = voItem.isexternal
	local IP = ""
	if fromInfo == nil then
		local sqlCase = "select notify_info from dy_vendor_order where id='"..voItem.hangid.."'"
		mysqlItem:executeQuery(sqlCase) 
		local sqlData = mysqlItem:fetch()
		if voItem.withdrawtype == 0 or sqlData == "" then
			coinfo.notifyurl = ""
			coinfo.body = ""
			local tTable = TimeUtils.GetTableTime()
			local rNum = ""
			for i = 1, 6 do 
				rNum = rNum..math.myrandom(0,9)
			end	
			local orderID = "A"..tTable.year..tTable.month..tTable.day..tTable.hour..tTable.min..tTable.sec..rNum
			coinfo.merchantorderid = orderID
		else
			local notifyInfo = luajson.decode(sqlData)
			coinfo.notifyurl = notifyInfo.notifyUrl
			coinfo.body = notifyInfo.body
			coinfo.merchantorderid = notifyInfo.outOrderNo
			IP = notifyInfo.IP 
		end
	else
		coinfo.notifyurl = tostring(fromInfo["notifyUrl"])
		coinfo.body = tostring(fromInfo["body"])
		coinfo.merchantorderid = fromInfo["outOrderNo"]  
		IP = fromInfo["IP"]  
	end
	coinfo.dealtype = dealType
	coinfo.fromtype = dealType / 100
	
	coinfo.feerate = '0'
	coinfo.fee = '0'
	coinfo.sellfeerate = '0'
	coinfo.sellfee = '0'
	coinfo.buyfeerate = '0'
	coinfo.buyfee = '0'
	coinfo.getamount = tostring(currencyCount)
	coinfo.aftermoney = tostring(moneyCount)
	coinfo.income = '0'
	coinfo.feemoney = '0'
	coinfo.tradeid = tradeID
	coinfo.chaintype = 0
	coinfo.chainname = ''
	coinfo.owership = 0
	coinfo.wallettype = voItem.wallettype
	local sellFeeMoney = 0
	if dealType == 100 then
		--查找渠道的总代费率
		local sqlCase = "select userid,add_sell_comm_rate,is_add_buy_comm,is_currency_buy_rate,currency_add_buy_rate from dy_channel_info where channel='"..payee.channel.."'"
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch({})
		if sqlData ~= nil then
			local rate = 0 
			if vInfo.usertype == 200 or vInfo.usertype == 201 then
				if tonumber(sqlData[3]) == 1 then
					rate = tonumber(sqlData[2])
				end
			elseif vInfo.usertype == 202 then
				if tonumber(sqlData[4]) == 1 then
					rate = tonumber(sqlData[5])
				end
			end
			if rate > 0 then
				local userRate = UserInfoModel.getUserFeeRate(tonumber(sqlData[1]), payInfo.paytype, g_humanDefine.sell_comm_rate) 
				coinfo.feerate = tostring(userRate + rate)
				coinfo.fee = tostring(tonumber(coinfo.feerate)  * currencyCount)
				coinfo.feemoney = tostring(tonumber(coinfo.feerate) * moneyCount)
				coinfo.aftermoney = tostring(moneyCount - tonumber(coinfo.feemoney))
				coinfo.buyfeerate = tostring(userRate)
				coinfo.buyfee = tostring(userRate * currencyCount)
				coinfo.getamount = tostring(currencyCount - tonumber(coinfo.fee))
				coinfo.income = tostring(currencyCount * UserInfoModel.getUserFeeRate(payee.userid, payInfo.paytype, g_humanDefine.sell_comm_rate)) 
			end
		end
	elseif dealType == 200 then
		local userRate =UserInfoModel.getUserFeeRate(payer.userid, payInfo.paytype, g_humanDefine.buy_fee_rate)
		coinfo.feerate = tostring(userRate)
		coinfo.fee = tostring(userRate * currencyCount)
		coinfo.feemoney = tostring(tonumber(coinfo.feerate)  * moneyCount)
		coinfo.aftermoney = tostring(moneyCount - tonumber(coinfo.feemoney))
		coinfo.buyfeerate = tostring(userRate)
		coinfo.buyfee = tostring(userRate * currencyCount)
		coinfo.getamount = tostring(currencyCount - tonumber(coinfo.fee))
		coinfo.income = tostring(currencyCount * UserInfoModel.getUserFeeRate(payee.userid, payInfo.paytype, g_humanDefine.sell_comm_rate)) 
	elseif dealType == 300 then
		local feeType = voItem.withdrawtype == 0 and g_humanDefine.sell_fee_rate or g_humanDefine.behalf_sell_fee_rate
		local userRate = UserInfoModel.getUserFeeRate(payee.userid, payInfo.paytype, feeType)
		coinfo.feerate = tostring(userRate)
		coinfo.fee = tostring(userRate * currencyCount)
		coinfo.feemoney = tostring(tonumber(coinfo.feerate)  * moneyCount)
		coinfo.sellfeerate = coinfo.feerate
		coinfo.sellfee = coinfo.fee
		sellFeeMoney = tonumber(coinfo.feemoney)
	end
	
	
	if channelInfo[payee.channel].isExternal == 0 then
		--先收款人的资金检查一次	
		if coinfo.wallettype == 0 then
			if payee.dealcointype == 1 then
				if (tonumber( payee.ercfcamount) - tonumber( payee.ercfclockamount)) < moneyCount + sellFeeMoney then
					return 1, "收款人的币数量不足"		
				end
			else
				if (tonumber( payee.ercusdtamount) - tonumber( payee.ercusdtlockamount)) < currencyCount + tonumber(coinfo.sellfee) then
					return 1, "收款人的余额不足"	
				end	
			end
		else
			if (tonumber( payee.coinpayusdtamount) - tonumber( payee.coinpayusdtlockamount)) < currencyCount + tonumber(coinfo.sellfee) then
				return 1, "收款人的币付钱包余额不足"		
			end
		end
	end
	
	
	if nil == CustomerOrderModel.InserInto(coinfo, payInfo, IP) then
	--先插入数据库
		return 1, "交易失败"	
	end
	
	if coinfo.wallettype == 0 then
		--检查成交货币类型 如果是法币交易
		if payee.dealcointype == 1 then
			if false == UserInfoModel.AddErcFCLockAmount(payee, moneyCount + sellFeeMoney, coinfo.dealid, coinfo.type, "卖币下单冻结") then
				local sqlCase = "update dy_customer_order set status="..g_marketDefine.deal_status_failed.." where id="..coinfo.dealid
				mysqlItem:execute(sqlCase)
				return 1, "收款人的币数量不足"		
			end
		end
		
		--冻结收款人的币
		if false == UserInfoModel.AddErcUsdtLockAmount(payee, currencyCount + tonumber(coinfo.sellfee), coinfo.dealid, coinfo.type, "卖币下单冻结",channelInfo[payee.channel].isExternal) then
			local sqlCase = "update dy_customer_order set status="..g_marketDefine.deal_status_failed.." where id="..coinfo.dealid
			mysqlItem:execute(sqlCase)
			return 1, "收款人的币数量不足"		
		end
	else
		--冻结收款人的币
		if false == UserInfoModel.AddErcCoinPayUsdtLockAmount(payee, currencyCount + tonumber(coinfo.sellfee), coinfo.dealid, coinfo.type, "卖币下单冻结",channelInfo[payee.channel].isExternal) then
			local sqlCase = "update dy_customer_order set status="..g_marketDefine.deal_status_failed.." where id="..coinfo.dealid
			mysqlItem:execute(sqlCase)
			return 1, "收款人的币数量不足"		
		end
	
	end
	
	--修改支付账号的最后交易时
	local pay = UserInfoModel.GetPayInfo(payee.userid, payType, payInfo.id)
	if pay ~= nil then
		pay.deallasttime = TimeUtils.GetTimeString()
		UserInfoModel.SetPayInfo(pay)
		local sqlCase = "update dy_user_pay set deal_last_time='"..pay.deallasttime.."' where id="..pay.id
		mysqlItem:execute(sqlCase)
	end
	
	voItem.unsoldordernum = voItem.unsoldordernum + 1
	voItem.unsoldorderamount = tostring(tonumber(voItem.unsoldorderamount) + currencyCount)
	voItem.unsoldordermoney = tostring((tonumber(voItem.unsoldordermoney) or 0) + moneyCount)
	VendorOrderModel.SetVendorOrderInfo( voItem.hangid, voItem )
	local sqlCase = "update dy_vendor_order set unsold_order_num=unsold_order_num+1, unsold_order_amount=unsold_order_amount+"
		..currencyCount..",unsold_order_money=unsold_order_money+"..moneyCount.." where id="..voItem.hangid
	mysqlItem:execute(sqlCase)

	--向前端更新用户冻结金币
	UserInfoModel.SendErcUsdtLockAmount(payee)
	
	--向客户更新用户大厅的待处理列表
	local userList = {payee.userid, payer.userid} 
	NoticeServices.NoticeHangSellOut(userList)
	
	--统计下单情况
	LogDispatch.userDealOrderCount(payer.userid, payee.userid)
	
	--统计支付方式的累计收款量
	UserInfoModel.SetDayLimit(payInfo.id, coinfo.money)
	
end

function VendorOrderService.CanelOrder(uInfo, coinfo, isTimeOut)
	
	--检查订单状态
	if coinfo.status ~= g_marketDefine.deal_status_wait then
		return 1, "该订单状态不能取消"
	end
	
	if tonumber(coinfo.payidlist) == 8401 then
		return 1, "币支付订单不支持手动取消"
	end
	
	local payee = nil  		--收款人
	if coinfo.type == g_marketDefine.hang_buy then
		--对于顾客是买币
		payee = UserInfoModel.GetUserInfo(coinfo.vendoruserid)
		if payee == nil then
			return ReturnCode["human_user_not_exist"][1], ReturnCode["human_user_not_exist"][2]
		end
	else
		--对于顾客是卖币
		payee = UserInfoModel.GetUserInfo(coinfo.customeruserid)
		if payee == nil then
			return ReturnCode["human_user_not_exist"][1], ReturnCode["human_user_not_exist"][2]
		end
	end
	
	
	local msg = isTimeOut == false and "卖币手动取消订单，解除冻结" or "卖币超时取消订单，解除冻结"
	
	--解冻订单所冻结的金额
	local sellfeeCount = 0
	local sellfeeMoney = 0
	if coinfo.dealtype == 300 then
		sellfeeCount = tonumber(coinfo.fee)
		sellfeeMoney = tonumber(coinfo.feemoney)
	end
	
	if coinfo.wallettype == 0 then
		--解冻订单所冻结的金额
		UserInfoModel.DecErcUsdtLockAmount(payee, (coinfo.amount + sellfeeCount), coinfo.dealid, coinfo.type, msg)
		
		if payee.dealcointype == 1 then
			--如果用户是法币用户就结算一下法币账户
			UserInfoModel.DecErcFCLockAmount(payee, (tonumber(coinfo.money) + sellfeeMoney), coinfo.dealid, coinfo.type, msg)
		end
	else
		UserInfoModel.DecErcCoinPayUsdtLockAmount(payee, (coinfo.amount + sellfeeCount), coinfo.dealid, coinfo.type, msg)
	end
	
	
	--更新订单状态
	if isTimeOut == true then
		CustomerOrderModel.UpdateStatus(coinfo, g_marketDefine.deal_status_wait_timeout ) --超时取消订单
	else
		CustomerOrderModel.UpdateStatus(coinfo, g_marketDefine.deal_status_cancel ) --取消订单
	end		
	
	--更新挂单的情况
	local voItem = VendorOrderModel.GetVendorOrderInfo(coinfo.vendororderid)
	if voItem ~= nil then
		if coinfo.dealtype == 300 and voItem.dealmodel == 0 then
			coinfo.iswait = 1
			CustomerOrderModel.SetCustomerOrderInfo(coinfo)	
			local sqlCase = "update dy_customer_order set is_wait="..coinfo.iswait.." where id="..coinfo.dealid
			mysqlItem:execute(sqlCase)
		else
			voItem.unsoldordernum = voItem.unsoldordernum - 1
			voItem.unsoldorderamount = tostring(tonumber(voItem.unsoldorderamount) - tonumber(coinfo.amount))
			voItem.unsoldordermoney = tostring((tonumber(voItem.unsoldordermoney) or 0) - tonumber(coinfo.money))
			voItem.cancelnum = voItem.cancelnum + 1
			local sqlCase = "update dy_vendor_order set unsold_order_num=unsold_order_num-1, unsold_order_amount=unsold_order_amount-"
					..tonumber(coinfo.amount)..",unsold_order_money=unsold_order_money-"..tonumber(coinfo.money).." and cancel_order_num=cancel_order_num+1"
			if voItem.dealmodel == 1 then
				local restTime = TimeUtils.GetTime() + g_marketDefine.order_time_rest
				voItem.resttime = TimeUtils.GetTimeString(restTime)
				sqlCase = sqlCase..",rest_time='"..voItem.resttime.."'"
			end
			VendorOrderModel.SetVendorOrderInfo(voItem.hangid, voItem)	
			sqlCase = sqlCase.." where id="..voItem.hangid
			mysqlItem:execute(sqlCase)
		end
	end
	
	--先前端更新用户的冻结金币
	UserInfoModel.SendErcUsdtLockAmount(payee)
	
	--记录取消次数
	CustomerOrderModel.MinusFreeDealNum(uInfo.userid)
	
	--统计取消订单的情况
	LogDispatch.userCancelOrderCount(uInfo.userid, payee.userid)
	
	--更新玩家的大厅的待处理列表
	local userList = {coinfo.vendoruserid, coinfo.customeruserid}
	NoticeServices.DealCancel(userList)
	
	--统计支付方式的累计收款量
	local payInfo = luajson.decode(coinfo.paytypelist)
	UserInfoModel.SetDayLimit(payInfo.id, -coinfo.money)

end

function VendorOrderService.CanelOrderCionPay(uInfo, coinfo, isTimeOut)
	
	--检查订单状态
	if coinfo.status ~= g_marketDefine.deal_status_wait then
		return 1, "该订单状态不能取消"
	end

	--更新订单状态
	if isTimeOut == true then
		CustomerOrderModel.UpdateStatus(coinfo, g_marketDefine.deal_status_wait_timeout ) --超时取消订单
	else
		CustomerOrderModel.UpdateStatus(coinfo, g_marketDefine.deal_status_cancel ) --取消订单
	end		
	
	--记录取消次数
	CustomerOrderModel.MinusFreeDealNum(uInfo.userid)
	
	--统计取消订单的情况
	LogDispatch.userCancelOrderCount(uInfo.userid, 0)
	
	--统计支付方式的累计收款量
	local payInfo = luajson.decode(coinfo.paytypelist)
	UserInfoModel.SetDayLimit(payInfo.id, -coinfo.money)

end

function VendorOrderService.PaidOrder(uInfo, coinfo, proofurl, isExternal)
	
	--检查订单状态
	if coinfo.status ~= g_marketDefine.deal_status_wait or (isExternal == 1 and coinfo.status == g_marketDefine.deal_status_wait_timeout) then
		return 1, "该订单状态不能确认付款"
	end	
	
	if tonumber(coinfo.payidlist) == 8401 then
		return 1, "币支付订单不支持确认付款操作"
	end
	
	local payer = nil  		--付款人
	if coinfo.type == g_marketDefine.hang_buy then
		--对于顾客是买币
		payer = coinfo.customeruserid
	else
		--对于顾客是卖币
		payer = coinfo.vendoruserid
	end
	
	if payer ~= uInfo.userid then
		return 1, "您不是付款人无法完成确定支付操作"
	end
	
	if proofurl ~= nil then
		while #coinfo.proofurl > 0 do 
			coinfo.proofurl:remove(1)
		end
		for k,v in ipairs(proofurl) do
			coinfo.proofurl:append(v)
			ChatModel.InsertInto(coinfo.dealid,coinfo.merchantorderid,coinfo.vendoruserid,coinfo.customeruserid,coinfo.dealtype,uInfo.channel,g_chatDefine.chat_type['customer_to_merchant'],2,v)
		end
	end
	
	--更新订单状态
	CustomerOrderModel.UpdateStatus(coinfo, g_marketDefine.deal_status_pay)
	
	--向客户更新用户大厅的待处理列表
	local userList = {coinfo.vendoruserid, coinfo.customeruserid}
	NoticeServices.NoticeOrderPass(userList)
	
end

function VendorOrderService.PassOrder(uInfo, coinfo)	
	
	if tonumber(coinfo.payidlist) == 8401 then
		return 1, "币支付订单不支持手动放行"
	end
	
	local payee = {}   			--收款人
	local payer = {}   			--付款人
	local amount = tonumber(coinfo.amount)
	local money = tonumber(coinfo.money)
	local payType = tonumber(coinfo.payidlist) 			--支付方式
	if coinfo.type == g_marketDefine.hang_buy then
		--顾客买币
		payee = UserInfoModel.GetUserInfo(coinfo.vendoruserid)
		if payee == nil then
			return ReturnCode["human_user_not_exist"][1], ReturnCode["human_user_not_exist"][2]
		end
		payer = UserInfoModel.GetUserInfo(coinfo.customeruserid)
		if payer == nil then
			return ReturnCode["human_user_not_exist"][1], ReturnCode["human_user_not_exist"][2]
		end
	else
		--顾客卖币
		payee = UserInfoModel.GetUserInfo(coinfo.customeruserid)
		if payee == nil then
			return ReturnCode["human_user_not_exist"][1], ReturnCode["human_user_not_exist"][2]
		end
		payer = UserInfoModel.GetUserInfo(coinfo.vendoruserid)
		if payer == nil then
			return ReturnCode["human_user_not_exist"][1], ReturnCode["human_user_not_exist"][2]
		end
	end 
	local sellfeeCount = 0
	local buyfeeCount = 0
	local feeCount = 0
	local sellFeeMoney = 0
	local buyFeeMoney = 0
	if coinfo.dealtype == 100 then
		buyfeeCount = tonumber(coinfo.fee)
		feeCount = tonumber(coinfo.buyfee)
		buyFeeMoney = tonumber(coinfo.feemoney)
	elseif coinfo.dealtype == 200 then
		buyfeeCount = tonumber(coinfo.fee)
		feeCount = tonumber(coinfo.buyfee)
		buyFeeMoney = tonumber(coinfo.feemoney)
	elseif coinfo.dealtype == 300 then
		sellfeeCount = tonumber(coinfo.sellfee)
		feeCount = tonumber(coinfo.sellfee)
		sellFeeMoney = tonumber(coinfo.feemoney)
	end
	
	--收款人减少总额
	local orderType = (payee.usertype == 200 or payee.usertype == 201 or payee.usertype == 202) and g_humanDefine.fund_details.type_sell or g_humanDefine.fund_details.type_withdraw
	if coinfo.wallettype == 0 then
		UserInfoModel.DecErcUsdtAmount(payee, (amount + sellfeeCount), coinfo.dealid, coinfo.type, "卖币交易成功，扣除资金",orderType,(money + sellFeeMoney))
		UserInfoModel.SendErcUsdtAmount(payee)
		--收款人解冻资金
		UserInfoModel.DecErcUsdtLockAmount(payee, (amount + sellfeeCount), coinfo.dealid, coinfo.type, "卖币交易成功，解除资金冻结")
		UserInfoModel.SendErcUsdtLockAmount(payee)
		
		if payee.dealcointype == 1 then
			--结算货币类型如果是法币把法币账户也结算一下
			UserInfoModel.DecErcFCAmount(payee, (money + sellFeeMoney), coinfo.dealid, coinfo.type, "卖币交易成功，扣除资金",orderType)
			UserInfoModel.DecErcFCLockAmount(payee, (money + sellFeeMoney), coinfo.dealid, coinfo.type, "卖币交易成功，解除资金冻结")
		end
	else
		UserInfoModel.DecErcCoinPayUsdtAmount(payee, (amount + sellfeeCount), coinfo.dealid, coinfo.type, "卖币交易成功，扣除资金",orderType,(money + sellFeeMoney))
		UserInfoModel.DecErcCoinPayUsdtLockAmount(payee, (amount + sellfeeCount), coinfo.dealid, coinfo.type, "卖币交易成功，解除资金冻结")
	end
	
	--付款人加总额
	local orderType = (payer.usertype == 200 or payer.usertype == 201 or payer.usertype == 202) and g_humanDefine.fund_details.type_buy or g_humanDefine.fund_details.type_recharge
	UserInfoModel.AddErcUsdtAmount(payer, (amount - buyfeeCount), coinfo.dealid, coinfo.type, "买币交易成功，增加资金", orderType,(money - buyFeeMoney))
	UserInfoModel.SendErcUsdtAmount(payer)
	
	if payer.dealcointype == 1 then
		--结算货币类型如果是法币把法币账户也结算一下
		UserInfoModel.AddErcFCAmount(payer, (money - buyFeeMoney), coinfo.dealid, coinfo.type, "买币交易成功，增加资金", orderType)
	end
	
	if coinfo.status == g_marketDefine.deal_status_appeal then
		local sqlCase = "update dy_order_appeal set status=5 where deal_orderid="..coinfo.dealid
		mysqlItem:execute(sqlCase)
	end
	
	--加减能量值
	if coinfo.dealtype == 200 then
		payee.energyvalue = tostring((tonumber(payee.energyvalue) or 0) - tonumber(coinfo.money))
		payee.energyvalue = tonumber(payee.energyvalue) < 0 and "0" or payee.energyvalue
		UserInfoModel.SetUserInfo(payee)
		local sqlCase = "update dy_user_info set energy_value="..payee.energyvalue.." where userid="..payee.userid
		mysqlItem:execute(sqlCase)
		UserInfoModel.SendInfoList(payee, {"energyvalue"})
	elseif coinfo.dealtype == 300 then
		payer.energyvalue = tostring((tonumber(payer.energyvalue) or 0) + tonumber(coinfo.money))
		UserInfoModel.SetUserInfo(payer)
		local sqlCase = "update dy_user_info set energy_value="..payer.energyvalue.." where userid="..payer.userid
		mysqlItem:execute(sqlCase)
		UserInfoModel.SendInfoList(payer, {"energyvalue"})
	end
	
	
	--修改状态
	CustomerOrderModel.UpdateStatus(coinfo, g_marketDefine.deal_status_finish)
	
	--向前端更新用户大厅的待处理列表
	local userList = {payer.userid, payee.userid}
	NoticeServices.OrderCarryOut(userList)
	
	--统计放行订单
	local confirm_time = TimeUtils.GetTime(coinfo.passtime) - TimeUtils.GetTime(coinfo.createtime)
	local payInfo = luajson.decode(coinfo.paytypelist)
	LogDispatch.userPassOrderCount(payer.userid, payee.userid, amount, buyfeeCount, confirm_time, payType, coinfo.money,payInfo.id, coinfo.dealtype, coinfo.withdrawtype,payInfo.paytype, coinfo.owership)
	
	--分佣
	if feeCount > 0 then
		LogDispatch.userPumpCount(coinfo.dealid)
	end

	--原挂单的统计
	local voItem = VendorOrderModel.GetVendorOrderInfo(coinfo.vendororderid)
	if voItem ~= nil then
		voItem.unsoldordernum = voItem.unsoldordernum - 1
		voItem.dealordernum = voItem.dealordernum + 1
		voItem.unsoldorderamount = tostring(tonumber(voItem.unsoldorderamount) - amount)
		voItem.unsoldordermoney = tostring((tonumber(voItem.unsoldordermoney) or 0) - tonumber(coinfo.money))
		voItem.dealorderamount = tostring(tonumber(voItem.dealorderamount) + amount)
		voItem.dealordermoney = tostring((tonumber(voItem.dealordermoney) or 0) + tonumber(coinfo.money))
		VendorOrderModel.SetVendorOrderInfo(voItem.hangid, voItem)
		local sqlCase = "update dy_vendor_order set unsold_order_num=unsold_order_num-1, unsold_order_amount=unsold_order_amount-"
			..tonumber(coinfo.amount)..",deal_order_num=deal_order_num+1,deal_order_amount=deal_order_amount+"..tonumber(coinfo.amount)
			..", deal_order_free=deal_order_free+"..feeCount..",unsold_order_money=unsold_order_money-"..tonumber(coinfo.money)
			..",deal_order_money=deal_order_money+"..tonumber(coinfo.money).." where id="..voItem.hangid
		mysqlItem:execute(sqlCase)
	end
	
	--等待回调
	if voItem.type == 1 and voItem.usertype == 300 and voItem.dealmodel == 0 then
		--商户的提现提现单最后一笔在回调
		if (tonumber(voItem.predictmoney) - tonumber(voItem.dealordermoney) <= 0) then
			CustomerOrderModel.addCallbackOrder(coinfo.dealid)
		end
	else
		CustomerOrderModel.addCallbackOrder(coinfo.dealid)
	end
	
end

function VendorOrderService.PassOrderCoinPay(uInfo, coinfo, tx_id)	
	
	if coinfo.owership == 0 then
		
		--[[
		UserInfoModel.AddErcUsdtAmount(uInfo, (tonumber(coinfo.amount) - tonumber(coinfo.fee)), coinfo.dealid, coinfo.type, "币支付-系统地址交易成功，增加资金"
				, g_humanDefine.fund_details.type_coin_pay,(tonumber(coinfo.money) - tonumber(coinfo.feemoney)))
		UserInfoModel.SendErcUsdtAmount(uInfo)
		
		if uInfo.dealcointype == 1 then
			--结算货币类型如果是法币把法币账户也结算一下
			UserInfoModel.AddErcFCAmount(uInfo, (tonumber(coinfo.money) - tonumber(coinfo.feemoney)), coinfo.dealid, coinfo.type, "币支付-系统地址交易成功，增加资金", g_humanDefine.fund_details.type_coin_pay)
		end
		]]
		UserInfoModel.AddErcCoinPayUsdtAmount(uInfo, (tonumber(coinfo.amount) - tonumber(coinfo.fee)), coinfo.dealid, coinfo.type, "币支付-系统地址交易成功，增加资金"
				, g_humanDefine.fund_details.type_coin_pay,(tonumber(coinfo.money) - tonumber(coinfo.feemoney)))
		
	else
		
		UserInfoModel.DecErcUsdtAmount(uInfo, tonumber(coinfo.fee), coinfo.dealid, coinfo.type, "币支付-商户地址交易成功，扣除手续费"
				, g_humanDefine.fund_details.type_coin_pay,tonumber(coinfo.feemoney))
		UserInfoModel.SendErcUsdtAmount(uInfo)
		
		if uInfo.dealcointype == 1 then
			--结算货币类型如果是法币把法币账户也结算一下
			UserInfoModel.DecErcFCAmount(uInfo, tonumber(coinfo.feemoney), coinfo.dealid, coinfo.type, "币支付-商户地址交易成功，扣除手续费", g_humanDefine.fund_details.type_coin_pay)
		end
	
	end

	--修改状态
	CustomerOrderModel.UpdateStatus(coinfo, g_marketDefine.deal_status_finish)
	local sqlCase = "update dy_customer_order set coin_pay_txid='"..tx_id.."' where id="..coinfo.dealid
	mysqlItem:execute(sqlCase)
	
	--统计放行订单
	local confirm_time = TimeUtils.GetTime(coinfo.passtime) - TimeUtils.GetTime(coinfo.createtime)
	local payInfo = luajson.decode(coinfo.paytypelist)
	LogDispatch.userPassOrderCount(uInfo.userid, 0, tonumber(coinfo.amount), tonumber(coinfo.fee), confirm_time, tonumber(coinfo.payidlist), coinfo.money, payInfo.id, coinfo.dealtype, coinfo.withdrawtype,tonumber(coinfo.payidlist), coinfo.owership)
	
	--分佣
	if tonumber(coinfo.fee) > 0 then
		LogDispatch.userPumpCount(coinfo.dealid)
	end
	
	--等待回调
	CustomerOrderModel.addCallbackOrder(coinfo.dealid)
	
end

function VendorOrderService.PaidPassOrder(uInfo, coinfo)
	
	--检查订单状态
	if coinfo.status ~= g_marketDefine.deal_status_wait then
		return 1, "该订单状态不能确认付款"
	end	
	
	local payer = {}   			--付款人
	if coinfo.type == g_marketDefine.hang_buy then
		--顾客买币
		payer = UserInfoModel.GetUserInfo(coinfo.customeruserid)
		if payer == nil then
			return ReturnCode["human_user_not_exist"][1], ReturnCode["human_user_not_exist"][2]
		end
	else
		--顾客卖币
		payer = UserInfoModel.GetUserInfo(coinfo.vendoruserid)
		if payer == nil then
			return ReturnCode["human_user_not_exist"][1], ReturnCode["human_user_not_exist"][2]
		end
	end
	if payer.userid ~= uInfo.userid then
		return 1, "您不是付款人无法完成确定支付操作"
	end
	
	local ret, msg = VendorOrderService.PassOrder(uInfo, coinfo)	
	if ret ~= nil  then
		return ret, msg
	end
	
end

function VendorOrderService.AppealOrder(uInfo, cInfo,aInfo)
	
	if cInfo.status ~= g_marketDefine.deal_status_pay then
		return 1,"该状态不能申述"
	end
	
	if tonumber(cInfo.payidlist) == 8401 then
		return 1, "币支付订单不支持申诉功能"
	end
	
	--检查有没有已经存在的订单
	local sqlCase = "select id from dy_order_appeal where deal_orderid="..cInfo.dealid
	
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		return 1,"该订单已申诉, 请勿重复提交"
	end
	
	local to_user_id = uInfo.userid == cInfo.vendoruserid and cInfo.customeruserid or cInfo.vendoruserid
	local sqlCase = "insert into dy_order_appeal(from_userid,to_userid,deal_orderid,reason,detail,proof_url,deal_type,platform_id) values("..uInfo.userid
					..","..to_user_id..","..cInfo.dealid..",'"..aInfo.reason.."','"..aInfo.description.."','"..aInfo.proofurl.."',"..cInfo.dealtype..","..uInfo.platformid..")"
	mysqlItem:execute(sqlCase)
	
	local sqlCase = "select * from dy_order_appeal where deal_orderid="..cInfo.dealid
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		return 1,"提交失败"
	end
	
	CustomerOrderModel.UpdateStatus(cInfo, g_marketDefine.deal_status_appeal)
	
	aInfo.appealid = tonumber(sqlData[1])
	aInfo.fromuserid = tonumber(sqlData[2])
	local fromuserInfo = UserInfoModel.GetUserInfo(aInfo.fromuserid)
	if fromuserInfo ~= nil then
		aInfo.fromusernickname = fromuserInfo.nickname
	end
	aInfo.touserid = tonumber(sqlData[3])
	local touserInfo = UserInfoModel.GetUserInfo(aInfo.touserid )
	if touserInfo ~= nil then
		aInfo.tousernickname = touserInfo.nickname
	end
	aInfo.orderid = tonumber(sqlData[4]) or 0
	aInfo.reason = sqlData[5] or ""
	aInfo.description = sqlData[6] or ""
	aInfo.proofurl = sqlData[7] or ""
	aInfo.replyreason = sqlData[8] or ""
	aInfo.replydescription = sqlData[9] or ""
	aInfo.replyproofurl = sqlData[10] or ""
	aInfo.replytime = sqlData[11] or ""
	aInfo.sysremark = sqlData[12] or ""
	aInfo.status = tonumber(sqlData[13]) or 0
	aInfo.needaudit = tonumber(sqlData[14]) or 0
	aInfo.createtime = sqlData[15] or ""
	
	--向客户更新用户大厅的待处理列表
	local userList = {cInfo.vendoruserid, cInfo.customeruserid} 
	NoticeServices.NoticeHangSellOut(userList)
	
	return 0, ""
end

function VendorOrderService.WithdrawCurrency(uInfo, wInfo, ordertype, touInfo, isReview, optID, optName, remark)
	
	local addrList = {}
	local sqlCase = "select coin_addr,addr_type,addr_name from dy_user_address_pre where user_id="..uInfo.userid.." and status=0"
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlDate = mysqlItem:fetch({})
		if sqlDate == nil then
			break
		end
		
		local tmp = {}
		tmp["address"] = sqlDate[1]
		tmp["addrtype"] = tonumber(sqlDate[2])
		tmp["addrname"] = sqlDate[3]
		addrList[tonumber(sqlDate[2])] = tmp
	end
	
	local coinID = tonumber(wInfo.currencytype) or 2003
	local addrName = g_marketDefine.addr_name_list[wInfo.addrtype] or ""
	
	--检查额度
	--local sqlCase = "select extract_currency_rate,min_extract_currency_count,max_extract_currency_count,transfer_curreny_rate,min_transfer_curreny_count,max_transfer_curreny_count from dy_coin_info where coin_id="..coinID.." and channel='"..uInfo.channel.."'"
	local sqlCase = "select extract_currency_rate,min_extract_currency_count,max_extract_currency_count,transfer_curreny_rate,min_transfer_curreny_count,max_transfer_curreny_count,extract_currency_rate_trc from dy_coin_info where channel='"..uInfo.channel.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		return 1, "未找到渠道设置"
	end
	
	local rate = 0
	local minCount = 0
	local maxCount = 0
	local msg = ""
	local toChannel = ""
	local toUserid = 0
	local toNickname = ""
	local toAddress = "" 
	if ordertype == 1 then
		--提币
		if uInfo.usertype == 301 then
			if wInfo.addrtype == 101 then
				--erc
				rate = tonumber(tonumber(uInfo.extractcurrencyrateerc) or 0) 
			else
				--trc
				rate = tonumber(tonumber(uInfo.extractcurrencyratetrc) or 0) 
			end
		else
			if wInfo.addrtype == 101 then
				--erc
				rate = tonumber(sqlData[1]) 
			else
				--trc
				rate = tonumber(sqlData[7]) 
			end
		end
		minCount = tonumber(sqlData[2])
		maxCount = tonumber(sqlData[3])
		msg = "提币，冻结资金"
		
		if wInfo.toaddress == nil or wInfo.toaddress == "" then
			return 1, "提币的地址不能为空"
		end
		
		if addrList[wInfo.addrtype] == nil or addrList[wInfo.addrtype] == "" then
			return 1, "您的地址为空，请联系客服！"
		end
		toAddress = addrList[wInfo.addrtype]["address"]
	else
		--转账
		rate = tonumber(sqlData[4])
		minCount = tonumber(sqlData[5])
		maxCount = tonumber(sqlData[6])
		if touInfo == nil then 
			return 1, "转账目标用户为空"
		end
		
		if uInfo.userid == touInfo.userid then
			return 1, "不能给自己转账"
		end
		
		toChannel = touInfo.channel
		toUserid = touInfo.userid
		toNickname = touInfo.nickname
		
		if isReview == true then
			if touInfo.channel ~= uInfo.channel then
				return 1, "只能在渠道内转账"
			end
		else
			if touInfo.platformid ~= uInfo.platformid then
				return 1, "只能在子平台内转账"
			end
		end
		
		--私有渠道只能再渠道内转账，公有渠道可以在所有公有渠道内转账
		--local channelInfoList = {}
		--local sqlCase = "select channel, channel_deal from dy_channel_info"
		--mysqlItem:executeQuery(sqlCase)
		--while true do 
		--	local sqlData = mysqlItem:fetch({})
		--	if sqlData == nil then
		--		break
		--	end
			
		--	local tmp = {}
		--	tmp["channelDeal"] = tonumber(sqlData[2])
		--	channelInfoList[sqlData[1]] = tmp
		--end
		--if channelInfoList[uInfo.channel] == 0 then
			--公有
		--	if channelInfoList[toChannel] == 1 then
		--		return 1, "公有渠道不能向私有渠道转账"
		--	end
		--else
			--私有
		--	if uInfo.channel ~= toChannel then
		--		return 1, "私有渠道只能在渠道内转账"
		--	end
		--end
		
		msg = "转账，冻结资金"
	end
	
	if minCount > tonumber(wInfo.currencyamount) or maxCount < tonumber(wInfo.currencyamount) then
		return 1, "提现或转账的金额不在设置的范围之内"
	end
	
	--[[
	--检查提币数量
	if tonumber(wInfo.currencyamount) < 2 then
		return 1,"提币数量至少2个"
	end
	]]
	--检查余额
	if wInfo.wallettype == 0 then
		if uInfo.dealcointype == 1 then
			if (tonumber( uInfo.ercfcamount) - tonumber( uInfo.ercfclockamount)) < tonumber(wInfo.money) then
				return 1, "余额不足"		
			end
			
		else
			if (tonumber( uInfo.ercusdtamount) - tonumber( uInfo.ercusdtlockamount)) < tonumber(wInfo.currencyamount) then
				return 1, "币数量不足"		
			end

		end
		
	else 
		if (tonumber( uInfo.coinpayusdtamount) - tonumber( uInfo.coinpayusdtlockamount)) < tonumber(wInfo.currencyamount) then
			return 1, "币付钱包数量不足"		
		end
	end
	
	local status = uInfo.usertype == g_humanDefine.usertype_merchant_dealer and 101 or 103
	
	local tx_fee = rate
	local get_amount = tonumber(wInfo.currencyamount) - tx_fee
	
	local coinName = ""
	local sqlCase = "select coin_type from dy_coin_info where coin_id="..coinID.." and channel='"..uInfo.channel.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		coinName = sqlData
	end
	
	--插入数据库
	local create_time = TimeUtils.GetTimeString()
	--local sqlCase = "insert into dy_block_chain_trans(tx_type,from_addr,to_addr,amount,tx_fee,recd_status,create_time,userid,get_amount,specific_type,channel,user_type,coin_id,coin_name,addr_type,addr_name) values(2,'"..addrList[wInfo.addrtype]
		--	.."','"..wInfo.toaddress.."',"..tonumber(wInfo.currencyamount)..","..tx_fee..","..status..",'"..create_time.."',"..uInfo.userid..","..get_amount..","..wInfo.specifictype..",'"
		--	..uInfo.channel.."',"..uInfo.usertype..","..coinID..",'"..coinName.."',"..wInfo.addrtype..",'"..addrName.."')"
	local remark = remark or  "" 
	local sqlCase = "insert into dy_block_chain_trans(tx_type,from_addr,to_addr,amount,tx_fee,recd_status,create_time,userid,get_amount,specific_type,channel,user_type,addr_type,addr_name,money,price,nickename,to_channel,to_userid,to_nickname,platform_id,opt_id,opt_name,remark,wallet_type) values(2,'"
		..toAddress.."','"..wInfo.toaddress.."',"..tonumber(wInfo.currencyamount)..","..tx_fee..","..status..",'"..create_time.."',"..uInfo.userid..","..get_amount..","..wInfo.specifictype..",'"
		..uInfo.channel.."',"..uInfo.usertype..","..wInfo.addrtype..",'"..addrName.."',"..wInfo.money..","..wInfo.price..",'"..uInfo.nickname.."','"..toChannel.."',"..toUserid..",'"..toNickname.."',"..uInfo.platformid..","..optID..",'"..optName.."','"..remark.."',"..wInfo.wallettype..")"
	mysqlItem:execute(sqlCase)
	
	local sqlCase = "select id from dy_block_chain_trans where userid='"..uInfo.userid.."' and specific_type="..wInfo.specifictype.." and get_amount="..get_amount.." and create_time='"..create_time.."'"
	mysqlItem:execute(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		return 1, "提交失败"		
	end
	
	local ID = tonumber(sqlData)
	
	--检查成交货币类型 如果是法币交易
	if wInfo.wallettype == 0 then
		if uInfo.dealcointype == 1 then
			if false == UserInfoModel.AddErcFCLockAmount(uInfo, tonumber(wInfo.money), sqlData, 0, msg) then
				local sqlCase = "update dy_block_chain_trans set recd_status=107 where id="..sqlData
				mysqlItem:execute(sqlCase)
				return 1, "余额不足"		
			end
		end
		
		--提币冻结资金
		if false == UserInfoModel.AddErcUsdtLockAmount(uInfo, tonumber(wInfo.currencyamount), sqlData, 0, msg) then
			local sqlCase = "update dy_block_chain_trans set recd_status=107 where id="..sqlData
			mysqlItem:execute(sqlCase)
			return 1, "币数量不足"
		end
		UserInfoModel.SendErcUsdtLockAmount(uInfo)
	else
		--提币冻结资金
		if false == UserInfoModel.AddErcCoinPayUsdtLockAmount(uInfo, tonumber(wInfo.currencyamount), sqlData, 0, msg) then
			local sqlCase = "update dy_block_chain_trans set recd_status=107 where id="..sqlData
			mysqlItem:execute(sqlCase)
			return 1, "币付钱包数量不足"
		end
	
	end
	
	if ordertype == 2 then
		--检查渠道是否需要审核
		local sqlCase = "select is_transfer_review from dy_channel_info where channel='"..uInfo.channel.."'"
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch()
		if (sqlData ~= nil and tonumber(sqlData) == 1) or isReview == false then
			local sqlCase = "INSERT INTO dy_block_chain_trans(tx_type,userid,amount,tx_fee,recd_status,get_amount,specific_type,tx_time,channel,platform_id)"
				.." VALUES('1',"..toUserid..","..tonumber(wInfo.currencyamount)..","..tx_fee..",106,"..get_amount..",3,'"..create_time.."','"..toChannel.."',"..touInfo.platformid..")"
			mysqlItem:execute(sqlCase)
				
			local sqlCase = "select id from dy_block_chain_trans where userid='"..toUserid.."' and specific_type=3 and get_amount="..get_amount.." and recd_status=106 and tx_time='"..create_time.."'"
			mysqlItem:executeQuery(sqlCase)
			local wID = mysqlItem:fetch()
			if wID ~= nil then
				--转账扣除资金
				local sqlCase = "update dy_block_chain_trans set recd_status=106 where id="..ID
				mysqlItem:execute(sqlCase)
				if wInfo.wallettype == 0 then
					UserInfoModel.DecErcUsdtLockAmount(uInfo, tonumber(wInfo.currencyamount), ID, 0, "转账审核通过，解除资金冻结")
					UserInfoModel.SendErcUsdtLockAmount(uInfo)	
					UserInfoModel.DecErcUsdtAmount(uInfo, tonumber(wInfo.currencyamount), ID, 0, "转账审核通过，扣除资金", g_humanDefine.fund_details.type_transfer_out,wInfo.money)	
					UserInfoModel.SendErcUsdtAmount(uInfo)
					if uInfo.dealcointype == 1 then
						UserInfoModel.DecErcFCLockAmount(uInfo, wInfo.money, ID, 0, "转账审核通过，解除资金冻结")
						UserInfoModel.DecErcFCAmount(uInfo, wInfo.money, ID, 0, "转账审核通过，扣除资金", g_humanDefine.fund_details.type_transfer_out)	
					end
				else
					UserInfoModel.DecErcCoinPayUsdtLockAmount(uInfo, tonumber(wInfo.currencyamount), ID, 0, "转账审核通过，解除资金冻结")
					UserInfoModel.DecErcCoinPayUsdtAmount(uInfo, tonumber(wInfo.currencyamount), ID, 0, "转账审核通过，扣除资金", g_humanDefine.fund_details.type_transfer_out,wInfo.money)	
				end
				
				
				UserInfoModel.AddErcUsdtAmount(touInfo, get_amount, wID, 0, "转账进来，增加资金",g_humanDefine.fund_details.type_transfer_in,(get_amount*wInfo.price))	
				UserInfoModel.SendErcUsdtAmount(touInfo)
				
				if touInfo.dealcointype == 1 then
					--结算货币类型如果是法币把法币账户也结算一下
					UserInfoModel.AddErcFCAmount(touInfo, (get_amount*wInfo.price), wID, 0, "转账进来，增加资金", g_humanDefine.fund_details.type_transfer_in)
				end
				
				
				local sysInfo = UserInfoModel.GetSysUserInfo(uInfo.platformid)
				if sysInfo ~= nil then
					local allPrice = CoinInfoService.Erc20USDTPrice("ALL", g_marketDefine.deal_buy, g_marketDefine.currency_type.USDT, false, 0)
					UserInfoModel.AddErcUsdtAmount(sysInfo, tx_fee, wID, 0, "转账手续费， 归入系统用户", g_humanDefine.fund_details.type_handling_fee,allPrice*tx_fee)
				
					local rate = tx_fee / tonumber(wInfo.currencyamount)
					local sqlCase = "insert into log_commission_details(order_id,order_type,currency_type,source_userid,source_nick,target_userid,target_nick,amount,"
					.."dea_price,money_count,free_rate,free_count,commission_rate,commission_count,platform_id) values("..wID..",2,2003,"
					..uInfo.userid..",'"..uInfo.nickname.."',"..sysInfo.userid..",'"..sysInfo.nickname.."',"..tonumber(wInfo.currencyamount)..",0,0,"..rate..","..tx_fee..","..rate..","..tx_fee..","..uInfo.platformid..")"
					mysqlItem:execute(sqlCase)
					
					LogDispatch.sysIncome(uInfo.userid, tx_fee)
				end
				
				LogDispatch.userTransferInAndOut(uInfo.userid, touInfo.userid, tonumber(wInfo.currencyamount), tx_fee, get_amount)
			end
		end
	end

	return 0,""
end

function VendorOrderService.coinPayOrder(uInfo, coinfo, moneyCount, fromInfo, payType, chainType, chainAddr, owership, currencyCount, price, payInfoID)
	
	if currencyCount <= 0 or moneyCount <= 0 then
		return 1, "金额有误"
	end
	
	local payInfo = {}
	payInfo["id"] = payInfoID
	payInfo["paytype"] = payType
	payInfo["account"] = ""
	payInfo["payee"] = ""
	payInfo["qrcode"] = ""
	payInfo["bankname"] = ""
	payInfo["bankaddr"] = ""
	payInfo["singlelimit"] = ""
	payInfo["daylimit"] = ""
	payInfo["fourthpartyid"] = 0
	payInfo["deallasttime"] = "2020-01-01 00:00:00"
	local IP = ""
	coinfo.dealid = 0
	coinfo.type = g_marketDefine.deal_buy
	coinfo.vendororderid = 0
	coinfo.customeruserid = uInfo.userid
	coinfo.vendoruserid = 0
	coinfo.price = tostring(price)
	coinfo.amount = tostring(currencyCount)
	coinfo.money = tostring(moneyCount)
	coinfo.status = g_marketDefine.deal_status_wait
	coinfo.paytypelist = luajson.encode(payInfo)
	coinfo.payidlist = tostring(payType)
	coinfo.createtime = TimeUtils.GetTimeString()
	coinfo.updatetime = coinfo.createtime
	coinfo.paytime = ''
	coinfo.passtime = ''
	coinfo.channel = uInfo.channel
	coinfo.publicprice = tostring(CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.deal_buy, g_marketDefine.currency_type.USDT, true, uInfo.userid))
	coinfo.customerusernickname = uInfo.nickname
	coinfo.vendorusernickname = ""
	coinfo.withdrawtype = 0
	coinfo.isexternal = 0
	coinfo.notifyurl = ""
	coinfo.body = ""
	coinfo.merchantorderid = ""
	
	if fromInfo ~= nil then
		coinfo.notifyurl = tostring(fromInfo["notifyUrl"])
		coinfo.body = tostring(fromInfo["body"])
		coinfo.merchantorderid = fromInfo["outOrderNo"]  
		IP = fromInfo["IP"]
	end
	coinfo.dealtype = 200
	coinfo.fromtype = 2
	coinfo.sellfeerate = '0'
	coinfo.sellfee = '0'
	local userRate =UserInfoModel.getUserFeeRate(uInfo.userid, payInfo.paytype, g_humanDefine.buy_fee_rate)
	coinfo.feerate = tostring(userRate)
	coinfo.fee = tostring(userRate * currencyCount)
	coinfo.feemoney = tostring(tonumber(coinfo.feerate)  * moneyCount)
	coinfo.aftermoney = tostring(moneyCount - tonumber(coinfo.feemoney))
	coinfo.buyfeerate = tostring(userRate)
	coinfo.buyfee = tostring(userRate * currencyCount)
	coinfo.getamount = tostring(currencyCount - tonumber(coinfo.fee))
	coinfo.income = "0"
	coinfo.chaintype = chainType
	coinfo.chainname = g_marketDefine.addr_name_list[chainType]
	coinfo.chainaddr = chainAddr
	coinfo.owership = owership
	coinfo.wallettype = 1
	
	if nil == CustomerOrderModel.InserInto(coinfo, payInfo, IP) then
	--先插入数据库
		return 1, "交易失败"	
	end
	
	--统计下单情况
	LogDispatch.userDealOrderCount(uInfo.userid, 0)
	
	
end
