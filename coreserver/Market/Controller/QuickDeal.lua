module("QuickDeal", package.seeall)

--快捷交易，快捷交易的逻辑
--如果是我是想买，那么就看挂的卖当，选择最低价格，而且数量满足的。
--如果是我是想卖，那么就是看挂的买单，选择最高价格的，而且数量满足的
function execute(packetID, operateID, buffer)

	local cgmsg = msg_order_pb.cgquickdeal()
	local gcmsg = msg_order_pb.gcquickdeal()
	
	cgmsg:ParseFromString(buffer)
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["player_not_exist"][0],ReturnCode["task_unexist"][1]
		return cgmsg.userid, gcmsg.result, gcmsg:ByteSize(), gcmsg:SerializeToString()		
	end
		
	--检查资金密码
	local ret, msg = UserInfoModel.CheckFundPassword(uInfo, cgmsg.fundPwd)
	if ret ~= 0 then
		gcmsg.result, gcmsg.msg = ret,msg
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--检查用户是否锁定
	if uInfo.islock == 1 then
		gcmsg.result,gcmsg.msg = 1, "你已经被锁定无法进行该操作"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--检查可取消次数
	local num = CustomerOrderModel.GetDealLeftCount(cgmsg.userid)
	if num <= 0 then
		gcmsg.result, gcmsg.msg = 1,"您今天的取消订单次数过多，已被系统限制购买"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--查询还有多少订单正在进行中
	local sqlCase = "select count(*) from dy_customer_order where customer_user_id="..uInfo.userid.." and status=1"
	mysqlItem:executeQuery(sqlCase)
	local sqlDate = mysqlItem:fetch()
	if sqlDate ~= nil and tonumber(sqlDate) >= 5 then
		gcmsg.result, gcmsg.msg = 1,"未付款订单已达上限，请先完成支付"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	local hangID, currencyCount,moneyCount, price = VendorOrderModel.CheckQuickHangid(uInfo, cgmsg.dealtype, tonumber(cgmsg.amount)
												, cgmsg.moneytype, uInfo.channel, cgmsg.paytype)				
	if hangID == nil or hangID == 0 then
		gcmsg.result,gcmsg.msg =1,"没有合适交易订单"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()			
	end
	
	ThreadManager.OrderLock(hangID)
	local voItem = VendorOrderModel.GetVendorOrderInfo(hangID)
	if voItem == nil then
		ThreadManager.OrderUnLock(hangID)
		return ReturnCode["deal_order_not_exist"][1],ReturnCode["deal_order_not_exist"][2]
	end
	
	local ret, msg = VendorOrderService.DealOrder(uInfo, gcmsg.coinfo,voItem, cgmsg.dealtype, currencyCount, moneyCount, nil, price, cgmsg.paytype,"")
	if ret ~= nil then
		ThreadManager.OrderUnLock(hangID)
		gcmsg.result,gcmsg.msg = ret, msg
		return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	ThreadManager.OrderUnLock(hangID)
	
	--给前端转化，前端只认对于这个这个用户说是买还是卖
	gcmsg.coinfo.type = CoinInfoService.orderTypeConversion(gcmsg.coinfo.type, gcmsg.coinfo.customeruserid, gcmsg.coinfo.vendoruserid, cgmsg.userid)
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
end