module("AppealList", package.seeall)


function execute(packetID, operateID, buffer)

	local cgmsg = msg_order_pb.cgappeallist()
	local gcmsg = msg_order_pb.gcappeallist()
	
	cgmsg:ParseFromString(buffer)
	
	local uInfo = UserModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["player_not_exist"][0],ReturnCode["task_unexist"][1]
		return cgmsg.userid, gcmsg.result, gcmsg:ByteSize(), gcmsg:SerializeToString()		
	end

	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
end