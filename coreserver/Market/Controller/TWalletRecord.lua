module("TWalletRecord", package.seeall)

--提交TXID

function execute(packetID, operateID, buffer)

	local cgmsg = msg_order2_pb.cgtwalletrecord()
	local gcmsg = msg_order2_pb.gctwalletrecord()
	
	cgmsg:ParseFromString(buffer)

	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end

	local startPos = (cgmsg.pagenum - 1) * cgmsg.pagesize
	local sqlCase = "select * from dy_trusteeship_wallet_order where userid="..uInfo.userid.." order by id desc limit "..startPos..", "..cgmsg.pagesize
	mysqlItem:executeQuery(sqlCase) 
	
	while true do 
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		local addInfo = gcmsg.infolist:add()
		addInfo.order_id = sqlData[2]
		addInfo.channel = sqlData[3]
		addInfo.userid = tonumber(sqlData[4])
		addInfo.nickname = sqlData[5]
		addInfo.currency_type = sqlData[6]
		addInfo.amount = sqlData[7]
		addInfo.actualamount = sqlData[8]
		addInfo.trusteeship = sqlData[9]
		addInfo.usertxid = sqlData[10]
		addInfo.create = sqlData[11]
		addInfo.remarks = sqlData[12]
		addInfo.status = tonumber(sqlData[13])
		addInfo.coinid = sqlData[14]
		addInfo.addrtype = tonumber(sqlData[15])
		addInfo.coinname = sqlData[16]
		addInfo.addrname = sqlData[17]
	end
	
	gcmsg.pagenum = cgmsg.pagenum
	gcmsg.pagesize = cgmsg.pagesize
	gcmsg.ordertype = cgmsg.ordertype
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
	
end