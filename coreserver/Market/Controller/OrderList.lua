module("OrderList", package.seeall)


function execute(packetID, operateID, buffer)

	local cgmsg = msg_order_pb.cgorderlist()
	local gcmsg = msg_order_pb.gcorderlist()
	
	cgmsg:ParseFromString(buffer)
	
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["player_not_exist"][0],ReturnCode["task_unexist"][1]
		return cgmsg.userid, gcmsg.result, gcmsg:ByteSize(), gcmsg:SerializeToString()		
	end
	local startPos = (cgmsg.pagenum - 1) * cgmsg.pagesize
	local endPos = startPos + cgmsg.pagesize - 1
	
	--[[
	local VendorOrderList = VendorOrderModel.GetUserVendorOrderList(uInfo.userid,cgmsg.ordertype, startPos, endPos)
	for k,v in ipairs(VendorOrderList) do
		local VendorOrderInfo = VendorOrderModel.GetVendorOrderInfo(v)
		if VendorOrderInfo ~= nil then
			local addVendorOrder = gcmsg.volist:add()
			addVendorOrder:ParseFromString(VendorOrderInfo:SerializeToString())
			gcmsg.dealnum:append(addVendorOrder.unsoldordernum)
		end
	end
	]]
	local sqlCase
	if cgmsg.ordertype == 0 then
		sqlCase = "select * from dy_vendor_order where userid="..cgmsg.userid.." order by id desc limit "..startPos..", "..endPos
	else
		sqlCase = "select * from dy_vendor_order where userid="..cgmsg.userid.." and  type="..cgmsg.ordertype.." order by id desc limit "..startPos..", "..endPos
	end
	mysqlItem:executeQuery(sqlCase)
	while true do
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		
		local addVendorOrder = gcmsg.volist:add()
		
		addVendorOrder.userid = uInfo.userid
		addVendorOrder.hangid = tonumber(sqlData[1])
		addVendorOrder.type = tonumber(sqlData[3])
		addVendorOrder.pricetype = tonumber(sqlData[6])
		addVendorOrder.price = sqlData[4]
		addVendorOrder.amount = sqlData[5]
		addVendorOrder.minmoney = sqlData[7]
		addVendorOrder.maxmoney = sqlData[8]
		addVendorOrder.autoswitch = tonumber(sqlData[10])
		addVendorOrder.message = sqlData[11]
		addVendorOrder.maxamount = sqlData[16]
		addVendorOrder.enablestatus = tonumber(sqlData[9])
		addVendorOrder.channel = sqlData[17]
		addVendorOrder.nickname = uInfo.nickname
		addVendorOrder.unsoldordernum = tonumber(sqlData[18])
		addVendorOrder.dealordernum = tonumber(sqlData[19])
		addVendorOrder.unsoldorderamount = sqlData[20]
		addVendorOrder.dealorderamount = sqlData[21]
		addVendorOrder.cancelnum = tonumber(sqlData[22])
		addVendorOrder.createtime = sqlData[12]
		addVendorOrder.unsoldordermoney = sqlData[44]
		addVendorOrder.dealordermoney = sqlData[45]
		gcmsg.dealnum:append(addVendorOrder.unsoldordernum)
	end
	
	for k,v in ipairs(gcmsg.volist) do
		if tonumber(v.pricetype) == 1 then
			v.price = tostring(CoinInfoService.Erc20USDTPrice(uInfo.channel,v.type, g_marketDefine.currency_type.USDT, true, uInfo.userid))
		end
	end
	

	--取到了对应的列表
	gcmsg.ordertype = cgmsg.ordertype
	gcmsg.pagenum = cgmsg.pagenum
	gcmsg.pagesize = cgmsg.pagesize
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
end