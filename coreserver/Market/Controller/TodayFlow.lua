module("TodayFlow", package.seeall)

--今日售出/买入
function execute(packetID, operateID, buffer)

	local cgmsg = msg_order2_pb.cgtodayflow()
	local gcmsg = msg_order2_pb.gctodayflow()
	
	cgmsg:ParseFromString(buffer)
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, gcmsg.result, gcmsg:ByteSize(), gcmsg:SerializeToString()		
	end
	
	local dayStr = TimeUtils.GetDayString()
	local sqlCase = "select * from log_user_daily where userid="..uInfo.userid.." and dateid='"..dayStr.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData ~= nil then
		gcmsg.todaybuy= sqlData[14]
		gcmsg.todaySell = sqlData[15]
		
		local time = tonumber(sqlData[24]) or 0
		local successOrderNum = tonumber(sqlData[9])+tonumber(sqlData[10])
		if successOrderNum == 0 or time == 0 then
			gcmsg.averagetime = 0
		else
			gcmsg.averagetime = time/successOrderNum
			
		end
		local allOrderNum = tonumber(sqlData[7])+tonumber(sqlData[8])
		local cancelOrderNum = tonumber(sqlData[11])+tonumber(sqlData[12])
		if successOrderNum == 0 or allOrderNum == 0 then
			gcmsg.successrate = "0%"
		else
			gcmsg.successrate = string.format("%.2f", tostring((successOrderNum/(successOrderNum+cancelOrderNum))*100)).."%"
		end
	else
		gcmsg.todaySell = "0"
		gcmsg.todaybuy = "0"
		gcmsg.averagetime = 0
		gcmsg.successrate = "0%"
	end
	
	gcmsg.exchangerate = tostring(CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.deal_buy, g_marketDefine.currency_type.USDT, true, uInfo.userid))
	
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
end