module("ExpenseStat", package.seeall)


--支出统计(币商)
function execute(packetID, operateID, buffer)

	local cgmsg = msg_order2_pb.cgexpensestat()
	local gcmsg = msg_order2_pb.gcexpensestat()
	
	cgmsg:ParseFromString(buffer)
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()		
	end
	
	local startDay = TimeUtils.GetDayString(cgmsg.starttime)
	local endDay = TimeUtils.GetDayString(cgmsg.endtime)
	
	local sqlCase = "select * from log_user_daily where userid="..uInfo.userid.." and dateid>='"..startDay.."' and dateid<='"..endDay.."'"
	mysqlItem:executeQuery(sqlCase)
	
	sellcount = 0
	withdrawcount = 0
	rolloutcount = 0
	feecount = 0
	
	while true do
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		
		sellcount = sellcount + tonumber(sqlData[15])
		withdrawcount = withdrawcount + tonumber(sqlData[17])
		rolloutcount = rolloutcount + tonumber(sqlData[19])
		feecount = feecount + tonumber(sqlData[21])
	end
	
	gcmsg.total =  string.format("%.6f", tostring(sellcount + withdrawcount + rolloutcount + feecount))
	gcmsg.sell =  string.format("%.6f", tostring(sellcount))
	gcmsg.withdraw = string.format("%.6f", tostring(withdrawcount))
	gcmsg.rollout = string.format("%.6f", tostring(rolloutcount))
	gcmsg.fee = string.format("%.6f", tostring(feecount))
	
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
end