module("Appeal", package.seeall)

--申诉

function execute(packetID, operateID, buffer)

	local cgmsg = msg_order_pb.cgappeal()
	local gcmsg = msg_order_pb.gcappeal()
	
	cgmsg:ParseFromString(buffer)

	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	ThreadManager.DealLock(cgmsg.dealid) 
	if nil == CustomerOrderModel.GetCustomerOrderInfo( cgmsg.dealid, gcmsg.coinfo) then
		ThreadManager.DealUnLock(cgmsg.dealid) 
		gcmsg.result,gcmsg.msg = ReturnCode["order_not_exist"][1],ReturnCode["order_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	gcmsg.info.orderid = 0
	gcmsg.info.reason = cgmsg.reason
	gcmsg.info.description = cgmsg.description
	gcmsg.info.proofurl = cgmsg.proofurl
	
	local ret, msg = VendorOrderService.AppealOrder(uInfo, gcmsg.coinfo,gcmsg.info)
	if ret ~= 0 then
		ThreadManager.DealUnLock(cgmsg.dealid) 
		gcmsg.result,gcmsg.msg = ret, msg
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	ThreadManager.DealUnLock(cgmsg.dealid) 
	
	--给前端转化，前端只认对于这个这个用户说是买还是卖
	gcmsg.coinfo.type = CoinInfoService.orderTypeConversion(gcmsg.coinfo.type, gcmsg.coinfo.customeruserid, gcmsg.coinfo.vendoruserid, cgmsg.userid)
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
	
end