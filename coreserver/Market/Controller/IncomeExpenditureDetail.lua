module("IncomeExpenditureDetail", package.seeall)

function execute(packetID, operateID, buffer)
	
	local cgmsg = msg_order_pb.cgincomeexpendituredetail()
	local gcmsg = msg_order_pb.gcincomeexpendituredetail()
	
	cgmsg:ParseFromString(buffer)
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--近7天
	local endTime= TimeUtils.GetTime() 
	local startTime = endTime - (86400 * 13)
	endTime = TimeUtils.GetDayString(endTime).." 23:59:59"
	startTime = TimeUtils.GetDayString(startTime).." 00:00:00"
	local startPos = (cgmsg.pagenum - 1) * cgmsg.pagesize
	local tmpSqlCase = ""
	if cgmsg.detailtype == 0 then
		--获取近7日统计
		local sqlCase = "select sum(amount) from log_fund_details where order_type=1 and createdate>'"..startTime.."' and createdate <'"..endTime.."' and userid="..uInfo.userid
		mysqlItem:executeQuery(sqlCase)
		local sqlDate = mysqlItem:fetch()
		if sqlDate ~= nil then
			gcmsg.parameter1 = sqlDate
		end
		
		local sqlCase = "select sum(amount) from log_fund_details where order_type=2 and createdate>'"..startTime.."' and createdate <'"..endTime.."' and userid="..uInfo.userid
		mysqlItem:executeQuery(sqlCase)
		local sqlDate = mysqlItem:fetch()
		if sqlDate ~= nil then
			gcmsg.parameter2 = sqlDate
		end
		
		--获取近7日明细
		tmpSqlCase = "select * from log_fund_details where amount!= 0 and userid="..uInfo.userid.." and createdate>'"..startTime.."' and createdate <'"..endTime.."' order by id desc limit "..startPos..", "..cgmsg.pagesize 
		
	else
		--获取近7日统计
		local sqlCase = "select sum(amount) from log_fund_details where order_type="..cgmsg.detailtype.." and createdate>'"..startTime.."' and createdate <'"..endTime.."' and userid="..uInfo.userid
		mysqlItem:executeQuery(sqlCase)
		local sqlDate = mysqlItem:fetch()
		if sqlDate ~= nil then
			gcmsg.parameter1 = sqlDate
		end
		
		--获取近7日明细
		tmpSqlCase = "select * from log_fund_details where amount!=0 and userid="..uInfo.userid.." and order_type="..cgmsg.detailtype.." and createdate>'"..startTime.."' and createdate <'"..endTime.."' order by id desc limit "..startPos..", "..cgmsg.pagesize
	end
	
	mysqlItem:executeQuery(tmpSqlCase)
	while true do 
		local sqlDate = mysqlItem:fetch({})
		if sqlDate == nil then
			break
		end
		
		local addDetail = gcmsg.detaillist:add()
		addDetail.detailtype = tonumber(sqlDate[8])
		addDetail.amount = sqlDate[4]
		addDetail.afteramount = sqlDate[6]
		addDetail.createtime = sqlDate[11]
	end

	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
end