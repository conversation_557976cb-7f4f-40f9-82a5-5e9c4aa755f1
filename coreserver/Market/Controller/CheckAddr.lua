module("CheckAddr", package.seeall)

--申诉

function execute(packetID, operateID, buffer)

	local cgmsg = msg_order2_pb.cgcheckaddr()
	local gcmsg = msg_order2_pb.gccheckaddr()
	
	cgmsg:ParseFromString(buffer)
	
	local sqlCase = "select * from dy_user_address_pre where user_id!=0 and coin_addr='"..cgmsg.adds.."'"
	mysqlItem:executeQuery(sqlCase)
	
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		gcmsg.ret = 1
	else
		gcmsg.ret = 0
	end
	
	gcmsg.adds = cgmsg.adds
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
	
end