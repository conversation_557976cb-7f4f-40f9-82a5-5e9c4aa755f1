module("WithdrawRecord", package.seeall)

--币币交易记录
function execute(packetID, operateID, buffer)

	local cgmsg = msg_order2_pb.cgwithdrawrecord()
	local gcmsg = msg_order2_pb.gcwithdrawrecord()
	
	cgmsg:ParseFromString(buffer)
	
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][0],ReturnCode["human_user_not_exist"][1]
		return cgmsg.userid, gcmsg.result, gcmsg:ByteSize(), gcmsg:SerializeToString()		
	end
	local startPos = (cgmsg.pagenum - 1) * cgmsg.pagesize

	local sqlCase = ""
	if cgmsg.ordertype == 0  then
		--全部
		sqlCase = "select * from dy_block_chain_trans where userid="..uInfo.userid.." order by id desc limit "..startPos..", "..cgmsg.pagesize
	else
		sqlCase = "select * from dy_block_chain_trans where specific_type="..cgmsg.ordertype.." and userid="..uInfo.userid.." order by id desc limit "..startPos..", "..cgmsg.pagesize
	end
	mysqlItem:executeQuery(sqlCase)
	
	while true do
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		
		local addRecrd = gcmsg.recordinfo:add()
		addRecrd.id = tonumber(sqlData[1])
		addRecrd.txid = sqlData[2]
		addRecrd.txtype = tonumber(sqlData[4]) or 1
		addRecrd.chainid = sqlData[5]
		addRecrd.txdata = sqlData[6]
		addRecrd.fromaddr = sqlData[7]
		addRecrd.toaddr = sqlData[8]
		addRecrd.amount = sqlData[9]
		addRecrd.txfee = sqlData[10]
		addRecrd.txstatus = tonumber(sqlData[11])
		addRecrd.txtime = sqlData[12]
		addRecrd.blockhash = sqlData[13]
		addRecrd.recdstatus = tonumber(sqlData[14])
		addRecrd.remark = sqlData[15]
		addRecrd.createtime = sqlData[16]
		addRecrd.specifictype = tonumber(sqlData[26])
		addRecrd.coinid = tonumber(sqlData[35])
		addRecrd.addrtype = tonumber(sqlData[36])
		addRecrd.coinname = sqlData[37]
		addRecrd.addrname = sqlData[38]
	end
	
	
	gcmsg.result = 0

	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
end