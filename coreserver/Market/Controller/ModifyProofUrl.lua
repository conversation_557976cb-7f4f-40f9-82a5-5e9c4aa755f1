module("ModifyProofUrl", package.seeall)

--修改支付凭证
function execute(packetID, operateID, buffer)

	local cgmsg = msg_order2_pb.cgmodifyproofurl()
	local gcmsg = msg_order2_pb.gcmodifyproofurl()
	
	cgmsg:ParseFromString(buffer)

	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	ThreadManager.DealLock(cgmsg.dealid) 
	if nil == CustomerOrderModel.GetCustomerOrderInfo( cgmsg.dealid, gcmsg.coinfo) then
		ThreadManager.DealUnLock(cgmsg.dealid) 
		gcmsg.result,gcmsg.msg = ReturnCode["order_not_exist"][1],ReturnCode["order_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	while #gcmsg.coinfo.proofurl > 0 do 
		gcmsg.coinfo.proofurl:remove(1)
	end
	local proofUrlList = ""
	for k,v in ipairs(cgmsg.proofurl) do
		gcmsg.coinfo.proofurl:append(v)
		proofUrlList = proofUrlList..v
		ChatModel.InsertInto(gcmsg.coinfo.dealid,gcmsg.coinfo.merchantorderid,gcmsg.coinfo.vendoruserid,gcmsg.coinfo.customeruserid,
					gcmsg.coinfo.dealtype,uInfo.channel,g_chatDefine.chat_type['customer_to_merchant'],2,v)
		if k ~= #cgmsg.proofurl then
			proofUrlList = proofUrlList..","
		end
	end
	local sqlCase = "update dy_customer_order set proof_url='"..proofUrlList.."' where id="..gcmsg.coinfo.dealid
	mysqlItem:execute(sqlCase)
	
	ThreadManager.DealUnLock(cgmsg.dealid) 
	
	--给前端转化，前端只认对于这个这个用户说是买还是卖
	gcmsg.coinfo.type = CoinInfoService.orderTypeConversion(gcmsg.coinfo.type, gcmsg.coinfo.customeruserid, gcmsg.coinfo.vendoruserid, cgmsg.userid)
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
	
end