module("HangOrder", package.seeall)

function execute(packetID, operateID, buffer)
	
	local cgmsg = msg_order_pb.cghangorder()
	local gcmsg = msg_order_pb.gchangorder()
	
	cgmsg:ParseFromString(buffer)
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--检查用户是否锁定
	if uInfo.islock == 1 then
		gcmsg.result,gcmsg.msg = 1, "你已经被锁定无法进行该操作"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--检查资金密码
	local ret, msg = UserInfoModel.CheckFundPassword(uInfo,cgmsg.fundpwd)
	if ret ~= 0 then
		gcmsg.result, gcmsg.msg = ret,msg
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	if tonumber(cgmsg.minmoney) <= 0 or tonumber(cgmsg.minmoney) > tonumber(cgmsg.maxmoney) then
		gcmsg.result,gcmsg.msg = 1,"限额有错误"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()	
	end
	
	local dealMonery = 0
	local dealAmount = 0
	if tonumber(cgmsg.autoswitch) == 0 then
		local amount = tonumber(cgmsg.amount)
		local price = cgmsg.pricetype == 0 and tonumber(cgmsg.price) or CoinInfoService.Erc20USDTPrice(uInfo.channel, cgmsg.type, g_marketDefine.currency_type.USDT,true,uInfo.userid)
		if amount * price < tonumber(cgmsg.minmoney) then
			gcmsg.result,gcmsg.msg = 1,"挂单数量小于最小单笔最大限额"
			return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
		end
		dealAmount = amount
	elseif tonumber(cgmsg.autoswitch) == 1 then
		if cgmsg.type == 0 then
			dealMonery = tonumber(cgmsg.amount)
		end
	end
	
	--只能卖单， 买单， 自动售卖分别只能有一个
	local vendoeList = VendorOrderModel.GetUserVendorOrderList(uInfo.userid,cgmsg.type, 0, -1)
	for k,v in ipairs(vendoeList) do 
		local hInfo = VendorOrderModel.GetVendorOrderInfo(v)
		if hInfo.type == cgmsg.type and hInfo.autoswitch == cgmsg.autoswitch then
			gcmsg.result,gcmsg.msg = 1,"已经有挂单了，请勿重复挂单！"
			return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()	
		end
	end
	
	--检查接单限制跟交易限制
	local sqlCase = "select orders_limit,deal_limit,channel_deal,add_sell_comm_rate,is_add_buy_comm,userid,is_currency_buy_rate,currency_add_buy_rate from dy_channel_info where channel='"..uInfo.channel.."'"
	mysqlItem:executeQuery(sqlCase)
	
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		gcmsg.result,gcmsg.msg = 1,"未找到渠道配置"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()	
	end
	local channel_deal = tonumber(sqlData[3]) == nil and 0 or tonumber(sqlData[3])
	local isAddbBuyComm = tonumber(sqlData[5])
	local isCurrencyBuyRate = tonumber(sqlData[7])
	local currencyAddBuyRate = tonumber(sqlData[8])
	local rate = 0
	if (uInfo.usertype == 200 or uInfo.usertype == 201) then
		if isAddbBuyComm == 1 then
			rate = tonumber(sqlData[4]) == nil and 0 or tonumber(sqlData[4])
		end
	elseif uInfo.usertype == 202  then
		if isCurrencyBuyRate == 1 then
			rate = tonumber(sqlData[8]) == nil and 0 or tonumber(sqlData[8])
		end
	end
	local channelUserid = tonumber(sqlData[6])
	
	local sysPrice = CoinInfoService.Erc20USDTPrice(uInfo.channel, cgmsg.type, g_marketDefine.currency_type.USDT, true, uInfo.userid)
	if cgmsg.autoswitch == 1 then
		--接单限制
		if cgmsg.type == 1 then
			--if tonumber(sqlData[1]) > tonumber(cgmsg.minmoney) then
			local tmpPrice = tonumber(string.format("%.04f", tostring(sysPrice)))
			if tonumber(sqlData[1]) > ((tonumber(uInfo.ercusdtamount) - tonumber(uInfo.ercusdtlockamount)) * tmpPrice) then
				gcmsg.result,gcmsg.msg = 1,"余额不足请充值！"
				return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()	
			end
		end
		
		if uInfo.isacceptorder == 1 then
			gcmsg.result,gcmsg.msg = 1,"您已被禁止接单"
			return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()	
		end
		
		if uInfo.isteamacceptorder == 1 then
			gcmsg.result,gcmsg.msg = 1,"您的团队已被禁止接单"
			return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()	
		end
	end
	
	--交易限制
	if tonumber(sqlData[2]) > tonumber(cgmsg.minmoney) then
		gcmsg.result,gcmsg.msg = 1,"最小限额小于交易设置"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()	
	end
	
	--检查是不是系统价格， 价格必须大于0
	if tonumber(cgmsg.pricetype) == 0 and tonumber(cgmsg.price) <= 0 then
		gcmsg.result,gcmsg.msg = 1, "请输入单价！"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--挂卖单的时候
	if g_marketDefine.hang_sell == cgmsg.type then
		
		--检查是否允许挂卖
		if uInfo.ishangsell == 2 then
			gcmsg.result,gcmsg.msg = 1,"您被设置为不允许挂卖"
			return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
		end
		
		--检查收款方式
		if  false == UserPayModel.CheckUserPayExist(uInfo) then
			gcmsg.result,gcmsg.msg = ReturnCode["user_pay_not_exist"][1],ReturnCode["user_pay_not_exist"][2]
			return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
		end
		
		--挂单总额为0时就是，挂身上可用余额不检查 
		--挂单总额不为0时就是只挂这么多， 检查可用余额
		if tonumber(cgmsg.autoswitch) == 0 then
			
			if tonumber(cgmsg.amount) <= 0 then
				gcmsg.result,gcmsg.msg = 1,"挂单数量必须大于0"
				return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()	
			end
			
			if tonumber(cgmsg.amount) > (tonumber(uInfo.ercusdtamount) - tonumber(uInfo.ercusdtlockamount)) then
				gcmsg.result,gcmsg.msg = ReturnCode["hang_money_not_enough"][1],ReturnCode["hang_money_not_enough"][2]
				return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
			end
		end
	else
		--检查是否允许挂卖
		if uInfo.ishangbuy == 2 then
			gcmsg.result,gcmsg.msg = 1,"您被设置为不允许挂买"
			return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
		end
	end
	
	--插入到数据库中。
	local currTime = TimeUtils.GetTimeString()
	local sqlCase = "insert into dy_vendor_order(userid,type,price,amount,price_type,min_money,max_money,auto_switch,message,create_time,max_amount,channel,enable_status,user_type,channel_deal,add_fee_rate,rest_time,deal_monery,platform_id) " 
		.."values("..uInfo.userid..","..cgmsg.type..","..cgmsg.price..","..dealAmount..","..cgmsg.pricetype..","..cgmsg.minmoney..","..cgmsg.maxmoney
		..","..cgmsg.autoswitch..",'"..cgmsg.message.."','"..currTime.."',"..cgmsg.amount..",'"..uInfo.channel.."',1,200,"..channel_deal..","..rate..",'"..currTime.."',"..dealMonery..","..uInfo.platformid..")"
	mysqlItem:execute(sqlCase)
	sqlCase = "select id from dy_vendor_order where userid="..cgmsg.userid.." and type="..cgmsg.type.." and create_time='"..currTime.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	
	if sqlData == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["hang_order_error"][1],ReturnCode["hang_order_error"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	ThreadManager.OrderLock(sqlData)
	
	local hInfo = st_order_pb.vendororderinfo()
	hInfo.userid = cgmsg.userid
	hInfo.hangid = tonumber(sqlData)
	hInfo.type = cgmsg.type
	hInfo.pricetype = cgmsg.pricetype   
	hInfo.price = tostring(cgmsg.price)
	hInfo.amount = tostring(cgmsg.amount)
	hInfo.minmoney = tostring(cgmsg.minmoney)
	hInfo.maxmoney = tostring(cgmsg.maxmoney)
	hInfo.autoswitch = cgmsg.autoswitch
	hInfo.message = cgmsg.message
	hInfo.maxamount = tostring(cgmsg.amount)
	hInfo.enablestatus = 1
	hInfo.channel = uInfo.channel    --渠道需要保留
	hInfo.nickname = uInfo.nickname    
	hInfo.unsoldordernum = 0  
	hInfo.dealordernum = 0  
	hInfo.unsoldorderamount = '0'  
	hInfo.dealorderamount = '0'  
	hInfo.cancelnum = 0
	hInfo.usertype = 200
	hInfo.addfeerate = tostring(rate)
	hInfo.createtime = currTime
	hInfo.channeldeal = channel_deal
	hInfo.dealmodel = 0
	hInfo.resttime = currTime
	hInfo.dealmonery = tostring(dealMonery)
	hInfo.dealcount = "0"
	hInfo.withdrawtype = 0
	hInfo.unsoldordermoney = '0'
	hInfo.dealordermoney = '0'
	
	--加到用户自己的挂单列表中区
	VendorOrderModel.addUserVendorOrder(cgmsg.userid, cgmsg.type, sqlData)
	--保存到缓存中
	VendorOrderModel.SetVendorOrderInfo( sqlData, hInfo )
	
	ThreadManager.OrderUnLock(sqlData)
	
	if hInfo.autoswitch == 1 then
		if hInfo.type == 1 then
			uInfo.autosell = hInfo.hangid
			UserInfoModel.SetUserInfo(uInfo)
			UserInfoModel.SendAutoSell(uInfo)
		end
	else
		VendorOrderModel.addVendorOrder(sqlData)
		--找出所有需要更新的用户
		local userList = {}
		if channel_deal == 0 then
			--私有的只发给自己渠道的
			local channelList = {}
			local sqlCase = "select channel from dy_channel_info where channel_deal=0 and (channel_type=200 or channel_type = 400)"
			mysqlItem:executeQuery(sqlCase)
			while true do 
				local sqlData = mysqlItem:fetch()
				if sqlData == nil then
					break
				end
				table.insert(channelList, sqlData)
			end
			
			local sqlCase = "select userid from dy_user_info where"
			for k,v in ipairs(channelList) do 
				sqlCase = sqlCase.." channel='"..v.."' "
				if k ~= #channelList then
					sqlCase = sqlCase.."or"
				end
			end
			
			mysqlItem:executeQuery(sqlCase)
			while true do 
				local sqlData = mysqlItem:fetch()
				if sqlData == nil then
					break
				end
				if true == OnlineModel.CheckOnline(sqlData) and tonumber(sqlData) ~= uInfo.userid then
					table.insert(userList, sqlData)
				end
			end
			
		else
			--私有的只发给自己渠道的
			local sqlCase = "select userid from dy_user_info where channel='"..uInfo.channel.."'"
			mysqlItem:executeQuery(sqlCase)
			while true do 
				local sqlData = mysqlItem:fetch()
				if sqlData == nil then
					break
				end
				
				if true == OnlineModel.CheckOnline(sqlData) and tonumber(sqlData) ~= uInfo.userid then
					table.insert(userList, sqlData)
				end
			end
		end
		local gcUp = msg_order2_pb.gcupdatevendororder()
		local addV = gcUp.volist:add()
		addV:ParseFromString(hInfo:SerializeToString())
		
		for _, paytype in ipairs(uInfo.paytypelist) do 
			local tmprate = 0
			if isAddbBuyComm == 1 and (uInfo.usertype == 200 or uInfo.usertype == 201) then
				tmprate = UserInfoModel.getUserFeeRate(channelUserid, paytype, g_humanDefine.sell_comm_rate) + rate
			end
			local payList = UserInfoModel.GetPayInfoList(uInfo.userid, paytype)
			if payList ~= nil then
				for k,v in pairs(payList)do
					if k ~= "Placeholder" then
						local PayInfo = st_human_pb.payinfo()
						PayInfo:ParseFromString(v)
						if PayInfo.status == 1  then
							addV.paylist:append(paytype)
							addV.feeRate:append(tostring(tmprate))
							break
						end
					end
				end
			end
		end	
		if addV.pricetype == 1 then
			addV.price = tostring(sysPrice)
		end
		addV.nickname = uInfo.nickname

		gcUp.result = 0
		SendMessage(userList, PacketCode[2064].client, gcUp:ByteSize(),gcUp:SerializeToString())
	end
	
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
end