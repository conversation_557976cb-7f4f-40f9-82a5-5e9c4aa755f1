module("GetWithdrawalFee", package.seeall)

--提币转账手续
function execute(packetID, operateID, buffer)

	local cgmsg = msg_order2_pb.cggetwithdrawalfee()
	local gcmsg = msg_order2_pb.gcgetwithdrawalfee()
	
	cgmsg:ParseFromString(buffer)
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	local sqlCase = "select extract_currency_rate,min_extract_currency_count,max_extract_currency_count,transfer_curreny_rate,min_transfer_curreny_count,max_transfer_curreny_count,extract_currency_rate_trc"
					.." from dy_coin_info where channel='"..uInfo.channel.."' and coin_id=2003"
	mysqlItem:executeQuery(sqlCase)	
	local sqlData = mysqlItem:fetch({})
	if sqlData ~= nil then
		if cgmsg.chaintype == 101 then
			gcmsg.withdrawalfee = tostring(sqlData[1])
		else
			gcmsg.withdrawalfee = tostring(sqlData[7])
		end
 
		gcmsg.minwithdrawalcount = tostring(sqlData[2])
		gcmsg.maxwithdrawalcount = tostring(sqlData[3])
		gcmsg.transferfee = tostring(sqlData[4])
		gcmsg.mintransfercount = tostring(sqlData[5])
		gcmsg.maxtransfercount = tostring(sqlData[6])
	else
		gcmsg.withdrawalfee = "0"
		gcmsg.minwithdrawalcount = "0"
		gcmsg.maxwithdrawalcount = "0"
		gcmsg.transferfee = "0"
		gcmsg.mintransfercount = "0"
		gcmsg.maxtransfercount = "0"
	end
	
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
	
end