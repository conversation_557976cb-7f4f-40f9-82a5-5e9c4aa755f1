module("Paid", package.seeall)
--我已经付款，请放行

function execute(packetID, operateID, buffer)

	local cgmsg = msg_order_pb.cgpaid()
	local gcmsg = msg_order_pb.gcpaid()
	
	cgmsg:ParseFromString(buffer)
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, gcmsg.result, gcmsg:ByteSize(), gcmsg:SerializeToString()		
	end

	--[[
	--检查资金密码
	local ret, msg = UserInfoModel.CheckFundPassword(uInfo, cgmsg.fundPwd)
	if ret ~= 0 then
		gcmsg.result, gcmsg.msg = ret,msg
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	]]
	--检查用户是否锁定
	if uInfo.islock == 1 then
		gcmsg.result,gcmsg.msg = 1, "你已经被锁定无法进行该操作"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--检查用户是否锁定
	local isOk = false
	for k, v in ipairs(cgmsg.proofurl) do 
		if v ~= "" then
			isOk = true
			break
		end
	end
	
	if isOk == false then
		gcmsg.result,gcmsg.msg = 1, "请上传支付凭证"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	ThreadManager.DealLock(cgmsg.dealid)
	local coItem = CustomerOrderModel.GetCustomerOrderInfo(cgmsg.dealid,gcmsg.coinfo)
	if coItem == nil then
		--挂单的信息不存在
		gcmsg.coinfo.dealid = 1
		ThreadManager.DealUnLock(cgmsg.dealid)
		gcmsg.result, gcmsg.msg = ReturnCode["deal_order_not_exist"][1],ReturnCode["deal_order_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end

	--我已付款
	local ret, msg = VendorOrderService.PaidOrder(uInfo, gcmsg.coinfo,cgmsg.proofurl)
	if ret ~= nil then
		ThreadManager.DealUnLock(cgmsg.dealid)
		gcmsg.result, gcmsg.msg = ret, msg
		return cgmsg.userid, gcmsg.result, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--查看是不是代付订单，如果是就直接放行
	local voItem = VendorOrderModel.GetVendorOrderInfo(gcmsg.coinfo.vendororderid)
	if voItem ~= nil and voItem.withdrawtype == 1 then
		local ret, msg = VendorOrderService.PassOrder(nil, gcmsg.coinfo)
		if ret ~= nil then
			ThreadManager.DealUnLock(cgmsg.dealid)
			gcmsg.result, gcmsg.msg = ret, msg
			return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
		end
	end
	
	--给前端转化，前端只认对于这个这个用户说是买还是卖
	gcmsg.coinfo.type = CoinInfoService.orderTypeConversion(gcmsg.coinfo.type, gcmsg.coinfo.customeruserid, gcmsg.coinfo.vendoruserid, cgmsg.userid)
		
	ThreadManager.DealUnLock(cgmsg.dealid)
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
end