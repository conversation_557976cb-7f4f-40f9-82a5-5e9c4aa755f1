module("CustomerOrderList", package.seeall)

function execute(packetID, operateID, buffer)

	local cgmsg = msg_order_pb.cgcustomerorderlist()
	local gcmsg = msg_order_pb.gccustomerorderlist()
	
	cgmsg:ParseFromString(buffer)
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result = ReturnCode["human_user_not_exist"][1]
		gcmsg.msg = ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()		
	end
	
	--先把所有的渠道跟价格查出来
	local channelPrice = {}
	local sqlCase = "select channel,(buy_price+add_buy_price),(free_price+minus_sell_price),is_fixed_buy_rate,fixed_buy_rate,"
		.."is_fixed_sell_rate,fixed_sell_rate from dy_coin_info where channel!='ALL'"
	mysqlItem:executeQuery(sqlCase)
	while true do
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		local tmp = {}
		if tonumber(sqlData[4]) == 1 then
			tmp[g_marketDefine.hang_buy] = tonumber(sqlData[5])
		else
			tmp[g_marketDefine.hang_buy] = tonumber(sqlData[2])
		end
		
		if tonumber(sqlData[6]) == 1 then
			tmp[g_marketDefine.hang_sell] = tonumber(sqlData[7])
		else
			tmp[g_marketDefine.hang_sell] = tonumber(sqlData[3])
		end
		
		channelPrice[sqlData[1]] = tmp
	end
	
	local channelrate = {}
	local sqlCase = "select channel,userid,add_sell_comm_rate,channel_deal,is_add_buy_comm,is_currency_buy_rate,currency_add_buy_rate from dy_channel_info"
	mysqlItem:executeQuery(sqlCase)
	while true do
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		local tmp = {}
		tmp["userid"] = tonumber(sqlData[2])
		tmp["rate"] = tonumber(sqlData[3])
		tmp["channelDeal"] = tonumber(sqlData[4])
		tmp["isAddbBuyComm"] = tonumber(sqlData[5])
		tmp["isCurrencyBuyRate"] = tonumber(sqlData[6])
		tmp["currencyAddBuyRate"] = tonumber(sqlData[7])
		channelrate[sqlData[1]] = tmp
	end
	local startPos = (cgmsg.pagenum - 1) * cgmsg.pagesize
	local endPos = startPos + cgmsg.pagesize
	local otype = (cgmsg.ordertype + 1) % 2
	local sqlCase = ""
	--检查用户的渠道是公有还是私有
	if channelrate[uInfo.channel].channelDeal == 0 then
		--公开
		--[[
		sqlCase = "select * from dy_vendor_order where userid!="..uInfo.userid.." and enable_status=1 and auto_switch=0 and is_external=0 and type="..otype.." and rest_time<'"..TimeUtils.GetTimeString().."' "
			.." and channel_deal=0 and (user_type !=300 or (user_type=300 and "
			.."((withdraw_type = 0 and EXISTS(SELECT * FROM dy_user_conf WHERE userid=dy_vendor_order.userid and is_exist=1 and (deal_type=1 and ((bind_type=101 and channel_name='"..uInfo.channel.."') or (bind_type=102 and bind_userid="..uInfo.userid..") or (bind_type=103 and (bind_userid="..uInfo.userid
			.." or EXISTS(SELECT * FROM ag_relation WHERE ag_relation.bind_userid=dy_user_conf.bind_userid and ag_relation.userid="..uInfo.userid..")))))))"
			.." or (withdraw_type = 1 and EXISTS(SELECT * FROM dy_user_conf WHERE userid=dy_vendor_order.userid and is_exist=1 and (deal_type=2 and ((bind_type=101 and channel_name='"..uInfo.channel.."') or (bind_type=102 and bind_userid="..uInfo.userid..") or (bind_type=103 and (bind_userid="..uInfo.userid
			.." or EXISTS(SELECT * FROM ag_relation WHERE ag_relation.bind_userid=dy_user_conf.bind_userid and ag_relation.userid="..uInfo.userid.."))))))))))"
		]]
		sqlCase = "select * from dy_vendor_order where userid!="..uInfo.userid.." and enable_status=1 and auto_switch=0 and is_external=0 and type="..otype.." and rest_time<'"..TimeUtils.GetTimeString().."' "
			.." and channel_deal=0 and (user_type !=300 or (user_type=300 and "
			.."((withdraw_type = 0 and EXISTS(SELECT * FROM dy_user_conf WHERE userid=dy_vendor_order.userid and is_exist=1 and (deal_type=1 and ((bind_type=101 and channel_name='"..uInfo.channel.."') or (bind_type=102 and bind_userid="..uInfo.userid..") or (bind_type=103 and (bind_userid="..uInfo.userid
			.." or EXISTS(SELECT * FROM ag_relation WHERE ag_relation.bind_userid=dy_user_conf.bind_userid and ag_relation.userid="..uInfo.userid.."))) or (bind_type=104 and EXISTS(SELECT * FROM dy_group_member WHERE dy_group_member.group_id=dy_user_conf.bind_userid and dy_group_member.group_user_id="..uInfo.userid.." and dy_group_member.status=1 and dy_group_member.group_withdraw_switch=1))))))"
			.." or (withdraw_type = 1 and EXISTS(SELECT * FROM dy_user_conf WHERE userid=dy_vendor_order.userid and is_exist=1 and (deal_type=2 and ((bind_type=101 and channel_name='"..uInfo.channel.."') or (bind_type=102 and bind_userid="..uInfo.userid..") or (bind_type=103 and (bind_userid="..uInfo.userid
			.." or EXISTS(SELECT * FROM ag_relation WHERE ag_relation.bind_userid=dy_user_conf.bind_userid and ag_relation.userid="..uInfo.userid.."))) or (bind_type=104 and EXISTS(SELECT * FROM dy_group_member WHERE dy_group_member.group_id=dy_user_conf.bind_userid and dy_group_member.group_user_id="..uInfo.userid.." and dy_group_member.status=1 and dy_group_member.group_behalf_switch=1)))))))))"
	else
		--私有
		--[[
		sqlCase = "select * from dy_vendor_order where userid!="..uInfo.userid.." and enable_status=1 and auto_switch=0 and is_external=0 and type="..otype.." and rest_time<'"..TimeUtils.GetTimeString().."' "
			.." and (channel='"..uInfo.channel.."' or (user_type=300 and "
			.."((withdraw_type = 0 and EXISTS(SELECT * FROM dy_user_conf WHERE userid=dy_vendor_order.userid and is_exist=1 and (deal_type=1 and ((bind_type=101 and channel_name='"..uInfo.channel.."') or (bind_type=102 and bind_userid="..uInfo.userid..") or (bind_type=103 and (bind_userid="..uInfo.userid
			.." or EXISTS(SELECT * FROM ag_relation WHERE ag_relation.bind_userid=dy_user_conf.bind_userid and ag_relation.userid="..uInfo.userid..")))))))"
			.." or (withdraw_type = 1 and EXISTS(SELECT * FROM dy_user_conf WHERE userid=dy_vendor_order.userid and is_exist=1 and (deal_type=2 and ((bind_type=101 and channel_name='"..uInfo.channel.."') or (bind_type=102 and bind_userid="..uInfo.userid..") or (bind_type=103 and (bind_userid="..uInfo.userid
			.." or EXISTS(SELECT * FROM ag_relation WHERE ag_relation.bind_userid=dy_user_conf.bind_userid and ag_relation.userid="..uInfo.userid.."))))))))))"
		]]
		--私有
		sqlCase = "select * from dy_vendor_order where userid!="..uInfo.userid.." and enable_status=1 and auto_switch=0 and is_external=0 and type="..otype.." and rest_time<'"..TimeUtils.GetTimeString().."' "
			.." and (channel='"..uInfo.channel.."' or (user_type=300 and "
			.."((withdraw_type = 0 and EXISTS(SELECT * FROM dy_user_conf WHERE userid=dy_vendor_order.userid and is_exist=1 and (deal_type=1 and ((bind_type=101 and channel_name='"..uInfo.channel.."') or (bind_type=102 and bind_userid="..uInfo.userid..") or (bind_type=103 and (bind_userid="..uInfo.userid
			.." or EXISTS(SELECT * FROM ag_relation WHERE ag_relation.bind_userid=dy_user_conf.bind_userid and ag_relation.userid="..uInfo.userid.."))) or (bind_type=104 and EXISTS(SELECT * FROM dy_group_member WHERE dy_group_member.group_id=dy_user_conf.bind_userid and dy_group_member.group_user_id="..uInfo.userid.." and dy_group_member.status=1 and dy_group_member.group_withdraw_switch=1))))))"
			.." or (withdraw_type = 1 and EXISTS(SELECT * FROM dy_user_conf WHERE userid=dy_vendor_order.userid and is_exist=1 and (deal_type=2 and ((bind_type=101 and channel_name='"..uInfo.channel.."') or (bind_type=102 and bind_userid="..uInfo.userid..") or (bind_type=103 and (bind_userid="..uInfo.userid
			.." or EXISTS(SELECT * FROM ag_relation WHERE ag_relation.bind_userid=dy_user_conf.bind_userid and ag_relation.userid="..uInfo.userid.."))) or (bind_type=104 and EXISTS(SELECT * FROM dy_group_member WHERE dy_group_member.group_id=dy_user_conf.bind_userid and dy_group_member.group_user_id="..uInfo.userid.." and dy_group_member.status=1 and dy_group_member.group_behalf_switch=1)))))))))"
	end
	mysqlItem:executeQuery(sqlCase)
	local VendorOrderList = {}
	while true do
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		
		local addVendorOrder = st_order_pb.vendororderinfo()
		addVendorOrder.userid = tonumber(sqlData[2])
		addVendorOrder.hangid = tonumber(sqlData[1])
		addVendorOrder.type = tonumber(sqlData[3])
		addVendorOrder.pricetype = tonumber(sqlData[6])
		addVendorOrder.price = sqlData[4]
		addVendorOrder.amount = sqlData[5]
		addVendorOrder.minmoney = sqlData[7]
		addVendorOrder.maxmoney = sqlData[8]
		addVendorOrder.autoswitch = tonumber(sqlData[10])
		addVendorOrder.message = sqlData[11]
		addVendorOrder.maxamount = sqlData[16]
		addVendorOrder.enablestatus = tonumber(sqlData[9])
		addVendorOrder.channel = sqlData[17]    
		addVendorOrder.unsoldordernum = tonumber(sqlData[18])  
		addVendorOrder.dealordernum = tonumber(sqlData[19])  
		addVendorOrder.unsoldorderamount = sqlData[20]
		addVendorOrder.dealorderamount = sqlData[21] 
		addVendorOrder.cancelnum = tonumber(sqlData[22])  
		addVendorOrder.usertype = tonumber(sqlData[23])  
		addVendorOrder.payeeaccount = sqlData[24]
		addVendorOrder.payeename = sqlData[25]
		addVendorOrder.payeebank = sqlData[26]
		addVendorOrder.payeebandaddr = sqlData[27]
		addVendorOrder.deal_order_free = sqlData[28]
		addVendorOrder.free = sqlData[29]
		addVendorOrder.addfeerate = sqlData[32]
		addVendorOrder.createtime = sqlData[12]
		addVendorOrder.channeldeal = tonumber(sqlData[30])
		addVendorOrder.dealmodel = tonumber(sqlData[35])
		addVendorOrder.predictmoney = sqlData[34]
		addVendorOrder.resttime = sqlData[36]
		addVendorOrder.dealmonery = sqlData[37]
		addVendorOrder.dealcount = sqlData[38]
		addVendorOrder.withdrawtype = tonumber(sqlData[39])
		addVendorOrder.unsoldordermoney = sqlData[44]
		addVendorOrder.dealordermoney = sqlData[45]
		
		local price = addVendorOrder.pricetype == 0 and tonumber(addVendorOrder.price) or CoinInfoService.getListUSDTPrice(addVendorOrder.channel, (addVendorOrder.type) % 2, true, addVendorOrder.userid, channelPrice)
		local money = (tonumber(addVendorOrder.amount) - tonumber(addVendorOrder.dealorderamount) + tonumber(addVendorOrder.unsoldorderamount)) * price
		if addVendorOrder.dealmodel == 1 then
			money = tonumber(addVendorOrder.predictmoney) - ((tonumber(addVendorOrder.dealorderamount) + tonumber(addVendorOrder.unsoldorderamount)) * price)
		end
		
		local uInfo = UserInfoModel.GetUserInfo(addVendorOrder.userid)
		if uInfo ~= nil and uInfo.dealcointype == 1 then
			money =  tonumber(addVendorOrder.predictmoney) - ((tonumber(addVendorOrder.unsoldordermoney) or 0) + (tonumber(addVendorOrder.dealordermoney) or 0))
		end
		if money >= tonumber(addVendorOrder.minmoney) then
			table.insert(VendorOrderList, addVendorOrder)
		end
	end
	
	local sortList = {}
	
	for pos,VendorOrder in ipairs(VendorOrderList) do 
		local uInfo = UserInfoModel.GetUserInfo(VendorOrder.userid)
		if uInfo ~= nil then
			if VendorOrder.usertype == 200 then
				for _, paytype in ipairs(uInfo.paytypelist) do 
					local rate = 0
					if uInfo.usertype == 200 or uInfo.usertype == 201 then
						if channelrate[uInfo.channel].isAddbBuyComm == 1 then
							rate = UserInfoModel.getUserFeeRate(channelrate[uInfo.channel].userid, paytype, g_humanDefine.sell_comm_rate) + channelrate[uInfo.channel].rate
						end
					elseif uInfo.usertype == 202 then
						if channelrate[uInfo.channel].isCurrencyBuyRate == 1 then
							rate = UserInfoModel.getUserFeeRate(channelrate[uInfo.channel].userid, paytype, g_humanDefine.sell_comm_rate) + channelrate[uInfo.channel].currencyAddBuyRate
						end
					end
					
					local payList = UserInfoModel.GetPayInfoList(uInfo.userid, paytype)
					if payList ~= nil then
						for k,v in pairs(payList)do
							if k ~= "Placeholder" then
								local PayInfo = st_human_pb.payinfo()
								PayInfo:ParseFromString(v)
								if PayInfo.status == 1 then
									VendorOrder.paylist:append(paytype)
									VendorOrder.feeRate:append(tostring(rate))
									break
								end
							end
						end
					end
				end 
			elseif VendorOrder.usertype == 300 then
				VendorOrder.paylist:append(g_humanDefine.user_payList.bank)
				VendorOrder.feeRate:append('0.00')
			end
					
			if VendorOrder.pricetype == 1 then
				VendorOrder.price = tostring(channelPrice[uInfo.channel][VendorOrder.type])
			end
			VendorOrder.nickname = uInfo.nickname
		end
		
		local tmp = {}
		tmp['pos'] = pos
		tmp['rate'] = 1
		for _,rate in ipairs(VendorOrder.feeRate) do 
			if tonumber(rate) < tmp['rate'] then
				tmp['rate'] = tonumber(rate)
			end
		end
		tmp['price'] = tonumber(VendorOrder.price) or 0
		table.insert(sortList, tmp)
	end
	
	--排序
	table.sort(sortList, function(a,b)
		if a.rate < b.rate then
			return true
		elseif a.rate > b.rate then
			return false
		else
			if a.price < b.price then
				return true
			else
				return false
			end
		end
	end)
	for k,v in ipairs(sortList) do 
		if k > startPos then
			local addVendorOrder = gcmsg.volist:add()
			addVendorOrder:ParseFromString(VendorOrderList[v.pos]:SerializeToString())
		end
		
		if k >= endPos then
			break
		end
	end
	
	--取到了对应的列表
	gcmsg.ordertype = cgmsg.ordertype
	gcmsg.pagenum = cgmsg.pagenum
	gcmsg.pagesize = cgmsg.pagesize
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
end