module("ExchangeRate", package.seeall)

--查询汇率
function execute(packetID, operateID, buffer)

	local cgmsg = msg_order2_pb.cgexchangerate()
	local gcmsg = msg_order2_pb.gcexchangerate()
	
	cgmsg:ParseFromString(buffer)

	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	gcmsg.buyrate = tostring(CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.hang_buy, g_marketDefine.currency_type.USDT, true, uInfo.userid))
	gcmsg.sellrate = tostring(CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.hang_sell, g_marketDefine.currency_type.USDT, true, uInfo.userid))
	
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
	
end