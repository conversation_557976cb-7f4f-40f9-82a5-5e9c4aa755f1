module("ModifyOrder", package.seeall)


function execute(packetID, operateID, buffer)

	local cgmsg = msg_order_pb.cgmodifyorder()
	local gcmsg = msg_order_pb.gcmodifyorder()
	
	cgmsg:ParseFromString(buffer)
	
	local uInfo = UserModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then

		gcmsg.result,gcmsg.msg = ReturnCode["player_not_exist"][0],ReturnCode["task_unexist"][1]
		
		return cgmsg.userid, gcmsg.result, gcmsg:ByteSize(), gcmsg:SerializeToString()		
	end


	if sqlData == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["get_out_price_fail"][0],ReturnCode["get_out_price_fail"][1]
		return cgmsg.userid, gcmsg.result, gcmsg:ByteSize(), gcmsg:SerializeToString()			
	end

	local hInfo = VendorOrderModel.GetVendorOrderInfo(cgmsg.hangid)
	
	if hInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["get_out_price_fail"][0],ReturnCode["get_out_price_fail"][1]
		return cgmsg.userid, gcmsg.result, gcmsg:ByteSize(), gcmsg:SerializeToString()			
	end

	if tonumber(sqlData[10]) == 1 and tonumber(sqlData[4]) ~= cgmsg.price then
		--自动挂单不能修改价格
		gcmsg.result,gcmsg.msg = ReturnCode["hang_modify_not_auto"][0],ReturnCode["hang_modify_not_auto"][1]
		return cgmsg.userid, gcmsg.result, gcmsg:ByteSize(), gcmsg:SerializeToString()				
	end
	
	hInfo.price = cgmsg.price
	
	local varNum = cgmsg.amount - hInfo.amount    --这个是变化的数量，提现在最大的总额上
	hInfo.amount = cgmsg.amount
	hInfo.minmoney = cgmsg.minmoney
	hInfo.maxmoney = cgmsg.maxmoney
	hInfo.message = cgmsg.message
	hInfo.enablestatus = cgmsg.enablestatus
	hInfo.maxmoney = hInfo.maxmoney + varNum
	
	
	local sqlCase = "update dy_vendor_order set price="..hInfo.price",amount="..hInfo.amount..",min_money="..hInfo.minmoney..",max_money="..hInfo.maxmoney..",message='"..hInfo.message..",enable_status="..hInfo.enablestatus.." where id="..hInfo.hangid
	
	mysqlItem:execute(sqlCase)
	
	VendorOrderModel.SetVendorOrderInfo(hInfo)
	
	if cgmsg.enablestatus == 0 then
		--如果是失效的。就需要清理了
		--这里是的
		VendorOrderModel.DelHangAmount(hInfo.hangid, hInfo.type)
	else
		VendorOrderModel.SetHangAmount(hInfo.hangid,hInfo.userid,hInfo.amount,hInfo.type) --把type抽离出来
	end
	
	
	--sqlCase = "update dy_vendor_order set"
	--更新对应的参数
	gcmsg.result = 0
	gcmsg.msg = "设置成功"
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
end