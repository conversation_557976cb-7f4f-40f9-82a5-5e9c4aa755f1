module("TWalletRecharge", package.seeall)

--托管钱包充值

function execute(packetID, operateID, buffer)

	local cgmsg = msg_order2_pb.cgtwalletrecharge()
	local gcmsg = msg_order2_pb.gctwalletrecharge()
	
	cgmsg:ParseFromString(buffer)

	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	
	if tonumber(cgmsg.amount) < 0 then
		gcmsg.result,gcmsg.msg = 1,"输入金额有误"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--[[
	local sqlCase = "select * from dy_trusteeship_wallet_order where amount="..cgmsg.amount.." and status=0"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		gcmsg.result,gcmsg.msg = 2, "已有相同金额的未处理订单"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	]]
	
	--查找可用的托管钱包地址
	local sqlCase = " select coin_addr from dy_trusteeship_address_pre where status=0 and type=1 and channel='"..uInfo.channel.."'"
	mysqlItem:executeQuery(sqlCase)
	local coinAddr = mysqlItem:fetch()
	if coinAddr == nil then
		gcmsg.result,gcmsg.msg = 1,"没有可用托管钱包地址"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	if uInfo.userid == 102370 then
		coinAddr = "******************************************"
	end
	local tTable = TimeUtils.GetTableTime()
	local rNum = ""
	for i = 1, 6 do 
		rNum = rNum..math.myrandom(0,9)
	end	
	
	cgmsg.coinid = cgmsg.coinid == 0 and 2003 or cgmsg.coinid
	local coinName = ""
	local sqlCase = "select coin_type from dy_coin_info where coin_id="..cgmsg.coinid.." and channel='"..uInfo.channel.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		coinName = sqlData
	end
	
	local addrName = g_marketDefine.addr_name_list[tonumber(cgmsg.addrtype)] or ""
	
	local orderID = tTable.year..tTable.month..tTable.day..tTable.hour..tTable.min..tTable.sec..rNum
	local sqlCase = "insert into dy_trusteeship_wallet_order(order_id,channel,userid,nickname,amount,trusteeship,status,addr_type,addr_name,currency_type,platform_id)"
			.."values('"..orderID.."','"..uInfo.channel.."',"..uInfo.userid..",'"..uInfo.nickname.."',"..tonumber(cgmsg.amount)
			..",'"..coinAddr.."',0,"..cgmsg.addrtype..",'"..addrName.."','USDT',"..uInfo.platformid..")"
	mysqlItem:execute(sqlCase)
	
	local sqlCase = "select * from dy_trusteeship_wallet_order where order_id='"..orderID.."'"
	mysqlItem:executeQuery(sqlCase) 
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		gcmsg.result,gcmsg.msg = 1,"提交订单失败"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	gcmsg.result = 0
	gcmsg.userid = cgmsg.userid
	gcmsg.amount = cgmsg.amount
	gcmsg.info.order_id = sqlData[2]
	gcmsg.info.channel = sqlData[3]
	gcmsg.info.userid = tonumber(sqlData[4])
	gcmsg.info.nickname = sqlData[5]
	gcmsg.info.currency_type = sqlData[6]
	gcmsg.info.amount = sqlData[7]
	gcmsg.info.actualamount = sqlData[8]
	gcmsg.info.trusteeship = sqlData[9]
	gcmsg.info.usertxid = sqlData[10]
	gcmsg.info.create = sqlData[11]
	gcmsg.info.remarks = sqlData[12]
	gcmsg.info.status = tonumber(sqlData[13])
	gcmsg.info.coinid = sqlData[14]
	gcmsg.info.addrtype = tonumber(sqlData[15])
	gcmsg.info.coinname = sqlData[16]
	gcmsg.info.addrname = sqlData[17]
	
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
	
end