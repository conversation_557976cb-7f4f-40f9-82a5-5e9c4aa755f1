module("Deal", package.seeall)


--发起交易
function execute(packetID, operateID, buffer)

	local cgmsg = msg_order_pb.cgdeal()
	local gcmsg = msg_order_pb.gcdeal()
	
	cgmsg:ParseFromString(buffer)
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()		
	end
	
	--检查用户是否锁定
	if uInfo.islock == 1 then
		gcmsg.result,gcmsg.msg = 1, "你已经被锁定无法进行该操作"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--检查资金密码
	local ret, msg = UserInfoModel.CheckFundPassword(uInfo, cgmsg.fundPwd)
	if ret ~= 0 then
		gcmsg.result, gcmsg.msg = ret,msg
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--检查可取消次数
	local num = CustomerOrderModel.GetDealLeftCount(cgmsg.userid)
	if num <= 0 then
		gcmsg.result, gcmsg.msg = 1,"您今天的取消订单次数过多，已被系统限制购买"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--查询还有多少订单正在进行中
	local sqlCase = "select count(*) from dy_customer_order where customer_user_id="..uInfo.userid.." and status=1"
	mysqlItem:executeQuery(sqlCase)
	local sqlDate = mysqlItem:fetch()
	if sqlDate ~= nil and tonumber(sqlDate) >= 5 then
		gcmsg.result, gcmsg.msg = 1,"未付款订单已达上限，请先完成支付"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	ThreadManager.OrderLock(cgmsg.hangid)
	local voItem = VendorOrderModel.GetVendorOrderInfo(cgmsg.hangid)
	if voItem == nil then
		ThreadManager.OrderUnLock(cgmsg.hangid)
		gcmsg.result, gcmsg.msg = ReturnCode["deal_order_not_exist"][1],ReturnCode["deal_order_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	if voItem.istake == 1 then
		ThreadManager.OrderUnLock(cgmsg.hangid)
		gcmsg.result, gcmsg.msg = 1, "此挂单已失效"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--检查渠道是否公开 
	local channelDeal = 1
	local sqlCase = "select channel_deal from dy_channel_info where channel='"..uInfo.channel.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		channelDeal = tonumber(sqlData)
	end
	
	if channelDeal == 0 then
		--公开
		--检查这个挂单是公开还是私有的
		if voItem.channeldeal == 1 then
			ThreadManager.OrderUnLock(cgmsg.hangid)
			gcmsg.result, gcmsg.msg = 1,"该订单是私有的，您无法购买"	
			return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
		end
		
	else
	
		local tmpMsg = "dy_group_member.group_withdraw_switch"
		if tonumber(voItem.withdrawtype) == 1 then
			tmpMsg = "dy_group_member.group_behalf_switch"
		end
		--私有
		local isOK = false
		if voItem.usertype == 300 then
			local sqlCase = ""
			if voItem.withdrawtype == 0 then
				sqlCase = "select * from dy_user_conf where userid="..voItem.userid.." and deal_type=1"
					.." and ((bind_type=101 and channel_name='"..uInfo.channel.."') or (bind_type=102 and bind_userid="..uInfo.userid..")"
					.." or (bind_type=103 and (bind_userid="..uInfo.userid.." or EXISTS(SELECT * FROM ag_relation WHERE ag_relation.bind_userid=dy_user_conf.bind_userid and ag_relation.userid="..uInfo.userid..")))"
					.." or (bind_type=104 and EXISTS(SELECT * FROM dy_group_member WHERE dy_group_member.group_id=dy_user_conf.bind_userid and dy_group_member.group_user_id="..uInfo.userid.." and dy_group_member.status=1 and "..tmpMsg.."=1)))"
				mysqlItem:executeQuery(sqlCase)
			else
				sqlCase = "select * from dy_user_conf where userid="..voItem.userid.." and deal_type=2"
					.." and ((bind_type=101 and channel_name='"..uInfo.channel.."') or (bind_type=102 and bind_userid="..uInfo.userid..")"
					.." or (bind_type=103 and (bind_userid="..uInfo.userid.." or EXISTS(SELECT * FROM ag_relation WHERE ag_relation.bind_userid=dy_user_conf.bind_userid and ag_relation.userid="..uInfo.userid..")))"
					.." or (bind_type=104 and EXISTS(SELECT * FROM dy_group_member WHERE dy_group_member.group_id=dy_user_conf.bind_userid and dy_group_member.group_user_id="..uInfo.userid.." and dy_group_member.status=1 and "..tmpMsg.."=1)))"
				mysqlItem:executeQuery(sqlCase)
			end
			local sqlData = mysqlItem:fetch()
			if sqlData ~= nil then
				isOK = true
			end
		end
		if voItem.channel ~= uInfo.channel and isOK == false then
			ThreadManager.OrderUnLock(cgmsg.hangid)
			gcmsg.result, gcmsg.msg = 1,"该订单不属于的您的渠道，您无法购买"	
			return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
		end
	end
	
	local vInfo = UserInfoModel.GetUserInfo(voItem.userid)
	local sysPrice = CoinInfoService.Erc20USDTPrice(vInfo.channel, voItem.type, g_marketDefine.currency_type.USDT, true, vInfo.userid)
	local price = 0
	--检查订单价格
	if voItem.pricetype == 0 then
		if tostring(voItem.price) ~= tostring(cgmsg.price) then
			--ThreadManager.OrderUnLock(cgmsg.hangid)
			--gcmsg.result, gcmsg.msg = 1,"订单价格已经更新，请刷新重试"	
			--return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
		end
		
		price = tonumber(voItem.price)
	else
		if tostring(sysPrice) ~= tostring(cgmsg.price) then
			--ThreadManager.OrderUnLock(cgmsg.hangid)
			--gcmsg.result, gcmsg.msg = 1,"订单价格已经更新，请刷新重试"	
			--return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
		end
		price = sysPrice
	end
	
	--该订单还不能购买
	if TimeUtils.GetTime(voItem.resttime) > TimeUtils.GetTime() then
		ThreadManager.OrderUnLock(cgmsg.hangid)
		gcmsg.result, gcmsg.msg = 1,"该订单还不能购买"	
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end

	local currencyCount = 0
	local moneyCount = 0

	if cgmsg.moneytype == g_marketDefine.amountType_RMB then
		currencyCount = math.floor((tonumber(cgmsg.amount) / price) * 10000000000) / 10000000000
		moneyCount = tonumber(cgmsg.amount)
	else
		currencyCount = tonumber(cgmsg.amount)
		moneyCount = tonumber(cgmsg.amount) * price
		if voItem.dealmodel == 1 then
			currencyCount = tonumber(voItem.amount)
			moneyCount = tonumber(voItem.predictmoney)
		end
	end
	
	--商户提现的下单
	if voItem.type == 1 and voItem.usertype == 300 and voItem.dealmodel == 0 then
		local remainingMoney = tonumber(voItem.predictmoney) - tonumber(voItem.unsoldordermoney) - tonumber(voItem.dealordermoney)
		remainingMoney = tonumber(string.format("%.4f", tostring(remainingMoney)))
		moneyCount = tonumber(string.format("%.4f", tostring(moneyCount)))
		if remainingMoney < moneyCount then
			ThreadManager.OrderUnLock(cgmsg.hangid)
			gcmsg.result, gcmsg.msg = 1, "挂单余额不足"
			return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
		end
		
		local msg = ""
		local diffMoney = remainingMoney - moneyCount
		diffMoney = tonumber(string.format("%.4f", tostring(diffMoney)))
		if diffMoney < tonumber(voItem.minmoney) and diffMoney > 0 then
			msg = "下单金额需为￥"..remainingMoney..""
		end
		
		if msg ~= "" then
			ThreadManager.OrderUnLock(cgmsg.hangid)
			gcmsg.result, gcmsg.msg = 1, msg
			return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
		end
	
	end
	
	local ret, msg = VendorOrderService.DealOrder(
            uInfo,
            gcmsg.coinfo,
            voItem,
            cgmsg.dealtype,
            currencyCount,
            moneyCount,
            nil,
            price,
            cgmsg.paytype,
            ""
	    )
	if ret ~= nil then
		ThreadManager.OrderUnLock(cgmsg.hangid)
		gcmsg.result = ret
		gcmsg.msg = msg
		gcmsg.coinfo.dealid = 1
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	ThreadManager.OrderUnLock(cgmsg.hangid)
	
	--给前端转化，前端只认对于这个这个用户说是买还是卖
	gcmsg.coinfo.type = CoinInfoService.orderTypeConversion(gcmsg.coinfo.type, gcmsg.coinfo.customeruserid, gcmsg.coinfo.vendoruserid, cgmsg.userid)
	
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
end
