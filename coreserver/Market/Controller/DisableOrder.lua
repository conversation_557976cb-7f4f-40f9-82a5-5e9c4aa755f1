module("DisableOrder", package.seeall)

--取消挂单
function execute(packetID, operateID, buffer)

	local cgmsg = msg_order_pb.cgdisableorder()
	local gcmsg = msg_order_pb.gcdisableorder()
	
	cgmsg:ParseFromString(buffer)
	
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, gcmsg.result, gcmsg:ByteSize(), gcmsg:SerializeToString()		
	end

	ThreadManager.OrderLock(cgmsg.hangid)
	local hInfo = VendorOrderModel.GetVendorOrderInfo(cgmsg.hangid)
	if hInfo == nil  then
		ThreadManager.OrderUnLock(cgmsg.hangid)
		gcmsg.result, gcmsg.msg = ReturnCode["deal_order_not_exist"][1],ReturnCode["deal_order_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--还有正在进行中的订单
	local sqlCase = "select count(*) from dy_customer_order where vendor_order_id="..hInfo.hangid.." and (status=1 "
		.." or status=3 or status=9)" 
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	local unsoldordernum = 0
	if sqlData ~= nil then
		unsoldordernum = tonumber(sqlData)
	end
	
	
	--if hInfo.unsoldordernum > 0 then
	if unsoldordernum > 0 then
		ThreadManager.OrderUnLock(cgmsg.hangid)
		gcmsg.result,gcmsg.msg = ReturnCode["hang_is_working"][1],ReturnCode["hang_is_working"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()		
	end
	
	hInfo.enablestatus = 0
	local sqlCase = "update dy_vendor_order set enable_status="..hInfo.enablestatus.." where id="..hInfo.hangid
	mysqlItem:execute(sqlCase)	
	
	if hInfo.autoswitch == 1  and hInfo.type == 1 then
		uInfo.autosell = 0
		UserInfoModel.SetUserInfo(uInfo)
		UserInfoModel.SendAutoSell(uInfo)
	end
	
	--检查是否还有进行中的订单
	--挂单已经失效
	VendorOrderModel.DelVendOrorderInfo(hInfo.type, uInfo.userid, hInfo.hangid)
	
	ThreadManager.OrderUnLock(cgmsg.hangid)
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
end