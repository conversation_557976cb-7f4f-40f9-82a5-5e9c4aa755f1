module("Pass", package.seeall)


--放行的币，这个比较复杂，需要放币，做币的转移。
function execute(packetID, operateID, buffer)

	local cgmsg = msg_order_pb.cgpass()
	local gcmsg = msg_order_pb.gcpass()
	
	cgmsg:ParseFromString(buffer)
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()		
	end
	
	--检查资金密码
	local ret, msg = UserInfoModel.CheckFundPassword(uInfo, cgmsg.fundPwd)
	if ret ~= 0 then
		gcmsg.result, gcmsg.msg = ret,msg
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--检查用户是否锁定
	if uInfo.islock == 1 then
		gcmsg.result,gcmsg.msg = 1, "你已经被锁定无法进行该操作"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	ThreadManager.DealLock(cgmsg.dealid)
	local coinfo = CustomerOrderModel.GetCustomerOrderInfo(cgmsg.dealid,gcmsg.coinfo)
	if coinfo == nil then
		--挂单的信息不存在
		ThreadManager.DealUnLock(cgmsg.dealid)
		gcmsg.result, gcmsg.msg = ReturnCode["deal_order_not_exist"][1],ReturnCode["deal_order_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--检查订单状态申诉中也可以放行
	if coinfo.status ~= g_marketDefine.deal_status_pay and coinfo.status ~= g_marketDefine.deal_status_appeal and coinfo.status ~= g_marketDefine.deal_status_wait then
		ThreadManager.DealUnLock(cgmsg.dealid)
		gcmsg.result, gcmsg.msg = ReturnCode["deal_order_not_match"][1], ReturnCode["deal_order_not_match"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end		
	
	local payee = 0
	if coinfo == g_marketDefine.deal_buy then
		payee = coinfo.customeruserid
	else
		payee = coinfo.vendoruserid
	end
	if payee ~= uInfo.userid then
		ThreadManager.DealUnLock(cgmsg.dealid)
		gcmsg.result, gcmsg.msg = 1,  "您不是收款人无法完成放行操作"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end	
	
	--放行
	local ret, msg = VendorOrderService.PassOrder(uInfo, gcmsg.coinfo)
	if ret ~= nil then
		ThreadManager.DealUnLock(cgmsg.dealid)
		gcmsg.result, gcmsg.msg = ret, msg
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	ThreadManager.DealUnLock(cgmsg.dealid)

	--给前端转化，前端只认对于这个这个用户说是买还是卖
	gcmsg.coinfo.type = CoinInfoService.orderTypeConversion(gcmsg.coinfo.type, gcmsg.coinfo.customeruserid, gcmsg.coinfo.vendoruserid, cgmsg.userid)
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
end