module("OrderActivation", package.seeall)

function execute(packetID, operateID, buffer)
	
	local cgmsg = msg_order_pb.cgorderactivation()
	local gcmsg = msg_order_pb.gcorderactivation()
	
	cgmsg:ParseFromString(buffer)
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--检查资金密码
	local ret, msg = UserInfoModel.CheckFundPassword(uInfo,cgmsg.fundpwd)
	if ret ~= 0 then
		gcmsg.result, gcmsg.msg = ret,msg
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	ThreadManager.DealLock(cgmsg.dealid) 
	if nil == CustomerOrderModel.GetCustomerOrderInfo(cgmsg.dealid, gcmsg.coinfo) then
		ThreadManager.DealUnLock(cgmsg.dealid) 
		gcmsg.result, gcmsg.msg = 1,"订单不存在"	
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	if gcmsg.coinfo.status ~= g_marketDefine.deal_status_wait_timeout and gcmsg.coinfo.status ~= g_marketDefine.deal_status_appeal_cancel and gcmsg.coinfo.status ~= g_marketDefine.deal_status_cancel then
		ThreadManager.DealUnLock(cgmsg.dealid) 
		gcmsg.result, gcmsg.msg = 1,"该状态下，不能激活订单"	
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	local payeeInfo = {}		--收款人
	local payerInfo = {}		--付款人
	if gcmsg.coinfo.type == g_marketDefine.hang_buy then
		--顾客买币
		payeeInfo = UserInfoModel.GetUserInfo(gcmsg.coinfo.vendoruserid)
		payerInfo = UserInfoModel.GetUserInfo(gcmsg.coinfo.customeruserid)
	else
		--顾客卖币
		payeeInfo = UserInfoModel.GetUserInfo(gcmsg.coinfo.customeruserid)
		payerInfo = UserInfoModel.GetUserInfo(gcmsg.coinfo.vendoruserid)
	end
	
	if payeeInfo == nil or payerInfo == nil then
		ThreadManager.DealUnLock(cgmsg.dealid) 
		gcmsg.result, gcmsg.msg = 1,"收款人或付款人不存在"	
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	local currencyCount = tonumber(gcmsg.coinfo.amount)
	local moneyCount = 0
	if gcmsg.coinfo.dealtype == 300 then
		currencyCount = currencyCount + tonumber(gcmsg.coinfo.fee)
		moneyCount = (tonumber(coinfo.predictmoney) or 0) + tonumber(coinfo.money)
	end
	
	if gcmsg.coinfo.wallettype == 0 then
		--检查收款人资金是否还有
		if (tonumber( payeeInfo.ercusdtamount) - tonumber( payeeInfo.ercusdtlockamount)) < currencyCount then
			ThreadManager.DealUnLock(cgmsg.dealid) 
			gcmsg.result, gcmsg.msg = 1,"收款人余额不足！"	
			return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()		
		end
	else
		if (tonumber( payeeInfo.coinpayusdtamount) - tonumber( payeeInfo.coinpayusdtlockamount)) < currencyCount then
			ThreadManager.DealUnLock(cgmsg.dealid) 
			gcmsg.result, gcmsg.msg = 1,"收款人币付钱包余额不足！"	
			return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()		
		end
	end
	
	if gcmsg.coinfo.wallettype == 0 then
		if payeeInfo.dealcointype == 1 then
			if false == UserInfoModel.AddErcFCLockAmount(payeeInfo, moneyCount, arrData.id, gcmsg.coinfo, "激活订单冻结资金") then
				ThreadManager.DealUnLock(arrData.id) 
				retMsg['code'] = 1
				retMsg['msg'] = "收款人余额不足！"	
				return luajson.encode(retMsg)	
			end
		end
		
		--冻结收款人的币
		if false == UserInfoModel.AddErcUsdtLockAmount(payeeInfo, currencyCount, cgmsg.dealid, gcmsg.coinfo.type, "激活订单冻结资金") then
			ThreadManager.DealUnLock(cgmsg.dealid) 
			gcmsg.result, gcmsg.msg = 1,"收款人币数量不足！"	
			return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()		
		end
	else
		--冻结收款人的币
		if false == UserInfoModel.AddErcCoinPayUsdtLockAmount(payeeInfo, currencyCount, cgmsg.dealid, gcmsg.coinfo.type, "激活订单冻结资金") then
			ThreadManager.DealUnLock(cgmsg.dealid) 
			gcmsg.result, gcmsg.msg = 1,"收款人币币付钱包余额不足！"	
			return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()		
		end
	end
	
	CustomerOrderModel.UpdateStatus(gcmsg.coinfo, g_marketDefine.deal_status_pay)
	local sqlCase = "update dy_customer_order set status=3 where id="..cgmsg.dealid
	mysqlItem:execute(sqlCase)
	
	--插到主循环列表中
	CustomerOrderModel.AddCustomerOrderList(gcmsg.coinfo)
	
	--向前端更新用户冻结金币
	UserInfoModel.SendErcUsdtLockAmount(payeeInfo)
	
	--向客户更新用户大厅的待处理列表
	local userList = {payeeInfo.userid, payerInfo.userid} 
	NoticeServices.NoticeHangSellOut(userList)
	
	local voItem = VendorOrderModel.GetVendorOrderInfo(gcmsg.coinfo.vendororderid)
	if voItem ~= nil then
		if gcmsg.coinfo.iswait == 1 then
			gcmsg.coinfo.iswait = 0
			CustomerOrderModel.SetCustomerOrderInfo(gcmsg.coinfo)	
			CustomerOrderModel.delCustomerWithdrawOrderList(gcmsg.coinfo.dealid)
			local sqlCase = "update dy_customer_order set is_wait="..gcmsg.coinfo.iswait.." where id="..gcmsg.coinfo.dealid
			mysqlItem:execute(sqlCase)
		else
			voItem.unsoldordernum = voItem.unsoldordernum + 1
			voItem.unsoldorderamount = tostring(tonumber(voItem.unsoldorderamount) + tonumber(gcmsg.coinfo.amount))
			voItem.unsoldordermoney = tostring((tonumber(voItem.unsoldordermoney) or 0) + tonumber(gcmsg.coinfo.money))
			VendorOrderModel.SetVendorOrderInfo( voItem.hangid, voItem )
			local sqlCase = "update dy_vendor_order set unsold_order_num=unsold_order_num+1, unsold_order_amount=unsold_order_amount+"
				..tonumber(gcmsg.coinfo.amount)..", unsold_order_money=unsold_order_money+"..tonumber(gcmsg.coinfo.money).." where id="..voItem.hangid
			mysqlItem:execute(sqlCase)
		end
		
		local payInfo = luajson.decode(gcmsg.coinfo.paytypelist)
		--统计支付方式的累计收款量
		UserInfoModel.SetDayLimit(payInfo.id, gcmsg.coinfo.money)
	end
	
	--给前端转化，前端只认对于这个这个用户说是买还是卖
	gcmsg.coinfo.type = CoinInfoService.orderTypeConversion(gcmsg.coinfo.type, gcmsg.coinfo.customeruserid, gcmsg.coinfo.vendoruserid, cgmsg.userid)
	
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
end