module("Cancel", package.seeall)

--取消交易
function execute(packetID, operateID, buffer)

	local cgmsg = msg_order_pb.cgcancel()
	local gcmsg = msg_order_pb.gccancel()
	
	cgmsg:ParseFromString(buffer)
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()		
	end
	
	ThreadManager.DealLock(cgmsg.dealid)
	CustomerOrderModel.GetCustomerOrderInfo(cgmsg.dealid,gcmsg.coinfo)
	if gcmsg.coinfo == nil then
		gcmsg.coinfo.dealid = 0
		ThreadManager.DealUnLock(cgmsg.dealid)
		gcmsg.result,gcmsg.msg = 1, "该订单不存在"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--取消订单
	local ret, msg = VendorOrderService.CanelOrder(uInfo, gcmsg.coinfo, false)
	if ret ~= nil then
		ThreadManager.DealUnLock(cgmsg.dealid)
		gcmsg.result, gcmsg.msg = ret, msg
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	ThreadManager.DealUnLock(cgmsg.dealid)
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
end