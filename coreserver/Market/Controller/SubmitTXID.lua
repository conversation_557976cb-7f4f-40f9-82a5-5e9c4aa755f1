module("SubmitTXID", package.seeall)

--提交TXID

function execute(packetID, operateID, buffer)

	local cgmsg = msg_order2_pb.cgsubmittxid()
	local gcmsg = msg_order2_pb.gcsubmittxid()
	
	cgmsg:ParseFromString(buffer)
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	if cgmsg.txid == "" then
		gcmsg.result,gcmsg.msg = 1, "TXID不能为空"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	local sqlCase = "select * from dy_trusteeship_wallet_order where user_txid='"..cgmsg.txid.."'"
	mysqlItem:executeQuery(sqlCase) 
	local sqlData = mysqlItem:fetch({})
	if sqlData ~= nil then
		gcmsg.result,gcmsg.msg = 1, "该TXID已上传过了"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	local sqlCase = "select amount, `create`,user_txid, status from dy_trusteeship_wallet_order where order_id='"..cgmsg.orderid.."' and userid="..uInfo.userid
	mysqlItem:executeQuery(sqlCase) 
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		gcmsg.result,gcmsg.msg = 1, "订单不存在"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	if tonumber(sqlData[4]) ~= 0 then
		gcmsg.result,gcmsg.msg = 1, "该状态不能上传TXID"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	if sqlData[3] ~= "" then
		gcmsg.result,gcmsg.msg = 1, "已经上传过TXID，请不要重复上传"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	local amount = tonumber(sqlData[1])
	local createTime = sqlData[2]
		
	
	--查找是否已经有链的记录
	local sqlCase = "select amount, tx_time from dy_block_chain_trans where tx_id='"..cgmsg.txid.."'".." and hosting_adds=1 and hosting_status=1 and (tx_status=0 or tx_status=2)"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	local status = 0
	local msg = "TXID不存在"
	local actualAmount = 0
	if sqlData ~= nil then
		
		if tonumber(sqlData[2]) <= TimeUtils.GetTime(createTime) then
			status = 0
			msg = "时间不符"	
		else
			if string.format("%.4f",sqlData[1]) == string.format("%.4f",tostring(amount)) then
				status = 1
				msg = "完全匹配"	
				actualAmount = tonumber(sqlData[1])
			else
				status = 0
				msg = "金额不符"	
			end
		end
		
	end

	local sqlCase = "update dy_trusteeship_wallet_order set user_txid='"..cgmsg.txid.."', remarks='"..msg.."', status="..status..", actual_amount="..actualAmount.." where order_id='"..cgmsg.orderid.."' and userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
	if status == 1 then
	
		local price = CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.deal_buy, g_marketDefine.currency_type.USDT,true, uInfo.userid)
		local money = price * actualAmount
		local sqlCase = "update dy_block_chain_trans set hosting_status=2,related_order='"..cgmsg.orderid.."', userid="..uInfo.userid
			..", money="..money..", price="..price.." where tx_id='"..cgmsg.txid.."'"
		mysqlItem:executeQuery(sqlCase)
		
		--加钱
		UserInfoModel.AddErcUsdtAmount( uInfo, actualAmount,cgmsg.orderid,0,"托管钱包充值，增加资金", g_humanDefine.fund_details.type_recharge_currency,money)
		UserInfoModel.SendErcUsdtAmount(uInfo)
		LogDispatch.userRecharge(uInfo.userid, actualAmount, 0,actualAmount, true)
		
		if uInfo.dealcointype == 1 then
			--结算货币类型如果是法币把法币账户也结算一下
			UserInfoModel.AddErcFCAmount(uInfo, money, cgmsg.orderid, 0, "托管钱包充值，增加资金", g_humanDefine.fund_details.type_recharge_currency)
		end
	end
	
	local sqlCase = "select * from dy_trusteeship_wallet_order where order_id='"..cgmsg.orderid.."' and userid="..uInfo.userid
	mysqlItem:executeQuery(sqlCase) 
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		gcmsg.result,gcmsg.msg = 1,"查找不到该订单"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	gcmsg.result = 0
	gcmsg.info.order_id = sqlData[2]
	gcmsg.info.channel = sqlData[3]
	gcmsg.info.userid = tonumber(sqlData[4])
	gcmsg.info.nickname = sqlData[5]
	gcmsg.info.currency_type = sqlData[6]
	gcmsg.info.amount = sqlData[7]
	gcmsg.info.actualamount = sqlData[8]
	gcmsg.info.trusteeship = sqlData[9]
	gcmsg.info.usertxid = sqlData[10]
	gcmsg.info.create = sqlData[11]
	gcmsg.info.remarks = sqlData[12]
	gcmsg.info.status = tonumber(sqlData[13])
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
	
end