module("SearchDealRecdList", package.seeall)

--搜索交易订单
function execute(packetID, operateID, buffer)

	local cgmsg = msg_order2_pb.cgsearchdealrecdlist()
	local gcmsg = msg_order2_pb.gcsearchdealrecdlist()
	
	cgmsg:ParseFromString(buffer)

	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	local startPos = (cgmsg.pagenum - 1) * cgmsg.pagesize
	local sqlCase = "select * from dy_customer_order where (customer_user_id="..uInfo.userid.." or vendor_user_id="..uInfo.userid..") and "
	
	
	if cgmsg.searchtype == 1 then
		--流水号
		sqlCase = sqlCase.."id='"..cgmsg.keyword.."'"
	elseif cgmsg.searchtype == 2 then
		--收款人
		sqlCase = sqlCase.."payee_name='"..cgmsg.keyword.."'"
	elseif cgmsg.searchtype == 3 then
		--收款账号
		sqlCase = sqlCase.."payee_account='"..cgmsg.keyword.."'"
	elseif cgmsg.searchtype == 4 then
		--商户订单号
		sqlCase = sqlCase.."merchant_order_id='"..cgmsg.keyword.."'"
	end
	sqlCase = sqlCase.." order by create_time desc limit "
						..startPos..", "..cgmsg.pagesize
	mysqlItem:executeQuery(sqlCase)
	print(sqlCase)
	while true do 
		local sqlDate = mysqlItem:fetch({})
		if sqlDate == nil then
			break
		end
		local addgInfo = gcmsg.infolist:add()
		addgInfo.dealid = tonumber(sqlDate[1])
		addgInfo.type = tonumber(sqlDate[2])
		addgInfo.vendororderid = tonumber(sqlDate[3])
		addgInfo.customeruserid = tonumber(sqlDate[4])
		addgInfo.vendoruserid = tonumber(sqlDate[5])
		addgInfo.price = sqlDate[6]
		addgInfo.amount = sqlDate[7]
		addgInfo.money = sqlDate[8]
		addgInfo.status = tonumber(sqlDate[9])
		addgInfo.merchantorderid = sqlDate[10]
		addgInfo.feerate = sqlDate[11]
		addgInfo.fee = sqlDate[12]
		if sqlDate[13] ~= nil and sqlDate[13] ~= "" then
			local proofUrlList = string.split(sqlDate[13], ",")
			for k,v in ipairs(proofUrlList) do
				addgInfo.proofurl:append(v)
			end
		end
		addgInfo.paytypelist = sqlDate[14]
		addgInfo.payidlist = sqlDate[15]
		addgInfo.createtime = sqlDate[16]
		addgInfo.paytime = sqlDate[18]
		addgInfo.passtime = sqlDate[19]
		addgInfo.channel = sqlDate[20]
		addgInfo.publicprice = sqlDate[21]
		addgInfo.fromtype = tonumber(sqlDate[22])
		addgInfo.notifyurl = sqlDate[23]
		addgInfo.body = sqlDate[24]
		addgInfo.sellfeerate = sqlDate[25]
		addgInfo.sellfee = sqlDate[26]
		addgInfo.canceltime = sqlDate[27]
		addgInfo.updatetime = sqlDate[17]
		addgInfo.getamount = sqlDate[42]
		addgInfo.dealtype = tonumber(sqlDate[32])
		addgInfo.income = sqlDate[44]
		addgInfo.withdrawtype = tonumber(sqlDate[47])
		addgInfo.aftermoney = sqlDate[33]
		addgInfo.feemoney = sqlDate[50]
		
		--给前端转化，前端只认对于这个这个用户说是买还是卖
		addgInfo.type = CoinInfoService.orderTypeConversion(addgInfo.type, addgInfo.customeruserid, addgInfo.vendoruserid, cgmsg.userid)
	end
	
	for k,v in ipairs(gcmsg.infolist) do 
		local uInfo = UserInfoModel.GetUserInfo(v.customeruserid)
		if uInfo ~= nil then
			v.customerusernickname = uInfo.nickname
		end
		local uInfo = UserInfoModel.GetUserInfo(v.vendoruserid)
		if uInfo ~= nil then
			v.vendorusernickname = uInfo.nickname
		end
	end

	gcmsg.searchtype = cgmsg.searchtype
	gcmsg.keyword = cgmsg.keyword
	gcmsg.pagenum = cgmsg.pagenum
	gcmsg.pagesize = cgmsg.pagesize
	gcmsg.result = 0
	
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
	
end