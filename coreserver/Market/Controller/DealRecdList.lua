module("DealRecdList", package.seeall)

--交易记录
function execute(packetID, operateID, buffer)

	local cgmsg = msg_order2_pb.cgdealrecdlist()
	local gcmsg = msg_order2_pb.gedealrecdlist()
	
	cgmsg:ParseFromString(buffer)
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["player_not_exist"][0],ReturnCode["task_unexist"][1]
		return cgmsg.userid, gcmsg.result, gcmsg:ByteSize(), gcmsg:SerializeToString()		
	end
	
	local startPos = (cgmsg.pagenum - 1) * cgmsg.pagesize
	local sqlCase = ""
	
	if cgmsg.type == 2 then
		--全部
		if cgmsg.status ~= 0 then
			if cgmsg.status == 8 then
				sqlCase = "select * from dy_customer_order where (customer_user_id="..uInfo.userid.." or vendor_user_id="
						..uInfo.userid..") and (status=8 or status=2 or status=16) order by create_time desc limit "..startPos..", "..cgmsg.pagesize
			elseif cgmsg.status == 2 then
				sqlCase = "select * from dy_customer_order where (customer_user_id="..uInfo.userid.." or vendor_user_id="
						..uInfo.userid..") and (status=1 or status=3) order by create_time desc limit "..startPos..", "..cgmsg.pagesize
			elseif cgmsg.status == 7 then
				sqlCase = "select * from dy_customer_order where (customer_user_id="..uInfo.userid.." or vendor_user_id="
						..uInfo.userid..") and (status=7 or status=11) order by create_time desc limit "..startPos..", "..cgmsg.pagesize
			else	
				sqlCase = "select * from dy_customer_order where (customer_user_id="..uInfo.userid.." or vendor_user_id="
						..uInfo.userid..") and status="..cgmsg.status.." order by create_time desc limit "..startPos..", "..cgmsg.pagesize
			end
		else
			sqlCase = "select * from dy_customer_order where (customer_user_id="..uInfo.userid.." or vendor_user_id="
					  ..uInfo.userid..") order by create_time desc limit "..startPos..", "..cgmsg.pagesize
		end
	else	
		--0-买 1-卖
		local type1 = 0
		local type2 = 1
		if cgmsg.type == 1 then
			type1 = 1
			type2 = 0
		end
		if cgmsg.status ~= 0 then
			if cgmsg.status == 8 then
				sqlCase = "select * from dy_customer_order where ((customer_user_id="..uInfo.userid.." and type="..type1
						..") or (vendor_user_id="..uInfo.userid.." and type="..type2..")) and (status=8 or status=2 or status=16) order by create_time desc limit "..startPos..", "..cgmsg.pagesize
			elseif cgmsg.status == 2 then
				sqlCase = "select * from dy_customer_order where ((customer_user_id="..uInfo.userid.." and type="..type1
						..") or (vendor_user_id="..uInfo.userid.." and type="..type2..")) and (status=1 or status=3) order by create_time desc limit "..startPos..", "..cgmsg.pagesize
			elseif cgmsg.status == 7 then
				sqlCase = "select * from dy_customer_order where ((customer_user_id="..uInfo.userid.." and type="..type1
						..") or (vendor_user_id="..uInfo.userid.." and type="..type2..")) and (status=7 or status=11) order by create_time desc limit "..startPos..", "..cgmsg.pagesize
			else
				sqlCase = "select * from dy_customer_order where ((customer_user_id="..uInfo.userid.." and type="..type1
						..") or (vendor_user_id="..uInfo.userid.." and type="..type2..")) and status="..cgmsg.status
						.." order by create_time desc limit "..startPos..", "..cgmsg.pagesize
			end
			
		else
			sqlCase = "select * from dy_customer_order where (customer_user_id="..uInfo.userid.." and type="..type1
						..") or (vendor_user_id="..uInfo.userid.." and type="..type2..") order by create_time desc limit "
						..startPos..", "..cgmsg.pagesize
		end
	end
	
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlDate = mysqlItem:fetch({})
		if sqlDate == nil then
			break
		end
		local addgInfo = gcmsg.infolist:add()
		addgInfo.dealid = tonumber(sqlDate[1])
		addgInfo.type = tonumber(sqlDate[2])
		addgInfo.vendororderid = tonumber(sqlDate[3])
		addgInfo.customeruserid = tonumber(sqlDate[4])
		addgInfo.vendoruserid = tonumber(sqlDate[5])
		addgInfo.price = sqlDate[6]
		addgInfo.amount = sqlDate[7]
		addgInfo.money = sqlDate[8]
		addgInfo.status = tonumber(sqlDate[9])
		addgInfo.merchantorderid = sqlDate[10]
		addgInfo.feerate = sqlDate[11]
		addgInfo.fee = sqlDate[12]
		if sqlDate[13] ~= nil and sqlDate[13] ~= "" then
			local proofUrlList = string.split(sqlDate[13], ",")
			for k,v in ipairs(proofUrlList) do
				addgInfo.proofurl:append(v)
			end
		end
		addgInfo.paytypelist = sqlDate[14]
		addgInfo.payidlist = sqlDate[15]
		addgInfo.createtime = sqlDate[16]
		addgInfo.paytime = sqlDate[18]
		addgInfo.passtime = sqlDate[19]
		addgInfo.channel = sqlDate[20]
		addgInfo.publicprice = sqlDate[21]
		addgInfo.fromtype = tonumber(sqlDate[22])
		addgInfo.notifyurl = sqlDate[23]
		addgInfo.body = sqlDate[24]
		addgInfo.sellfeerate = sqlDate[25]
		addgInfo.sellfee = sqlDate[26]
		addgInfo.canceltime = sqlDate[27]
		addgInfo.updatetime = sqlDate[17]
		addgInfo.getamount = sqlDate[42]
		addgInfo.dealtype = tonumber(sqlDate[32])
		addgInfo.income = sqlDate[44]
		addgInfo.withdrawtype = tonumber(sqlDate[47])
		addgInfo.aftermoney = sqlDate[33]
		addgInfo.feemoney = sqlDate[50]
		
		--给前端转化，前端只认对于这个这个用户说是买还是卖
		addgInfo.type = CoinInfoService.orderTypeConversion(addgInfo.type, addgInfo.customeruserid, addgInfo.vendoruserid, cgmsg.userid)
	end
	
	for k,v in ipairs(gcmsg.infolist) do 
		local uInfo = UserInfoModel.GetUserInfo(v.customeruserid)
		if uInfo ~= nil then
			v.customerusernickname = uInfo.nickname
		end
		local uInfo = UserInfoModel.GetUserInfo(v.vendoruserid)
		if uInfo ~= nil then
			v.vendorusernickname = uInfo.nickname
		end
	end
	
	local sqlCase = "select * from log_user where userid="..cgmsg.userid
	mysqlItem:executeQuery(sqlCase)
	local sqlDate = mysqlItem:fetch({})
	if sqlDate ~= nil then
		gcmsg.ordercount = tonumber(sqlDate[5]) + tonumber(sqlDate[6])
		gcmsg.finishordercount = tonumber(sqlDate[7]) + tonumber(sqlDate[8])
	end

	gcmsg.type = cgmsg.type
	gcmsg.paytype = cgmsg.paytype
	gcmsg.pagenum = cgmsg.pagenum
	gcmsg.pagesize = cgmsg.pagesize
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
end