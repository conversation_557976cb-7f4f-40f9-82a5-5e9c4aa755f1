module("CancelLeftCount", package.seeall)

--获取剩余的取消交易的次数
--理论上，每天只能取消3次


function execute(packetID, operateID, buffer)

	local cgmsg = msg_order_pb.cgcancelleftcount()
	local gcmsg = msg_order_pb.gccancelleftcount()
	
	cgmsg:ParseFromString(buffer)
	
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, gcmsg.result, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	gcmsg.leftcount = CustomerOrderModel.GetDealLeftCount(cgmsg.userid)
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
end