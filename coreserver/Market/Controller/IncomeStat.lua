module("IncomeStat", package.seeall)


--收入统计(币商)
function execute(packetID, operateID, buffer)

	local cgmsg = msg_order2_pb.cgincomestat()
	local gcmsg = msg_order2_pb.gcincomestat()
	
	cgmsg:ParseFromString(buffer)
	
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, gcmsg.result, gcmsg:ByteSize(), gcmsg:SerializeToString()		
	end

	local startDay = TimeUtils.GetDayString(cgmsg.starttime)
	local endDay = TimeUtils.GetDayString(cgmsg.endtime)
	
	local sqlCase = "select * from log_user_daily where userid="..uInfo.userid.." and dateid>='"..startDay.."' and dateid<='"..endDay.."'"
	mysqlItem:executeQuery(sqlCase)
	
	buycount = 0
	topupcount = 0
	transferintocount = 0
	rewardintocount = 0
	
	while true do
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		
		buycount = buycount + tonumber(sqlData[14])
		topupcount = topupcount + tonumber(sqlData[16])
		transferintocount = transferintocount + tonumber(sqlData[18])
		rewardintocount = rewardintocount + tonumber(sqlData[20])
	end
	
	gcmsg.total =  string.format("%.6f", tostring(buycount + topupcount + transferintocount + rewardintocount))
	gcmsg.buy =  string.format("%.6f", tostring(buycount))
	gcmsg.topup = string.format("%.6f", tostring(topupcount))
	gcmsg.transferinto = string.format("%.6f", tostring(transferintocount))
	gcmsg.reward = string.format("%.6f", tostring(rewardintocount))
	
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
end