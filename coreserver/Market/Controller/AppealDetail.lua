module("AppealDetail", package.seeall)


function execute(packetID, operateID, buffer)

	local cgmsg = msg_order_pb.cgorderlist()
	local gcmsg = msg_order_pb.gcorderlist()
	
	cgmsg:ParseFromString(buffer)
	
	local uInfo = UserModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["player_not_exist"][0],ReturnCode["task_unexist"][1]
		return cgmsg.userid, gcmsg.result, gcmsg:ByteSize(), gcmsg:SerializeToString()		
	end

	local voItem = VendorOrderModel.GetVendorOrderInfo(cgmsg.hangid)
	if voItem == nil then
		--挂单的信息不存在
		gcmsg.result, gcmsg.msg = ReturnCode["deal_order_not_exist"][0],ReturnCode["deal_order_not_exist"][1]
		return cgmsg.userid, gcmsg.result, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end	
	
	--申诉的详情

	

	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
end