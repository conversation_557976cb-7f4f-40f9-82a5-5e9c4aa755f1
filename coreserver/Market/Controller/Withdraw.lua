module("Withdraw", package.seeall)

--提币/转账

function execute(packetID, operateID, buffer)

	local cgmsg = msg_order2_pb.cgwithdraw()
	local gcmsg = msg_order2_pb.gcwithdraw()
	
	cgmsg:ParseFromString(buffer)
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	local touInfo = nil
	if cgmsg.ordertype == 2 then
		touInfo = UserInfoModel.GetUserInfo(cgmsg.touserid)
		if touInfo == nil then
			gcmsg.result,gcmsg.msg = 1,"目标用户不存在"
			return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
		end
	end
	
	--检查用户是否锁定
	if uInfo.islock == 1 then
		gcmsg.result,gcmsg.msg = 1, "你已经被锁定无法进行该操作"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--检查资金密码
	local ret, msg = UserInfoModel.CheckFundPassword(uInfo, cgmsg.fundpwd)
	if ret ~= 0 then
		gcmsg.result, gcmsg.msg = ret,msg
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end

	local sysPrice = nil 
	if cgmsg.ordertype == 1 then
		sysPrice = CoinInfoService.withdrawCoinPrice(uInfo.channel, g_marketDefine.currency_type.USDT, true, uInfo.userid)
	else
		sysPrice = CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.deal_sell, g_marketDefine.currency_type.USDT, true, uInfo.userid)
	end
	
	local wInfo = {}
	wInfo.toaddress = cgmsg.toaddress
	wInfo.currencytype = cgmsg.currencytype
	wInfo.currencyamount = cgmsg.currencyamount
	wInfo.subject = cgmsg.subject
	wInfo.version = cgmsg.version
	wInfo.notifyurl = cgmsg.notifyurl
	wInfo.body = cgmsg.body
	wInfo.outtradeno = cgmsg.outtradeno
	wInfo.specifictype = cgmsg.ordertype == 1 and 2 or 4
	wInfo.addrtype = cgmsg.addrtype
	wInfo.price = sysPrice
	wInfo.money = cgmsg.currencyamount * wInfo.price
	wInfo.wallettype = 0
	
	
	local ret, msg = VendorOrderService.WithdrawCurrency(
	    uInfo,
	    wInfo,
	    cgmsg.ordertype,
	    touInfo,
	    true,
	    uInfo.userid,
	    uInfo.nickname,
	    ""
	)
	if ret ~= 0 then
		gcmsg.result, gcmsg.msg = ret, msg
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	gcmsg.result = 0
	return cgmsg.userid, 0 , gcmsg:ByteSize(), gcmsg:SerializeToString()
	
end