module("MatchOrderWoker", package.seeall)

function work(buffer)

	VendorOrderModel.setMatchOrderState()
	local sqlCase = "select id from dy_vendor_order where type=1 and enable_status=1 and auto_switch=0 and is_external=1 and is_assigned=0"
	mysqlItem:executeQuery(sqlCase)
	
	local vList = {}
	while true do 
		local sqlData = mysqlItem:fetch()
		if sqlData == nil then
			break
		end
		
		table.insert(vList, tonumber(sqlData))
	end
	for _,hangid in ipairs(vList) do 
		ThreadManager.OrderLock(hangid)
		local hInfo = VendorOrderModel.GetVendorOrderInfo(hangid)
		if hInfo.enablestatus == 1 and hInfo.isassigned == 0 then
			local pInfo = UserInfoModel.GetUserInfo(hInfo.externalorderuserid)
			if pInfo ~= nil then 
				local parameter = {}
				parameter['price'] = hInfo.price
				parameter['amount'] = hInfo.amount
				parameter['money'] = hInfo.predictmoney
				parameter['coinName'] = "USDT"
				local mInfo = UserInfoModel.GetUserInfo(hInfo.userid)
				parameter['merchantName'] = mInfo.nickname
				parameter['merchantId'] = mInfo.userid
				parameter['paymentName'] = hInfo.payeeaccount
				parameter['paymentNumber'] = hInfo.payeename
				local feeType = hInfo.withdrawtype == 0 and g_humanDefine.sell_fee_rate or g_humanDefine.behalf_sell_fee_rate
				local userRate = UserInfoModel.getUserFeeRate(mInfo.userid, g_humanDefine.user_payList.bank, feeType)
				parameter['fee'] = hInfo.amount * userRate
				parameter['merchantUserId'] = mInfo.userid
				parameter['apiOrderNo'] = hInfo.hangid
				parameter['paymentBankName'] = hInfo.payeebank
				local info = "price="..parameter['price'].."&amount="..parameter['amount'].."&money="..parameter['money'].."&coinName="..parameter['coinName']
							.."&merchantName="..parameter['merchantName'].."&merchantId="..parameter['merchantId'].."&paymentName="..parameter['paymentName']
							.."&paymentNumber="..parameter['paymentNumber'].."&fee="..parameter['fee'].."&merchantUserId="..parameter['merchantUserId']
							.."&apiOrderNo="..parameter['apiOrderNo'].."&paymentBankName="..parameter['paymentBankName']
				local httpUrl = g_withdraw_http.."?"..info
				local getData = HttpGet(httpUrl)
				if getData ~= "" and getData ~= nil then
					local s1 = string.sub(getData, 0, 1)
					if getData ~= nil and (s1 == '[' or s1 == '{') then
						getData = luajson.decode(getData)  
						if getData.code == 1 then
							local coinInfo = st_order_pb.customerorderinfo()
							local tradeID = getData.data.tradeId or ""
							local ret, msg = VendorOrderService.DealOrder(pInfo,coinInfo, hInfo,g_marketDefine.deal_buy
								,tonumber(parameter.amount),tonumber(parameter.money),nil,tonumber(parameter.price),g_humanDefine.user_payList.bank,tradeID)
							if ret == nil then
								hInfo.isassigned = 1
								VendorOrderModel.SetVendorOrderInfo(hInfo.hangid, hInfo)
								local sqlCase = "update dy_vendor_order set is_assigned=1 where id="..hangid
								mysqlItem:execute(sqlCase)
							end
						end
					end
				end
			end
		end
		ThreadManager.OrderUnLock(hangid)
	end
	VendorOrderModel.delMatchOrderState()
	
end