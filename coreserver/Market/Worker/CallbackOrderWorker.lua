module("CallbackOrderWorker", package.seeall)

function work(buffer)

	local dInfo = CustomerOrderModel.GetCustomerOrderInfo(buffer)
	if dInfo ~= nil then
		if dInfo.isexternal == 0 and dInfo.notifyurl ~= "" then
			local info = "money="..dInfo.money.."&merchantorderid="..dInfo.merchantorderid.."&orderid="..dInfo.dealid.."&status="..dInfo.status
			local httpUrl = dInfo.notifyurl.."?"..info
			local getData = HttpGet(httpUrl)
			if getData ~= "" and getData ~= nil then
				local s1 = string.sub(getData, 0, 1)
				if getData ~= nil and (s1 == '[' or s1 == '{') then
					getData = luajson.decode(getData)  
					if getData ~= "" then
						if getData.code == 200 then
							--到这里订单完成了， 就充缓存中删除吧
							CustomerOrderModel.delCustomerOrderInfo(buffer)
							local sqlCase = "update dy_customer_order set notify_amount=notify_amount+1 where id="..buffer
							mysqlItem:execute(sqlCase)
						end
					end
				end
			end
		elseif dInfo.isexternal == 1 and dInfo.dealtype == 300 then
			local parameter = {}
			parameter["apiOrderNo"] = dInfo.vendororderid
			parameter["tradeId"] = dInfo.tradeid
			local info = "apiOrderNo="..parameter["apiOrderNo"].."&tradeId="..parameter["tradeId"]
			local httpUrl = g_confirm_http.."?"..info
			local getData = HttpGet(httpUrl)
			if getData ~= "" and getData ~= nil then
				local s1 = string.sub(getData, 0, 1)
				if getData ~= nil and (s1 == '[' or s1 == '{') then
					getData = luajson.decode(getData)  
					if getData ~= "" then
						if getData.code == 1 then
							--到这里订单完成了， 就充缓存中删除吧
							CustomerOrderModel.delCustomerOrderInfo(buffer)
							local sqlCase = "update dy_customer_order set notify_amount=notify_amount+1 where id="..buffer
							mysqlItem:execute(sqlCase)
						end
					end
				end
			end
		end
		
	end

end