
VendorOrderModel = {}


VendorOrderModel.redis_index = "redis_vendororder"

VendorOrderModel.vendororder_check_list = "vendororder_check_list"   --这个是存放每一秒需要循环的ID的列表

VendorOrderModel.vendororder_list = "vendororder_list_"          		--订单列表       
VendorOrderModel.vendororder_user_list = "vendororder_user_list"          		--订单列表       
VendorOrderModel.vendororder_info = "vendororder_info"                 	--订单信息
VendorOrderModel.userinfo_account = "userinfo_account_list"  --保存用户信息的，以account为key，存管的json结构串，包括：密码，userID


VendorOrderModel.hang_buy_amount_list = "hang_buy_amount_by_id"   -- 根据hangid去做的
VendorOrderModel.hang_buy_userid_list = "hang_buy_userid_by_id"   -- 根据hangid去做的
VendorOrderModel.hang_sell_amount_list = "hang_sell_amount_by_id"   -- 根据hangid去做的
VendorOrderModel.hang_sell_userid_list = "hang_sell_userid_by_id"   -- 根据hangid去做的

VendorOrderModel.hang_user_hangid_list = "hang_user_hangid_list"   --每个用户在挂单的时候，
VendorOrderModel.hang_match_order_state = "hang_match_order_state" 


function VendorOrderModel.addVendorOrder(handID)
	
	redisItem:lpush( VendorOrderModel.vendororder_list, handID, VendorOrderModel.redis_index )
	
end

function VendorOrderModel.GetVendorOrderList(startPos, endPos)
	
	
	--if true == redisItem:exists( VendorOrderModel.vendororder_list, VendorOrderModel.redis_index) then
	--	return redisItem:lrange( VendorOrderModel.vendororder_list, startPos, endPos, VendorOrderModel.redis_index )
	--end
	
	VendorOrderModel.LoadVendorOrderList()
	
	return redisItem:lrange( VendorOrderModel.vendororder_list, startPos, endPos, VendorOrderModel.redis_index )
end

function VendorOrderModel.LoadVendorOrderList()
	
	redisItem:del( VendorOrderModel.vendororder_list, VendorOrderModel.redis_index )
	local sqlCase = "select id from dy_vendor_order where enable_status=1 and auto_switch=0 order by id desc"
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlData = mysqlItem:fetch()
		if sqlData == nil then
			break
		end
		
		redisItem:lpush( VendorOrderModel.vendororder_list, tonumber(sqlData), VendorOrderModel.redis_index )
	end
end

function VendorOrderModel.addUserVendorOrder(userid, ordertype, handID)
	
	redisItem:lpush( VendorOrderModel.vendororder_user_list..userid.."_"..ordertype, handID, VendorOrderModel.redis_index )
	
end

function VendorOrderModel.GetUserVendorOrderList(userid,ordertype, startPos, endPos)
	
	--[[if true == redisItem:exists( VendorOrderModel.vendororder_user_list..userid.."_"..ordertype, VendorOrderModel.redis_index) then
		return redisItem:lrange( VendorOrderModel.vendororder_user_list..userid.."_"..ordertype, startPos, endPos, VendorOrderModel.redis_index )
	end]]
	
	return VendorOrderModel.LoadUserVendorOrderList(userid,ordertype)
	
	--return redisItem:lrange( VendorOrderModel.vendororder_user_list..userid.."_"..ordertype, startPos, endPos, VendorOrderModel.redis_index )
end

function VendorOrderModel.LoadUserVendorOrderList(userid,ordertype)
	
	local vList = {}
	local sqlCase = "select id from dy_vendor_order where type="..ordertype.." and userid = '"..userid
	.."' and enable_status=1 order by id desc"
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlData = mysqlItem:fetch()
		if sqlData == nil then
			break
		end
		table.insert(vList, tonumber(sqlData))
		--redisItem:lpush( VendorOrderModel.vendororder_user_list..userid.."_"..ordertype, tonumber(sqlData), VendorOrderModel.redis_index )
	end
	return vList
end


function VendorOrderModel.GetVendorOrderInfo( handID, hInfo )

	local strGet = redisItem:hget(VendorOrderModel.vendororder_info, handID, VendorOrderModel.redis_index)
	if strGet then

		if hInfo == nil then
			hInfo = st_order_pb.vendororderinfo()
		end
		hInfo:ParseFromString(strGet)
		return hInfo
	end

	return VendorOrderModel.LoadVendorOrderInfo(handID,hInfo)
end

function VendorOrderModel.LoadVendorOrderInfo(handID,hInfo)
	
	local sqlCase = "select * from dy_vendor_order where id="..handID
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		return nil			
	end
	if hInfo == nil then
		hInfo = st_order_pb.vendororderinfo()
	end	

	hInfo.userid = tonumber(sqlData[2])
	hInfo.hangid = tonumber(sqlData[1])
	hInfo.type = tonumber(sqlData[3])
	hInfo.pricetype = tonumber(sqlData[6])
	hInfo.price = sqlData[4]
	hInfo.amount = sqlData[5]
	hInfo.minmoney = sqlData[7]
	hInfo.maxmoney = sqlData[8]
	hInfo.autoswitch = tonumber(sqlData[10])
	hInfo.message = sqlData[11]
	hInfo.maxamount = sqlData[16]
	hInfo.enablestatus = tonumber(sqlData[9])
	hInfo.channel = sqlData[17]    
	local uInfo = UserInfoModel.GetUserInfo(hInfo.userid)
	if uInfo ~= nil then
		hInfo.nickname = uInfo.nickname
	end
	hInfo.unsoldordernum = tonumber(sqlData[18])  
	hInfo.dealordernum = tonumber(sqlData[19])  
	hInfo.unsoldorderamount = sqlData[20]
	hInfo.dealorderamount = sqlData[21] 
	hInfo.cancelnum = tonumber(sqlData[22])  
	hInfo.usertype = tonumber(sqlData[23])  
	hInfo.payeeaccount = sqlData[24]
	hInfo.payeename = sqlData[25]
	hInfo.payeebank = sqlData[26]
	hInfo.payeebandaddr = sqlData[27]
	hInfo.deal_order_free = sqlData[28]
	hInfo.free = sqlData[29]
	hInfo.addfeerate = sqlData[32]
	hInfo.createtime = sqlData[12]
	hInfo.channeldeal = tonumber(sqlData[30])
	hInfo.dealmodel = tonumber(sqlData[35])
	hInfo.predictmoney = sqlData[34]
	hInfo.resttime = sqlData[36]
	hInfo.dealmonery = sqlData[37]
	hInfo.dealcount = sqlData[38]
	hInfo.withdrawtype = tonumber(sqlData[39])
	hInfo.unsoldordermoney = sqlData[44]
	hInfo.dealordermoney = sqlData[45]
	hInfo.isexternal = tonumber(sqlData[43])
	hInfo.externalorderuserid = tonumber(sqlData[46])
	hInfo.isassigned = tonumber(sqlData[47])
	hInfo.istake = tonumber(sqlData[48])
	hInfo.wallettype = tonumber(sqlData[51])
	
	VendorOrderModel.SetVendorOrderInfo(hInfo.hangid, hInfo)
	return hInfo
end

function VendorOrderModel.SetVendorOrderInfo( handID, hInfo )

	redisItem:hset(VendorOrderModel.vendororder_info, handID, hInfo:SerializeToString(), VendorOrderModel.redis_index)

end

function VendorOrderModel.DelVendOrorderInfo(ordertype, userid, handID)
	redisItem:lrem( VendorOrderModel.vendororder_list, 0, handID, VendorOrderModel.redis_index )
	redisItem:lrem( VendorOrderModel.vendororder_user_list..userid.."_"..ordertype, 0, handID, VendorOrderModel.redis_index )
	redisItem:hdel(VendorOrderModel.vendororder_info, handID, VendorOrderModel.redis_index)
	
end


function VendorOrderModel.GetCheckIDList()
	local allList = redisItem:hgetall(VendorOrderModel.vendororder_check_list,VendorOrderModel.redis_index)
	if allList == nil then
		return {}
	else
		return allList
	end
end

function VendorOrderModel.AddCheckID(handID)
	
	redisItem:hset(VendorOrderModel.vendororder_check_list, handID, 1, VendorOrderModel.redis_index)  --
	
end

function VendorOrderModel.DelCheckID(handID)
	
	redisItem:hdel(VendorOrderModel.vendororder_check_list, handID, VendorOrderModel.redis_index)
	
end

function VendorOrderModel.GetHandIDByPrice(price, amount)
	--快捷交易中，根据密码和数量来查询对应的订单
	return 0
end

function VendorOrderModel.SetHangAmount(handid,userid,amount,type)
	
	--把这里是以ID为key
	if type == g_marketDefine.hang_buy then
		redisItem:hset(VendorOrderModel.hang_buy_amount_list, handid,amount,VendorOrderModel.redis_index)
		redisItem:hset(VendorOrderModel.hang_buy_userid_list, handid,amount,VendorOrderModel.redis_index)
	else
		redisItem:hset(VendorOrderModel.hang_sell_amount_list, handid,amount,VendorOrderModel.redis_index)
		redisItem:hset(VendorOrderModel.hang_sell_userid_list, handid,amount,VendorOrderModel.redis_index)		
	end
	
end


function VendorOrderModel.DelHangAmount(handid,type)
	--把这里是以ID为key
	--这里其实是的需要按照渠道分开的
	if type == g_marketDefine.hang_buy then
		redisItem:hdel(VendorOrderModel.hang_buy_amount_list, handid,VendorOrderModel.redis_index)
		redisItem:hdel(VendorOrderModel.hang_buy_userid_list, handid,VendorOrderModel.redis_index)
	else
		redisItem:hdel(VendorOrderModel.hang_sell_amount_list, handid,VendorOrderModel.redis_index)
		redisItem:hdel(VendorOrderModel.hang_sell_userid_list, handid,VendorOrderModel.redis_index)
	end	
end


function VendorOrderModel.CheckQuickHangid(uInfo, dealtype, amount, moneytype, channel,payType)
	
	
	--mony是人民币计价的
	local selectType = (dealtype + 1)%2

	--先把所有的渠道跟价格查出来
	local channelPrice = {}
	local sqlCase = "select channel,(buy_price+add_buy_price),(free_price+minus_sell_price) from dy_coin_info where channel!='ALL'"
	mysqlItem:executeQuery(sqlCase)
	while true do
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		local tmp = {}
		tmp[g_marketDefine.hang_buy] = tonumber(sqlData[2])
		tmp[g_marketDefine.hang_sell] = tonumber(sqlData[3])
		channelPrice[sqlData[1]] = tmp
	end
	
	--获取渠道的交易类型
	local channelDeal = 1
	local sqlCase = "select channel_deal from dy_channel_info where channel='"..uInfo.channel.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		channelDeal = tonumber(sqlData)
	end
	
	local vList = {}
	local fanxiang = (selectType == g_marketDefine.hang_buy) and ">=" or "<="
	if channelDeal == 0 then
		--公开
		for channel,v in pairs(channelPrice) do 
			local sysPrice = v[selectType]
			local sqlCase = ""
			if moneytype == g_marketDefine.amountType_RMB then
				sqlCase = "select "
									.."id,"
									.."userid,"
									.."price,"
									.."price_type "
								.."from " 
									.."dy_vendor_order "
								.."where "
									.."channel='"..channel.."' "
									.." and rest_time<'"..TimeUtils.GetTimeString().."' "
									.."and min_money<="..amount.." "
									.."and max_money>="..amount.." "
									.."and auto_switch=0 "
									.."and enable_status=1 "
									.."and type="..selectType.." "
									.."and "
											.."((price_type=1 and amount>=unsold_order_amount+deal_order_amount+"..(amount/sysPrice)..") "
											.."or (price_type=0 and  price"..fanxiang..sysPrice.." and amount>=unsold_order_amount+deal_order_amount+("..amount.."/price)))" 
									.."and userid!="..uInfo.userid.." "	
									.."and channel_deal=0"
			else
				sqlCase = "select "
									.."id,"
									.."userid,"
									.."price,"
									.."price_type "
								.."from " 
									.."dy_vendor_order "
								.."where "
									.."channel='"..channel.."' "
									.." and rest_time<'"..TimeUtils.GetTimeString().."' "
									.."and auto_switch=0 "
									.."and enable_status=1 "
									.."and type="..selectType.." "
									.."and "
											.."((price_type=1 and min_money<="..amount*sysPrice.." and max_money>="..amount*sysPrice..") "
											.."or (price_type=0 and price"..fanxiang..sysPrice.." and min_money<=price*"..amount.." and max_money>=price*"..amount..")) "
									.."and amount>=unsold_order_amount+deal_order_amount+"..amount.." "
									.."and userid!="..uInfo.userid.." "
									.."and channel_deal=0"
			end
			
			mysqlItem:executeQuery(sqlCase)
			while true do
				local sqlData = mysqlItem:fetch({})
				if sqlData == nil then
					break
				end
				local tmp = {}
				tmp["id"] = sqlData[1]
				tmp["userid"] = sqlData[2]
				if tonumber(sqlData[4]) == 0 then
					tmp["price"] = tonumber(sqlData[3])
				else
					tmp["price"] = sysPrice
				end
				tmp["channel"] = channel
					
				if moneytype == g_marketDefine.amountType_RMB then
					tmp["moneyCount"] = amount
					tmp["amountCount"] = amount / tmp["price"]
				else
					tmp["moneyCount"] = amount * tmp["price"]
					tmp["amountCount"] = amount
				end
				
				table.insert(vList, tmp)
			end
		end
		
	else
		--私有
		local sqlCase = ""
		local sysPrice = channelPrice[uInfo.channel][selectType]
		if moneytype == g_marketDefine.amountType_RMB then
			sqlCase = "select "
							.."id,"
							.."userid,"
							.."price,"
							.."price_type "
						.."from " 
							.."dy_vendor_order "
						.."where "
							.."channel='"..uInfo.channel.."' "
							.." and rest_time<'"..TimeUtils.GetTimeString().."' "
							.."and min_money<="..amount.." "
							.."and max_money>="..amount.." "
							.."and auto_switch=0 "
							.."and enable_status=1 "
							.."and type="..selectType.." "
							.."and "
								.."((price_type=1 and amount>=unsold_order_amount+deal_order_amount+"..(amount/sysPrice)..") "
							.."or (price_type=0 and  price"..fanxiang..sysPrice.." and amount>=unsold_order_amount+deal_order_amount+("..amount.."/price)))" 
							.."and userid!="..uInfo.userid.." "	
		else
			sqlCase = "select "
						.."id,"
						.."userid,"
						.."price,"
						.."price_type "
					.."from " 
						.."dy_vendor_order "
					.."where "
						.."channel='"..uInfo.channel.."' "
						.." and rest_time<'"..TimeUtils.GetTimeString().."' "
						.."and auto_switch=0 "
						.."and enable_status=1 "
						.."and type="..selectType.." "
						.."and "
							.."((price_type=1 and min_money<="..amount*sysPrice.." and max_money>="..amount*sysPrice..") "
							.."or (price_type=0 and price"..fanxiang..sysPrice.." and min_money<=price*"..amount.." and max_money>=price*"..amount..")) "
						.."and amount>=unsold_order_amount+deal_order_amount+"..amount.." "
						.."and userid!="..uInfo.userid.." "
			end
			
			mysqlItem:executeQuery(sqlCase)
			while true do
				local sqlData = mysqlItem:fetch({})
				if sqlData == nil then
					break
				end
				local tmp = {}
				tmp["id"] = sqlData[1]
				tmp["userid"] = sqlData[2]
				if tonumber(sqlData[4]) == 0 then
					tmp["price"] = tonumber(sqlData[3])
				else
					tmp["price"] = sysPrice
				end
				tmp["channel"] = channel
					
				if moneytype == g_marketDefine.amountType_RMB then
					tmp["moneyCount"] = amount
					tmp["amountCount"] = amount / tmp["price"]
				else
					tmp["moneyCount"] = amount * tmp["price"]
					tmp["amountCount"] = amount
				end
				table.insert(vList, tmp)
		end	
	end
		
	
	
	--按价格排序
	table.sort(vList, function (a,b)
		if a.price < b.price then
			return true
		else
			return false
		end
	end)
	
	--查找出这个价格还未完成的订单
	local sqlCase = ""
	if moneytype == g_marketDefine.amountType_RMB then
		sqlCase = "select payee_account,customer_user_id,vendor_user_id,type from dy_customer_order where money="..amount.." and (status=1 or status=3 or status=9)"
	else
		sqlCase = "select payee_account,customer_user_id,vendor_user_id,type from dy_customer_order where amount="..amount.." and (status=1 or status=3 or status=9)"
	end
	local orderList = {}
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		if tonumber(sqlData[4]) == 0 then
			orderList[sqlData[1]] = tonumber(sqlData[3])
		else
			orderList[sqlData[1]] = tonumber(sqlData[2])
		end
	end
	if dealtype == g_marketDefine.hang_buy then
		for k,v in ipairs(vList) do 
			local vInfo = UserInfoModel.GetUserInfo(v.userid)
			if vInfo ~= nil then
				if tonumber(vInfo.ercusdtamount) - tonumber(vInfo.ercusdtlockamount) >= v.amountCount then
					local payType = UserPayModel.GetAvailablePayType(vInfo, payType, v.amountCount, orderList) --
					if payType ~= 0 then
						return v.id, v.amountCount, v.moneyCount, v.price
					end
				end
			end
		end
	else
		if tonumber(uInfo.ercusdtamount) - tonumber(uInfo.ercusdtlockamount) >= v.amountCount then
			local payType = UserPayModel.GetAvailablePayType(uInfo, payType, v.amountCount, orderList) --
			if payType ~= 0 then
				for k,v in ipairs(vList) do 
					if tonumber(vInfo.ercusdtamount) - tonumber(vInfo.ercusdtlockamount) >= v.amountCount then
						return v.id, v.amountCount, v.moneyCount, v.price
					end
				end
			end
		end
	end
	
	return 0,0,0,0
end


function VendorOrderModel.CheckQuickHangid_shop(uInfo, amount, moneytype, channel, payType,channelInfo)

	--mony是人民币计价的
	--只查找我配置的币商
	
	local tmpList = {}
	local sqlCase = "select * from dy_user_conf where userid="..uInfo.userid.." and deal_type=0 and is_exist=1"
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		
		
		local tmp = {}
		tmp["bind_type"] = tonumber(sqlData[6])
		tmp["userid"] = tonumber(sqlData[3])
		tmp["channel"] = sqlData[5]
		table.insert(tmpList, tmp)
	end
	
	local userList = {}
	if moneytype == g_marketDefine.amountType_RMB then
		--购买
		
		for k,v in ipairs(tmpList) do 
			if v["bind_type"] == 101 then
				--渠道
				local sqlCase = "select id, userid from dy_vendor_order where type=1 and is_take=0 and min_money<="..amount.." and max_money>="..amount
				.." and auto_switch=1 and enable_status=1 and price_type=1 and type=1".." and userid!="..uInfo.userid.." and channel='"..v["channel"].."'"
				mysqlItem:executeQuery(sqlCase)
				while true do
					local sqlData = mysqlItem:fetch({})
					if sqlData == nil then
						break
					end
					
					local isEx = false 
					for k,v in ipairs(userList) do 
						if v.userID == tonumber(sqlData[2]) then 
							isEx = true
						end
					end
					if isEx == false then
						local tmp = {}
						tmp["vID"] = tonumber(sqlData[1])
						tmp["userID"] = tonumber(sqlData[2])
						table.insert(userList, tmp)
					end
				end
			elseif v["bind_type"] == 102 then
				--币商
				local pInfo = UserInfoModel.GetUserInfo(v["userid"]) 
				local sqlCase = "select id, userid from dy_vendor_order where type=1 and is_take=0 and min_money<="..amount.." and max_money>="..amount
				.." and auto_switch=1 and enable_status=1 and price_type=1 and type=1".." and userid="..v["userid"]
				mysqlItem:executeQuery(sqlCase)
				local sqlData = mysqlItem:fetch({})
				if sqlData ~= nil then
					local isEx = false 
					for k,v in ipairs(userList) do 
						if v.userID == tonumber(sqlData[2]) then 
							isEx = true
						end
					end
					if isEx == false then
						local tmp = {}
						tmp["vID"] = tonumber(sqlData[1])
						tmp["userID"] = tonumber(sqlData[2])
						table.insert(userList, tmp)
					end
				end
				
			elseif v["bind_type"] == 103 then
				--团队
				local sqlCase = "select id, userid from dy_vendor_order where type=1 and is_take=0 and min_money<="..amount.." and max_money>="..amount
				.." and auto_switch=1 and enable_status=1 and price_type=1 and type=1 and (dy_vendor_order.userid="..v["userid"]
				.." or EXISTS(select * from ag_relation where bind_userid="..v["userid"].." and userid=dy_vendor_order.userid))"
				mysqlItem:executeQuery(sqlCase)
				while true do
					local sqlData = mysqlItem:fetch({})
					if sqlData == nil then
						break
					end
					local isEx = false 
					for k,v in ipairs(userList) do 
						if v.userID == tonumber(sqlData[2]) then 
							isEx = true
						end
					end
					if isEx == false then
						local tmp = {}
						tmp["vID"] = tonumber(sqlData[1])
						tmp["userID"] = tonumber(sqlData[2])
						table.insert(userList, tmp)
					end
				end
	
			elseif v["bind_type"] == 104 then
				--小组
				local sqlCase = "select id, userid from dy_vendor_order where type=1 and is_take=0 and min_money<="..amount.." and max_money>="..amount
						.." and auto_switch=1 and enable_status=1 and price_type=1 and type=1 and EXISTS("
						.."select * from dy_group_member where group_id="..v["userid"].." and group_user_id=dy_vendor_order.userid and status=1 and group_recharge_switch=1)"
				mysqlItem:executeQuery(sqlCase)
				while true do
					local sqlData = mysqlItem:fetch({})
					if sqlData == nil then
						break
					end
					local isEx = false 
					for k,v in ipairs(userList) do 
						if v.userID == tonumber(sqlData[2]) then 
							isEx = true
						end
					end
					if isEx == false then
						local tmp = {}
						tmp["vID"] = tonumber(sqlData[1])
						tmp["userID"] = tonumber(sqlData[2])
						table.insert(userList, tmp)
					end
				end
			end
		end
	end
	
	
	
	--把所有正在进行的充值单查询出来
	local rechargeorderList = {}
	local sqlCase = "select vendor_user_id from dy_customer_order where deal_type=200 and (status=1 or status=3 or status=9)"
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlData = mysqlItem:fetch()
		if sqlData == nil then
			break
		end
		rechargeorderList[tonumber(sqlData)] = true
	end
	
	local priorityList = {}			--优先等级
	local ordinaryList = {}			--普通等级
	for k,v in ipairs(userList) do 
		local uInfo = UserInfoModel.GetUserInfo(v.userID)
		v.dealLastTime = uInfo.deallasttime
		if (tonumber(uInfo.energyvalue) or 0) > 0 and rechargeorderList[uInfo.userid] ~= true then
			table.insert(priorityList, v)
		else
			table.insert(ordinaryList, v)
		end
	end
	--按时间排序
	if #priorityList > 1 then
		table.sort(priorityList, function(a, b)
			if a.dealLastTime < b.dealLastTime then
				return true
			else
				return false
			end
		end);
	end
	--按时间排序
	if #ordinaryList > 1 then
		table.sort(ordinaryList, function(a, b)
			if a.dealLastTime < b.dealLastTime then
				return true
			else
				return false
			end
		end);
	end
	--整合
	userList = {}
	for k,v in ipairs(priorityList) do 
		table.insert(userList, v)
	end
	for k,v in ipairs(ordinaryList) do 
		table.insert(userList, v)
	end
	
	--[[
	--符合条件的先按人的最后交易时间排序
	if #userList > 0 then
		table.sort(userList, function(a, b)
			local aInfo = UserInfoModel.GetUserInfo(a.userID)
			local bInfo = UserInfoModel.GetUserInfo(b.userID)
			if TimeUtils.GetTime(aInfo.deallasttime) < TimeUtils.GetTime(bInfo.deallasttime) then
				return true
			else
				return false
			end
		end);
	end
	]]
	--查找出这个价格还未完成的订单
	local orderList = {}
	local sqlCase = "select payee_account,customer_user_id,vendor_user_id,type from dy_customer_order where money="..amount.." and (status=1 or status=3 or status=9)"
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		if tonumber(sqlData[4]) == 0 then
			orderList[sqlData[1]] = tonumber(sqlData[3])
		else
			orderList[sqlData[1]] = tonumber(sqlData[2])
		end
	end
	
	local sysPrice = CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.hang_buy, g_marketDefine.currency_type.USDT, true, uInfo.userid)
	for k,v in pairs(userList) do 
		local vInfo = UserInfoModel.GetUserInfo(v.userID)
		if g_is_uniotc == true then
			sysPrice = CoinInfoService.Erc20USDTPrice(vInfo.channel, g_marketDefine.hang_sell, g_marketDefine.currency_type.USDT, true, vInfo.userid)
		end
		local currencyCount = tonumber(string.format("%0.10f", tostring(amount / sysPrice)))
		if vInfo.isacceptorder == 0 and vInfo.isteamacceptorder == 0 then
			if channelInfo[vInfo.channel].isExternal == 1 or tonumber(vInfo.ercusdtamount) - tonumber(vInfo.ercusdtlockamount) >= currencyCount then
				local payType = UserPayModel.GetAvailablePayType(vInfo, payType, amount, orderList) --
				if channelInfo[vInfo.channel].isExternal == 1 or payType ~= 0 then
					return tonumber(v.vID), currencyCount, amount, sysPrice, payType
				end
			end
		end
	end

	return 0,0,0,0

end


function VendorOrderModel.getMatchOrderState()

	return redisItem:hget(VendorOrderModel.hang_match_order_state, 1,VendorOrderModel.redis_index)

end

function VendorOrderModel.setMatchOrderState()

	redisItem:hset(VendorOrderModel.hang_match_order_state, 1, 1, VendorOrderModel.redis_index)

end

function VendorOrderModel.delMatchOrderState()

	redisItem:hdel(VendorOrderModel.hang_match_order_state, 1, VendorOrderModel.redis_index)

end