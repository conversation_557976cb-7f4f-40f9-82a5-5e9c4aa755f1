
CustomerOrderModel = {}

CustomerOrderModel.redis_index = "redis_customer"

CustomerOrderModel.customerorder_list = "customerorder_list"				--保存所有未完成的订单ID

CustomerOrderModel.customerorder_info = "customerorder_info"				--保存订单信息

CustomerOrderModel.deal_left_amount = "deal_left_amount"    			 	--交易次数,每天只有三次的取消次数

CustomerOrderModel.callback_order = "callback_order"						--回调等待回调订单列表

CustomerOrderModel.commission_order = "commission_order"					--回调等待回调订单列表

CustomerOrderModel.order_num = "order_num"									

CustomerOrderModel.customerorder_withdraw_list = "customerorder_withdraw_list"	--提现单等待释放								

function CustomerOrderModel.AddOrderNum(dInfo)
	
	return redisItem:hincrby(CustomerOrderModel.order_num, 1, 1, CustomerOrderModel.redis_index )
end

function CustomerOrderModel.AddCustomerOrderList(dInfo)
	
	redisItem:lpush( CustomerOrderModel.customerorder_list, dInfo.dealid, CustomerOrderModel.redis_index )
end

function CustomerOrderModel.GetCustomerOrderList()
	
	if true == redisItem:exists( CustomerOrderModel.customerorder_list, CustomerOrderModel.redis_index) then
		return redisItem:lrange( CustomerOrderModel.customerorder_list, 0, -1, CustomerOrderModel.redis_index )
	end

	CustomerOrderModel.LoadCustomerOrderList()
	
	return redisItem:lrange( CustomerOrderModel.customerorder_list, 0, -1, CustomerOrderModel.redis_index )
end

function CustomerOrderModel.LoadCustomerOrderList()
	
	local sqlCase = "select id from dy_customer_order"
	.." where status="..g_marketDefine.deal_status_wait
	.. " or status="..g_marketDefine.deal_status_pay
	.. " or status="..g_marketDefine.deal_status_appeal
	.. " or status="..g_marketDefine.deal_status_matching
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlData = mysqlItem:fetch()
		if sqlData == nil then
			break
		end
		redisItem:lpush( CustomerOrderModel.customerorder_list, tonumber(sqlData), CustomerOrderModel.redis_index )
	end
end

function CustomerOrderModel.delCustomerOrderList(dealid)
	redisItem:lrem(CustomerOrderModel.customerorder_list, 0, dealid, CustomerOrderModel.redis_index)
end

function CustomerOrderModel.GetCustomerOrderInfo( dealid, dInfo )

	local strGet = redisItem:hget(CustomerOrderModel.customerorder_info, dealid, CustomerOrderModel.redis_index)
	if strGet then

		if dInfo == nil then
			dInfo = st_order_pb.customerorderinfo()
		end
		
		dInfo:ParseFromString(strGet)
		return dInfo
	end
	
	return CustomerOrderModel.LoadCustomerOrderInfo(dealid,dInfo)
end

function CustomerOrderModel.LoadCustomerOrderInfo(dealid,dInfo)
	
	local sqlCase = "select * from dy_customer_order where id="..dealid
	mysqlItem:executeQuery(sqlCase)
	
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		return nil			
	end
	if dInfo == nil then
		dInfo = st_order_pb.customerorderinfo()
	end	


	dInfo.dealid = tonumber(sqlData[1])
	dInfo.type = tonumber(sqlData[2])
	dInfo.vendororderid = tonumber(sqlData[3])
	dInfo.customeruserid = tonumber(sqlData[4])
	dInfo.vendoruserid = tonumber(sqlData[5])
	dInfo.price = sqlData[6]
	dInfo.amount = sqlData[7]
	dInfo.money = sqlData[8]
	dInfo.status = tonumber(sqlData[9])
	dInfo.merchantorderid = sqlData[10]
	dInfo.feerate = sqlData[11]
	dInfo.fee = sqlData[12]
	if sqlData[13] ~= nil and sqlData[13] ~= "" then
		local proofUrlList = string.split(sqlData[13], ",")
		for k,v in ipairs(proofUrlList) do
			dInfo.proofurl:append(v)
		end
	end
	dInfo.paytypelist = sqlData[14]
	dInfo.payidlist = sqlData[15]
	dInfo.createtime = sqlData[16]
	dInfo.paytime = sqlData[18]
	dInfo.passtime = sqlData[19]
	dInfo.channel = sqlData[20]
	dInfo.publicprice = sqlData[21]
	dInfo.fromtype = tonumber(sqlData[22])
	local cInfo	= UserInfoModel.GetUserInfo(dInfo.customeruserid)
	if cInfo ~= nil then
		dInfo.customerusernickname = cInfo.nickname
	end
	local vInfo	= UserInfoModel.GetUserInfo(dInfo.vendororderid)
	if vInfo ~= nil then
		dInfo.vendorusernickname = vInfo.nickname
	end
	dInfo.notifyurl = sqlData[23]
	dInfo.body = sqlData[24]
	dInfo.sellfeerate = sqlData[25]
	dInfo.sellfee = sqlData[26]
	dInfo.canceltime = sqlData[27]
	dInfo.updatetime = sqlData[17]
	dInfo.dealtype = tonumber(sqlData[32])
	dInfo.buyfeerate = sqlData[28]
	dInfo.buyfee = sqlData[29]
	dInfo.getamount = sqlData[42]
	dInfo.income = sqlData[44]
	dInfo.withdrawtype = tonumber(sqlData[47])
	dInfo.aftermoney = sqlData[33]
	dInfo.feemoney = sqlData[50]
	dInfo.isexternal = tonumber(sqlData[49])
	dInfo.tradeid = sqlData[51]
	dInfo.chaintype = tonumber(sqlData[57])
	dInfo.chainname = sqlData[58]
	dInfo.chainaddr = sqlData[59]
	dInfo.iswait = tonumber(sqlData[61])
	dInfo.owership = tonumber(sqlData[62])
	dInfo.wallettype = tonumber(sqlData[63])
	
	CustomerOrderModel.SetCustomerOrderInfo(dInfo)
	return dInfo
end


function CustomerOrderModel.SetCustomerOrderInfo(dInfo)
	redisItem:hset(CustomerOrderModel.customerorder_info, dInfo.dealid, dInfo:SerializeToString(), CustomerOrderModel.redis_index)
end

function CustomerOrderModel.delCustomerOrderInfo(dealid)
	redisItem:hdel(CustomerOrderModel.customerorder_info, dealid, CustomerOrderModel.redis_index)
end

function CustomerOrderModel.InserInto(dInfo, payInfo, IP)
	
	
	local num = CustomerOrderModel.AddOrderNum(dInfo) + 10000
	num = tostring(num)
	num = string.sub(num, -3, -1)
	
	local tTable = TimeUtils.GetTableTime()
	local month = tTable.month > 9 and tTable.month or "0"..tTable.month
	local day = tTable.day > 9 and tTable.day or "0"..tTable.day
	local hour = tTable.hour > 9 and tTable.hour or "0"..tTable.hour
	local min = tTable.min > 9 and tTable.min or "0"..tTable.min
	local sec = tTable.sec > 9 and tTable.sec or "0"..tTable.sec
	
	local orderID = tTable.year..month..day..hour..min..sec..num
	
	local proofUrlList = ""
	for k,v in ipairs(dInfo.proofurl) do
		proofUrlList = proofUrlList..v
		if k ~= #dInfo.proofurl then
			proofUrlList = proofUrlList..","
		end
	end
	local vendorCahnenl = ""
	local vendorPlatformID = 0
	local uInfo = UserInfoModel.GetUserInfo(dInfo.vendoruserid)
	if uInfo ~= nil then
		vendorCahnenl = uInfo.channel
		--vendorPlatformID = uInfo.platformid
	end
	
	local platformID = 0
	local cInfo = UserInfoModel.GetUserInfo(dInfo.customeruserid)
	if cInfo ~= nil then
		--platformID = cInfo.platformid
	end
	
	if dInfo.dealtype == 100 then
		vendorPlatformID = uInfo.platformid
		platformID = uInfo.platformid
	elseif dInfo.dealtype == 200 then
		if tonumber(dInfo.payidlist) ~= g_payCenterDefine.pay_id_list['bitpay'] then
			vendorPlatformID = uInfo.platformid
			platformID = uInfo.platformid
		else
			vendorPlatformID = cInfo.platformid
			platformID = cInfo.platformid
		end
	elseif dInfo.dealtype == 300 then
		vendorPlatformID = cInfo.platformid
		platformID = cInfo.platformid
	end
	
	local sqlCase = "insert into dy_customer_order(type,vendor_order_id,customer_user_id,vendor_user_id,price,amount,money,status,merchant_order_id,"
		.."fee_rate,fee,proof_url,pay_type,pay_id,channel,publiceprice,from_type,notify_url,body,sell_fee_rate,sell_fee, create_time, buy_fee_rate,"
		.."buy_fee,update_time,deal_type,user_pay_id,payee_account,payee_name,payee_bank,payee_band_addr,after_money,get_amount,income,customer_ip,"
		.."only_mark,vendor_channel,withdraw_type,fee_money,is_external,trade_id,history_amount,history_money, platform_id,vendor_platform_id,chain_type,chain_name,chain_addr,owership,wallet_type) values("
		..dInfo.type..","..dInfo.vendororderid..","..dInfo.customeruserid..","..dInfo.vendoruserid..","..dInfo.price
		..","..dInfo.amount..","..dInfo.money..","..dInfo.status..",'"..dInfo.merchantorderid.."',"..dInfo.feerate..","
		..dInfo.fee..",'"..proofUrlList.."','"..dInfo.paytypelist.."','"..dInfo.payidlist.."','"..dInfo.channel.."',"
		..dInfo.publicprice..","..dInfo.fromtype..",'"..dInfo.notifyurl.."','"..dInfo.body.."',"..dInfo.sellfeerate..","..dInfo.sellfee..",'"..dInfo.createtime.."'"
		..","..dInfo.buyfeerate..","..dInfo.buyfee..",'"..dInfo.updatetime.."',"..dInfo.dealtype..","..payInfo.id..",'"..payInfo.account
		.."','"..payInfo.payee.."','"..payInfo.bankname.."','"..payInfo.bankaddr.."',"..dInfo.aftermoney..","..dInfo.getamount..","..dInfo.income..",'"..IP.."','"..orderID
		.."','"..vendorCahnenl.."',"..dInfo.withdrawtype..","..dInfo.feemoney..","..dInfo.isexternal..",'"..dInfo.tradeid.."',"..dInfo.amount..","..dInfo.money
		..","..platformID..","..vendorPlatformID..","..dInfo.chaintype..",'"..dInfo.chainname.."','"..dInfo.chainaddr.."',"..dInfo.owership..","..dInfo.wallettype..")"
	mysqlItem:execute(sqlCase)
	
	local sqlCase = "select id from dy_customer_order where only_mark='"..orderID.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		return nil
	end
	dInfo.dealid = tonumber(sqlData)
	
	--插到主循环列表中
	CustomerOrderModel.AddCustomerOrderList(dInfo)
	--把订单信息保存到缓存中
	CustomerOrderModel.SetCustomerOrderInfo(dInfo)
	
	return dInfo
end


--由于冻结资金失败，导致该交易失败
function CustomerOrderModel.DealFailed(dInfo)
	
	dInfo.status = g_marketDefine.deal_status_failed
	local sqlCase = "update dy_customer_order set status="..dInfo.status.." where id="..dInfo.dealid
	mysqlItem:execute(sqlCase)
	CustomerOrderModel.SetCustomerOrderInfo(dInfo)
end


function CustomerOrderModel.UpdateStatus(dInfo, status)
	dInfo.status = status
	dInfo.updatetime = TimeUtils.GetTimeString()
	CustomerOrderModel.SetCustomerOrderInfo(dInfo)	
	local sqlCase = ""
	if dInfo.status == g_marketDefine.deal_status_pay then
		dInfo.paytime = TimeUtils.GetTimeString()
		local proofUrlList = ""
		for k,v in ipairs(dInfo.proofurl) do
			proofUrlList = proofUrlList..v
			if k ~= #dInfo.proofurl then
				proofUrlList = proofUrlList..","
			end
		end
		sqlCase = "update dy_customer_order set status="..dInfo.status..", pay_time='"..dInfo.paytime.."',proof_url='"..proofUrlList.."' where id="..dInfo.dealid
	elseif dInfo.status == g_marketDefine.deal_status_finish then
		dInfo.passtime = TimeUtils.GetTimeString()
		sqlCase = "update dy_customer_order set status="..dInfo.status..", pass_time='"..TimeUtils.GetTimeString().."' where id="..dInfo.dealid
	elseif dInfo.status == g_marketDefine.deal_status_wait_timeout or dInfo.status == g_marketDefine.deal_status_cancel then
		dInfo.canceltime = TimeUtils.GetTimeString()
		sqlCase = "update dy_customer_order set status="..dInfo.status..", cancel_time='"..TimeUtils.GetTimeString().."' where id="..dInfo.dealid
	else	
		sqlCase = "update dy_customer_order set status="..dInfo.status.." where id="..dInfo.dealid
		
	end
	
	mysqlItem:execute(sqlCase)
	ChatModel.OrderUpdate(dInfo.vendoruserid, dInfo.customeruserid, dInfo.dealid, dInfo.merchantorderid, dInfo.status)
	
	NoticeServices.updateOrderInfo(dInfo)
	
	
end



--获取这个玩家今天剩余的取消次数
function CustomerOrderModel.GetDealLeftCount(userID)
	local nowDay = TimeUtils.GetDayString()
	local tmpData = redisItem:hget(CustomerOrderModel.deal_left_amount, userID, CustomerOrderModel.redis_index)
	local getNum = 0
	if tmpData ~= nil then
		tmpData = luajson.decode(tmpData)
		if tmpData["date"] == nowDay then
			getNum = tmpData["num"]
		end
	end
	return getNum == nil and g_marketDefine.max_deal_num or (g_marketDefine.max_deal_num - tonumber(getNum))
	
end

function CustomerOrderModel.MinusFreeDealNum(userID)
	local nowDay = TimeUtils.GetDayString()
	local tmpData = redisItem:hget(CustomerOrderModel.deal_left_amount, userID, CustomerOrderModel.redis_index)
	if tmpData == nil then
		tmpData = {}
		tmpData["date"] = nowDay
		tmpData["num"] = 0
	else
		tmpData = luajson.decode(tmpData)
	end
	
	if tmpData["date"] == nowDay then
		tmpData["num"] = tmpData["num"] + 1
		
	else
		tmpData["date"] = nowDay
		tmpData["num"] = 1
	end
	
	redisItem:hset(CustomerOrderModel.deal_left_amount, userID, luajson.encode(tmpData), CustomerOrderModel.redis_index)
end


--商户用户，发起支付的交易
function CustomerOrderModel.ShopBuy(userID,outOrderNo,money,payType,notifyUrl,body,timestamp, IP,payer,addrType)
	
	local code = 1
	local msg = "fail"
	local info = nil
	
	local uInfo = UserInfoModel.GetUserInfo(userID)
	if uInfo == nil then
		return 1, "商户不存在", nil
	end
	
	--检查用户是否锁定
	if uInfo.islock == 1 then
		return 1, "你已经被锁定无法进行该操作", nil
	end
	
	--检查三方订单号十分存在
	local sqlCase = "select * from dy_customer_order where merchant_order_id ='"..outOrderNo.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		return 1, "该订单已存在", nil
	end
	
	local channelInfo = {}
	local sqlCase = "select channel,is_external from dy_channel_info"
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		local tmp = {}
		tmp['isExternal'] = tonumber(sqlData[2])
		channelInfo[sqlData[1]] = tmp
	end
	
	local coinfo = st_order_pb.customerorderinfo()
	local fromInfo = {}
	fromInfo["outOrderNo"] = outOrderNo
	fromInfo["notifyUrl"] = notifyUrl
	fromInfo["body"] = body
	fromInfo["timestamp"] = timestamp
	fromInfo["IP"] = IP
	
	
	if payType ~= g_payCenterDefine.pay_id_list['bitpay'] then
		
		--检查符不符合玩家自身的限额
		if uInfo.usertype == 301 and (tonumber(uInfo.minbuy) > money or money >tonumber(uInfo.maxbuy)) then
			return 1, "不符合自身设置的购买区间", nil
		end
	
		--查找合适的订单
		local hangID, currencyCount,moneyCount, price, payTypeOk = VendorOrderModel.CheckQuickHangid_shop(uInfo, money, g_marketDefine.amountType_RMB, uInfo.channel, payType, channelInfo)
		if hangID == nil or hangID == 0 then
			return 1, "没有合适交易订单"
		end
		
		ThreadManager.OrderLock(hangID)
		local voItem = VendorOrderModel.GetVendorOrderInfo(hangID)
		if voItem == nil then
			ThreadManager.OrderUnLock(hangID)
			return ReturnCode["deal_order_not_exist"][1],ReturnCode["deal_order_not_exist"][2]
		end
		
		local mInfo = UserInfoModel.GetUserInfo(voItem.userid)
		if channelInfo[mInfo.channel].isExternal == 1 then
			price = CoinInfoService.getOTCExchangePrice(g_marketDefine.deal_buy)
			if price == nil then
				return 1, "未获取道交易所的交易价格"
			end
			currencyCount = moneyCount / price
			payTypeOk = g_humanDefine.user_payList.bank
		end

		local ret, msg = VendorOrderService.DealOrder(uInfo, coinfo,voItem, g_marketDefine.deal_buy, currencyCount, moneyCount, fromInfo, price, payTypeOk,"")
		if ret ~= nil then
			ThreadManager.OrderUnLock(hangID)
			return 1,msg, nil
		end
		
		local sellUserID = coinfo.type == 0 and coinfo.vendoruserid or coinfo.customeruserid
		local sInfo =UserInfoModel.GetUserInfo(sellUserID)
		if sInfo ~= nil then
			sInfo.deallasttime = TimeUtils.GetTimeString()
			UserInfoModel.SetUserInfo(sInfo)
			local sqlCase = "update dy_user_info set deal_last_tiime='"..sInfo.deallasttime.."' where userid="..sellUserID
			mysqlItem:execute(sqlCase)
		end
		
		ThreadManager.OrderUnLock(hangID)
	else
		--检查商户是否有开启币支付
		local status = false
		local sqlCase = "select is_used from dy_user_rate where userid="..uInfo.userid.." and paytype=8401"
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch(sqlCase)
		if sqlData ~= nil then
			if tonumber(sqlData) == 1 then
				status = true
			end
		end
		if status == false then
			return 1, "商户未开通币支付通道"
		end
		
		--[[
		if tonumber(uInfo.coinpayrate) == nil or tonumber(uInfo.coinpayrate) <= 0 then 
			return 1, "请先设置商户的币支付价格"
		end
		]]
		
		--local currencyCount = money / tonumber(uInfo.coinpayrate)
		local sysPrice = CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.hang_coin_pay, g_marketDefine.currency_type.USDT, true, uInfo.userid)
		--local currencyCount = money / sysPrice
		--currencyCount = tonumber(string.format("%.10f", tostring(currencyCount)))
		local currencyCount = money
		money = tonumber(string.format("%.4f", money*sysPrice))
		
		if addrType == 101 and uInfo.coinpayerc == 0 then
			return 1, "未开启erc20的币支付"
		elseif addrType == 102 and uInfo.coinpaytrc == 0 then
			return 1, "未开启trc20的币支付"
		end
		
		--检查商户自己的地址
		local shopAddrList = {}
		local sqlCase = "select adds,owership,id  from dy_user_pay where userid="..uInfo.userid.." and status = 1 and pay_id=8401 and addr_type="..addrType
		mysqlItem:executeQuery(sqlCase)
		while true do 
			local sqlData = mysqlItem:fetch({})
			if sqlData == nil then
				break
			end
			
			table.insert(shopAddrList, {sqlData[1], tonumber(sqlData[2]),tonumber(sqlData[3])})
		end
		
		if #shopAddrList <= 0 and uInfo.allowsysaddr == 0 then
			return 1, "商户未设置有效的地址"
		end
		--检查有效的地址是否有相同的金额
		local shopUseAddrList = {}
		local sqlCase = "select adds from dy_user_pay where userid="..uInfo.userid.." and status=1 and pay_id=8401 and addr_type="..addrType.." and"
			.." EXISTS(select * from dy_customer_order where userid="..uInfo.userid.." and status=1 and pay_id=8401 and chain_type="..addrType
			.." and money="..money.." and chain_addr=dy_user_pay.adds)"
		mysqlItem:executeQuery(sqlCase)
		while true do 
			local sqlData = mysqlItem:fetch()
			if sqlData == nil then
				break
			end
			
			shopUseAddrList[sqlData] = 1
		end
		
		local shopSlfeAddrList = {}
		local shopSysAddrList = {}
		
		for k,v in ipairs(shopAddrList) do 
			if shopUseAddrList[v[1]] ~= 1 then
				if v[2] == 0 then
					table.insert(shopSysAddrList, {v[1], v[3]})
				elseif v[2] == 1 then
					table.insert(shopSlfeAddrList, {v[1], v[3]})
				end
			end
		end
			
		local chainAddr = ""
		local payInfoID = 0
		local owership = 0
		if #shopSlfeAddrList <= 0 then
			if uInfo.allowsysaddr == 0 then
				return 1, "充值地址繁忙，建议更改充值金额再尝试。"
			else
			
				if currencyCount  >= tonumber(uInfo.sysercmin) then
					--系统地址
					if #shopSysAddrList <= 0 then
						--没有空闲地址了就取一个
						if addrType == 101 then
							local address = AddressPreModel.GetNewAddress(uInfo.channel) or "" 
							if address ~= "" then
								AddressPreModel.UpdatePreAddressCoinPay(address, uInfo.channel, uInfo.userid)
								chainAddr = address
							end
						else
							local address = AddressPreModel.GetNewTRCAddress(uInfo.channel) or "" 
							if address ~= "" then
								AddressPreModel.UpdatePreTRCAddressCoinPay(address, uInfo.channel, uInfo.userid)
								chainAddr = address
							end
						end
						
						if chainAddr ~= nil then
							local sqlCase = "insert into dy_user_pay(pay_id,userid,status,channel,platform_id,owership,adds,addr_type,addr_name,nickname) values(8401,"
								..uInfo.userid..",1,'"..uInfo.channel.."',"..uInfo.platformid..",0,'"..chainAddr.."',"..addrType..",'"..g_marketDefine.addr_name_list[addrType].."','"..uInfo.nickname.."')"
							mysqlItem:execute(sqlCase)
							local sqlCase = "select id from dy_user_pay where adds='"..chainAddr.."' and userid="..uInfo.userid.." and status=1 and addr_type="..addrType.." and owership=0 order by id desc"
							mysqlItem:executeQuery(sqlCase)
							local sqlData = mysqlItem:fetch()
							if sqlData ~= nil then
								payInfoID = sqlData
							end
						end
					else
						chainAddr = shopSysAddrList[1][1]
						payInfoID = shopSysAddrList[1][2]
					end
				else
					return 1, "充值地址繁忙，建议更改充值金额再尝试。"
				end
			end
		else
			chainAddr = shopSlfeAddrList[1][1]
			owership = 1
			payInfoID = shopSlfeAddrList[1][2]
		end
		
		if chainAddr == "" then
			return 1, "未找到可用的地址"
		end
				
		local ret, msg = VendorOrderService.coinPayOrder(uInfo, coinfo, money, fromInfo, payType, addrType, chainAddr, owership, currencyCount,sysPrice, tonumber(payInfoID))
		if ret ~= nil then
			return 1,msg, nil
		end
	end
	
		
	if payer ~= nil and payer ~= "" then
		local msg = "付款人："..payer
		ChatModel.InsertInto(coinfo.dealid,coinfo.merchantorderid,coinfo.vendoruserid,coinfo.customeruserid,
		coinfo.dealtype,uInfo.channel,g_chatDefine.chat_type['customer_to_merchant'],1,msg)
	end
	return 0,"success", coinfo
end


--商户用户，发起支付的交易
function CustomerOrderModel.ShopSell(merchantInfo,payInfo,money)
	
	local code = 1
	local msg = "fail"
	local info = nil
	
	local uInfo = UserInfoModel.GetUserInfo(merchantInfo.userid)
	if uInfo == nil then
		return 1, "商户不存在", nil
	end
	
	--检查用户是否锁定
	if uInfo.islock == 1 then
		return 1, "你已经被锁定无法进行该操作", nil
	end
	
	--检查符不符合玩家自身的限额
	if (uInfo.usertype == 301 or uInfo.usertype == 300) and (tonumber(uInfo.minsell) > money or money >tonumber(uInfo.maxsell)) then
		return 1, "不符合自身设置的提现区间", nil
	end
	
	--检查三方订单号十分存在
	local sqlCase = "select * from dy_vendor_order where merchant_order_id ='"..merchantInfo.outOrderNo.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		return 1, "该订单已存在", nil
	end

	--先搞一个挂单
	
	local sysPrice = CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.hang_sell, g_marketDefine.currency_type.USDT, true, uInfo.userid)
	local amount = tonumber(string.format("%.10f", tonumber(money / sysPrice)))
	local rate = UserInfoModel.getUserFeeRate(uInfo.userid, g_humanDefine.user_payList.bank, g_humanDefine.sell_fee_rate)
	local free = rate * amount
	local feeMoney = rate * money
	local amountCount = 0
	local moneyCount = 0
	--还在进行中的订单
	local sqlCase = "select amount,unsold_order_amount,deal_order_amount,predict_money,unsold_order_money,deal_order_money from dy_vendor_order where userid="..uInfo.userid.." and type=1 and enable_status=1"
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		amountCount = amountCount + (tonumber(sqlData[1]) - tonumber(sqlData[2]) - tonumber(sqlData[3]))
		moneyCount = moneyCount + (tonumber(sqlData[4]) - tonumber(sqlData[5]) - tonumber(sqlData[6]))
	end
	
	--检查身上的币数量是否足够
	if uInfo.dealcointype == 1 then
		local preAuthorizationMoney = tonumber(uInfo.preauthorization) or 0
		if money + feeMoney > (tonumber(uInfo.ercfcamount) - tonumber(uInfo.ercfclockamount) + preAuthorizationMoney) - moneyCount then
			return 1, "余额不足", nil
		end
		
	else
		local preAuthorizationMoney = tonumber(uInfo.preauthorization) or 0
		local preAuthorizationAmount = 0
		if preAuthorizationMoney > 0 then
			preAuthorizationAmount = tonumber(string.format("%.10f", tonumber(preAuthorizationMoney / sysPrice)))
		end
		if amount + free > (tonumber(uInfo.ercusdtamount) - tonumber(uInfo.ercusdtlockamount) + preAuthorizationAmount) - amountCount then
			return 1, "余额不足", nil
		end

	end

	local currTime = TimeUtils.GetTimeString()
	local sqlCase = "insert into dy_vendor_order(userid,type,price,amount,price_type,min_money,max_money,auto_switch,create_time,channel,enable_status,user_type,payee_account,payee_name,payee_bank,free,predict_money,deal_mode,rest_time,withdraw_type,notify_info,platform_id,merchant_order_id) " 
		.."values("..uInfo.userid..","..g_marketDefine.hang_sell..","..sysPrice..","..amount..",0,"..money..","..money..",0,'"..currTime.."','"..uInfo.channel
		.."',1,300,'"..payInfo.payeeAccount.."','"..payInfo.payeeName.."','"..payInfo.bankerName.."',"..free..","..money..",1,'"..currTime.."',1,'"..luajson.encode(merchantInfo).."',"..uInfo.platformid..",'"..merchantInfo.outOrderNo.."')"
	mysqlItem:execute(sqlCase)
	local sqlCase = "select id from dy_vendor_order where userid="..uInfo.userid.." and type="..g_marketDefine.hang_sell.." and create_time='"..currTime.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		return 1, "提现失败"
	end
	
	local hangid = tonumber(sqlData)
	VendorOrderModel.addVendorOrder(hangid)

	local isOk = false
	ThreadManager.OrderLock(hangid)
	local hInfo = VendorOrderModel.GetVendorOrderInfo(hangid)
	if hInfo.dealmodel == 1 then
		local userID, state = CustomerOrderModel.GetSellOrderUser(uInfo, hInfo)
		if userID == 0 and state == false then
			local sqlCase = "update dy_vendor_order set enable_status=4 where id="..vInfo.hangid
			mysqlItem:execute(sqlCase)
			VendorOrderModel.DelVendOrorderInfo(vInfo.type, vInfo.userid, vInfo.handID)	
			return 1, "未取到交易所价格"	
		end
		
		if userID ~= nil and userID ~= 0 then
			isOk = true
		end
	end
	ThreadManager.OrderUnLock(hangid)
	if isOk == false then
		--找出所有配置了的玩家更新挂单
		local List = {}
		local channelList = {}
		local teamList = {}
		local groupList = {}
		local dealType = hInfo.withdrawtype == 0 and 1 or 2
		local sqlCase = "select bind_type,channel_name,bind_userid from dy_user_conf where userid="..uInfo.userid.." and deal_type="..dealType.." and is_exist=1"
		mysqlItem:executeQuery(sqlCase)
		while true do 
			local sqlData = mysqlItem:fetch({})
			if sqlData == nil then
				break
			end
			
			if tonumber(sqlData[1]) == 102 then
				List[tonumber(sqlData[3])] = 1
			elseif tonumber(sqlData[1]) == 101 then
				table.insert(channelList, sqlData[2])
			elseif tonumber(sqlData[1]) == 103 then
				table.insert(teamList, sqlData[3])
			elseif tonumber(sqlData[1]) == 104 then
				table.insert(groupList, sqlData[3])
			end
		end
		
		if #channelList > 0 then
			local sqlCase = "select userid from dy_user_info where"
			for k,v in ipairs(channelList) do 
				sqlCase = sqlCase.." channel='"..v.."' "
				if k ~= #channelList then
					sqlCase = sqlCase.."or"
				end
			end
			mysqlItem:executeQuery(sqlCase)
			while true do 
				local sqlData = mysqlItem:fetch()
				if sqlData == nil then
					break
				end
				
				List[tonumber(sqlData)] = 1
			end
		end
		
		if #teamList > 0 then
			local sqlCase = "select userid from ag_relation where"
			for k,v in ipairs(teamList) do 
				sqlCase = sqlCase.." bind_userid="..v.." "
				if k ~= #teamList then
					sqlCase = sqlCase.."or"
				end
				List[tonumber(v)] = 1
			end
			mysqlItem:executeQuery(sqlCase)
			while true do 
				local sqlData = mysqlItem:fetch()
				if sqlData == nil then
					break
				end
				
				List[tonumber(sqlData)] = 1
			end
		end
		
		if #groupList > 0 then
			local tmpMsg = "group_behalf_switch"
			local condition = ""
			local num = 0
			for k,v in ipairs(groupList) do 
				if num > 0 then
					condition = condition.." or"
				end
				condition = condition.." (group_id="..v.." and "..tmpMsg.."=1)"
				num = num + 1
			end
			
			if num ~= 0 then
				local sqlCase = "select group_user_id from dy_group_member where status=1 and ("..condition..")"
				mysqlItem:executeQuery(sqlCase)
				while true do 
					local sqlData = mysqlItem:fetch()
					if sqlData == nil then
						break
					end
					
					List[tonumber(sqlData)] = 1
				end
			end
		end
		
		local userList = {}
		for k,v in pairs(List) do 
			if true == OnlineModel.CheckOnline(k) then
				table.insert(userList, k)
			end
		end
		local gcUp = msg_order2_pb.gcupdatevendororder()
		local addV = gcUp.volist:add()
		addV:ParseFromString(hInfo:SerializeToString())
		addV.paylist:append(g_humanDefine.user_payList.bank)
		addV.feeRate:append('0.00')
		addV.nickname = uInfo.nickname
		
		gcUp.result = 0
		SendMessage(userList, PacketCode[2064].client, gcUp:ByteSize(),gcUp:SerializeToString())
	end
	
	return 0,"success", coinfo
end

--商户的用户，在支付成功后，点击，我已支付按钮
function CustomerOrderModel.ShopPaidPass(userID, orderID, timestamp)

	local code = 1
	local msg = "fail"
	local info = nil
	
	local uInfo = UserInfoModel.GetUserInfo(userID)
	if uInfo == nil then
		return 1, "商户不存在", nil
	end
	
	--检查用户是否锁定
	if uInfo.islock == 1 then
		return 1, "你已经被锁定无法进行该操作", nil
	end
	
	ThreadManager.DealLock(orderID)
	local info = CustomerOrderModel.GetCustomerOrderInfo(orderID)
	if info == nil then
		--挂单的信息不存在
		ThreadManager.DealUnLock(orderID)
		return 1, "交易的订单已经失效"
	end

	local ret, msg = VendorOrderService.PaidPassOrder(uInfo, info)
	if ret ~= nil then
		ThreadManager.DealUnLock(orderID)
		return 1, msg
	end
	ThreadManager.DealUnLock(orderID)
	
	return 0,"success", info
end



--商户的用户，在支付成功后，点击，我已支付按钮
function CustomerOrderModel.ShopPaid(userID, orderID, timestamp, payerName)
	
	local code = 1
	local msg = "fail"
	local info = nil
	
	local uInfo = UserInfoModel.GetUserInfo(userID)
	if uInfo == nil then
		return 1, "商户不存在", nil
	end
	
	--检查用户是否锁定
	if uInfo.islock == 1 then
		return 1, "你已经被锁定无法进行该操作", nil
	end

	ThreadManager.DealLock(orderID)
	local cinfo = CustomerOrderModel.GetCustomerOrderInfo(orderID)
	if cinfo == nil then
		ThreadManager.DealUnLock(orderID)
		return 1, "交易的订单已经失效"
	end

	local ret, msg = VendorOrderService.PaidOrder(uInfo, cinfo)
	if ret ~= nil then
		ThreadManager.DealUnLock(orderID)
		return 1, msg
	end
	
	ThreadManager.DealUnLock(orderID)
	
	if payerName ~= nil and payerName ~= "" then
		local msg = "付款人："..payerName
		ChatModel.InsertInto(cinfo.dealid,cinfo.merchantorderid,cinfo.vendoruserid,cinfo.customeruserid,
		cinfo.dealtype,uInfo.channel,g_chatDefine.chat_type['customer_to_merchant'],1,msg)
	end
	
	return 0,"success", info
end


--加入回调列表
function CustomerOrderModel.addCallbackOrder(dealid)
	
	redisItem:lpush(CustomerOrderModel.callback_order, dealid, CustomerOrderModel.redis_index)
	
end

--去一个最久的订单来回调
function CustomerOrderModel.GetCallbackOrder()

	return redisItem:rpop(CustomerOrderModel.callback_order, CustomerOrderModel.redis_index)

end

function CustomerOrderModel.GetSellOrderUser(uInfo, vInfo)
	
	--查找出所有的挂出售订单的币商
	local orderUserList = {} 
	local dealType = vInfo.withdrawtype == 0 and 1 or 2
	--[[
	local sqlCase = "select userid,id,deal_monery,deal_count from dy_vendor_order where type=0 and enable_status=1 and auto_switch=1 "
			.." and is_take=0 and min_money<="..vInfo.predictmoney.." and max_money>="..vInfo.predictmoney
			.." and EXISTS(SELECT * FROM dy_user_conf WHERE userid="..vInfo.userid.." and is_exist=1 and deal_type="..dealType
			.." and ((bind_type=101 and channel_name=dy_vendor_order.channel) or (bind_type=102 and bind_userid=dy_vendor_order.userid) "
			.." or (bind_type=103 and(dy_user_conf.bind_userid=dy_vendor_order.userid or EXISTS(SELECT * FROM ag_relation WHERE ag_relation.bind_userid=dy_user_conf.bind_userid and ag_relation.userid=dy_vendor_order.userid)))))"
	]]
	local sqlCase = "select userid,id,deal_monery,deal_count from dy_vendor_order where type=0 and enable_status=1 and auto_switch=1 "
			.." and is_take=0 and min_money<="..vInfo.predictmoney.." and max_money>="..vInfo.predictmoney
			.." and EXISTS(SELECT * FROM dy_user_conf WHERE userid="..vInfo.userid.." and is_exist=1 and deal_type="..dealType
			.." and ((bind_type=101 and channel_name=dy_vendor_order.channel)"
			.." or (bind_type=102 and bind_userid=dy_vendor_order.userid) "
			.." or (bind_type=103 and(dy_user_conf.bind_userid=dy_vendor_order.userid or EXISTS(SELECT * FROM ag_relation WHERE ag_relation.bind_userid=dy_user_conf.bind_userid and ag_relation.userid=dy_vendor_order.userid)))"
			.." or (bind_type=104 and(EXISTS(SELECT * FROM dy_group_member WHERE dy_group_member.group_id=dy_user_conf.bind_userid and dy_group_member.group_user_id=dy_vendor_order.userid and dy_group_member.status=1 and dy_group_member.group_behalf_switch=1)))))"
			
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		local tmp = {}
		tmp["userid"] = tonumber(sqlData[1])
		tmp["id"] = tonumber(sqlData[2])
		tmp["dealMonery"] = tonumber(sqlData[3])
		tmp["dealCount"] = tonumber(sqlData[4])
		table.insert(orderUserList, tmp)
	end
				
	--符合条件的先按人的最后交易时间排序
	if #orderUserList > 0 then
		table.sort(orderUserList, function(a, b)
			local aInfo = UserInfoModel.GetUserInfo(a.userid)
			local bInfo = UserInfoModel.GetUserInfo(b.userid)
			if TimeUtils.GetTime(aInfo.deallasttime) < TimeUtils.GetTime(bInfo.deallasttime) then
				return true
			else
				return false
			end
		end);
	end
	
	local channelInfo = {}
	local sqlCase = "select channel,is_external from dy_channel_info"
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		local tmp = {}
		tmp['isExternal'] = tonumber(sqlData[2])
		channelInfo[sqlData[1]] = tmp
	end
	
		
	for k,v in ipairs(orderUserList) do 
		local aInfo = UserInfoModel.GetUserInfo(v.userid)
		
		if channelInfo[aInfo.channel].isExternal == 1 then
		
			local price = CoinInfoService.getOTCExchangePrice(g_marketDefine.deal_sell)
			if price == nil then
				return 0, false
			end
			vInfo.price = tostring(price)
			vInfo.amount = tostring(tonumber(vInfo.predictmoney)/price)
			vInfo.isexternal = 1
			vInfo.externalorderuserid = aInfo.userid
			vInfo.isassigned = 0
			VendorOrderModel.SetVendorOrderInfo(vInfo.hangid, vInfo)
			local sqlCase = "update dy_vendor_order set is_external="..vInfo.isexternal..", external_order_userid="..vInfo.externalorderuserid
			..",is_assigned="..vInfo.isassigned..",price="..vInfo.price..",amount="..vInfo.amount.." where id="..vInfo.hangid
			mysqlItem:execute(sqlCase)
			
			aInfo.deallasttime = TimeUtils.GetTimeString()
			UserInfoModel.SetUserInfo(aInfo)
			local sqlCase = "update dy_user_info set deal_last_tiime='"..aInfo.deallasttime.."' where userid="..aInfo.userid
			mysqlItem:execute(sqlCase)
			return aInfo.userid, true
		else
			if v.dealMonery > (v.dealCount + vInfo.predictmoney) then
				--检查可取消次数
				local num = CustomerOrderModel.GetDealLeftCount(aInfo.userid)
				--查询还有多少订单正在进行中
				local sqlCase = "select count(*) from dy_customer_order where customer_user_id="..aInfo.userid.." and status=1"
				mysqlItem:executeQuery(sqlCase)
				local sqlDate = mysqlItem:fetch()
							
				if aInfo ~= nil and num > 0 and (sqlDate ~= nil and tonumber(sqlDate) < 5) and aInfo.isacceptorder == 0 and aInfo.isteamacceptorder == 0 then
					local coinfo = st_order_pb.customerorderinfo()
					local ret, msg = VendorOrderService.DealOrder(aInfo, coinfo,vInfo, g_marketDefine.deal_buy,
					tonumber(vInfo.amount), tonumber(vInfo.predictmoney), nil, tonumber(vInfo.price), g_humanDefine.user_payList.bank, "")
					if ret == nil then
						--修改支付账号的最后交易时
						aInfo.deallasttime = TimeUtils.GetTimeString()
						UserInfoModel.SetUserInfo(aInfo)
						local sqlCase = "update dy_user_info set deal_last_tiime='"..aInfo.deallasttime.."' where userid="..aInfo.userid
						mysqlItem:execute(sqlCase)
									
						local voItem = VendorOrderModel.GetVendorOrderInfo(v.id)
						if voItem ~= nil then
							voItem.dealcount = tostring((tonumber(voItem.dealcount) or 0)+ tonumber(vInfo.predictmoney))
								
							if (voItem.dealmonery - voItem.dealcount) < tonumber(voItem.minmoney) then
								voItem.enablestatus = 0
							end
								
							VendorOrderModel.SetVendorOrderInfo(v.id, voItem)
							local sqlCase = "update dy_vendor_order set deal_count="..voItem.dealcount..", enable_status="..voItem.enablestatus.." where id="..v.id
							mysqlItem:execute(sqlCase)
							return aInfo.userid, true
						end
					end
				end
			end
		end
	end
end

function CustomerOrderModel.AddOrderNum(dInfo)
	
	return redisItem:hincrby(CustomerOrderModel.order_num, 1, 1, CustomerOrderModel.redis_index )
end

function CustomerOrderModel.AddCustomerWithdrawOrderList(dInfo)
	
	redisItem:lpush( CustomerOrderModel.customerorder_withdraw_list, dInfo.dealid, CustomerOrderModel.redis_index )
end

function CustomerOrderModel.GetCustomerWithdrawOrderList()
	
	--[[
	if true == redisItem:exists( CustomerOrderModel.customerorder_withdraw_list, CustomerOrderModel.redis_index) then
		return redisItem:lrange( CustomerOrderModel.customerorder_withdraw_list, 0, -1, CustomerOrderModel.redis_index )
	end
	]]
	CustomerOrderModel.LoadCustomerWithdrawOrderList()
	
	return redisItem:lrange( CustomerOrderModel.customerorder_withdraw_list, 0, -1, CustomerOrderModel.redis_index )
end

function CustomerOrderModel.LoadCustomerWithdrawOrderList()
	
	redisItem:del(CustomerOrderModel.customerorder_withdraw_list, CustomerOrderModel.redis_index)
	local sqlCase = "select id from dy_customer_order where (status="..g_marketDefine.deal_status_wait_timeout.." or status="..g_marketDefine.deal_status_cancel..") and deal_type=300 and is_wait=1"
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlData = mysqlItem:fetch()
		if sqlData == nil then
			break
		end
		redisItem:lpush( CustomerOrderModel.customerorder_withdraw_list, tonumber(sqlData), CustomerOrderModel.redis_index )
	end
end

function CustomerOrderModel.delCustomerWithdrawOrderList(dealid)
	redisItem:lrem(CustomerOrderModel.customerorder_withdraw_list, 0, dealid, CustomerOrderModel.redis_index)
end
