module("NoticMessageDelete", package.seeall)

--删除消息
function execute(packetID, operateID, buffer)

	local cgmsg = msg_notice_pb.cgnoticemessagedelete()
	local gcmsg = msg_notice_pb.gcnoticemessagedelete()

	cgmsg:ParseFromString(buffer)
	
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result = ReturnCode["human_authcode_not_exist"][1]
		gcmsg.msg = ReturnCode["human_authcode_not_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	local messageInfo = NoticeModel.GetMessageInfo(cgmsg.userid, cgmsg.id)
	if messageInfo == nil then
		gcmsg.result = 0
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	NoticeModel.DelMessageInfo(cgmsg.userid, messageInfo.id)
	
	local sqlCase = "update dy_message_notice set enable_status=0 where user_id="..cgmsg.userid
	mysqlItem:execute(sqlCase)
	
	gcmsg.result = 0
	return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()

end