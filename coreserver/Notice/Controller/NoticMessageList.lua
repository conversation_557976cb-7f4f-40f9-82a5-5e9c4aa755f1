module("NoticMessageList", package.seeall)

--消息通知列表
function execute(packetID, operateID, buffer)

	local cgmsg = msg_notice_pb.cgnoticemessagelist()
	local gcmsg = msg_notice_pb.gcnoticemessagelist()

	cgmsg:ParseFromString(buffer)
	
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result = ReturnCode["human_authcode_not_exist"][1]
		gcmsg.msg = ReturnCode["human_authcode_not_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	local startPos = (cgmsg.pagenum - 1) * cgmsg.pagesize + 1
	local endPos = startPos + cgmsg.pagesize
	
	local messageList = NoticeModel.GetMessageList(cgmsg.userid)
	for k,v in pairs(messageList) do 
		messageList[tonumber[k]] = v
	end
	
	for k,v in ipairs(messageList) do
		if k >= startPos then
			local addMessage = gcmsg.messagelist()
			addMessage:ParseFromString(v)
		end
		if k > endPos then
			break
		end
	end
	
	gcmsg.result = 0
	return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()

end