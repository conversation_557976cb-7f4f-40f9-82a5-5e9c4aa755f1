module("NoticeList", package.seeall)

--公告列表
function execute(packetID, operateID, buffer)

	local cgmsg = msg_notice_pb.cgnoticelist()
	local gcmsg = msg_notice_pb.gcnoticelist()

	cgmsg:ParseFromString(buffer)
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result = ReturnCode["human_authcode_not_exist"][1]
		gcmsg.msg = ReturnCode["human_authcode_not_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	local startPos = (cgmsg.pagenum - 1) * cgmsg.pagesize
	local endPos = startPos + cgmsg.pagesize - 1
	local noticeList = NoticeModel.GetNoticeList(uInfo.channel, startPos, endPos)
	for k,v in ipairs(noticeList) do
		local noticeinfo = st_notice_pb.noticeinfo()
		noticeinfo:ParseFromString(v)
		if noticeinfo.expirationtime > TimeUtils.GetTime() then
			local addNotice = gcmsg.noticelist:add()
			addNotice.id = noticeinfo.id
			addNotice.title = noticeinfo.title
			addNotice.content = noticeinfo.content
			addNotice.noticetype = noticeinfo.noticetype
			addNotice.expirationtime = noticeinfo.expirationtime
			addNotice.remark = noticeinfo.remark
		else
			NoticeModel.DelNoticeInfo(uInfo.channel, v)
		end
	end
	
	gcmsg.result = 0
	return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()

end