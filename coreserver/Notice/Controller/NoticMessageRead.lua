module("NoticMessageRead", package.seeall)

--阅读消息
function execute(packetID, operateID, buffer)

	local cgmsg = msg_notice_pb.cgnoticemessageread()
	local gcmsg = msg_notice_pb.gcnoticemessageread()

	cgmsg:ParseFromString(buffer)
	
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result = ReturnCode["human_authcode_not_exist"][1]
		gcmsg.msg = ReturnCode["human_authcode_not_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	local messageInfo = NoticeModel.GetMessageInfo(cgmsg.userid, cgmsg.id)
	if messageInfo == nil then
		gcmsg.result = ReturnCode["notice_message_not_exist"][1]
		gcmsg.msg = ReturnCode["notice_message_not_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	messageInfo.is_read = 1
	NoticeModel.SetMessageInfo(cgmsg.userid, messageInfo.id, messageInfo)
	
	local sqlCase = "update dy_message_notice set is_read=1 where user_id="..cgmsg.userid
	mysqlItem:execute(sqlCase)
	gcmsg.result = 0
	return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()

end