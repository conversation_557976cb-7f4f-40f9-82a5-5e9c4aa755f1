
NoticeModel = {}

NoticeModel.redis_index = "redis_notice"
NoticeModel.notice_notice_list = "notice_notice_list_"       			--公告列表
NoticeModel.notice_message_list = "notice_message_list_"       			--公告列表

function NoticeModel.GetNoticeList(channel, startPos, endPos)
	
	if true == redisItem:exists( NoticeModel.notice_notice_list..channel, NoticeModel.redis_index) then
		return redisItem:lrange( NoticeModel.notice_notice_list..channel, startPos, endPos, NoticeModel.redis_index )
	end
	
	NoticeModel.LoadNoticeList(channel)
	
	return redisItem:lrange( NoticeModel.notice_notice_list..channel, startPos, endPos, NoticeModel.redis_index )
end


function NoticeModel.LoadNoticeList(channel)

	
	local t = TimeUtils.GetTime()
	
	local sqlCase = "select * from dy_notice_content where enable_status=1 and channel = '"..channel
	.."' and expiration_time>'"..TimeUtils.GetTimeString().."' order by weight"
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		local noticeInfo = st_notice_pb.noticeinfo()
		
		noticeInfo.id = tonumber(sqlData[1])
		noticeInfo.title = sqlData[2]
		noticeInfo.content = sqlData[3]
		noticeInfo.noticetype = tonumber(sqlData[4])
		noticeInfo.expirationtime = tonumber(TimeUtils.GetTime(sqlData[5]))
		noticeInfo.remark = sqlData[8]
		
		redisItem:lpush( NoticeModel.notice_notice_list..channel, noticeInfo:SerializeToString(), NoticeModel.redis_index )
	end
end

function NoticeModel.DelNoticeInfo(channel, v)

	redisItem:lrem( NoticeModel.notice_notice_list..channel, v, NoticeModel.redis_index )
	
end

function NoticeModel.GetMessageList(userid)
	
	if true == redisItem:exists( NoticeModel.notice_message_list..userid, NoticeModel.redis_index) then
		return redisItem:hgetall( NoticeModel.notice_message_list..userid, NoticeModel.redis_index )
	end
	
	NoticeModel.LoadMessageList(userid)
	
	return redisItem:hgetall( NoticeModel.notice_message_list..userid, NoticeModel.redis_index )
end

function NoticeModel.LoadMessageList(userid)
	
	local sqlCase = "select * from dy_message_notice where enable_status=1 and user_id="..userid
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		
		local messageInfo = st_notice_pb.messageinfo()
		
		messageInfo.id = tonumber(sqlData[1])
		messageInfo.messagetype = tonumber(sqlData[2])
		messageInfo.title = sqlData[3]
		messageInfo.content = sqlData[4]
		messageInfo.remark = sqlData[7]
		messageInfo.isread = sqlData[8]
		
		NoticeModel.SetMessageInfo(userid, messageInfo.id, messageInfo)
	end
end

function NoticeModel.SetMessageInfo(userid, id, messageInfo)
	
	redisItem:hget( NoticeModel.notice_message_list..userid, id,  messageInfo:SerializeToString(), NoticeModel.redis_index )
	
end

function NoticeModel.GetMessageInfo(userid, id)
	
	redisItem:hget(NoticeModel.notice_message_list..userid, id, NoticeModel.redis_index )
	
end

function NoticeModel.DelMessageInfo(userid, id)
	
	redisItem:hdel(NoticeModel.notice_message_list..userid, id, NoticeModel.redis_index )
	
end