
module("HttpPayCenter", package.seeall)

--购买下单
--merchantUserId			商户ID
--money						金额（人民币）
--payType					支付类型 支付宝银行卡之类的
--outOrderNo				三方平台订单
--notifyUrl					通知回调
--body						商户客户的订单，给商户返回的参数
--timestamp					时间戳
--addrType					地址类型101-erc20 102-trc20 
--IP
--payer						付款人
--sign						签证
function buy(rcvData)    --买币
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	if arrData['merchantUserId'] == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['shop_merchant_is_null'][1],g_payCenterDefine.retuen_code['shop_merchant_is_null'][2]
		return luajson.encode(retMsg)
	end
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local userID = tonumber(arrData['merchantUserId']) or 0
	local money = tonumber(arrData['money']) or 0
	local payType = tonumber(arrData['payType']) or 0
	local IP = arrData.IP or ""
	
	local uInfo = UserInfoModel.GetUserInfo(userID)
	if uInfo == nil then
		return 1, "商户不存在", nil
	end
	
	--检查该商户的用户是否存在未完成的订单
	if g_is_uniotc == false and uInfo.ischeck == 0 then
		local sqlCase = "select id,merchant_order_id,channel,is_external,pay_type,amount,money,price,chain_addr from dy_customer_order where customer_user_id="..userID.." and body='"..arrData['body'].."' and status=1"
		if payType == 8401 then
			sqlCase = sqlCase.." and pay_id=8401"
		else
			sqlCase = sqlCase.." and pay_id!=8401"
		end
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch({})
		if sqlData ~= nil then
			local tmpData = {}
			tmpData["id"] = sqlData[1]
			tmpData["merchantorderid"] = sqlData[2]
			tmpData["channel"] = sqlData[3]
			tmpData["isexternal"] = sqlData[4]
			tmpData["amount"] = sqlData[6]
			tmpData["money"] = sqlData[7]
			local PayInfo = luajson.decode(sqlData[5])
			tmpData["paytype"] = PayInfo.paytype
			tmpData["account"] = PayInfo.account
			tmpData["payee"] = PayInfo.payee
			tmpData["qrcode"] = PayInfo.qrcode
			tmpData["bankname"] = PayInfo.bankname
			tmpData["bankaddr"] = PayInfo.bankaddr
			tmpData["paystatus"] = PayInfo.bankaddr
			tmpData["fourthpartyid"] = PayInfo.fourthpartyid
			tmpData["price"] = sqlData[8]
			tmpData["chainaddr"] = sqlData[9]
			retMsg['isNew'] = 0
			retMsg['data'] = tmpData
			retMsg['code'] = 200
			retMsg['msg'] = "success"
			return luajson.encode(retMsg)	
		end
	end 
	ThreadManager.DealLock(arrData['outOrderNo'])
	--订单
	local addrType = tonumber(arrData['addrType']) or 0
	local ret, msg, info = CustomerOrderModel.ShopBuy(userID,arrData['outOrderNo'],money,payType,arrData['notifyUrl'],arrData['body'],arrData['timestamp'],IP, arrData['payer'],addrType)
	if ret ~= 0 then
		ThreadManager.DealUnLock(arrData['outOrderNo'])
		retMsg['code'],retMsg['msg'] = ret, msg
		return luajson.encode(retMsg)	
	end
	ThreadManager.DealUnLock(arrData['outOrderNo'])
	local tmpData = {}
	tmpData["id"] = info.dealid
	tmpData["merchantorderid"] = info.merchantorderid
	tmpData["amount"] = info.amount
	tmpData["money"] = info.money
	local PayInfo = luajson.decode(info.paytypelist)
	tmpData["paytype"] = PayInfo.paytype
	tmpData["account"] = PayInfo.account
	tmpData["payee"] = PayInfo.payee
	tmpData["qrcode"] = PayInfo.qrcode
	tmpData["bankname"] = PayInfo.bankname
	tmpData["bankaddr"] = PayInfo.bankaddr
	tmpData["paystatus"] = PayInfo.bankaddr
	tmpData["channel"] = info.channel   --把channel返回去
	tmpData["isexternal"] = info.isexternal
	tmpData["fourthpartyid"] = PayInfo.fourthpartyid
	tmpData["chainaddr"] = info.chainaddr
	tmpData["price"] = info.price
	retMsg['isNew'] = 1
	retMsg['data'] = tmpData
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
	
end

--取消旧订单下新订单的接口
--merchantUserId			商户ID
--oldOrderId				旧的订单号
--money						金额（人民币）
--payType					支付类型 支付宝银行卡之类的
--outOrderNo				三方平台订单
--notifyUrl					通知回调
--body						商户客户的订单，给商户返回的参数
--timestamp					时间戳
--addrType					地址类型101-erc20 102-trc20 
--IP
--payer						付款人
--sign						签证
function canceloldorder(rcvData)
		
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	if arrData['merchantUserId'] == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['shop_merchant_is_null'][1],g_payCenterDefine.retuen_code['shop_merchant_is_null'][2]
		return luajson.encode(retMsg)
	end
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local userID = tonumber(arrData['merchantUserId']) or 0
	local money = tonumber(arrData['money']) or 0
	local payType = tonumber(arrData['payType']) or 0
	local IP = arrData.IP or ""
	
	local sqlCase = "select id from dy_customer_order where customer_user_id="..userID.." and body='"..arrData['body'].."' and status=1"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		ThreadManager.DealLock(sqlData)
		local coinInfo = CustomerOrderModel.GetCustomerOrderInfo(sqlData)
		if coinInfo ~= nil then
			local uInfo = UserInfoModel.GetUserInfo(userID)
			if uInfo ~= nil then
				if tonumber(coinInfo.payidlist) == 8401 then
					VendorOrderService.CanelOrderCionPay(uInfo, coinInfo, false)
				else
					VendorOrderService.CanelOrder(uInfo, coinInfo, false)
				end
			end
		end
		
		ThreadManager.DealUnLock(sqlData)
	end
	
	--订单
	local addrType = tonumber(arrData['addrType']) or 0
	local ret, msg, info = CustomerOrderModel.ShopBuy(userID,arrData['outOrderNo'],money,payType,arrData['notifyUrl'],arrData['body'],arrData['timestamp'],IP,arrData['payer'],addrType)
	if ret ~= 0 then
		retMsg['code'],retMsg['msg'] = ret, msg
		return luajson.encode(retMsg)	
	end
	local tmpData = {}
	tmpData["id"] = info.dealid
	tmpData["merchantorderid"] = info.merchantorderid
	tmpData["amount"] = info.amount
	tmpData["money"] = info.money
	local PayInfo = luajson.decode(info.paytypelist)
	tmpData["paytype"] = PayInfo.paytype
	tmpData["account"] = PayInfo.account
	tmpData["payee"] = PayInfo.payee
	tmpData["qrcode"] = PayInfo.qrcode
	tmpData["bankname"] = PayInfo.bankname
	tmpData["bankaddr"] = PayInfo.bankaddr
	tmpData["paystatus"] = PayInfo.bankaddr
	tmpData["channel"] = info.channel   --把channel返回去
	tmpData["isexternal"] = info.isexternal
	tmpData["fourthpartyid"] = PayInfo.fourthpartyid
	tmpData["chainaddr"] = info.chainaddr
	tmpData["price"] = info.price
	retMsg['isNew'] = 1
	retMsg['data'] = tmpData
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
	
end


--购买下单
--merchantUserId      商户ID
--money            金额（人民币）
--payType          支付类型 支付宝银行卡之类的
--fourthpartyId		可用的第四分ID列表
--outOrderNo        三方平台订单
--notifyUrl          通知回调
--body            商户客户的订单，给商户返回的参数
--timestamp          时间戳
--IP
--sign            签证
function buynew(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	if arrData['merchantUserId'] == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['shop_merchant_is_null'][1],g_payCenterDefine.retuen_code['shop_merchant_is_null'][2]
		return luajson.encode(retMsg)
	end
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local userID = tonumber(arrData['merchantUserId']) or 0
	local money = tonumber(arrData['money']) or 0
	local payType = tonumber(arrData['payType']) or 0
	local IP = arrData.IP or ""
	
	--订单
	local ret, msg, info = CustomerOrderModel.ShopBuy(userID,arrData['outOrderNo'],money,payType,arrData['notifyUrl'],arrData['body'],arrData['timestamp'],IP)
	if ret ~= 0 then
		retMsg['code'],retMsg['msg'] = ret, msg
		return luajson.encode(retMsg)	
	end
	local tmpData = {}
	tmpData["id"] = info.dealid
	local PayInfo = luajson.decode(info.paytypelist)
	tmpData["paytype"] = PayInfo.paytype
	tmpData["account"] = PayInfo.account
	tmpData["payee"] = PayInfo.payee
	tmpData["qrcode"] = PayInfo.qrcode
	tmpData["bankname"] = PayInfo.bankname
	tmpData["bankaddr"] = PayInfo.bankaddr
	tmpData["paystatus"] = PayInfo.bankaddr
	tmpData["fourthpartyid"] = PayInfo.fourthpartyid
	tmpData["channel"] = info.channel   --把channel返回去
	tmpData["isexternal"] = info.isexternal
	
	retMsg['data'] = tmpData
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
	
end

--确认支付(准确到账，立马放行)
--merchantUserId			商户ID
--orderId					订单ID
--timestamp					时间戳
--sign						签证
function paid_pass(rcvData)   
	
	local retMsg = {}
	retMsg['code'] = 200;
	
	if rcvData == nil then
		--
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	
	local arrData = luajson.decode(rcvData)
	if arrData['merchantUserId'] == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['shop_merchant_is_null'][1],g_payCenterDefine.retuen_code['shop_merchant_is_null'][2]
		return luajson.encode(retMsg)
	end
	
	--开始进行签名
	
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)
	end	
	
	--确认支付
	local userID = tonumber(arrData['merchantUserId']) or 0
	
	local sqlCase = "select id from dy_customer_order where merchant_order_id='"..arrData['orderId'].."' and customer_user_id="..userID
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		retMsg['code'],retMsg['msg'] = 1, "订单号不存在"
		return luajson.encode(retMsg)
	end
	local orderID = tonumber(sqlData) 
	local ret, msg, info = CustomerOrderModel.ShopPaidPass(userID, orderID, arrData['timestamp'])
	if ret ~= 0 then
		retMsg['code'],retMsg['msg'] = ret, msg
		return luajson.encode(retMsg)	
	end


	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
	
	
end

--支付中心提现
--merchantUserId			商户ID
--money						金额（人民币）
--payType					支付类型 支付宝银行卡之类的
--payeeName					收款人
--payeeAccount				收款人账号
--bankerName				银行名称
--bankaddr					开户地址
--outOrderNo				三方平台订单
--notifyUrl					通知回调
--body						商户客户的订单，给商户返回的参数
--IP						IP地址
--fourthpartyid				四方ID
--sign						签证
function sell(rcvData)    --卖币
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	if arrData['merchantUserId'] == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['shop_merchant_is_null'][1],g_payCenterDefine.retuen_code['shop_merchant_is_null'][2]
		return luajson.encode(retMsg)
	end
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local userID = tonumber(arrData['merchantUserId']) or 0
	local money = tonumber(arrData['money']) or 0
	local IP = arrData['IP'] or ""
	local payInfo = {}
	payInfo["payType"] = tonumber(arrData['payType']) or 0
	payInfo["payeeName"] = arrData['payeeName'] or ""
	payInfo["payeeAccount"] = arrData['payeeAccount'] or ""
	payInfo["bankerName"] = arrData['bankerName'] or ""
	payInfo["bankaddr"] = arrData['bankaddr'] or ""
	payInfo["fourthpartyid"] = arrData['fourthpartyid'] or ""
	
	local merchantInfo = {}
	merchantInfo["userid"] = tonumber(arrData['merchantUserId']) or 0
	merchantInfo["outOrderNo"] = arrData['outOrderNo'] or ""
	merchantInfo["notifyUrl"] = arrData['notifyUrl'] or ""
	merchantInfo["body"] = arrData['body'] or ""
	merchantInfo["IP"] = arrData['IP'] or ""
	ThreadManager.OrderLock(merchantInfo["outOrderNo"])
	--提现
	local ret, msg, info = CustomerOrderModel.ShopSell(merchantInfo,payInfo,money)
	if ret ~= 0 then
		ThreadManager.OrderUnLock(merchantInfo["outOrderNo"])
		retMsg['code'],retMsg['msg'] = ret, msg
		return luajson.encode(retMsg)	
	end
	
	ThreadManager.OrderUnLock(merchantInfo["outOrderNo"])

	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end

--确认支付(准确到账，等待放行)
--merchantUserId			商户ID
--orderId					订单ID
--payerName					付款人姓名	
--timestamp					时间戳
--sign						签证
function paid(rcvData)   
	
	local retMsg = {}
	retMsg['code'] = 200;
	
	if rcvData == nil then
		--
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	
	local arrData = luajson.decode(rcvData)
	
	if arrData['merchantUserId'] == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['shop_merchant_is_null'][1],g_payCenterDefine.retuen_code['shop_merchant_is_null'][2]
		return luajson.encode(retMsg)
	end

	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)
	end	
	
	--确认支付
	local userID = tonumber(arrData['merchantUserId']) or 0
	local payerName = arrData['payerName'] or ""

	local sqlCase = "select id from dy_customer_order where merchant_order_id='"..arrData['orderId'].."' and customer_user_id="..userID
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		retMsg['code'],retMsg['msg'] = 1, "订单号不存在"
		return luajson.encode(retMsg)
	end
	local orderID = tonumber(sqlData) 
	
	local ret, msg, info = CustomerOrderModel.ShopPaid(userID, orderID, arrData['timestamp'],payerName)
	if ret ~= 0 then
		retMsg['code'],retMsg['msg'] = ret, msg
		return luajson.encode(retMsg)	
	end
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
end

--卖币下单（下的是挂单）
--merchantUserId			商户ID
--currencyAmount			币数量
--currencyType				币类型
--payeeName					收款人
--bankerName				银行名称
--bankAccount				银行账号
--fundPwd					资金密码
--predictMoney				预计到账	
--minLimitAmount			单笔最低额度
--dealMode					交易模式 0 可多次交易 1 一次交易完成
--sellType					1 OTC 2 代付
--wallet_type				钱包类型 0 基础钱包 1 币付钱包
--sign						签证
function sellOrderInsert(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	if arrData['merchantUserId'] == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['shop_merchant_is_null'][1],g_payCenterDefine.retuen_code['shop_merchant_is_null'][2]
		return luajson.encode(retMsg)
	end
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	
	--检查用户存不存在
	local uInfo = UserInfoModel.GetUserInfo(arrData.merchantUserId)
	if uInfo == nil or (uInfo.usertype == 301 and uInfo.usertype == 300)  then
		retMsg['code'],retMsg['msg'] = 1,"商户不存在"
		return luajson.encode(retMsg)	
	end
	
	--检查用户是否锁定
	if uInfo.islock == 1 then
		retMsg['code'],retMsg['msg'] = 1, "你已经被锁定无法进行该操作"
		return luajson.encode(retMsg)	
	end
	arrData.sellType = tonumber(arrData.sellType) or 1
	if uInfo.usertype == 300 then
		if tonumber(arrData.sellType) == 1 then
			if uInfo.isotc == 1 then
				retMsg['code'],retMsg['msg'] = 1,"该提现方式已被禁止"
				return luajson.encode(retMsg)	
			end
		else
			if uInfo.isbehalfpay == 1 then
				retMsg['code'],retMsg['msg'] = 1,"该提现方式已被禁止"
				return luajson.encode(retMsg)	
			end
		end
	end
	
	--检查资金密码
	local ret, msg = UserInfoModel.CheckFundPassword(uInfo, arrData.fundPwd)
	if ret ~= 0 then
		retMsg['code'],retMsg['msg'] = ret,msg
		return luajson.encode(retMsg)	
	end
	
	--检查符不符合玩家自身的限额
	if uInfo.usertype == 301 and (tonumber(uInfo.minsell) > tonumber(arrData.predictMoney) or tonumber(arrData.predictMoney) >tonumber(uInfo.maxsell)) then
		retMsg['code'],retMsg['msg'] = 1,  "提现订单金额需在￥"..uInfo.minsell.."--￥"..uInfo.maxsell.."范围内才可提交"
		return luajson.encode(retMsg)	
	end
	
	arrData.dealMode = (arrData.dealMode == nil or arrData.dealMode == "") and 1 or arrData.dealMode
	arrData.sellType = (arrData.sellType == nil or arrData.sellType == "") and 1 or arrData.sellType
	local money = tonumber(arrData.currencyAmount) * CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.hang_sell, g_marketDefine.currency_type.USDT, true, uInfo.userid)
	if money < tonumber(arrData.minLimitAmount) or (tonumber(arrData.dealMode) == 0 and tonumber(arrData.minLimitAmount) < tonumber(uInfo.minsell)) then
		retMsg['code'],retMsg['msg'] = 1, "提现金额小于单笔最低额度"
		return luajson.encode(retMsg)	
	end
	
	local Amount = tonumber(arrData.predictMoney) / CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.hang_sell, g_marketDefine.currency_type.USDT, true, uInfo.userid)
	arrData.currencyAmount = math.floor(Amount * ***********) /***********
	
	--卖币需要付的手续费
	local rate = UserInfoModel.getUserFeeRate(uInfo.userid, g_humanDefine.user_payList.bank, g_humanDefine.sell_fee_rate)
	local free = tonumber(string.format("%.10f", tostring(rate * arrData.currencyAmount)))
	local feeMoney = tonumber(string.format("%.4f", tostring(rate * tonumber(arrData.predictMoney))))
	local walletType = tonumber(arrData.wallet_type) or 0
	--检查用户的余额是否足够	
	if uInfo.dealcointype == 0 then
		if tonumber(arrData.currencyAmount) + free > (tonumber(uInfo.ercusdtamount) - tonumber(uInfo.ercusdtlockamount)) then
			retMsg['code'],retMsg['msg'] = ReturnCode["hang_money_not_enough"][1],ReturnCode["hang_money_not_enough"][2]
			return luajson.encode(retMsg)	
		end
	else
		if tonumber(arrData.predictMoney) + feeMoney > (tonumber(uInfo.ercfcamount) - tonumber(uInfo.ercfclockamount)) then
			retMsg['code'],retMsg['msg'] = ReturnCode["hang_money_not_enough"][1],ReturnCode["hang_money_not_enough"][2]
			return luajson.encode(retMsg)	
		end
	end
	
	local status = uInfo.usertype == 301 and 2 or 1
	local withdrawType = tonumber(arrData.sellType) == 1 and 0 or 1
	local sysPrice = CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.hang_sell, g_marketDefine.currency_type.USDT, true, uInfo.userid)
	local min_money = tonumber(arrData.dealMode) == 1 and arrData.predictMoney or arrData.minLimitAmount
	local max_money = arrData.predictMoney
	--插入到数据库中。
	local currTime = TimeUtils.GetTimeString()
	local sqlCase = "insert into dy_vendor_order(userid,type,price,amount,price_type,min_money,max_money,auto_switch,create_time,channel,enable_status,user_type,payee_account,payee_name,payee_bank,free,predict_money,deal_mode,rest_time,withdraw_type,platform_id,wallet_type) " 
		.."values("..uInfo.userid..","..g_marketDefine.hang_sell..","..sysPrice..","..arrData.currencyAmount..",0,"..min_money..","..max_money..",0,'"..currTime.."','"..uInfo.channel
		.."',"..status..",300,'"..arrData.bankAccount.."','"..arrData.payeeName.."','"..arrData.bankerName.."',"..free..","..arrData.predictMoney..","..arrData.dealMode..",'"..currTime.."',"..withdrawType..","..uInfo.platformid..","..walletType..")"
	mysqlItem:execute(sqlCase)
	local sqlCase = "select id from dy_vendor_order where userid="..uInfo.userid.." and type="..g_marketDefine.hang_sell.." and create_time='"..currTime.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		retMsg['code'],retMsg['msg'] = 1, "提交失败"
		return luajson.encode(retMsg)	
	end
	
	if status == 1 then
	
		ThreadManager.OrderLock(sqlData)
		local hInfo = VendorOrderModel.GetVendorOrderInfo(sqlData)
	
		local isOk = false
		if hInfo.dealmodel == 1 then
			local userID, state = CustomerOrderModel.GetSellOrderUser(uInfo, hInfo)
			if userID == 0 and state == false then
				local sqlCase = "update dy_vendor_order set enable_status=4 where id="..sqlData
				mysqlItem:execute(sqlCase)
				VendorOrderModel.DelVendOrorderInfo(hInfo.type, hInfo.userid, hInfo.handID)
				ThreadManager.OrderUnLock(hInfo.hangid)
				retMsg['code'] = 1
				retMsg['msg'] = "未取到交易所价格"	
				return luajson.encode(retMsg)
			end
			
			
			if userID ~= nil and userID ~= 0 then
				isOk = true
			end
		end
		VendorOrderModel.addVendorOrder(hInfo.hangid)
		--保存到缓存中
		VendorOrderModel.SetVendorOrderInfo( hInfo.hangid, hInfo)
		ThreadManager.OrderUnLock(hInfo.hangid)
		if isOk == false then
			--找出所有配置了的玩家更新挂单
			local List = {}
			local channelList = {}
			local teamList = {}
			local groupList = {}
			local dealType = tonumber(arrData.sellType) == 1 and 1 or 2
			local sqlCase = "select bind_type,channel_name,bind_userid from dy_user_conf where userid="..uInfo.userid.." and deal_type="..dealType.." and is_exist=1"
			mysqlItem:executeQuery(sqlCase)
			while true do 
				local sqlData = mysqlItem:fetch({})
				if sqlData == nil then
					break
				end
				
				if tonumber(sqlData[1]) == 102 then
					List[tonumber(sqlData[3])] = 1
				elseif tonumber(sqlData[1]) == 101 then
					table.insert(channelList, sqlData[2])
				elseif tonumber(sqlData[1]) == 103 then
					table.insert(teamList, sqlData[3])
				elseif tonumber(sqlData[1]) == 104 then
					table.insert(groupList, sqlData[3])
				end
			end
			
			if #channelList > 0 then
				local sqlCase = "select userid from dy_user_info where"
				for k,v in ipairs(channelList) do 
					sqlCase = sqlCase.." channel='"..v.."' "
					if k ~= #channelList then
						sqlCase = sqlCase.."or"
					end
				end
				mysqlItem:executeQuery(sqlCase)
				while true do 
					local sqlData = mysqlItem:fetch()
					if sqlData == nil then
						break
					end
					
					List[tonumber(sqlData)] = 1
				end
			end
			
			if #teamList > 0 then
				local sqlCase = "select userid from ag_relation where"
				for k,v in ipairs(teamList) do 
					sqlCase = sqlCase.." bind_userid="..v.." "
					if k ~= #teamList then
						sqlCase = sqlCase.."or"
					end
					List[tonumber(v)] = 1
				end
				mysqlItem:executeQuery(sqlCase)
				while true do 
					local sqlData = mysqlItem:fetch()
					if sqlData == nil then
						break
					end
					
					List[tonumber(sqlData)] = 1
				end
			end
			
			if #groupList > 0 then
				local tmpMsg = "group_withdraw_switch"
				if tonumber(arrData.sellType) == 2 then
					tmpMsg = "group_behalf_switch"
				end
			
				local condition = ""
				local num = 0
				for k,v in ipairs(groupList) do 
					if num > 0 then
						condition = condition.." or"
					end
					condition = condition.." (group_id="..v.." and "..tmpMsg.."=1)"
					num = num + 1
				end
				
				if num ~= 0 then
					local sqlCase = "select group_user_id from dy_group_member where status=1 and ("..condition..")"
					mysqlItem:executeQuery(sqlCase)
					while true do 
						local sqlData = mysqlItem:fetch()
						if sqlData == nil then
							break
						end
						
						List[tonumber(sqlData)] = 1
					end
				end
			end
			
			local userList = {}
			for k,v in pairs(List) do 
				if true == OnlineModel.CheckOnline(k) then
					table.insert(userList, k)
				end
			end
			local gcUp = msg_order2_pb.gcupdatevendororder()
			local addV = gcUp.volist:add()
			addV:ParseFromString(hInfo:SerializeToString())
			addV.paylist:append(g_humanDefine.user_payList.bank)
			addV.feeRate:append('0.00')
			addV.nickname = uInfo.nickname
			
			gcUp.result = 0
			SendMessage(userList, PacketCode[2064].client, gcUp:ByteSize(),gcUp:SerializeToString())
		end
	end

	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
	
end

--提现审核
--merchantUserId			商户ID
--orderID					订单ID列表逗号隔开
--auditResults				审核结果 0 通过 1 不通过
--remarks					备注
--sellType					1 OTC 2 代付
--sign						签证
function sellOrderAudit(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	if arrData['merchantUserId'] == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['shop_merchant_is_null'][1],g_payCenterDefine.retuen_code['shop_merchant_is_null'][2]
		return luajson.encode(retMsg)
	end
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	
	--检查用户存不存在
	local uInfo = UserInfoModel.GetUserInfo(arrData.merchantUserId)
	if uInfo == nil or (uInfo.usertype == 301 and uInfo.usertype == 300)  then
		retMsg['code'],retMsg['msg'] = 1,"商户不存在"
		return luajson.encode(retMsg)	
		
	end
	
	--检查资金密码
	local ret, msg = UserInfoModel.CheckFundPassword(uInfo, arrData.fundPwd)
	if ret ~= 0 then
		retMsg['code'],retMsg['msg'] = ret,msg
		return luajson.encode(retMsg)	
	end
	
	arrData.sellType = tonumber(arrData.sellType) or 1
	if tonumber(arrData.sellType) == 1 then
		if uInfo.isotc == 1 then
			retMsg['code'],retMsg['msg'] = 1,"该提现方式已被禁止"
			return luajson.encode(retMsg)	
		end
	else
		if uInfo.isbehalfpay == 1 then
			retMsg['code'],retMsg['msg'] = 1,"该提现方式已被禁止"
			return luajson.encode(retMsg)	
		end
	end
	
	local gcUp = msg_order2_pb.gcupdatevendororder()
	local orderIdList = string.split(arrData.orderID, ",")
	local sysPrice = CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.hang_sell, g_marketDefine.currency_type.USDT, true, uInfo.userid)
	
	--先整体余额是否足够
	if tonumber(arrData.auditResults) == 0 then
		local amountCount = 0
		local moneyCount = 0
		local coinPayCount = 0
		for k,v in ipairs(orderIdList) do 
			local vInfo = VendorOrderModel.GetVendorOrderInfo(v)
			if vInfo ~= nil and vInfo.enablestatus == 2 then
				local amount = string.format("%.10f", tostring(vInfo.predictmoney / sysPrice))
				if vInfo.wallettype == 0 then
					amountCount = amountCount + amount
					moneyCount = moneyCount + vInfo.predictmoney
				else
					coinPayCount = coinPayCount + amount
				end
			end
			
		end
		
		--还在进行中的订单
		local sqlCase = "select amount,unsold_order_amount,deal_order_amount,predict_money,unsold_order_money,deal_order_money,wallet_type from dy_vendor_order where userid="..uInfo.userid.." and type=1 and enable_status=1"
		mysqlItem:executeQuery(sqlCase)
		while true do 
			local sqlData = mysqlItem:fetch({})
			if sqlData == nil then
				break
			end
			if tonumber(sqlData[7]) == 0 then
				amountCount = amountCount + (tonumber(sqlData[1]) - tonumber(sqlData[2]) - tonumber(sqlData[3]))
				moneyCount = moneyCount + (tonumber(sqlData[4]) - tonumber(sqlData[5]) - tonumber(sqlData[6]))
			else
				coinPayCount = coinPayCount + (tonumber(sqlData[1]) - tonumber(sqlData[2]) - tonumber(sqlData[3]))
			end
		end
		
		if uInfo.dealcointype == 1 then
			if (tonumber(uInfo.ercfcamount) - tonumber(uInfo.ercfclockamount)) < moneyCount then
				retMsg['code'],retMsg['msg'] = 1,"用户余额不足"
				return luajson.encode(retMsg)
			end
		else
			if (tonumber(uInfo.ercusdtamount) - tonumber(uInfo.ercusdtlockamount)) < amountCount then
				retMsg['code'],retMsg['msg'] = 1,"用户币数量不足"
				return luajson.encode(retMsg)	
			end
		end
		
		
		if (tonumber(uInfo.coinpayusdtamount) - tonumber(uInfo.coinpayusdtlockamount)) < coinPayCount then
			retMsg['code'],retMsg['msg'] = 1,"币付钱包余额不足"
			return luajson.encode(retMsg)	
		end
	end
	
	for k,v in ipairs(orderIdList) do 
		ThreadManager.OrderLock(v)
		local vInfo = VendorOrderModel.GetVendorOrderInfo(v)
		if vInfo == nil then
			retMsg['code'],retMsg['msg'] = 1, "该挂单不存在！"
			return luajson.encode(retMsg)
		end

		if vInfo.enablestatus ~= 2 then
			retMsg['code'],retMsg['msg'] = 1, "该订单已经审核过来"
			return luajson.encode(retMsg)
		end
		if tonumber(arrData.auditResults) == 0 then
			--更新一下价格
			local withdrawType = tonumber(arrData.sellType) == 1 and 0 or 1
			local amount = string.format("%.10f", (vInfo.predictmoney / sysPrice))
			--通过
			local sqlCase = "update dy_vendor_order set enable_status=1, amount="..amount..", price="..sysPrice..",withdraw_type="..withdrawType.." where id="..v
			mysqlItem:execute(sqlCase)
			vInfo.enablestatus = 1
			vInfo.price = tostring(sysPrice)
			vInfo.amount = amount
			vInfo.withdrawtype = withdrawType
			--加到用户自己的挂单列表中区
			VendorOrderModel.addUserVendorOrder(uInfo.userid, vInfo.type, vInfo.hangid)
			
			local isOk = false
			--单笔吃挂单优先派给币商 
			if vInfo.dealmodel == 1 then
				local userID, state = CustomerOrderModel.GetSellOrderUser(uInfo, vInfo)
				if userID == 0 and state == false then
					local sqlCase = "update dy_vendor_order set enable_status=4 where id="..vInfo.hangid
					mysqlItem:execute(sqlCase)
					VendorOrderModel.DelVendOrorderInfo(vInfo.type, vInfo.userid, vInfo.handID)
					retMsg['code'] = 1
					retMsg['msg'] = "未取到交易所价格"	
					return luajson.encode(retMsg)
				end
			
				if userID ~= nil and userID ~= 0 then
					isOk = true
				end
			end
			
			--保存到缓存中
			VendorOrderModel.SetVendorOrderInfo( vInfo.hangid, vInfo )
			VendorOrderModel.addVendorOrder(vInfo.hangid)
			if isOk == false then
				local addV = gcUp.volist:add()
				addV:ParseFromString(vInfo:SerializeToString())
					
				addV.paylist:append(g_humanDefine.user_payList.bank)
				addV.feeRate:append('0.00')
				addV.nickname = uInfo.nickname
			end
		else
			local remarks = arrData.remarks or ""
			--不通过
			local sqlCase = "update dy_vendor_order set enable_status=3, fail_remark='"..remarks.."' where id="..v
			mysqlItem:execute(sqlCase)
			VendorOrderModel.DelVendOrorderInfo(g_marketDefine.hang_sell, uInfo.userid,v)
		end
		
		ThreadManager.OrderUnLock(arrData.orderID)
	end
	
	if #gcUp.volist > 0 then 
	--找出所有配置了的玩家更新挂单
		local List = {}
		local channelList = {}
		local teamList = {}
		local groupList = {}
		local dealType = tonumber(arrData.sellType) == 1 and 1 or 2
		local sqlCase = "select bind_type,channel_name,bind_userid from dy_user_conf where userid="..uInfo.userid.." and deal_type="..dealType.." and is_exist=1"
		mysqlItem:executeQuery(sqlCase)
		while true do 
			local sqlData = mysqlItem:fetch({})
			if sqlData == nil then
				break
			end
			
			if tonumber(sqlData[1]) == 102 then
				List[tonumber(sqlData[3])] = 1
			elseif tonumber(sqlData[1]) == 101 then
				table.insert(channelList, sqlData[2])
			elseif tonumber(sqlData[1]) == 103 then
				table.insert(teamList, sqlData[3])
			elseif tonumber(sqlData[1]) == 104 then
				table.insert(groupList, sqlData[3])
			end
		end
		
		if #channelList > 0 then
			local sqlCase = "select userid from dy_user_info where"
			for k,v in ipairs(channelList) do 
				sqlCase = sqlCase.." channel='"..v.."' "
				if k ~= #channelList then
					sqlCase = sqlCase.."or"
				end
			end

			mysqlItem:executeQuery(sqlCase)
			while true do 
				local sqlData = mysqlItem:fetch()
				if sqlData == nil then
					break
				end
				
				List[tonumber(sqlData)] = 1
			end
		end
		
		if #teamList > 0 then
			local sqlCase = "select userid from ag_relation where"
			for k,v in ipairs(teamList) do 
				sqlCase = sqlCase.." bind_userid="..v.." "
				if k ~= #teamList then
					sqlCase = sqlCase.."or"
				end
				List[tonumber(v)] = 1
			end
			mysqlItem:executeQuery(sqlCase)
			while true do 
				local sqlData = mysqlItem:fetch()
				if sqlData == nil then
					break
				end
					
				List[tonumber(sqlData)] = 1
			end
		end
		if #groupList > 0 then
			local tmpMsg = "group_withdraw_switch"
			if tonumber(arrData.sellType) == 2 then
				tmpMsg = "group_behalf_switch"
			end
		
			local condition = ""
			local num = 0
			for k,v in ipairs(groupList) do 
				if num > 0 then
					condition = condition.." or"
				end
				condition = condition.." (group_id="..v.." and "..tmpMsg.."=1)"
				num = num + 1
			end
			
			if num ~= 0 then
				local sqlCase = "select group_user_id from dy_group_member where status=1 and ("..condition..")"
				mysqlItem:executeQuery(sqlCase)
				while true do 
					local sqlData = mysqlItem:fetch()
					if sqlData == nil then
						break
					end
					
					List[tonumber(sqlData)] = 1
				end
			end
		end
		
		local userList = {}
		for k,v in pairs(List) do 
			if true == OnlineModel.CheckOnline(k) then
				table.insert(userList, k)
			end
		end
		gcUp.result = 0
		SendMessage(userList, PacketCode[2064].client, gcUp:ByteSize(),gcUp:SerializeToString())
	end
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
	
end

--提现取消
--orderID					订单ID
--sign					签证
function sellOrderCancel(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	ThreadManager.OrderLock(arrData.orderID)
	local hInfo = VendorOrderModel.GetVendorOrderInfo(arrData.orderID)
	if hInfo == nil  then
		ThreadManager.OrderUnLock(hInfo.hangid)
		retMsg['code'],retMsg['msg'] = ReturnCode["deal_order_not_exist"][1],ReturnCode["deal_order_not_exist"][2]
		return luajson.encode(retMsg)
	end
	
	--还有正在进行中的订单
	if hInfo.unsoldordernum > 0 then
		ThreadManager.OrderUnLock(hInfo.hangid)
		retMsg['code'],retMsg['msg'] = ReturnCode["hang_is_working"][1],ReturnCode["hang_is_working"][2]
		return luajson.encode(retMsg)	
	end
	
	local uInfo = UserInfoModel.GetUserInfo(hInfo.userid)
	if uInfo == nil then
		ThreadManager.OrderUnLock(hInfo.hangid)
		retMsg['code'],retMsg['msg'] = 1,"用户不存在"
		return luajson.encode(retMsg)	
	end
	
	if hInfo.enablestatus ~= 1 then
		ThreadManager.OrderUnLock(hInfo.hangid)
		retMsg['code'],retMsg['msg'] = 1, "该订单已经审核过来"
		return luajson.encode(retMsg)
	end
	
	hInfo.enablestatus = 4
	local sqlCase = "update dy_vendor_order set enable_status="..hInfo.enablestatus.." where id="..hInfo.hangid
	mysqlItem:execute(sqlCase)	
	
	--检查是否还有进行中的订单
	--挂单已经失效
	VendorOrderModel.DelVendOrorderInfo(hInfo.type, uInfo.userid, hInfo.hangid)
	
	ThreadManager.OrderUnLock(hInfo.hangid)
	
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
end

--卖币申诉
--merchantUserId			商户ID
--orderID					订单ID
--remarks					申诉原因
--certificateURL			凭证
--sign						签证
function sellOrderAppeal(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	if arrData['merchantUserId'] == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['shop_merchant_is_null'][1],g_payCenterDefine.retuen_code['shop_merchant_is_null'][2]
		return luajson.encode(retMsg)
	end
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	--检查用户存不存在
	local uInfo = UserInfoModel.GetUserInfo(arrData.merchantUserId)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1,"商户不存在"
		return luajson.encode(retMsg)	
	end
	
	--检查资金密码
	local ret, msg = UserInfoModel.CheckFundPassword(uInfo, arrData.fundPwd)
	if ret ~= 0 then
		retMsg['code'],retMsg['msg'] = ret,msg
		return luajson.encode(retMsg)	
	end
	
	local orderID = tonumber(arrData.orderID)
	ThreadManager.DealLock(orderID) 
	local cInfo = CustomerOrderModel.GetCustomerOrderInfo(orderID)
	if cInfo == nil then
		ThreadManager.DealUnLock(orderID) 
		retMsg['code'],retMsg['msg'] = ReturnCode["order_not_exist"][1],ReturnCode["order_not_exist"][2]
		return luajson.encode(retMsg)
	end
	
	local aInfo = st_order_pb.apppealinfo()
	
	aInfo.orderid = 0
	aInfo.reason = arrData.remarks
	aInfo.description = arrData.remarks
	aInfo.proofurl = arrData.certificateURL
	
	local ret, msg = VendorOrderService.AppealOrder(uInfo, cInfo,aInfo)
	if ret ~= 0 then
		ThreadManager.DealUnLock(orderID) 
		retMsg['code'],retMsg['msg'] = ret, msg
		return luajson.encode(retMsg)
	end
	
	ThreadManager.DealUnLock(orderID) 
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
end

--卖币放行
--merchantUserId			商户ID
--orderID					订单ID
--sign						签证
function sellOrderPass(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	if arrData['merchantUserId'] == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['shop_merchant_is_null'][1],g_payCenterDefine.retuen_code['shop_merchant_is_null'][2]
		return luajson.encode(retMsg)
	end
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	--检查用户存不存在
	local uInfo = UserInfoModel.GetUserInfo(arrData.merchantUserId)
	if uInfo == nil or (uInfo.usertype == 301 and uInfo.usertype == 300)  then
		retMsg['code'],retMsg['msg'] = 1,"商户不存在"
		return luajson.encode(retMsg)	
		
	end
	
	--检查资金密码
	local ret, msg = UserInfoModel.CheckFundPassword(uInfo, arrData.fundPwd)
	if ret ~= 0 then
		retMsg['code'],retMsg['msg'] = ret, msg
		return luajson.encode(retMsg)	
	end
	
	ThreadManager.DealLock(arrData.orderID)
	local cInfo = CustomerOrderModel.GetCustomerOrderInfo( arrData.orderID ) 
	if cInfo == nil then
		ThreadManager.DealUnLock(arrData.orderID)
		retMsg['code'],retMsg['msg'] = ReturnCode["order_not_exist"][1], ReturnCode["order_not_exist"][2]
		return luajson.encode(retMsg)
	end
	
	--检查订单状态
	if cInfo.status ~= g_marketDefine.deal_status_pay and cInfo.status ~= g_marketDefine.deal_status_appeal then
		retMsg['code'],retMsg['msg'] = ReturnCode["deal_order_not_match"][1], ReturnCode["deal_order_not_match"][2]
		return luajson.encode(retMsg)
	end	
	
	local payee = 0
	if cInfo == g_marketDefine.deal_buy then
		payee = cInfo.customeruserid
	else
		payee = cInfo.vendoruserid
	end
	if payee ~= uInfo.userid then
		retMsg['code'],retMsg['msg'] =1, "您不是收款人无法完成放行操作"
		return luajson.encode(retMsg)
	end	
	
	local ret, msg = VendorOrderService.PassOrder(uInfo, cInfo)
	if ret ~= nil then
		ThreadManager.DealUnLock(arrData.orderID)
		ThreadManager.DealUnLock(arrData.orderID)
		retMsg['code'],retMsg['msg'] = ret, msg
		return luajson.encode(retMsg)	
	end
	
	ThreadManager.DealUnLock(arrData.orderID)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
	
end

--申诉裁决
--id						申诉订单ID
--ruling_result				裁决结果 1-买放胜 2-卖方胜
--buy_punishment			处理买方 1-不惩罚 2-账户冻结24小时 3-永久冻结账户
--sell_punishment			处理卖方 1-不惩罚 2-账户冻结24小时 3-永久冻结账户
--sys_remark				备注
--sign						签证
function appealRuling(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	ThreadManager.AppealLock(arrData.id)
	local sqlCase = "select * from dy_order_appeal where id="..arrData.id
	mysqlItem:executeQuery(sqlCase)

	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		retMsg['code'],retMsg['msg'] = 1, "申诉单不存在"
		ThreadManager.AppealUnLock(arrData.id)
		return luajson.encode(retMsg)	
	end

	local dealid = tonumber(sqlData[2])
	ThreadManager.OrderLock(dealid)

	if tonumber(sqlData[11]) ~= 1 then
		retMsg['code'],retMsg['msg'] = 1, "申诉单已处理过了"
		ThreadManager.AppealUnLock(arrData.id)
		ThreadManager.OrderUnLock(dealid)
		return luajson.encode(retMsg)	
	end

	
	local cInfo = CustomerOrderModel.GetCustomerOrderInfo(dealid)
	if cInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "申诉单对应的交易单不存在"
		ThreadManager.AppealUnLock(arrData.id)
		ThreadManager.OrderUnLock(dealid)
		return luajson.encode(retMsg)	
	end

	if cInfo.status ~= g_marketDefine.deal_status_appeal then
		retMsg['code'],retMsg['msg'] = 1, "申诉单对应的交易单状态不能申诉"
		ThreadManager.AppealUnLock(arrData.id)
		ThreadManager.OrderUnLock(dealid)
		return luajson.encode(retMsg)	
	end
	
	
	local payee = {}			--收款人
	local payer = {}			--付款人
	local buyfeeCount = tonumber(cInfo.buyfee) or 0	--买手续费
	local buyfeeRate = tonumber(cInfo.buyfeerate) or 0--卖手续费
	
	local sellfeeCount = tonumber(cInfo.sellfee)	--卖手续费
	local sellfeeRate = tonumber(cInfo.sellfeerate)	--卖手续费
	local payType = tonumber(cInfo.payidlist) 			--支付方式
	if cInfo.type == g_marketDefine.deal_buy then
		--买单
		payee = UserInfoModel.GetUserInfo(cInfo.vendoruserid)
		payer = UserInfoModel.GetUserInfo(cInfo.customeruserid)
	else
		--卖单
		payee = UserInfoModel.GetUserInfo(cInfo.customeruserid)
		payer = UserInfoModel.GetUserInfo(cInfo.vendoruserid)
	end
	
	--币数量
	local amount = cInfo.amount 
	local money = tonumber(cInfo.money)
		
	--判决
	if tonumber(arrData.ruling_result) == 1 then
		--买方胜，放行
		VendorOrderService.PassOrder(nil, cInfo)	
		--修改为申诉完成
		CustomerOrderModel.UpdateStatus(cInfo, g_marketDefine.deal_status_appeal_finish)
	else
		--卖方胜， 取消订单
		
		--解冻订单所冻结的金额
		local sellfeeCount = 0
		local sellfeeMoney = 0
		if cInfo.dealtype == 300 then
			sellfeeCount = tonumber(cInfo.fee)
			sellfeeMoney = tonumber(cInfo.feemoney)
		end
		
		if cInfo.wallettype == 0 then
			UserInfoModel.DecErcUsdtLockAmount(payee, (cInfo.amount +  sellfeeCount), cInfo.dealid, cInfo.type, "判决卖方胜， 解除卖方资金冻结")
			if payee.dealcointype == 1 then
				--如果用户是法币用户就结算一下法币账户
				UserInfoModel.DecErcFCLockAmount(payee, money + sellfeeMoney, cInfo.dealid, 0, "判决卖方胜， 解除卖方资金冻结")
			end
		else
			UserInfoModel.DecErcCoinPayUsdtLockAmount(payee, (cInfo.amount +  sellfeeCount), cInfo.dealid, cInfo.type, "判决卖方胜， 解除卖方资金冻结")
		end
		
		--更新挂单的情况
		local voItem = VendorOrderModel.GetVendorOrderInfo(cInfo.vendororderid)
		if voItem ~= nil then
			voItem.unsoldordernum = voItem.unsoldordernum - 1
			voItem.unsoldorderamount = tostring(tonumber(voItem.unsoldorderamount) - tonumber(cInfo.amount))
			voItem.unsoldordermoney = tostring((tonumber(voItem.unsoldordermoney) or 0) - tonumber(cInfo.money))
			voItem.cancelnum = voItem.cancelnum + 1
			local sqlCase = "update dy_vendor_order set unsold_order_num=unsold_order_num-1, unsold_order_amount=unsold_order_amount-"
				..tonumber(voItem.amount).." and cancel_order_num=cancel_order_num+1"..", unsold_order_money=unsold_order_money-"..tonumber(cInfo.money)
			if voItem.dealmodel == 1 then
				local restTime = TimeUtils.GetTime() + g_marketDefine.order_time_rest
				voItem.resttime = TimeUtils.GetTimeString(restTime)
				sqlCase = sqlCase..",rest_time='"..voItem.resttime.."'"
			end
			VendorOrderModel.SetVendorOrderInfo(voItem.hangid, voItem)	
			sqlCase = sqlCase.." where id="..voItem.hangid
			mysqlItem:execute(sqlCase)
		end
		
		--先前端更新用户的冻结金币
		UserInfoModel.SendErcUsdtLockAmount(payee)
		
		--更新玩家的大厅的待处理列表
		local userList = {cInfo.vendoruserid, cInfo.customeruserid}
		NoticeServices.DealCancel(userList)
		
		--修改为申诉取消
		CustomerOrderModel.UpdateStatus(cInfo, g_marketDefine.deal_status_appeal_cancel)
	end
	
	--处罚
	--处罚买方
	if tonumber(arrData.buy_punishment) > 1 then
		local banTime = 0
		local msg = ""
		if tonumber(arrData.buy_punishment) == 2 then
			tNumBer = TimeUtils.GetTime() + 86400
			banTime = TimeUtils.GetTimeString(tNumBer)
			msg = "账号已被封禁，解封时间："..banTime
		elseif tonumber(arrData.buy_punishment) == 3 then
			banTime = "-1"
			msg = "账号已被永久封禁"
		end
		payer.bantime = banTime
		UserInfoModel.SetUserInfo(payer)
		local sqlData = "update dy_user_info set ban_time='"..payer.bantime.."' where userid="..payer.userid
		mysqlItem:execute(sqlData)

		local gckit = msg_human_pb.gckituser()
		gckit.result = 0
		gckit.kittype = 1
		gckit.msg = msg
		SendMessage(payer.userid, PacketCode[1006].client, gckit:ByteSize(), gckit:SerializeToString())
	end
	
	--处罚卖方
	if tonumber(arrData.sell_punishment) > 1 then
		local banTime = 0
		local msg = ""
		if tonumber(arrData.sell_punishment) == 2 then
			tNumBer = TimeUtils.GetTime() + 86400
			banTime = TimeUtils.GetTimeString(tNumBer)
			msg = "账号已被封禁，解封时间："..banTime
		elseif tonumber(arrData.sell_punishment) == 3 then
			banTime = "-1"
			msg = "账号已被永久封禁"
		end
		payee.bantime = banTime
		UserInfoModel.SetUserInfo(payee)
		local sqlData = "update dy_user_info set ban_time='"..payee.bantime.."' where userid="..payee.userid
		mysqlItem:execute(sqlData)

		local gckit = msg_human_pb.gckituser()
		gckit.result = 0
		gckit.kittype = 1
		gckit.msg = msg
		SendMessage(payee.userid, PacketCode[1006].client, gckit:ByteSize(), gckit:SerializeToString())
	end
	
	
	local sqlCase = "update dy_order_appeal set status=4 , ruling_result="..arrData.ruling_result..", buy_punishment="..arrData.buy_punishment
	..", sell_punishment="..arrData.sell_punishment..", sys_remark='"..arrData.sys_remark.."' where id="..arrData.id
	mysqlItem:execute(sqlCase)
	
	ThreadManager.AppealUnLock(arrData.id)
	ThreadManager.OrderUnLock(dealid)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
end

--提币
--merchantUserId			商户ID
--toaddress					提币地址
--currencyType				币类型
--currencyAmount			币数量
--price						价格
--fundPwd					资金密码
--addrtype					地址类型
--wallet_type               钱包类型 0 基础钱包 1 币付钱包
--sign						签证
function withdraw(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	if arrData['merchantUserId'] == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['shop_merchant_is_null'][1],g_payCenterDefine.retuen_code['shop_merchant_is_null'][2]
		return luajson.encode(retMsg)
	end
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	--检查用户存不存在
	local uInfo = UserInfoModel.GetUserInfo(arrData.merchantUserId)
	if uInfo == nil or (uInfo.usertype == 301 and uInfo.usertype == 300)  then
		retMsg['code'],retMsg['msg'] = 1,"商户不存在"
		return luajson.encode(retMsg)	
	end
	
	--检查用户是否锁定
	if uInfo.islock == 1 then
		retMsg['code'],retMsg['msg'] = 1, "你已经被锁定无法进行该操作"
		return luajson.encode(retMsg)	
	end
	
	--检查资金密码
	local ret, msg = UserInfoModel.CheckFundPassword(uInfo, arrData.fundPwd)
	if ret ~= 0 then
		retMsg['code'],retMsg['msg'] = ret,msg
		return luajson.encode(retMsg)	
	end
	
	--看看有没有地址
	local addrList = {}
	local sqlCase = "select coin_addr,addr_type,addr_name from dy_user_address_pre where user_id="..uInfo.userid.." and status=0"
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlDate = mysqlItem:fetch({})
		if sqlDate == nil then
			break
		end
		
		local tmp = {}
		tmp["address"] = sqlDate[1]
		tmp["addrtype"] = tonumber(sqlDate[2])
		tmp["addrname"] = sqlDate[3]
		table.insert(addrList, tmp)
	end
	
	for _, addrType in ipairs(g_marketDefine.addr_type_list) do 
		local isOk = false 
		for k,v in ipairs(addrList)do 
			if v.addrtype == addrType then
				isOk = true
			end
		end
		
		if isOk == false then
			if addrType == 101 then
				local address = AddressPreModel.GetNewAddress(uInfo.channel) or "" 
				if address ~= "" then
					AddressPreModel.UpdatePreAddress(address, uInfo.channel, uInfo.userid)
				end
			elseif addrType == 102 then
				local address = AddressPreModel.GetNewTRCAddress(uInfo.channel) or "" 
				if address ~= "" then
					AddressPreModel.UpdatePreTRCAddress(address, uInfo.channel, uInfo.userid)
				end
			end
		end
	end
	
	local wInfo = {}
	wInfo.toaddress = arrData.toaddress
	wInfo.currencytype = arrData.currencyType
	wInfo.subject = ""
	wInfo.version = ""
	wInfo.notifyurl = ""
	wInfo.body = ""
	wInfo.outtradeno = ""
	wInfo.specifictype = 2
	wInfo.addrtype = tonumber(arrData.addrtype) or 0
	wInfo.wallettype = tonumber(arrData.wallet_type) or 0
	
	local sysPrice = CoinInfoService.withdrawCoinPrice(uInfo.channel, g_marketDefine.currency_type.USDT, true, uInfo.userid)
	--if tonumber(arrData.price) ~= tonumber(tostring(sysPrice)) then
	--	retMsg['code'],retMsg['msg'] = 1, "价格已经刷新，请重试！"
	--	return luajson.encode(retMsg)	
	--end
	
	if wInfo.wallettype == 0 then
		
		if uInfo.dealcointype == 1 then
			if (tonumber(uInfo.ercfcamount) - tonumber(uInfo.ercfclockamount)) <= 0 then
				retMsg['code'],retMsg['msg'] = 1, "法币数量数量不足"
				return luajson.encode(retMsg)	
			end
			--sysPrice = (tonumber(uInfo.ercfcamount) - tonumber(uInfo.ercfclockamount)) / (tonumber(uInfo.ercusdtamount) - tonumber(uInfo.ercusdtlockamount))
			--sysPrice = math.ceil(sysPrice*10000)/10000
			
		else
			if (tonumber(uInfo.ercusdtamount) - tonumber(uInfo.ercusdtlockamount)) <= 0 then
				retMsg['code'],retMsg['msg'] = 1, "数字货币数量不足"
				return luajson.encode(retMsg)	
			end
		
		end
	else
		if (tonumber(uInfo.coinpayusdtamount) - tonumber(uInfo.coinpayusdtlockamount)) <= 0 then
			retMsg['code'],retMsg['msg'] = 1, "币支付钱包余额不足"
			return luajson.encode(retMsg)	
		end
	end
	
	wInfo.price = tonumber(sysPrice)
	
	--if uInfo.dealcointype == 0 then
		wInfo.currencyamount = arrData.currencyAmount
		wInfo.money = tonumber(arrData.currencyAmount) * wInfo.price
	--else
	--	wInfo.currencyamount = tonumber(arrData.currencyAmount) / wInfo.price
	--	wInfo.money = arrData.currencyAmount
	--end
	
	local ret, msg = VendorOrderService.WithdrawCurrency(uInfo, wInfo, 1, nil, true, uInfo.userid, uInfo.nickname,"")
	if ret ~= 0 then
		retMsg['code'],retMsg['msg'] = ret, msg
		return luajson.encode(retMsg)	
	end
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
end

--提币审核
--orderID						订单ID
--remarks					备注 
--auditResults   					审核结果 0 通过 1 不通过
--sign						签证
function withdrawAudit(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local orederList = string.split(arrData.orderID, ",")
	
	for k,v in ipairs(orederList) do 
		
		local ID = tonumber(v) or 0
		ThreadManager.UserWithdrawLock(ID)
		--查找订单是否存在
		local sqlCase = "select amount,userid,recd_status,money,wallet_type from dy_block_chain_trans where id="..ID
		mysqlItem:execute(sqlCase)
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			ThreadManager.UserWithdrawUnLock(ID)
			retMsg['code'],retMsg['msg'] = 1, "提现单不存在"
			return luajson.encode(retMsg)	
		end
		
		if tonumber(sqlData[3]) ~= 101 then
			ThreadManager.UserWithdrawUnLock(ID)
			retMsg['code'],retMsg['msg'] = 1, "该状态不能商户审核"
			return luajson.encode(retMsg)
		end
		
		local amount = tonumber(sqlData[1])
		local money = tonumber(sqlData[4])
		local userid =  tonumber(sqlData[2])
		local walletType =  tonumber(sqlData[5])
		local uInfo = UserInfoModel.GetUserInfo(userid)
		if uInfo == nil then
			ThreadManager.UserWithdrawUnLock(ID)
			retMsg['code'],retMsg['msg'] = 1, "用户不存在"
			return luajson.encode(retMsg)	
		end
		
		
		
		if tonumber(arrData.auditResults) == 0 then
			--通过
			--扣除资金
			local sqlCase = "update dy_block_chain_trans set recd_status=103 where id="..ID
			mysqlItem:execute(sqlCase)
			
		else
			--不通过
			--解掉冻结的押金
			local sqlCase = "update dy_block_chain_trans set recd_status=102, remark='"..arrData.remarks.."' where id="..ID
			mysqlItem:execute(sqlCase)
			if walletType == 0 then
				UserInfoModel.DecErcUsdtLockAmount(uInfo, amount, ID, 0, "提币审核不通过，解除资金冻结")	
				UserInfoModel.SendErcUsdtLockAmount(uInfo)	
				if uInfo.dealcointype == 1 then
					--如果用户是法币用户就结算一下法币账户
					UserInfoModel.DecErcFCLockAmount(uInfo, money, ID, 0, "提币审核不通过，解除资金冻结")
				end
			else
				UserInfoModel.DecErcCoinPayUsdtLockAmount(uInfo, amount, ID, 0, "提币审核不通过，解除资金冻结")
			end
		end
		ThreadManager.UserWithdrawUnLock(ID)
	end
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
end

--提币审核
--id						订单ID
--remark					备注 
--status   					审核结果 101 通过 103 不通过
--sign						签证					
function withdrawAuditPlatform(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local ID = tonumber(arrData.id) or 0
	ThreadManager.UserWithdrawLock(ID)
	--查找订单是否存在
	local sqlCase = "select amount,userid,recd_status,specific_type,to_addr,from_addr,tx_fee,get_amount,money,price,to_userid,wallet_type from dy_block_chain_trans where id="..ID
	mysqlItem:execute(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		ThreadManager.UserWithdrawUnLock(ID)
		retMsg['code'],retMsg['msg'] = 1, "提现单不存在"
		return luajson.encode(retMsg)	
	end
	
	if tonumber(sqlData[3]) ~= 103 then
		ThreadManager.UserWithdrawUnLock(ID)
		retMsg['code'],retMsg['msg'] = 1, "该状态不能平台审核"
		return luajson.encode(retMsg)
	end
	
	local amount = tonumber(sqlData[1])
	local money = tonumber(sqlData[9])
	local userid =  tonumber(sqlData[2])
	local specificType = tonumber(sqlData[4])
	local toAddr = sqlData[5]
	local fromAddr = sqlData[6]
	local txFee = tonumber(sqlData[7])
	local getAmount = tonumber(sqlData[8])
	local price = tonumber(sqlData[10])
	local toUserID = tonumber(sqlData[11])
	local walletType = tonumber(sqlData[12])
	local uInfo = UserInfoModel.GetUserInfo(userid)
	if uInfo == nil then
		ThreadManager.UserWithdrawUnLock(ID)
		retMsg['code'],retMsg['msg'] = 1, "用户不存在"
		return luajson.encode(retMsg)	
	end
	
	if specificType ~= 2 and specificType ~= 4 then
		ThreadManager.UserWithdrawUnLock(ID)
		retMsg['code'],retMsg['msg'] = 1, "订单类型错误"
		return luajson.encode(retMsg)	
	end
	
	if tonumber(arrData.status) == 101 then
	
		if specificType == 2 then 
			--通过,到打币中
			local sqlCase = "update dy_block_chain_trans set recd_status=105 where id="..ID
			mysqlItem:execute(sqlCase)

		elseif specificType == 4 then
			--[[
			local sqlCase = "select user_id from dy_user_address_pre where coin_addr='"..toAddr.."'"
			mysqlItem:executeQuery(sqlCase)
			local toUserID = mysqlItem:fetch()
			toUserID = tonumber(toUserID) or 0
			]]
			local toInfo = UserInfoModel.GetUserInfo(toUserID)
			if toInfo == nil then 
				ThreadManager.UserWithdrawUnLock(ID)
				retMsg['code'],retMsg['msg'] = 1, "转账地址错误"
				return luajson.encode(retMsg)	
			end
			
			local tString = TimeUtils.GetTimeString()
			--转给玩家
			local sqlCase = "INSERT INTO dy_block_chain_trans(tx_type,from_addr,to_addr,userid,amount,tx_fee,recd_status,get_amount,specific_type,tx_time,channel,platform_id)"
						.." VALUES('1','"..fromAddr.."','"..toAddr.."',"..toUserID..","..amount..","..txFee..",106,"..getAmount..",3,'"..tString.."','"..toInfo.channel.."',"..toInfo.platformid..")"
			mysqlItem:execute(sqlCase)
			
			local sqlCase = "select id from dy_block_chain_trans where userid='"..toUserID.."' and specific_type=3 and get_amount="..getAmount.." and recd_status=106 and tx_time='"..tString.."'"
			mysqlItem:executeQuery(sqlCase)
			local wID = mysqlItem:fetch()
			if wID == nil then
				ThreadManager.UserWithdrawUnLock(ID)
				retMsg['code'],retMsg['msg'] = 1, "转账审核失败"
				return luajson.encode(retMsg)	
			end
			--通过
			--转账扣除资金
			local sqlCase = "update dy_block_chain_trans set recd_status=106 where id="..ID
			mysqlItem:execute(sqlCase)
			if walletType == 0 then
				UserInfoModel.DecErcUsdtLockAmount(uInfo, amount, ID, 0, "转账审核通过，解除资金冻结")
				UserInfoModel.SendErcUsdtLockAmount(uInfo)	
				UserInfoModel.DecErcUsdtAmount(uInfo, amount, ID, 0, "转账审核通过，扣除资金", g_humanDefine.fund_details.type_transfer_out,money)	
				UserInfoModel.SendErcUsdtAmount(uInfo)
				if uInfo.dealcointype == 1 then
					UserInfoModel.DecErcFCLockAmount(uInfo, money, ID, 0, "转账审核通过，解除资金冻结")
					UserInfoModel.DecErcFCAmount(uInfo, money, ID, 0, "转账审核通过，扣除资金", g_humanDefine.fund_details.type_transfer_out)	
				end
			else
				UserInfoModel.DecErcCoinPayUsdtLockAmount(uInfo, amount, ID, 0, "转账审核通过，解除资金冻结")
				UserInfoModel.DecErcCoinPayUsdtAmount(uInfo, amount, ID, 0, "转账审核通过，扣除资金", g_humanDefine.fund_details.type_transfer_out,money)	
			end
			
			UserInfoModel.AddErcUsdtAmount(toInfo, getAmount, wID, 0, "转账进来，增加资金",g_humanDefine.fund_details.type_transfer_in, (getAmount*price))
			UserInfoModel.SendErcUsdtAmount(toInfo)
			
			if toInfo.dealcointype == 1 then
				--结算货币类型如果是法币把法币账户也结算一下
				UserInfoModel.AddErcFCAmount(toInfo, (getAmount*price), wID, 0, "转账进来，增加资金", g_humanDefine.fund_details.type_transfer_in)
			end
			
			
			local sysInfo = UserInfoModel.GetSysUserInfo(uInfo.platformid)
			if sysInfo ~= nil then
				local allPrice = CoinInfoService.Erc20USDTPrice("ALL", g_marketDefine.deal_buy, g_marketDefine.currency_type.USDT, false, 0)
				UserInfoModel.AddErcUsdtAmount(sysInfo, txFee, ID, 0, "转账手续费， 归入系统用户", g_humanDefine.fund_details.type_handling_fee,allPrice*txFee)
			
				local rate = txFee / amount
				local sqlCase = "insert into log_commission_details(order_id,order_type,currency_type,source_userid,source_nick,target_userid,target_nick,amount,"
				.."dea_price,money_count,free_rate,free_count,commission_rate,commission_count,platform_id) values("..wID..",2,2003,"
				..uInfo.userid..",'"..uInfo.nickname.."',"..sysInfo.userid..",'"..sysInfo.nickname.."',"..amount..",0,0,"..rate..","..txFee..","..rate..","..txFee..","..uInfo.platformid..")"
				mysqlItem:execute(sqlCase)
				LogDispatch.sysIncome(uInfo.userid, txFee)
			end
			
			LogDispatch.userTransferInAndOut(uInfo.userid, toInfo.userid, amount, txFee, getAmount)
		end
		
		
	elseif tonumber(arrData.status) == 103 then
		--不通过
		--解掉冻结的押金
		local sqlCase = "update dy_block_chain_trans set recd_status=104, remark='"..arrData.remark.."' where id="..ID
		mysqlItem:execute(sqlCase)
		if walletType == 0 then
			UserInfoModel.DecErcUsdtLockAmount(uInfo, amount, ID, 0, "提币审核不通过，解除资金冻结")	
			UserInfoModel.SendErcUsdtLockAmount(uInfo)	
			
			if uInfo.dealcointype == 1 then
				--如果用户是法币用户就结算一下法币账户
				UserInfoModel.DecErcFCLockAmount(uInfo, money, ID, 0, "提币审核不通过，解除资金冻结")	
			end
		else
			UserInfoModel.DecErcCoinPayUsdtLockAmount(uInfo, amount, ID, 0, "提币审核不通过，解除资金冻结")	
		
		end
	end
	ThreadManager.UserWithdrawUnLock(ID)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)
	
	
end

--确定打币
--id						订单ID
--txid						转账凭证
--coin_payer				打币人ID
--coin_payer_nickname		打币人昵称
--coin_payer_remark			打币人备注
function coinpay(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local ID = tonumber(arrData.id) or 0
	ThreadManager.UserWithdrawLock(ID)
	--查找订单是否存在
	local sqlCase = "select amount,userid,recd_status,specific_type,tx_fee,get_amount,money,wallet_type from dy_block_chain_trans where id="..ID
	mysqlItem:execute(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		ThreadManager.UserWithdrawUnLock(ID)
		retMsg['code'],retMsg['msg'] = 1, "提现单不存在"
		return luajson.encode(retMsg)	
	end
	
	if tonumber(sqlData[3]) ~= 105 then
		ThreadManager.UserWithdrawUnLock(ID)
		retMsg['code'],retMsg['msg'] = 1, "该状态不能确定打币"
		return luajson.encode(retMsg)
	end
	
	local amount = tonumber(sqlData[1])
	local money = tonumber(sqlData[7])
	local userid =  tonumber(sqlData[2])
	local specificType = tonumber(sqlData[4])
	local txFee = tonumber(sqlData[5])
	local getAmount = tonumber(sqlData[6])
	local walletType = tonumber(sqlData[8])
	local uInfo = UserInfoModel.GetUserInfo(userid)
	if uInfo == nil then
		ThreadManager.UserWithdrawUnLock(ID)
		retMsg['code'],retMsg['msg'] = 1, "用户不存在"
		return luajson.encode(retMsg)	
	end
	
	if specificType ~= 2 then
		ThreadManager.UserWithdrawUnLock(ID)
		retMsg['code'],retMsg['msg'] = 1, "订单类型错误"
		return luajson.encode(retMsg)	
	end
	
	local sqlCase = "update dy_block_chain_trans set recd_status=106,txid='"..arrData.txid.."',coin_payer="..arrData.coin_payer
					..",coin_payer_nickname='"..arrData.coin_payer_nickname.."',coin_payer_remark='"..arrData.coin_payer_remark.."' where id="..ID
	mysqlItem:execute(sqlCase)
	
	if walletType == 0 then
		UserInfoModel.DecErcUsdtLockAmount(uInfo, amount, ID, 0, "提币打币完成，解除资金冻结")
		UserInfoModel.SendErcUsdtLockAmount(uInfo)	
		UserInfoModel.DecErcUsdtAmount(uInfo, amount, ID, 0, "提币打币完成， 扣除资金", g_humanDefine.fund_details.type_withdraw_currency,money)	
		UserInfoModel.SendErcUsdtAmount(uInfo)
		
		if uInfo.dealcointype == 1 then
			UserInfoModel.DecErcFCLockAmount(uInfo, money, ID, 0, "提币打币完成，解除资金冻结")
			UserInfoModel.DecErcFCAmount(uInfo, money, ID, 0, "提币打币完成， 扣除资金", g_humanDefine.fund_details.type_withdraw_currency)	
		
		end
	else
		UserInfoModel.DecErcCoinPayUsdtLockAmount(uInfo, amount, ID, 0, "提币打币完成，解除资金冻结")
		UserInfoModel.DecErcCoinPayUsdtAmount(uInfo, amount, ID, 0, "提币打币完成， 扣除资金", g_humanDefine.fund_details.type_withdraw_currency,money)	
	end
	
	local sysInfo = UserInfoModel.GetSysUserInfo(uInfo.platformid)
	if sysInfo ~= nil then
		local allPrice = CoinInfoService.Erc20USDTPrice("ALL", g_marketDefine.deal_buy, g_marketDefine.currency_type.USDT, false, 0)
		UserInfoModel.AddErcUsdtAmount(sysInfo, txFee, ID, 0, "提币手续费， 归入系统用户", g_humanDefine.fund_details.type_handling_fee,allPrice*txFee)
		
		local rate = txFee / amount
		local sqlCase = "insert into log_commission_details(order_id,order_type,currency_type,source_userid,source_nick,target_userid,target_nick,amount,"
			.."dea_price,money_count,free_rate,free_count,commission_rate,commission_count,platform_id) values("..ID..",3,2003,"
			..uInfo.userid..",'"..uInfo.nickname.."',"..sysInfo.userid..",'"..sysInfo.nickname.."',"..amount..",0,0,"..rate..","..txFee..","..rate..","..txFee..","..uInfo.platformid..")"
		mysqlItem:execute(sqlCase)
		LogDispatch.sysIncome(uInfo.userid, txFee)
	end
	
	ThreadManager.UserWithdrawUnLock(ID)
	
	LogDispatch.userExtractCurrency(uInfo.userid, amount, txFee, getAmount)
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
	
end

--确定入账
--id						订单ID
--sign						签证					
function confirmBill(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	
	if true then
		retMsg['code'],retMsg['msg'] = 1, "接口已被弃用"
		return luajson.encode(retMsg)
	end
	
	local arrData = luajson.decode(rcvData)
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local sqlCase = "select status,remarks,userid,user_txid from dy_trusteeship_wallet_order where order_id="..arrData.id
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlCase == nil then
		retMsg['code'],retMsg['msg'] = 1, "订单不存在"
		return luajson.encode(retMsg)	
	end
	local status = tonumber(sqlData[1])
	local remarks = sqlData[2]
	local userid = sqlData[3]
	local user_txid = sqlData[4]
	
	local uInfo = UserInfoModel.GetUserInfo(userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "用户不存在"
		return luajson.encode(retMsg)	
	end
	
	if status ~= 0 then
		retMsg['code'],retMsg['msg'] = 1, "订单状态不符"
		return luajson.encode(retMsg)	
	end
	
	if remarks ~= "金额不符" then
		retMsg['code'],retMsg['msg'] = 1, "只有金额不符的订单才可以入账"
		return luajson.encode(retMsg)	
	end
	
	local sqlCase = "select amount from dy_block_chain_trans where tx_id='"..user_txid.."'".." and hosting_adds=1 and hosting_status=1 and tx_status=0"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		retMsg['code'],retMsg['msg'] = 1, "找不到TXID"
		return luajson.encode(retMsg)
	end
	
	local amount = tonumber(sqlData)
	local price = CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.deal_buy, g_marketDefine.currency_type.USDT, true, uInfo.userid)
	local money = price * amount
	
	local sqlCase = "update dy_block_chain_trans set hosting_status=2, related_order='"..arrData.id.."', userid="..uInfo.userid
		..", money="..money..", price="..price.." where tx_id='"..user_txid.."'"
	mysqlItem:executeQuery(sqlCase)
	
	local sqlCase = "update dy_trusteeship_wallet_order set status=1 , actual_amount="..amount.." where order_id='"..arrData.id.."'"
	mysqlItem:execute(sqlCase)
	
	--加钱
	UserInfoModel.AddErcUsdtAmount(uInfo, amount, arrData.id, 0, "托管钱包充值，增加资金", g_humanDefine.fund_details.type_recharge_currency,money)
	UserInfoModel.SendErcUsdtAmount(uInfo)
	LogDispatch.userRecharge(uInfo.userid, amount, 0,amount, true)
	
	if uInfo.dealcointype == 1 then
		--结算货币类型如果是法币把法币账户也结算一下
		UserInfoModel.AddErcFCAmount(uInfo, money, arrData.id, 0, "托管钱包充值，增加资金", g_humanDefine.fund_details.type_recharge_currency)
	end
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
	
end

--激活订单
--id						订单ID
--userID					用户ID
--fundPwd					资金密码 有传就 验证 不传就不验证了
--sign						签证					
function orderactivation(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	
	local arrData = luajson.decode(rcvData)
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	if arrData.userID ~= nil  then
		local uInfo = UserInfoModel.GetUserInfo(arrData.userID)
		if uInfo == nil then
			retMsg['code'],retMsg['msg'] = 1, "用户不存在"
			return luajson.encode(retMsg)		
		end
		arrData.fundPwd = arrData.fundPwd or ""
		local ret, msg = UserInfoModel.CheckFundPassword(uInfo, arrData.fundPwd)
		if ret ~= 0 then
			retMsg['code'],retMsg['msg'] = 1, "资金密码错误"
			return luajson.encode(retMsg)	
		end
	end
	
	ThreadManager.DealLock(arrData.id) 
	local coinfo = CustomerOrderModel.GetCustomerOrderInfo(arrData.id)
	if coinfo == nil then
		ThreadManager.DealUnLock(arrData.id) 
		retMsg['code'] = 1
		retMsg['msg'] = "订单不存在"	
		return luajson.encode(retMsg)	
	end
	
	if coinfo.status ~= g_marketDefine.deal_status_wait_timeout and coinfo.status ~= g_marketDefine.deal_status_appeal_cancel and coinfo.status ~= g_marketDefine.deal_status_cancel then
		ThreadManager.DealUnLock(arrData.id) 
		retMsg['code'] = 1
		retMsg['msg'] = "该状态下，不能激活订单"	
		return luajson.encode(retMsg)	
	end
	
	if tonumber(coinfo.payidlist) ~= g_payCenterDefine.pay_id_list['bitpay'] then
		local payeeInfo = {}		--收款人
		local payerInfo = {}		--付款人
		if coinfo.type == g_marketDefine.hang_buy then
			--顾客买币
			payeeInfo = UserInfoModel.GetUserInfo(coinfo.vendoruserid)
			payerInfo = UserInfoModel.GetUserInfo(coinfo.customeruserid)
		else
			--顾客卖币
			payeeInfo = UserInfoModel.GetUserInfo(coinfo.customeruserid)
			payerInfo = UserInfoModel.GetUserInfo(coinfo.vendoruserid)
		end
		
		if payeeInfo == nil or payerInfo == nil then
			ThreadManager.DealUnLock(arrData.id) 
			retMsg['code'] = 1
			retMsg['msg'] = "收款人或付款人不存在"	
			return luajson.encode(retMsg)	
		end
		
		local currencyCount = tonumber(coinfo.amount)
		local moneyCount = 0
		if coinfo.dealtype == 300 then
			currencyCount = currencyCount + tonumber(coinfo.fee)
			moneyCount = (tonumber(coinfo.money) or 0) + tonumber(coinfo.feemoney)
		end
		
		if coinfo.wallettype == 0 then
			--检查收款人资金是否还有
			if payeeInfo.dealcointype == 1 then
				if (tonumber( payeeInfo.ercfcamount) - tonumber( payeeInfo.ercfclockamount)) < moneyCount then
					ThreadManager.DealUnLock(arrData.id) 
					retMsg['code'] = 1
					retMsg['msg'] = "收款人余额不足！"	
					return luajson.encode(retMsg)		
				end
			else
				if (tonumber( payeeInfo.ercusdtamount) - tonumber( payeeInfo.ercusdtlockamount)) < currencyCount then
					ThreadManager.DealUnLock(arrData.id) 
					retMsg['code'] = 1
					retMsg['msg'] = "收款人币数量不足！"	
					return luajson.encode(retMsg)		
				end
			end
		else
			if (tonumber( payeeInfo.coinpayusdtamount) - tonumber( payeeInfo.coinpayusdtlockamount)) < currencyCount then
				ThreadManager.DealUnLock(arrData.id) 
				retMsg['code'] = 1
				retMsg['msg'] = "收款人币付钱包余额不足！"	
				return luajson.encode(retMsg)		
			end
		end
		
		if coinfo.wallettype == 0 then
			if payeeInfo.dealcointype == 1 then
				if false == UserInfoModel.AddErcFCLockAmount(payeeInfo, moneyCount, arrData.id, coinfo.type, "激活订单冻结资金") then
					ThreadManager.DealUnLock(arrData.id) 
					retMsg['code'] = 1
					retMsg['msg'] = "收款人余额不足！"	
					return luajson.encode(retMsg)	
				end
			else
				--冻结收款人的币
				if false == UserInfoModel.AddErcUsdtLockAmount(payeeInfo, currencyCount, arrData.id, coinfo.type, "激活订单冻结资金") then
					ThreadManager.DealUnLock(arrData.id) 
					retMsg['code'] = 1
					retMsg['msg'] = "收款人币数量不足！"	
					return luajson.encode(retMsg)	
				end
			end
			
		else
			if false == UserInfoModel.AddErcCoinPayUsdtLockAmount(payeeInfo, currencyCount, arrData.id, coinfo.type, "激活订单冻结资金") then
				ThreadManager.DealUnLock(arrData.id) 
				retMsg['code'] = 1
				retMsg['msg'] = "收款人币付钱包余额不足！"	
				return luajson.encode(retMsg)	
			end
		
		end
		
		CustomerOrderModel.UpdateStatus(coinfo, g_marketDefine.deal_status_pay)
		local sqlCase = "update dy_customer_order set status=3 where id="..arrData.id
		mysqlItem:execute(sqlCase)
		
		--插到主循环列表中
		CustomerOrderModel.AddCustomerOrderList(coinfo)
		
		--向前端更新用户冻结金币
		UserInfoModel.SendErcUsdtLockAmount(payeeInfo)
		
		--向客户更新用户大厅的待处理列表
		local userList = {payeeInfo.userid, payerInfo.userid} 
		NoticeServices.NoticeHangSellOut(userList)
		
		local voItem = VendorOrderModel.GetVendorOrderInfo(coinfo.vendororderid)
		if voItem ~= nil then
			if coinfo.iswait == 1 then
				coinfo.iswait = 0
				CustomerOrderModel.SetCustomerOrderInfo(coinfo)	
				CustomerOrderModel.delCustomerWithdrawOrderList(coinfo.dealid)
				local sqlCase = "update dy_customer_order set is_wait="..coinfo.iswait.." where id="..coinfo.dealid
				mysqlItem:execute(sqlCase)
			else
				voItem.unsoldordernum = voItem.unsoldordernum + 1
				voItem.unsoldorderamount = tostring(tonumber(voItem.unsoldorderamount) + tonumber(coinfo.amount))
				voItem.unsoldordermoney = tostring((tonumber(voItem.unsoldordermoney) or 0) + tonumber(coinfo.money))
				VendorOrderModel.SetVendorOrderInfo( voItem.hangid, voItem )
				local sqlCase = "update dy_vendor_order set unsold_order_num=unsold_order_num+1, unsold_order_amount=unsold_order_amount+"
					..tonumber(coinfo.amount)..", unsold_order_money=unsold_order_money+"..tonumber(coinfo.money).." where id="..voItem.hangid
				mysqlItem:execute(sqlCase)
			end
			local payInfo = luajson.decode(coinfo.paytypelist)
			--统计支付方式的累计收款量
			UserInfoModel.SetDayLimit(payInfo.id, coinfo.money)
		end
	else
		CustomerOrderModel.UpdateStatus(coinfo, g_marketDefine.deal_status_pay)
		local sqlCase = "update dy_customer_order set status=3 where id="..arrData.id
		mysqlItem:execute(sqlCase)
		
		--插到主循环列表中
		CustomerOrderModel.AddCustomerOrderList(coinfo)
	end
	
	ThreadManager.DealUnLock(arrData.id) 
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
	
end

--给玩家添加附件
function usermail(rcvData)
	if rcvData == nil then
		return "success";
	end
	local jsonArr = luajson.decode(rcvData)
	--
	MailModel.LoadMailList(jsonArr["userid"])
	
	NoticeModel.SendNotice(jsonArr["userid"], g_noticeType.mail_unread)	
	
	return "success";
end

--支付中心传入商户ID，获取商户的秘钥和回调。
function getshopinfo(rcvData)
	
	
	local retMsg = {}
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	if rcvData == nil or rcvData == "" then
		retMsg['code'],retMsg["msg"] = 1001,"参数错误"
		return luajson.encode(retMsg)
	end
	local jsonArr = luajson.decode(rcvData)
	
	if jsonArr['appid'] == nil or jsonArr['sign'] == nil or jsonArr['payid'] == nil  then
		retMsg['code'],retMsg["msg"] = 1002,"参数缺失"
		return luajson.encode(retMsg)
	end

	if jsonArr['appid'] == '' or jsonArr['sign'] == '' or jsonArr['payid'] == '' then
		retMsg['code'],retMsg["msg"] = 1003,"参数不能传空"
		return luajson.encode(retMsg)
	end


	local sqlCase = "select pay_secret,return_secret,return_url,default_return_url,white_ip,sell_return_url from dy_user_api where userid="..jsonArr['appid']
	mysqlItem:executeQuery(sqlCase)
	
	local sqlData = mysqlItem:fetch({})
	
	if sqlData == nil then
		retMsg['code'],retMsg["msg"] = 1004,"请设置回调参数"
		return luajson.encode(retMsg)
	end


	if sqlData[3] ~= '' then
		local urlList = luajson.decode( sqlData[3] )
		if urlList[jsonArr['payid']] ~= nil then
			retMsg['callback_url'] = urlList[jsonArr['payid']]
		end
	end
	
	retMsg['callback_url'] = retMsg['callback_url'] == nil and sqlData[4] or retMsg['callback_url']   --如果没有，就用默认的
	
	retMsg['pay_secret'] = sqlData[1]
	retMsg['return_secret'] = sqlData[2]
	retMsg['white_ip'] = sqlData[5]
	retMsg['sell_return_url'] = sqlData[6]
	retMsg['code'],retMsg["msg"] = 0,"success"
	return luajson.encode(retMsg)
	
end

--后台客服放行
--orderID					订单ID
--TXID 						放行币支付是需要填写TXID
--sign						签证
function backOrderPass(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	ThreadManager.DealLock(arrData.orderID)
	local cInfo = CustomerOrderModel.GetCustomerOrderInfo( arrData.orderID ) 
	if cInfo == nil then
		ThreadManager.DealUnLock(arrData.orderID)
		retMsg['code'],retMsg['msg'] = ReturnCode["order_not_exist"][1], ReturnCode["order_not_exist"][2]
		return luajson.encode(retMsg)
	end
	
	if tonumber(cInfo.payidlist) ~= g_payCenterDefine.pay_id_list['bitpay'] then
		--检查订单状态
		if cInfo.status ~= g_marketDefine.deal_status_pay and cInfo.status ~= g_marketDefine.deal_status_appeal then
			retMsg['code'],retMsg['msg'] = ReturnCode["deal_order_not_match"][1], ReturnCode["deal_order_not_match"][2]
			return luajson.encode(retMsg)
		end	
		
		local ret, msg = VendorOrderService.PassOrder(nil, cInfo)
		if ret ~= nil then
			ThreadManager.DealUnLock(arrData.orderID)
			retMsg['code'],retMsg['msg'] = ret, msg
			return luajson.encode(retMsg)	
		end
	else
	
		if arrData.TXID == nil or arrData.TXID == "" then
			ThreadManager.DealUnLock(arrData.orderID)
			retMsg['code'],retMsg['msg'] = 1, "币支付放行需要填写TXID"
			return luajson.encode(retMsg)
		
		end
		--检查订单状态
		if cInfo.status ~= g_marketDefine.deal_status_pay and cInfo.status ~= g_marketDefine.deal_status_wait then
			ThreadManager.DealUnLock(arrData.orderID)
			retMsg['code'],retMsg['msg'] = ReturnCode["deal_order_not_match"][1], ReturnCode["deal_order_not_match"][2]
			return luajson.encode(retMsg)
		end	
		
		local passUserInfo = UserInfoModel.GetUserInfo(cInfo.customeruserid)
		if passUserInfo == nil then
			ThreadManager.DealUnLock(arrData.orderID)
			retMsg['code'],retMsg['msg'] = 1, "订单用户不存在"
			return luajson.encode(retMsg)
		end
		
		--检查TXID
		local sqlCase = "select to_addr,amount,hosting_status from dy_block_chain_trans where tx_id='"..arrData.TXID.."'"
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			ThreadManager.DealUnLock(arrData.orderID)
			retMsg['code'],retMsg['msg'] = 1, "找不到TXID对应的充币记录"
			return luajson.encode(retMsg)
		end
		
		local toAddr = sqlData[1]
		if toAddr ~= cInfo.chainaddr then
			ThreadManager.DealUnLock(arrData.orderID)
			retMsg['code'],retMsg['msg'] = 1, "TXID的充币记录与订单的充币地址不相符"
			return luajson.encode(retMsg)
		end
		
		local amount = tonumber(sqlData[2])
		if amount ~= tonumber(cInfo.amount) then
			ThreadManager.DealUnLock(arrData.orderID)
			retMsg['code'],retMsg['msg'] = 1, "TXID的充币记录与订单的充币数量不相符"
			return luajson.encode(retMsg)
		end
		
		local hostingStatus = tonumber(sqlData[3])
		if hostingStatus ~= 1 and hostingStatus ~= 5  then
			ThreadManager.DealUnLock(arrData.orderID)
			retMsg['code'],retMsg['msg'] = 1, "TXID的充币记录已经被匹配了"
			return luajson.encode(retMsg)
		end
	
		VendorOrderService.PassOrderCoinPay(passUserInfo, cInfo, arrData.TXID)
		
		--更新充币记录
		local sqlCase = "update dy_block_chain_trans set specific_type=5, hosting_status=2,userid="..passUserInfo.userid
			..", channel='"..passUserInfo.channel.."', user_type="..passUserInfo.usertype..", related_order="..cInfo.dealid
			.." where tx_id='"..arrData.TXID.."'"
		mysqlItem:execute(sqlCase)
	end
	
	ThreadManager.DealUnLock(arrData.orderID)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
	
end

--补发回调
--id						订单ID
--sign						签证
function reissuecallback(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	--查询订单信息 
	local sqlCase = "select deal_type,notify_amount,money,merchant_order_id,status,notify_url,id from dy_customer_order where id="..arrData.id
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		retMsg['code'] = 1
		retMsg['msg'] = "订单不存在！"
		return luajson.encode(retMsg)	
	end
	
	--[[
	if tonumber(sqlData[1]) ~= 200 then
		retMsg['code'] = 1
		retMsg['msg'] = "不是商户充值订单，不能补发回调"
		return luajson.encode(retMsg)	
	end
	]]
	--[[
	if tonumber(sqlData[2]) ~= 0 then
		retMsg['code'] = 1
		retMsg['msg'] = "订单已回调成功请勿回调！"
		return luajson.encode(retMsg)	
	end
	]]
	if sqlData[6] == nil or sqlData[6] == "" then
		retMsg['code'] = 1
		retMsg['msg'] = "回调地址为空，回调失败"
		return luajson.encode(retMsg)	
	end
	
		
	local info = "money="..sqlData[3].."&merchantorderid="..sqlData[4].."&orderid="..sqlData[7].."&status="..sqlData[5]
	local httpUrl = sqlData[6].."?"..info
	local getData = HttpGet(httpUrl)
		
	local isOk = false
	if getData ~= "" and getData ~= nil then
		local s1 = string.sub(getData, 0, 1)
		if getData ~= nil and (s1 == '[' or s1 == '{') then
			getData = luajson.decode(getData)  
			if getData ~= "" then
				if getData.code == 200 then
					--到这里订单完成了， 就充缓存中删除吧
					CustomerOrderModel.delCustomerOrderInfo(arrData.id)
					local sqlCase = "update dy_customer_order set notify_amount=notify_amount+1 where id="..arrData.id
					mysqlItem:execute(sqlCase)
					isOk = true
				end	
			end
		end
	end
	
	if isOk == false then
		retMsg['code'] = 1
		retMsg['msg'] = "回调失败"	
		return luajson.encode(retMsg)	
	end
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
	
end

--批量卖币
--userid					用户ID
--orderIdList				订单列表逗号隔开
--sign						签证
function batchsellOrderInsert(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	if arrData['userid'] == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['shop_merchant_is_null'][1],g_payCenterDefine.retuen_code['shop_merchant_is_null'][2]
		return luajson.encode(retMsg)
	end
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	--检查用户存不存在
	local uInfo = UserInfoModel.GetUserInfo(arrData.userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1,"商户不存在"
		return luajson.encode(retMsg)	
	end
	
	--检查用户是否锁定
	if uInfo.islock == 1 then
		retMsg['code'],retMsg['msg'] = 1, "你已经被锁定无法进行该操作"
		return luajson.encode(retMsg)	
	end
	
	local sysPrice = CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.hang_sell, g_marketDefine.currency_type.USDT, true, uInfo.userid)
	local orderIdList = string.split(arrData.orderIdList, ",") 
	local orderInfoList = {}
	for k,v in ipairs(orderIdList) do 
		
		local sqlCase = "select * from dy_temp_order where id="..v
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch({})
		if sqlData ~= nil then
			local tmp = {}
			tmp['userid'] =  sqlData[2]
			tmp['payee_name'] =  sqlData[4]
			tmp['payee_account'] =  sqlData[5]
			tmp['payee_bank'] =  sqlData[6]
			tmp['money'] =  tonumber(sqlData[7])
			tmp['amount'] = tonumber(string.format("%.10f", (tmp['money'] / sysPrice)))
			tmp['rate'] = UserInfoModel.getUserFeeRate(uInfo.userid, g_humanDefine.user_payList.bank, g_humanDefine.sell_fee_rate)
			tmp['fee'] = tonumber(string.format("%.10f", tostring(tmp['rate'] * tmp['money'])))
			--检查符不符合玩家自身的限额
			if tonumber(uInfo.minsell) > tmp['money'] or tmp['money'] > tonumber(uInfo.maxsell) then
				retMsg['code'],retMsg['msg'] = 1, "提现订单金额需在￥"..uInfo.minsell.."--￥"..uInfo.maxsell.."范围内才可提交"
				return luajson.encode(retMsg)	
			end
			table.insert(orderInfoList, tmp)
		end
	end
	
	for k,v in ipairs(orderInfoList) do 
		local status = 2
		--插入到数据库中。
		local currTime = TimeUtils.GetTimeString()
		local sqlCase = "insert into dy_vendor_order(userid,type,price,amount,price_type,min_money,max_money,auto_switch,create_time,channel,enable_status,user_type,payee_account,payee_name,payee_bank,free,predict_money,deal_mode,rest_time,platform_id) " 
			.."values("..uInfo.userid..","..g_marketDefine.hang_sell..","..sysPrice..","..v.amount..",0,"..v.money..","..v.money..",0,'"..currTime.."','"..uInfo.channel
			.."',"..status..",300,'"..v.payee_account.."','"..v.payee_name.."','"..v.payee_bank.."',"..v.fee..","..v.money..", 1,'"..currTime.."',"..uInfo.platformid..")"
		mysqlItem:execute(sqlCase)
		local sqlCase = "select id from dy_vendor_order where userid="..uInfo.userid.." and type="..g_marketDefine.hang_sell.." and create_time='"..currTime.."'"
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch()
		if sqlData == nil then
			retMsg['code'],retMsg['msg'] = 1, "提交失败"
			return luajson.encode(retMsg)	
		end
	end
	
	local sqlCase = "delete from dy_temp_order where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)

	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
	
end

--查询订单是否已经完成
--orderID					订单ID
--sign						签证
function checkordercarryout(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	--查询订单信息
	local sqlCase = "select status,id,merchant_order_id,amount,money,price,chain_type,chain_name,chain_addr from dy_customer_order where merchant_order_id='"..arrData.orderID.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		retMsg['code'] = 1
		retMsg['msg'] = "订单不存在！"
		return luajson.encode(retMsg)	
	end
		
	if tonumber(sqlData[1]) ~= 7 and tonumber(sqlData[1]) ~= 11 then
		retMsg['code'] = tonumber(sqlData[1])
	else
		retMsg['code'] = 200
	end
	
	local tmpData = {}
	tmpData["id"] = sqlData[2]
	tmpData["merchantorderid"] = sqlData[3]
	tmpData["amount"] = sqlData[4]
	tmpData["money"] = sqlData[5]
	tmpData["price"] = sqlData[6]
	tmpData["chain_type"] = sqlData[7]
	tmpData["chain_name"] = sqlData[8]
	tmpData["chain_addr"] = sqlData[9]
	retMsg['data'] = tmpData
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)
end

--批量提币
--userid					用户ID
--orderIdList				订单列表逗号隔开
--coinID					币种ID
--addrType					地址类型
--sign						签证
function batchwithdraworderInsert(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	if arrData['userid'] == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['shop_merchant_is_null'][1],g_payCenterDefine.retuen_code['shop_merchant_is_null'][2]
		return luajson.encode(retMsg)
	end
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local coinID = tonumber(arrData.coinID) or 0
	coinID = 2003
	if coinID == 0 then
		retMsg['code'],retMsg['msg'] = 1, "币种错误"
		return luajson.encode(retMsg)		
	end
	
	--检查用户存不存在
	local uInfo = UserInfoModel.GetUserInfo(arrData.userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1,"商户不存在"
		return luajson.encode(retMsg)	
	end
	
	--检查用户是否锁定
	if uInfo.islock == 1 then
		retMsg['code'],retMsg['msg'] = 1, "你已经被锁定无法进行该操作"
		return luajson.encode(retMsg)	
	end
	
	local sysPrice = CoinInfoService.withdrawCoinPrice(uInfo.channel, g_marketDefine.currency_type.USDT, true, uInfo.userid)
	local orderIdList = string.split(arrData.orderIdList, ",") 
	local orderInfoList = {}
	local coinCount = 0
	local moneyCount = 0
	for k,v in ipairs(orderIdList) do 
		
		local sqlCase = "select payee_bank,money from dy_temp_order where id="..v.." and type=200"
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch({})
		if sqlData ~= nil then
		
			local wInfo = {}
			wInfo.toaddress = sqlData[1]
			wInfo.currencytype = coinID
			wInfo.currencyamount = tonumber(sqlData[2]) or 0
			wInfo.subject = ""
			wInfo.version = ""
			wInfo.notifyurl = ""
			wInfo.body = ""
			wInfo.outtradeno = ""
			wInfo.specifictype = 2
			wInfo.addrtype = tonumber(arrData.addrType) or 0
			wInfo.price = sysPrice
			wInfo.money = wInfo.currencyamount * wInfo.price
			wInfo.wallettype = 0
			
			table.insert(orderInfoList, wInfo)
			
			coinCount = coinCount + wInfo.currencyamount
			moneyCount = moneyCount + wInfo.money
		end
	end
	
	if uInfo.dealcointype == 0 then
		if coinCount > (tonumber( uInfo.ercusdtamount) - tonumber( uInfo.ercusdtlockamount)) then
			retMsg['code'],retMsg['msg'] = 1, "币数量不足"
			return luajson.encode(retMsg)	
		end
	else
		if moneyCount > (tonumber( uInfo.ercfcamount) - tonumber( uInfo.ercfclockamount)) then
			retMsg['code'],retMsg['msg'] = 1, "余额不足"
			return luajson.encode(retMsg)	
		end
	end
	
	for k,v in ipairs(orderInfoList) do 
	
		local ret, msg = VendorOrderService.WithdrawCurrency(uInfo, v, 1, nil, true, uInfo.userid, uInfo.nickname,"")
		if ret ~= 0 then
			retMsg['code'],retMsg['msg'] = ret, msg
			return luajson.encode(retMsg)	
		end
		
	end
	
	local sqlCase = "delete from dy_temp_order where userid="..uInfo.userid.." and type=200"
	mysqlItem:execute(sqlCase)

	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
	
end

--修改订单金额
--orderId					订单ID
--amunot					修改订单金额
--sign						签证
function modifyorderamount(rcvData)
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	local orderId = tonumber(arrData.orderId)
	ThreadManager.DealLock(orderId)
	local coinfo = CustomerOrderModel.GetCustomerOrderInfo(orderId)
	if coinfo == nil then
		--挂单的信息不存在
		ThreadManager.DealUnLock(orderId)
		retMsg['code'] = 1
		retMsg['msg'] = "订单不存在！"
		return luajson.encode(retMsg)
	end
	
	--检查订单状态申诉中也可以放行
	if coinfo.status ~= g_marketDefine.deal_status_pay and coinfo.status ~= g_marketDefine.deal_status_appeal then
		ThreadManager.DealUnLock(orderId)
		retMsg['code'] = 1
		retMsg['msg'] = "该状态下不能修改金额！"
		return luajson.encode(retMsg)
	end		
	
	local payee = 0
	if coinfo == g_marketDefine.deal_buy then
		payee = coinfo.customeruserid
		
		
	else
		payee = coinfo.vendoruserid
	end
	
	local money = tonumber(arrData.amunot)
	local amunot = tonumber(string.format("%0.10f", tostring(money / tonumber(coinfo.price))))
	
	local diffMoney = math.abs(money - tonumber(coinfo.money))
	local diffAmount =  math.abs(amunot - tonumber(coinfo.amunot))
	local sellfee = 0
	if dealType == 100 then
		--查找渠道的总代费率
		coinfo.fee = tostring(tonumber(coinfo.feerate)  * currencyCount)
		coinfo.feemoney = tostring(tonumber(coinfo.feerate) * moneyCount)
		coinfo.aftermoney = tostring(moneyCount - tonumber(coinfo.feemoney))
		coinfo.buyfeerate = tostring(userRate)
		coinfo.buyfee = tostring(userRate * currencyCount)
		coinfo.getamount = tostring(currencyCount - tonumber(coinfo.fee))
		coinfo.income = tostring(currencyCount * UserInfoModel.getUserFeeRate(payee.userid, payInfo.paytype, g_humanDefine.sell_comm_rate)) 
	elseif dealType == 200 then
		local userRate =UserInfoModel.getUserFeeRate(payer.userid, payInfo.paytype, g_humanDefine.buy_fee_rate)
		coinfo.feerate = tostring(userRate)
		coinfo.fee = tostring(userRate * currencyCount)
		coinfo.feemoney = tostring(tonumber(coinfo.feerate)  * moneyCount)
		coinfo.aftermoney = tostring(moneyCount - tonumber(coinfo.feemoney))
		coinfo.buyfeerate = tostring(userRate)
		coinfo.buyfee = tostring(userRate * currencyCount)
		coinfo.getamount = tostring(currencyCount - tonumber(coinfo.fee))
		coinfo.income = tostring(currencyCount * UserInfoModel.getUserFeeRate(payee.userid, payInfo.paytype, g_humanDefine.sell_comm_rate)) 
	elseif dealType == 300 then
		local feeType = voItem.withdrawtype == 0 and g_humanDefine.sell_fee_rate or g_humanDefine.behalf_sell_fee_rate
		local userRate = UserInfoModel.getUserFeeRate(payee.userid, payInfo.paytype, feeType)
		coinfo.feerate = tostring(userRate)
		coinfo.fee = tostring(userRate * currencyCount)
		coinfo.feemoney = tostring(tonumber(coinfo.feerate)  * moneyCount)
		coinfo.sellfeerate = coinfo.feerate
		coinfo.sellfee = coinfo.fee
		sellFeeMoney = tonumber(coinfo.feemoney)
	end
	
	local sellFeeMoney = 0
	if money > tonumber(coinfo.money) then
		--多加冻结资金
		--先收款人的资金检查一次
		if payee.dealcointype == 0 then
			if (tonumber( payee.ercusdtamount) - tonumber( payee.ercusdtlockamount)) < diffAmount + tonumber(coinfo.sellfee) then
				return 1, "收款人的币数量不足"		
			end
		else
			if (tonumber( payee.ercfcamount) - tonumber( payee.ercfclockamount)) < diffAmount + sellFeeMoney then
				return 1, "收款人的币数量不足"		
			end
		end

	
	elseif money < tonumber(coinfo.money) then
		--减少冻结资金
	end
	
	
	
	ThreadManager.DealUnLock(orderId)
	
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)
end

--创建订单
--ordertype 				订单类型 200 充值 300 提现
--withdrawType				提现类型 0 OTC  1 代付
--merchantID				商户ID
--coindealerID				币商ID	
--amount					金额
--replenishment_remarks		备注
--payType 					支付类型
--sign						签证
function createorder(rcvData)
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	local orderType = tonumber(arrData.ordertype)
	if orderType == 200 then
		local payType = tonumber(arrData.payType)
		if payType == g_payCenterDefine.pay_id_list['bitpay'] then
			--币支付
			local mInfo = UserInfoModel.GetUserInfo(arrData.merchantID or 0)
			if mInfo == nil then
				--币商不存在
				retMsg['code'] = 1
				retMsg['msg'] = "商户不存在"
				return luajson.encode(retMsg)
			end
			
			if mInfo.usertype ~= 300 and mInfo.usertype ~= 301 then
				retMsg['code'] = 1
				retMsg['msg'] = "商户不存在"
				return luajson.encode(retMsg)
			end
			
			local sqlCase = "select hosting_status, get_amount,addr_type,to_addr,hosting_adds from dy_block_chain_trans where tx_id='"..arrData.coindealerID.."' and (hosting_adds=2 or hosting_adds=3)"
			mysqlItem:executeQuery(sqlCase)
			local sqlData = mysqlItem:fetch({})
			if sqlCase == nil then
				retMsg['code'] = 1
				retMsg['msg'] = "TXID不存在"
				return luajson.encode(retMsg)
			end
			
			local hostingStatus = tonumber(sqlData[1])
			local getAmount =	tonumber(sqlData[2])
			local addrType =	tonumber(sqlData[3])
			local toAddr =	tostring(sqlData[4])
			local hostingAdds =	tonumber(sqlData[5])
			if hostingStatus ~= 1 then
				retMsg['code'] = 1
				retMsg['msg'] = "该TXID状态不正确，无法创建订单"
				return luajson.encode(retMsg)
			end
			
			--[[if getAmount*tonumber(mInfo.coinpayrate) ~= tonumber(arrData.amount) then
				retMsg['code'] = 1
				retMsg['msg'] = "该TXID的金额与输入的不相符！"
				return luajson.encode(retMsg)
			end]]
			
			local owership = hostingAdds == 2 and 0 or 1
			local coinfo = st_order_pb.customerorderinfo()
			local ret, msg = VendorOrderService.coinPayOrder(mInfo, coinfo, tonumber(arrData.amount), nil
				, g_payCenterDefine.pay_id_list['bitpay'], addrType, toAddr,owership,getAmount)
			if ret ~= nil then
				return 1,msg, nil
			end
			
			--完成订单
			VendorOrderService.PassOrderCoinPay(mInfo, coinfo, arrData.coindealerID)
			userid = mInfo.userid
			relatedOrder = coinfo.dealid
			platformID = mInfo.platformid
			local sqlCase = "update dy_block_chain_trans set hosting_status=3, related_order="..relatedOrder..",userid="
				..userid..",platform_id="..platformID.." where tx_id='"..arrData.coindealerID.."'"
			mysqlItem:execute(sqlCase)
			
			local remarks = ""..tostring(arrData.replenishment_remarks or "")
			local sqlCase = "update dy_customer_order set replenishment_remarks='"..remarks.."' where id="..coinfo.dealid
			mysqlItem:execute(sqlCase)
		else
			--其他支付
			--检查币商是否有挂单
			local uInfo = UserInfoModel.GetUserInfo(arrData.coindealerID or 0)
			if uInfo == nil then
				--币商不存在
				retMsg['code'] = 1
				retMsg['msg'] = "币商不存在"
				return luajson.encode(retMsg)
			end
			
			if uInfo.usertype ~= 200 and uInfo.usertype ~= 201 and uInfo.usertype ~= 202 then
				retMsg['code'] = 1
				retMsg['msg'] = "币商不存在"
				return luajson.encode(retMsg)
			end
			
			local mInfo = UserInfoModel.GetUserInfo(arrData.merchantID or 0)
			if mInfo == nil then
				--币商不存在
				retMsg['code'] = 1
				retMsg['msg'] = "商户不存在"
				return luajson.encode(retMsg)
			end
			
			if mInfo.usertype ~= 300 and mInfo.usertype ~= 301 then
				retMsg['code'] = 1
				retMsg['msg'] = "商户不存在"
				return luajson.encode(retMsg)
			end
			
			local money =  tonumber(arrData.amount)
			local sysPrice = CoinInfoService.Erc20USDTPrice(mInfo.channel, g_marketDefine.hang_buy, g_marketDefine.currency_type.USDT, true, mInfo.userid)
			local amount = tonumber(string.format("%0.10f", tostring(money / sysPrice)))
			local sqlCase = "select id from dy_vendor_order where userid="..uInfo.userid.." and type=1 and enable_status=1 and auto_switch=1"
			mysqlItem:executeQuery(sqlCase)
			local sqlData = mysqlItem:fetch()
			if sqlData == nil then
				retMsg['code'] = 1
				retMsg['msg'] = "币商未开启接单"
				return luajson.encode(retMsg)
			end
			
			local handID = tonumber(sqlData)
			ThreadManager.OrderLock(handID)
			local voItem = VendorOrderModel.GetVendorOrderInfo(handID)
			if voItem == nil then
				ThreadManager.OrderUnLock(handID)
				retMsg['code'] = 1
				retMsg['msg'] = "币商未开启接单"
				return luajson.encode(retMsg)
			end
			local coinfo = st_order_pb.customerorderinfo()
			local ret, msg = VendorOrderService.DealOrder(mInfo,coinfo, voItem,g_marketDefine.deal_buy
											,amount,money,nil,sysPrice,8201,"")
			if ret ~= nil then
				ThreadManager.OrderUnLock(handID)
				retMsg['code'] = 1
				retMsg['msg'] = msg
				return luajson.encode(retMsg)
			end
			
			--我已付款
			VendorOrderService.PaidOrder(mInfo, coinfo, nil)
			local remarks = ""..tostring(arrData.replenishment_remarks or "")
			local sqlCase = "update dy_customer_order set replenishment_remarks='"..remarks.."' where id="..coinfo.dealid
			mysqlItem:execute(sqlCase)
			ThreadManager.OrderUnLock(handID)
		end
		
	elseif orderType == 300 then
		
		
	end
	
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)
end

--取消托管钱包充值订单
--orderid					订单ID
--sign						签证
function cancelhostingorder(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local orderid = arrData.orderid or ""
	local sqlCase = "select status from dy_trusteeship_wallet_order where order_id='"..orderid.."'"
	mysqlItem:executeQuery(sqlCase) 
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		retMsg['code'] = 1
		retMsg['msg'] = "订单不存在"
		return luajson.encode(retMsg)
	end
	
	local state = tonumber(sqlData)
	if state ~= 0 then
		retMsg['code'] = 1
		retMsg['msg'] = "该订单状态无法取消"
		return luajson.encode(retMsg)
	end
	
	local sqlCase = "update dy_trusteeship_wallet_order set status=2 where order_id='"..orderid.."'"
	mysqlItem:execute(sqlCase) 
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	

end


--入款托管钱包充值订单
--orderid					订单ID
--amount 					实际到账数量
--sign						签证
function Deposithostingorder(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	if true then
		retMsg['code'],retMsg['msg'] = 1, "接口已被弃用"
		return luajson.encode(retMsg)
	end
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local orderid = arrData.orderid or 0
	local sqlCase = "select status,userid from dy_trusteeship_wallet_order where order_id='"..orderid.."'"
	mysqlItem:executeQuery(sqlCase) 
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		retMsg['code'] = 1
		retMsg['msg'] = "订单不存在"
		return luajson.encode(retMsg)
	end
	
	local state = tonumber(sqlData[1])
	local userid = tonumber(sqlData[2])
	local amount = tonumber(arrData.amount) or 0
	if state ~= 0 then
		retMsg['code'] = 1
		retMsg['msg'] = "该订单状态无法入账"
		return luajson.encode(retMsg)
	end
	
	local uInfo = UserInfoModel.GetUserInfo(userid)
	if uInfo == nil then
		retMsg['code'] = 1
		retMsg['msg'] = "充值用户不存在"
		return luajson.encode(retMsg)
	end
	
	local price = CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.deal_buy, g_marketDefine.currency_type.USDT, true, uInfo.userid)
	local money = price * amount
	--加钱
	UserInfoModel.AddErcUsdtAmount( uInfo, amount,orderid,0,"托管钱包充值，增加资金", g_humanDefine.fund_details.type_recharge_currency,money)
	UserInfoModel.SendErcUsdtAmount(uInfo)
	LogDispatch.userRecharge(uInfo.userid, amount, 0,amount, true)
		
	if uInfo.dealcointype == 1 then
		--结算货币类型如果是法币把法币账户也结算一下
		UserInfoModel.AddErcFCAmount(uInfo, money, orderid, 0, "托管钱包充值，增加资金", g_humanDefine.fund_details.type_recharge_currency)
	end
	
	local sqlCase = "update dy_trusteeship_wallet_order set status=1, actual_amount="..amount.." where order_id='"..orderid.."'"
	mysqlItem:execute(sqlCase) 
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	

end


--设置挂单接单状态
--orderid					订单ID
--status					是否允许接单 0 允许 1 不允许
--sign						签证
function setpendingordertakestatus(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local orderid = tonumber(arrData.orderid) or 0
	local status = tonumber(arrData.status) or 0
	
	local voItem = VendorOrderModel.GetVendorOrderInfo(orderid) 
	if voItem == nil then
		retMsg['code'],retMsg['msg'] = 1, "挂单不存在"
		return luajson.encode(retMsg)
	end
	voItem.istake = status
	VendorOrderModel.SetVendorOrderInfo(voItem.hangid, voItem)
	
	local sqlCase = "update dy_vendor_order set is_take="..status.." where id="..orderid
	mysqlItem:execute(sqlCase)	
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
end

--手动派单
--orderid					订单ID
--toUserID					接单用户
--money						金额
--sign						签证
function manualdispatchorder(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local orderid = tonumber(arrData.orderid) or 0
	local toUserID = tonumber(arrData.toUserID) or 0
	local money = tonumber(arrData.money) or 0
	
	local uInfo = UserInfoModel.GetUserInfo(toUserID)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "接单用户不存在"
		return luajson.encode(retMsg)	
	end
	
	if money <= 0 then 
		retMsg['code'],retMsg['msg'] = 1, "金额有误"
		return luajson.encode(retMsg)
	end
	
	ThreadManager.OrderLock(orderid)
	local voItem = VendorOrderModel.GetVendorOrderInfo(orderid)
	if voItem == nil then
		retMsg['code'],retMsg['msg'] = 1, "挂单不存在"
		ThreadManager.OrderUnLock(orderid)
		return luajson.encode(retMsg)	
	end
	
	if money < tonumber(voItem.minmoney) or money > tonumber(voItem.maxmoney) then
		retMsg['code'],retMsg['msg'] = 1, "金额不再挂定范围内"
		ThreadManager.OrderUnLock(orderid)
		return luajson.encode(retMsg)
	end
	
	local currencyCount = math.floor(money / tonumber(voItem.price) * ***********) / ***********
	
	local remainingMoney = tonumber(voItem.predictmoney) - tonumber(voItem.unsoldordermoney) - tonumber(voItem.dealordermoney)
	remainingMoney = tonumber(string.format("%.4f", tostring(remainingMoney)))
	money = tonumber(string.format("%.4f", tostring(money)))

	local diffMoney = remainingMoney - money
	diffMoney = tonumber(string.format("%.4f", tostring(diffMoney)))
	if diffMoney < tonumber(voItem.minmoney) and diffMoney > 0 then
		retMsg['code'],retMsg['msg'] = 1, "下单金额需为￥"..remainingMoney..""
		ThreadManager.OrderUnLock(orderid)
		return luajson.encode(retMsg)
	end
	
	local coinInfo = st_order_pb.customerorderinfo()
	local ret, msg = VendorOrderService.DealOrder(uInfo,coinInfo, voItem,g_marketDefine.deal_buy
										,currencyCount,money,nil,tonumber(voItem.price),g_humanDefine.user_payList.bank,"")
	if ret ~= nil then
		ThreadManager.OrderUnLock(orderid)
		retMsg['code'],retMsg['msg'] = 1, msg
		return luajson.encode(retMsg)
	end

	ThreadManager.OrderUnLock(orderid)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
end

--批量转账
--userid					用户ID
--toUserIDList				目标用户ID列表逗号隔开
--amountList				数量列表逗号隔开
--coinID					币种ID
--fundPwd					资金密码
--sign						签证
function batchtransfer(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	if arrData['userid'] == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['shop_merchant_is_null'][1],g_payCenterDefine.retuen_code['shop_merchant_is_null'][2]
		return luajson.encode(retMsg)
	end
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local uInfo = UserInfoModel.GetUserInfo(arrData.userid or 0)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "用户不存在"
		return luajson.encode(retMsg)
	end
	
	--检查用户是否锁定
	if uInfo.islock == 1 then
		retMsg['code'],retMsg['msg'] = 1, "你已经被锁定无法进行该操作"
		return luajson.encode(retMsg)
	end
	
	--检查资金密码
	local ret, msg = UserInfoModel.CheckFundPassword(uInfo, arrData.fundPwd)
	if ret ~= 0 then
		retMsg['code'],retMsg['msg'] = ret, msg
		return luajson.encode(retMsg)
	end
	
	local toUserIDList = string.split((arrData.toUserIDList or ""), ",")
	local amountList = string.split((arrData.amountList or ""), ",")
	local amountCount = 0
	for k,v in ipairs(amountList) do 
		amountCount = amountCount + tonumber(v)
	end
	
	for k,v in ipairs(toUserIDList) do 
		if tonumber(v) == uInfo.userid then
			retMsg['code'],retMsg['msg'] = 1, "不能给自己转账"
			return luajson.encode(retMsg)
		end
	end
	
	if tonumber(uInfo.ercusdtamount) - tonumber(uInfo.ercusdtlockamount) < amountCount then
		retMsg['code'],retMsg['msg'] = 1, "余额不足！"
		return luajson.encode(retMsg)
	end
	
	local sysPrice = CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.deal_sell, g_marketDefine.currency_type.USDT, true, uInfo.userid)
	for k, v in ipairs(toUserIDList) do 
		local touInfo = UserInfoModel.GetUserInfo(v)
		if touInfo == nil then
			retMsg['code'],retMsg['msg'] = 1, "目标用户不存在"
			return luajson.encode(retMsg)
		end
		local wInfo = {}
		wInfo.toaddress = ""
		wInfo.currencytype = 2003
		wInfo.currencyamount = tonumber(amountList[k]) or 0
		wInfo.subject = ""
		wInfo.version = ""
		wInfo.notifyurl = ""
		wInfo.body = ""
		wInfo.outtradeno = ""
		wInfo.specifictype = 4
		wInfo.addrtype = 0
		wInfo.price = sysPrice
		wInfo.money = wInfo.currencyamount * wInfo.price
		wInfo.wallettype = 0
		
		local ret, msg = VendorOrderService.WithdrawCurrency(uInfo, wInfo, 2, touInfo, true, uInfo.userid, uInfo.nickname,"")
		if ret ~= 0 then
			retMsg['code'],retMsg['msg'] = ret, msg
			return luajson.encode(retMsg)
		end
	end
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
	
end

--自动售卖开关
--userid					用户ID
--status					0-关 1-开
--minmoney					最小金额
--maxmoney					最大金额
--sign						签证
function setautovending(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	if arrData['userid'] == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['shop_merchant_is_null'][1],g_payCenterDefine.retuen_code['shop_merchant_is_null'][2]
		return luajson.encode(retMsg)
	end
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local uInfo = UserInfoModel.GetUserInfo(arrData.userid or 0)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "用户不存在"
		return luajson.encode(retMsg)
	end
	
	local status = tonumber(arrData.status) or 0
	
	local sqlCase = "select id, enable_status from dy_vendor_order where type=1 and auto_switch=1 and userid="..uInfo.userid.." order by id desc limit 1"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	local userStatus = 0
	local handID = 0
	if sqlData ~= nil then
		handID = tonumber(sqlData[1])
		userStatus = tonumber(sqlData[2]) == 1 and 1 or 0
	end
	
	if userStatus ~= status then
		if status == 0 then
			ThreadManager.OrderLock(handID)
			local hInfo = VendorOrderModel.GetVendorOrderInfo(handID)
			if hInfo ~= nil  then
				--还有正在进行中的订单
				if hInfo.unsoldordernum > 0 then
					ThreadManager.OrderUnLock(handID)
					retMsg['code'] = 1
					retMsg['msg'] = "存在进行中的订单无法关闭"	
					return luajson.encode(retMsg)		
				end
				
				hInfo.enablestatus = 0
				local sqlCase = "update dy_vendor_order set enable_status="..hInfo.enablestatus.." where id="..hInfo.hangid
				mysqlItem:execute(sqlCase)	
				
				if hInfo.autoswitch == 1  and hInfo.type == 1 then
					uInfo.autosell = 0
					UserInfoModel.SetUserInfo(uInfo)
					UserInfoModel.SendAutoSell(uInfo)
				end
			end
	
			VendorOrderModel.DelVendOrorderInfo(hInfo.type, uInfo.userid, hInfo.hangid)
			ThreadManager.OrderUnLock(handID)		
		else	
			--检查用户是否锁定
			if uInfo.islock == 1 then
				retMsg['code'] = 1
				retMsg['msg'] = "你已经被锁定无法进行该操作"	
				return luajson.encode(retMsg)	
			end
			
			--检查是否允许挂卖
			if uInfo.ishangbuy == 2 then
				retMsg['code'] = 1
				retMsg['msg'] = "您被设置为不允许挂买"	
				return luajson.encode(retMsg)
			end
			
			if uInfo.isacceptorder == 1 then
				retMsg['code'] = 1
				retMsg['msg'] = "该用户已被禁止接单"	
				return luajson.encode(retMsg)
			end
			
			if uInfo.isteamacceptorder == 1 then
				retMsg['code'] = 1
				retMsg['msg'] = "该团队已被禁止接单"	
				return luajson.encode(retMsg)
			end
			
			local minmoney = tonumber(arrData.minmoney) or 0
			local maxmoney = tonumber(arrData.maxmoney) or 0
			if minmoney <= 0 or minmoney > maxmoney then
				retMsg['code'] = 1
				retMsg['msg'] = "限额有错误"	
				return luajson.encode(retMsg)
			end
		
			--检查接单限制跟交易限制
			local sqlCase = "select orders_limit,deal_limit,channel_deal from dy_channel_info where channel='"..uInfo.channel.."'"
			mysqlItem:executeQuery(sqlCase)
			
			local sqlData = mysqlItem:fetch({})
			if sqlData == nil then
				retMsg['code'] = 1
				retMsg['msg'] = "未找到渠道配置"	
				return luajson.encode(retMsg)	
			end
			
			local ordersLimit = tonumber(sqlData[1])	--接单限额
			local dealLimit = tonumber(sqlData[2])		--交易限额	
			local channel_deal = tonumber(sqlData[3]) == nil and 0 or tonumber(sqlData[3])
			local sysPrice = CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.deal_sell, g_marketDefine.currency_type.USDT, true, uInfo.userid)
			local tmpPrice = tonumber(string.format("%.04f", tostring(sysPrice)))
			if ordersLimit > ((tonumber(uInfo.ercusdtamount) - tonumber(uInfo.ercusdtlockamount)) * tmpPrice) then
				retMsg['code'] = 1
				retMsg['msg'] = "该用户余额不足请充值！"	
				return luajson.encode(retMsg)
			end
				
			--交易限制
			if dealLimit > minmoney then
				retMsg['code'] = 1
				retMsg['msg'] = "最小限额小于交易设置"	
				return luajson.encode(retMsg)
			end
			
			--插入到数据库中。
			local currTime = TimeUtils.GetTimeString()
			local sqlCase = "insert into dy_vendor_order(userid,type,price_type,min_money,max_money,auto_switch,create_time,channel,enable_status,user_type,channel_deal,rest_time,platform_id) " 
				.."values("..uInfo.userid..",1,1,"..minmoney..","..maxmoney..",1,'"..currTime.."','"..uInfo.channel.."',1,200,"..channel_deal..",'"..currTime.."',"..uInfo.platformid..")"
			mysqlItem:execute(sqlCase)
			sqlCase = "select id from dy_vendor_order where userid="..uInfo.userid.." and type=1 and create_time='"..currTime.."'"
			mysqlItem:executeQuery(sqlCase)
			local sqlData = mysqlItem:fetch()
			
			if sqlData == nil then
				retMsg['code'] = 1
				retMsg['msg'] = "开启失败"	
				return luajson.encode(retMsg)
			end
			
			local voItem = VendorOrderModel.GetVendorOrderInfo(sqlData)
			if voItem ~= nil then
				--加到用户自己的挂单列表中区
				VendorOrderModel.addUserVendorOrder(uInfo.userid, voItem.type, voItem.hangid)
				--保存到缓存中
				VendorOrderModel.SetVendorOrderInfo( voItem.hangid, voItem )
				if voItem.type == 1 then
					uInfo.autosell = voItem.hangid
					UserInfoModel.SetUserInfo(uInfo)
					UserInfoModel.SendAutoSell(uInfo)
				end
			end
		end
	end
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	

end