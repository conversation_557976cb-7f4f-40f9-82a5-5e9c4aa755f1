
module("HttpGroup", package.seeall)


--创建分组				
--groupName 				组名
--channel					渠道	
--platformID				创建的平台ID											
--sign						签证	
function creategroup(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local groupName = arrData.groupName or ""
	local channel = arrData.channel or ""
	local platformID = arrData.platformID or ""
	
	--检查组名是否存在
	local sqlCase = "select * from dy_group_info where group_name='"..groupName.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlDate = mysqlItem:fetch()
	if sqlDate  ~= nil then
		retMsg['code'],retMsg['msg'] = 1, "组名已经存在！"
		return luajson.encode(retMsg)	
	end
	
	local sqlCase = "select channel_type from dy_channel_info where channel='"..channel.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		retMsg['code'],retMsg['msg'] = 1, "渠道号不存在"
		return luajson.encode(retMsg)
	end
	
	if tonumber(sqlData) ~= 200 and tonumber(sqlData) ~= 400  then
		retMsg['code'],retMsg['msg'] = 1, "不是币商渠道或码商渠道创建失败"
		return luajson.encode(retMsg)
	end
	
	local sqlCase = "insert into dy_group_info(group_name, channel,platform_id) values('"..groupName.."','"..channel.."',"..platformID..")"
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end

--设置小组成员	
--groupID					小组ID			
--userid 					用户ID										
--optType 					1-添加 2-删除										
--sign						签证	
function setgroupmember(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local groupID = tonumber(arrData.groupID) or 0
	local userid = tonumber(arrData.userid) or 0
	local optType = tonumber(arrData.optType) or 0
	local groupName = ""
	
	local sqlCase = "select group_name, channel from dy_group_info where id="..groupID
	mysqlItem:executeQuery(sqlCase)
	local sqlDate = mysqlItem:fetch({})
	if sqlDate == nil then
		retMsg['code'],retMsg['msg'] = 1, "小组不存在"
		return luajson.encode(retMsg)		
	end
	
	groupName = sqlDate[1]
	channel = sqlDate[2]
	
	if optType ~= 1 and optType ~= 2 then
		retMsg['code'],retMsg['msg'] = 1, "操作类型错误"
		return luajson.encode(retMsg)	
	end
	
	local uInfo = UserInfoModel.GetUserInfo(userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "用户不存在"
		return luajson.encode(retMsg)
	end
	
	local status = 0
	local sqlCase = "select status from dy_group_member where group_user_id="..userid.." and group_id="..groupID.." order by id desc"
	mysqlItem:executeQuery(sqlCase)
	local sqlDate = mysqlItem:fetch()
	if sqlDate ~= nil then
		status = tonumber(sqlDate)
	end
	
	
	local num = 0
	if optType == 1 then
		--添加
		if status == 1 then
			retMsg['code'],retMsg['msg'] = 1, "用户已存在"
			return luajson.encode(retMsg)
		end
		
		if uInfo.channel ~= channel then
			retMsg['code'],retMsg['msg'] = 1, "用户不属于小组渠道，添加失败"
			return luajson.encode(retMsg)
		end
		
		local sqlCase = "insert into dy_group_member(group_id,group_name,group_user_id,group_user_name,status) values("
			..groupID..",'"..groupName.."',"..uInfo.userid..",'"..uInfo.nickname.."', 1)"
		mysqlItem:execute(sqlCase)
		
		local sqlCase = "update dy_group_info set group_num=group_num+1 where id="..groupID
		mysqlItem:execute(sqlCase)
		
	elseif optType == 2 then
		--删除
		if status ~= 1 then
			retMsg['code'],retMsg['msg'] = 1, "用户已经被删除或者已经存在"
			return luajson.encode(retMsg)
		end
		local sqlCase = "update dy_group_member set status=2 where group_id="..groupID.." and group_user_id="..uInfo.userid
		mysqlItem:execute(sqlCase)
		
		local sqlCase = "update dy_group_info set group_num=group_num-1 where id="..groupID
		mysqlItem:execute(sqlCase)
	end
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end



--设置小组接单
--groupID					小组ID			
--optID						1-充值开关 2-提现开关 3-代付开关									
--status 					0-关闭 1-开启									
--sign						签证	
function setgrouporders(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end

	local groupID = tonumber(arrData.groupID) or 0
	local optID = tonumber(arrData.optID) or 0
	local status = tonumber(arrData.status) or 0
	
	local sqlCase = "select * from dy_group_info where id="..groupID
	mysqlItem:executeQuery(sqlCase)
	local sqlDate = mysqlItem:fetch()
	if sqlDate == nil then
		retMsg['code'],retMsg['msg'] = 1, "小组不存在"
		return luajson.encode(retMsg)		
	end
	
	if optID ~= 1 and optID ~= 2 and optID ~= 3 then
		retMsg['code'],retMsg['msg'] = 1, "参数错误1"
		return luajson.encode(retMsg)		
	end
	
	if status ~= 0 and status ~= 1 then
		retMsg['code'],retMsg['msg'] = 1, "参数错误2"
		return luajson.encode(retMsg)		
	end
	
	local tmp = ""
	if optID == 1 then
		tmp = "group_recharge_switch"
	elseif optID == 2 then
		tmp = "group_withdraw_switch"
	else
		tmp = "group_behalf_switch"
	end
	
	local sqlCase = "update dy_group_info set "..tmp.."="..status.." where id="..groupID
	mysqlItem:execute(sqlCase)
	
	local sqlCase = "update dy_group_member set "..tmp.."="..status.." where group_id="..groupID
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end

--删除分组
--groupID					小组ID											
--sign						签证	
function delgrouporders(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end

	local groupID = tonumber(arrData.groupID) or 0
	
	local sqlCase = "select * from dy_group_info where id="..groupID
	mysqlItem:executeQuery(sqlCase)
	local sqlDate = mysqlItem:fetch()
	if sqlDate == nil then
		return luajson.encode(retMsg)		
	end
	
	local sqlCase = "delete from dy_group_info where id="..groupID
	mysqlItem:execute(sqlCase)
	
	local sqlCase = "delete from dy_group_member where group_id="..groupID
	mysqlItem:execute(sqlCase)
	
	local sqlCase = "delete from dy_user_conf where bind_type=104 and bind_userid="..groupID
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end




