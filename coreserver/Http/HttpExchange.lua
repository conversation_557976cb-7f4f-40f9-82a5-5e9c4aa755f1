module("HttpExchange", package.seeall)


--更新订单信息
--orderID				 	订单号
--payeeAccount 				收款人账号
--payeeName 				收款人名字
--payeeBank 				收款银行
--tradeId 					交易所订单号
--sign 						签证
function updateOrderInfo(rcvData)
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	local coinInfo = CustomerOrderModel.GetCustomerOrderInfo(arrData.orderID)
	
	if coinInfo == nil then
		retMsg['code'] = 1
		retMsg['msg'] = "订单不存在"
		return luajson.encode(retMsg)	
	end
	
	local payInfo = {}
	payInfo["id"] = 0
	payInfo["paytype"] = g_humanDefine.user_payList.bank
	payInfo["account"] = arrData.payeeAccount or ""
	payInfo["payee"] = arrData.payeeName or ""
	payInfo["qrcode"] = ""
	payInfo["bankname"] = arrData.payeeBank or ""
	payInfo["bankaddr"] = ""
	payInfo["singlelimit"] = "0"
	payInfo["daylimit"] = "0"
	payInfo["fourthpartyid"] = 0
	payInfo["deallasttime"] = "2020-01-01 00:00:00"
	coinInfo.paytypelist = luajson.encode(payInfo)
	CustomerOrderModel.SetCustomerOrderInfo(coinInfo)
	
	local sqlCase = "update dy_customer_order set pay_type='"..coinInfo.paytypelist.."',payee_account='"..payInfo.account.."',payee_name='"
	..payInfo.payee.."',payee_bank='"..payInfo.bankname.."',trade_id="..(arrData.tradeId or "").." where id="..arrData.orderID
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	


end


--更新订单状态
--orderID 							订单号
--status 							状态 1-成功, 2-取消, 3-待放行, 4-激活
--proofurl 							支付凭证
--tradeId							交易所订单号					
--sign 签证
function updateOrderStatus(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	--查找订单号
	local sqlCase = "select id from dy_customer_order where trade_id="..arrData.tradeId
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		retMsg['code'] = 1
		retMsg['msg'] = "订单不存在"
		return luajson.encode(retMsg)	
	end
	arrData.orderID = tonumber(sqlData)
		
	ThreadManager.DealLock(arrData.orderID)
	local coinInfo = CustomerOrderModel.GetCustomerOrderInfo(arrData.orderID)
	
	if coinInfo == nil then
		retMsg['code'] = 1
		retMsg['msg'] = "订单不存在"
		return luajson.encode(retMsg)	
	end
	
	if coinInfo.dealtype ~= 200 and coinInfo.dealtype ~= 300 then
		retMsg['code'] = 1
		retMsg['msg'] = "订单类型错误"
		return luajson.encode(retMsg)	
	end
	
	local userid = 0
	if coinInfo.dealtype == 200 then
		userid = coinInfo.vendoruserid
	elseif coinInfo.dealtype == 300 then
		userid = coinInfo.customeruserid
	end
	
	local uInfo = UserInfoModel.GetUserInfo(userid)
	if uInfo == nil then
		retMsg['code'] = 1
		retMsg['msg'] = "用户不存在"
		return luajson.encode(retMsg)	
	end
	
	local status = tonumber(arrData.status)
	if status == 1 then
		--放行
		if coinInfo.status == g_marketDefine.deal_status_pay or coinInfo.status == g_marketDefine.deal_status_appeal or coinInfo.status == g_marketDefine.deal_status_wait then
			local ret, msg = VendorOrderService.PassOrder(uInfo,coinInfo)
			if ret ~= nil then
				ThreadManager.DealUnLock(arrData.orderID)
				retMsg['code'] = ret
				retMsg['msg'] = msg
				return luajson.encode(retMsg)	
			end
		end
	elseif status == 2 then
		--取消
		local ret, msg = VendorOrderService.CanelOrder(uInfo, coinInfo, false)
		if ret ~= nil then
			ThreadManager.DealUnLock(arrData.orderID)
			retMsg['code'] = ret
			retMsg['msg'] = msg
			return luajson.encode(retMsg)	
		end
	elseif status == 3 then
		--付款
		local proofurlList = nil
		if arrData.proofurl ~= nil and arrData.proofurl ~= "" then
			proofurlList = string.split((arrData.proofurl), ",")
		end
		local ret, msg = VendorOrderService.PaidOrder(uInfo, coinInfo, proofurlList, 1)
		if ret ~= nil then
			ThreadManager.DealUnLock(arrData.orderID)
			retMsg['code'] = ret
			retMsg['msg'] = msg
			return luajson.encode(retMsg)	
		end
	elseif status == 4 then
		--激活
		if coinInfo.status ~= g_marketDefine.deal_status_wait_timeout and coinInfo.status ~= g_marketDefine.deal_status_cancel and coinInfo.status ~= g_marketDefine.deal_status_appeal_cancel then
			ThreadManager.DealUnLock(arrData.orderID) 
			retMsg['code'] = 1
			retMsg['msg'] = "该状态下，不能激活订单"	
			return luajson.encode(retMsg)	
		end
		
		local payeeInfo = {}		--收款人
		local payerInfo = {}		--付款人
		if coinInfo.type == g_marketDefine.hang_buy then
			--顾客买币
			payeeInfo = UserInfoModel.GetUserInfo(coinInfo.vendoruserid)
			payerInfo = UserInfoModel.GetUserInfo(coinInfo.customeruserid)
		else
			--顾客卖币
			payeeInfo = UserInfoModel.GetUserInfo(coinInfo.customeruserid)
			payerInfo = UserInfoModel.GetUserInfo(coinInfo.vendoruserid)
		end
		
		local currencyCount = tonumber(coinInfo.amount)
		local moneyCount = 0
		if coinInfo.dealtype == 300 then
			currencyCount = currencyCount + tonumber(coinInfo.fee)
			moneyCount = (tonumber(coinInfo.money) or 0) + tonumber(coinInfo.feemoney)
		end
		
		local channelInfo = {}
		local sqlCase = "select channel,is_external from dy_channel_info"
		mysqlItem:executeQuery(sqlCase)
		while true do 
			local sqlData = mysqlItem:fetch({})
			if sqlData == nil then
				break
			end
			local tmp = {}
			tmp['isExternal'] = tonumber(sqlData[2])
			channelInfo[sqlData[1]] = tmp
		end
		
		--检查收款人资金是否还有
		if channelInfo[payeeInfo.channel].isExternal == 0 then
			if (tonumber( payeeInfo.ercusdtamount) - tonumber( payeeInfo.ercusdtlockamount)) < currencyCount then
				ThreadManager.DealUnLock(arrData.orderID) 
				retMsg['code'] = 1
				retMsg['msg'] = "收款人余额不足！"	
				return luajson.encode(retMsg)		
			end
		end
		
		if payeeInfo.dealcointype == 1 and channelInfo[payeeInfo.channel].isExternal == 0 then
			if false == UserInfoModel.AddErcFCLockAmount(payeeInfo, moneyCount, arrData.orderID, coinInfo.type, "激活订单冻结资金") then
				ThreadManager.DealUnLock(arrData.orderID) 
				retMsg['code'] = 1
				retMsg['msg'] = "收款人余额不足！"	
				return luajson.encode(retMsg)	
			end
		end
		
		--冻结收款人的币
		if false == UserInfoModel.AddErcUsdtLockAmount(payeeInfo, currencyCount, arrData.orderID, coinInfo.type, "激活订单冻结资金", channelInfo[payeeInfo.channel].isExternal) then
			ThreadManager.DealUnLock(arrData.orderID) 
			retMsg['code'] = 1
			retMsg['msg'] = "收款人币数量不足！"	
			return luajson.encode(retMsg)	
		end
		
		CustomerOrderModel.UpdateStatus(coinInfo, g_marketDefine.deal_status_pay)
		local sqlCase = "update dy_customer_order set status=3 where id="..arrData.orderID
		mysqlItem:execute(sqlCase)
		
		--插到主循环列表中
		CustomerOrderModel.AddCustomerOrderList(coinInfo)
		
		--向前端更新用户冻结金币
		UserInfoModel.SendErcUsdtLockAmount(payeeInfo)
		
		--向客户更新用户大厅的待处理列表
		local userList = {payeeInfo.userid, payerInfo.userid} 
		NoticeServices.NoticeHangSellOut(userList)
		
		local voItem = VendorOrderModel.GetVendorOrderInfo(coinInfo.vendororderid)
		if voItem ~= nil then
			if coinInfo.iswait == 1 then
				coinInfo.iswait = 0
				CustomerOrderModel.SetCustomerOrderInfo(coinInfo)	
				local sqlCase = "update dy_customer_order set is_wait="..coinInfo.iswait.." where id="..coinInfo.dealid
				mysqlItem:execute(sqlCase)
				CustomerOrderModel.delCustomerWithdrawOrderList(coinInfo.dealid)
			else
				voItem.unsoldordernum = voItem.unsoldordernum + 1
				voItem.unsoldorderamount = tostring(tonumber(voItem.unsoldorderamount) + tonumber(coinInfo.amount))
				voItem.unsoldordermoney = tostring((tonumber(voItem.unsoldordermoney) or 0) + tonumber(coinInfo.money))
				VendorOrderModel.SetVendorOrderInfo( voItem.hangid, voItem )
				local sqlCase = "update dy_vendor_order set unsold_order_num=unsold_order_num+1, unsold_order_amount=unsold_order_amount+"
					..tonumber(coinInfo.amount)..", unsold_order_money=unsold_order_money+"..tonumber(coinInfo.money).." where id="..voItem.hangid
				mysqlItem:execute(sqlCase)
			end
			
			local payInfo = luajson.decode(coinInfo.paytypelist)
			--统计支付方式的累计收款量
			UserInfoModel.SetDayLimit(payInfo.id, coinInfo.money)
		end
	end
	ThreadManager.DealUnLock(arrData.orderID)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end

