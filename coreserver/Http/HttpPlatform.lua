module("HttpPlatform", package.seeall)

--创建子平台
--platformName 					子平台名称
--account						账号
--pwd							密码
--contactDetails				联系方式
--googleAuthSecret				谷歌密钥
--isVerifiedGoogle				是否开启谷歌验证 1-验证 2-不验证
--sign 							签证
function createsubplatform(rcvData)   
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local platformName = arrData.platformName or ""
	local account = arrData.account or ""
	local pwd = arrData.pwd or ""
	local contactDetails = arrData.contactDetails or ""
	local googleAuthSecret = arrData.googleAuthSecret or ""
	local isVerifiedGoogle = tonumber(arrData.isVerifiedGoogle) or 0
	
	if platformName == "" or account == "" or pwd == "" or contactDetails == "" then
		retMsg['code'],retMsg['msg'] = 1, "参数错误"
		return luajson.encode(retMsg)
	end
	
	if isVerifiedGoogle == 1 and googleAuthSecret == "" then
		retMsg['code'],retMsg['msg'] = 1, "开启谷歌验证时， 谷歌密钥不能为空"
		return luajson.encode(retMsg)		
	end
	
	--检查子平台名称
	local sqlCase = "select * from dy_subsystem_info where platform_name='"..platformName.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData ~= nil then
		retMsg['code'],retMsg['msg'] = 1, "平台已经存在"
		return luajson.encode(retMsg)		
	end
	
	--检查账号是否存在
	local sqlCase = "select * from dy_subsystem_info where account='"..account.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData ~= nil then
		retMsg['code'],retMsg['msg'] = 1, "账号已经存在1"
		return luajson.encode(retMsg)		
	end
	
	--检查账号是否存在
	local sqlCase = "select * from dy_sub_user_info where account='"..account.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData ~= nil then
		retMsg['code'],retMsg['msg'] = 1, "账号已经存在2"
		return luajson.encode(retMsg)		
	end
	
	local sqlCase = "insert into dy_subsystem_info(platform_name,accout,contact_etails) value('"..platformName.."','"..account.."','"..contactDetails.."')"
	mysqlItem:execute(sqlCase)
	
	local sqlCase = "select id from dy_subsystem_info where platform_name='"..platformName.."' and accout='"..account.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		retMsg['code'],retMsg['msg'] = 1, "创建失败"
		return luajson.encode(retMsg)	
	end
	
	local platformID = tonumber(sqlData)
	local sqlCase = "insert into dy_sub_user_info(account,password,user_type,is_verified_google,google_auth_secret,platform_id) value('"
		..account.."','"..md5(pwd).."',100,"..isVerifiedGoogle..",'"..googleAuthSecret.."',"..platformID..")"
	mysqlItem:execute(sqlCase)
	
	--创建子平台账号
	local cid = UserInfoModel.GetRandomCID()
	local sqlCase = "insert into dy_user_info(cid,account,password,eth_address,nickname,user_type,username,payidlist,platform_id) values('','','','','"..platformName.."-系统用户',100,'"..platformName.."-系统用户','[]',"..platformID..")"
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end


--编辑子平台
--platformID					子平台ID
--pwd							密码
--googleAuthSecret				谷歌密钥
--isVerifiedGoogle				是否开启谷歌验证
--contactDetails				联系方式
--sign 							签证
function editsubplatform(rcvData)   
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local platformID = arrData.platformID or 0 
	local pwd = arrData.pwd or ""
	local googleAuthSecret = arrData.googleAuthSecret or ""
	local contactDetails = arrData.contactDetails or ""
	local isVerifiedGoogle = arrData.isVerifiedGoogle or 0
	
	if contactDetails == "" then
		retMsg['code'],retMsg['msg'] = 1, "参数错误"
		return luajson.encode(retMsg)
	end
	
	if isVerifiedGoogle == 1 and googleAuthSecret == "" then
		retMsg['code'],retMsg['msg'] = 1, "开启谷歌验证时， 谷歌密钥不能为空"
		return luajson.encode(retMsg)		
	end
	
	--检查子平台是否存在
	local sqlCase = "select accout from dy_subsystem_info where id="..platformID
	mysqlItem:execute(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		retMsg['code'],retMsg['msg'] = 1, "子平台不存在"
		return luajson.encode(retMsg)	
	end
	
	local account = sqlData
	
	local sqlCase = "update dy_subsystem_info set contact_etails='"..contactDetails.."' where id="..platformID
	mysqlItem:execute(sqlCase)
	
	local sqlCase = "update dy_sub_user_info set is_verified_google="..isVerifiedGoogle..", google_auth_secret='"..googleAuthSecret.."'"
	if pwd ~= "" then
		sqlCase = sqlCase..", password='"..md5(pwd).."'"
	end
	sqlCase = sqlCase.." where platform_id="..platformID.." and account='"..account.."' and user_type=100"
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end



--设置子平台是否能登陆
--platformID					子平台ID
--status						是否能登陆 0-允许 1 禁止
--sign 							签证
function setsubplatformlogin(rcvData)   
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local platformID = arrData.platformID or 0 
	local status = tonumber(arrData.status) or 1
	
	--检查子平台是否存在
	local sqlCase = "select accout from dy_subsystem_info where id="..platformID
	mysqlItem:execute(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		retMsg['code'],retMsg['msg'] = 1, "子平台不存在"
		return luajson.encode(retMsg)	
	end
	
	local account = sqlData
	
	local sqlCase = "update dy_sub_user_info set prohibit_login="..status..",is_lock="..status.." where platform_id="..platformID.." and account='"..account.."' and user_type=100"
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end

--关联用户
--platformID					子平台ID
--type							关联类型 1-商户 2 渠道
--userid						关联用户ID
--optType						1-添加 2-删除
--sign 							签证
function associatedusers(rcvData)  
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local platformID = arrData.platformID or 0 
	local userid = tonumber(arrData.userid) or 0
	local optType = tonumber(arrData.optType)
	local userType = tonumber(arrData.type)
	
	if (optType ~= 1 and optType ~= 2) 
	or (userType ~= 1 and userType ~= 2) then
		retMsg['code'],retMsg['msg'] = 1, "参数错误"
		return luajson.encode(retMsg)	
	end 
	
	
	--检查子平台是否存在
	local sqlCase = "select related_shop_channel_list,related_coin_channel_list  from dy_subsystem_info where id="..platformID
	mysqlItem:execute(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		retMsg['code'],retMsg['msg'] = 1, "子平台不存在"
		return luajson.encode(retMsg)	
	end
	
	local relatedShopChannelList = {}
	if sqlData[1] ~= nil and sqlData[1] ~= "" then
		relatedShopChannelList = luajson.decode(sqlData[1])
	end
	local relatedCoinChannelList = {}
	if sqlData[2] ~= nil and sqlData[2] ~= "" then
		relatedCoinChannelList = luajson.decode(sqlData[2])
	end
	
	local uInfo = UserInfoModel.GetUserInfo(userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "用户不存在"
		return luajson.encode(retMsg)	
	end
	
	
	if userType == 1 then
		--商户
		
		--[[
		if uInfo.usertype ~= 300 or uInfo.agent ~= 0 then
			retMsg['code'],retMsg['msg'] = 1, "该用户不是总代无法关联"
			return luajson.encode(retMsg)
		end
		]]
		if uInfo.usertype ~= 300 and uInfo.usertype ~= 301 then
			retMsg['code'],retMsg['msg'] = 1, "该用户不是商户无法关联"
			return luajson.encode(retMsg)
		end
		
	else
		--渠道
		if (uInfo.usertype ~= 200 and uInfo.usertype ~= 400) or uInfo.agent ~= 0 then
			retMsg['code'],retMsg['msg'] = 1, "该用户不是总代无法关联"
			return luajson.encode(retMsg)
		end
	end
	
	
	local targetPlatformID = 0
	if optType == 1 then
		--保存
		targetPlatformID = tonumber(platformID)
		if uInfo.platformid ~= 0 then
			retMsg['code'],retMsg['msg'] = 1, "该总代属于其他平台，无法关联"
			return luajson.encode(retMsg)
		end
		
	else
		--删除
		if uInfo.platformid ~= tonumber(platformID) then
			retMsg['code'],retMsg['msg'] = 1, "该总代不属于你，无法删除"
			return luajson.encode(retMsg)
		end
	end
	
	if userType == 1 then
	
		uInfo.platformid = targetPlatformID
		UserInfoModel.SetUserInfo(uInfo)
		
		local sqlCase = "update dy_user_info set platform_id="..targetPlatformID.."  where userid="..uInfo.userid
		mysqlItem:execute(sqlCase)
		
		--支付通道
		local sqlCase = "update dy_user_pay set platform_id="..targetPlatformID.."  where userid="..uInfo.userid
		mysqlItem:execute(sqlCase)
		
	else
		
		local userList = {}
		local sqlCase = "select userid from dy_user_info where channel='"..uInfo.channel.."'"
		mysqlItem:executeQuery(sqlCase)
		while true do 
			local sqlData = mysqlItem:fetch()
			if sqlData == nil then
				break
			end
			
			table.insert(userList, tonumber(sqlData))
		end
		
		for k,v in ipairs(userList) do 
			local toInfo = UserInfoModel.GetUserInfo(v)
			if toInfo ~= nil then
				toInfo.platformid = targetPlatformID
				UserInfoModel.SetUserInfo(toInfo)
			end
		end
		local sqlCase = "update dy_user_info set platform_id="..targetPlatformID.."  where channel='"..uInfo.channel.."'"
		mysqlItem:execute(sqlCase)
		
		--支付通道
		local sqlCase = "update dy_user_pay set platform_id="..targetPlatformID.."  where channel='"..uInfo.channel.."'"
		mysqlItem:execute(sqlCase)
		
		--支付通道
		local sqlCase = "update dy_channel_info set platform_id="..targetPlatformID.."  where channel='"..uInfo.channel.."'"
		mysqlItem:execute(sqlCase)
	
	end
	
	if optType == 1 then
		--保存
		if userType == 1 then
			--商户
			local isEx = false 
			for k,v in ipairs(relatedShopChannelList) do
				if uInfo.userid == tonumber(v.userid) then
					isEx = true
				end
			end
			
			if isEx == false then
				local tmp = {}
				tmp['userid'] = uInfo.userid
				tmp['nickname'] = uInfo.nickname
				table.insert(relatedShopChannelList, tmp)
				local sqlCase = "update dy_subsystem_info set related_shop_channel_list='"..luajson.encode(relatedShopChannelList).."' where id="..platformID
				mysqlItem:execute(sqlCase)
			end
		else
			--渠道
			local isEx = false 
			for k,v in ipairs(relatedCoinChannelList) do
				if uInfo.userid == tonumber(v.userid) then
					isEx = true
				end
			end
			if isEx == false then
				local tmp = {}
				tmp['userid'] = uInfo.userid
				tmp['nickname'] = uInfo.nickname
				table.insert(relatedCoinChannelList, tmp)
				local sqlCase = "update dy_subsystem_info set related_coin_channel_list='"..luajson.encode(relatedCoinChannelList).."' where id="..platformID
				mysqlItem:execute(sqlCase)
			end
		end
	else
		--删除
		if userType == 1 then
			--商户
			for i=1, #relatedShopChannelList do
				if uInfo.userid == tonumber(relatedShopChannelList[i].userid) then
					table.remove(relatedShopChannelList, i)
					break
				end
			end
			
			local sqlCase = "update dy_subsystem_info set related_shop_channel_list='"..luajson.encode(relatedShopChannelList).."' where id="..platformID
			mysqlItem:execute(sqlCase)
		else
			--渠道
			for i=1, #relatedCoinChannelList do
				if uInfo.userid == tonumber(relatedCoinChannelList[i].userid) then
					table.remove(relatedCoinChannelList, i)
					break
				end
			end
			local sqlCase = "update dy_subsystem_info set related_coin_channel_list='"..luajson.encode(relatedCoinChannelList).."' where id="..platformID
			mysqlItem:execute(sqlCase)
		end
	end
	
	
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
	
	
	
end


--设置服务费率
--platformID					子平台ID
--payIdList 					通道ID列表逗号隔开
--rechargeRateList				充值费率列表逗号隔开
--withdrawRateList				提现费率列表逗号隔开
--behalfRateList 				代付费率列表逗号隔开
--takeCoinRate					提币费率
--closeRate						归拢费率
--sign 							签证
function setservicechargerate(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local platformID = arrData.platformID or 0
	--检查子平台是否存在
	local sqlCase = "select * from dy_subsystem_info where id="..platformID
	mysqlItem:execute(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		retMsg['code'],retMsg['msg'] = 1, "子平台不存在"
		return luajson.encode(retMsg)	
	end
	
	local takeCoinRate = arrData.takeCoinRate or 0
	local closeRate = arrData.closeRate or 0
	
	local payIdList = {}
	local rechargeRateList = {}
	local behalfRateList = {}
	local withdrawRateList = {}
	
	if arrData.payIdList ~= "" then
		payIdList = string.split((arrData.payIdList or ""), ",")
		rechargeRateList = string.split((arrData.rechargeRateList or ""), ",")
		behalfRateList = string.split((arrData.behalfRateList or ""), ",")
		withdrawRateList = string.split((arrData.withdrawRateList or ""), ",")
	end
	
	local rechargeList = {}
	local behalfList = {}
	local withdrawList = {}
	for k, v in ipairs(payIdList) do 
		rechargeList[v] = rechargeRateList[k]
		behalfList[v] = behalfRateList[k]
		withdrawList[v] = withdrawRateList[k]
	end
	
	local rechargeMsg = #payIdList == 0 and "" or luajson.encode(rechargeList)
	local withdrawMsg = #payIdList == 0 and "" or luajson.encode(withdrawList)
	local behalfeMsg = #payIdList == 0 and "" or luajson.encode(behalfList)
	local sqlCase = "update dy_subsystem_info set recharge_rate_list='"..rechargeMsg.."', withdraw_rate_list='"..withdrawMsg
		.."', behalf_rate_list='"..behalfeMsg.."', close_rate="..closeRate..",take_coin_rate="..takeCoinRate.." where id="..platformID
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end


--设置缴费状态
--ID 							
--status						状态 0-未缴费 1-已缴费
--remarks						备注
--optid							操作人
--optname						操作名称
--sign 							签证
function setpaystatus(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local ID = arrData.ID or 0
	local status = arrData.status or 0
	local remarks = arrData.remarks or ""
	local optid = arrData.optid or 0
	local optname = arrData.optname or ""
	--检查子平台是否存在
	local sqlCase = "select * from log_subsystem_daily where id="..ID
	mysqlItem:execute(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		retMsg['code'],retMsg['msg'] = 1, "记录不存在"
		return luajson.encode(retMsg)	
	end
	
	local sqlCase = "update log_subsystem_daily set status="..status..", remarks='"..remarks.."', optid="..optid..",opt_name='"..optname.."' where id="..ID
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end
