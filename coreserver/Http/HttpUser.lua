
module("HttpUser", package.seeall)

--创建渠道商
--channel 					渠道标识						
--phonenum 					联系方式	
--account  					后台账号
--password 					后台密码
--web_account				app账号
--web_password				app密码				
--channel_name				渠道名字	
--channel_deal 				交易类型 0 公开 1 私有	
--fund_scheduling			允许资金调度 0-允许 1 不允许
--is_external				是否外部渠道 0-不是 1-是
--sign						签证	
function createChannelAgent(rcvData)   
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	--检查渠道是否存在
	local sqlCase = "select * from dy_channel_info where channel='"..arrData.channel.."'"
	mysqlItem:execute(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData ~= nil then
		retMsg['code'],retMsg['msg'] = 1, "渠道已存在"
		return luajson.encode(retMsg)	
	end
	--[[
	--检查后台账号是否存在
	local sqlCase = "select * from dy_channel_info where account='"..arrData.account.."'"
	mysqlItem:execute(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData ~= nil then
		retMsg['code'],retMsg['msg'] = 1, "后台账号已存在"
		return luajson.encode(retMsg)	
	end
	]]
	
	--检查前台账号是否
	local sqlCase = "select * from dy_user_info where account='"..arrData.web_account.."' or phonenum='"..arrData.web_account.."' or email='"..arrData.web_account.."'"
	mysqlItem:execute(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData ~= nil then
		retMsg['code'],retMsg['msg'] = 1, "前端账号已存在"
		return luajson.encode(retMsg)	
	end
	
	local channelInfo = {}
	channelInfo["channelName"] = arrData.channel_name
	channelInfo["channel"] = arrData.channel
	channelInfo["contactInfo"] = arrData.phonenum
	channelInfo["backAccount"] = ""
	channelInfo["backPwd"] = ""
	channelInfo["appAccount"] = ""
	channelInfo["appPwd"] = ""
	channelInfo["channelType"] = arrData.channel_type
	channelInfo["channelDeal"] = tonumber(arrData.channel_deal) or 0
	channelInfo["fundScheduling"] = tonumber(arrData.fund_scheduling) or 1
	channelInfo["isExternal"] = tonumber(arrData.is_external) or 0
	local ret = UserInfoModel.CreateChanel(channelInfo)
	if ret == nil or ret == 0 then
		retMsg['code'] = 1
		retMsg['msg'] = "创建渠道失败"
		return luajson.encode(retMsg)		
	end
	
	--创建渠道APP用户
	local regInfo = {}
	local tmp = {"A","B","C","D","E","F","G","H","I","J","K","L","N","M","O","P","q","R","S","T","U","V","W","X","Z"}
	local rName = ""
	for i = 1,7 do 
		rName = rName..(tmp[math.myrandom(1, #tmp)])
	end
	local regInfo = {}
	regInfo["nickname"] = rName
	regInfo["phonenum"] = arrData.web_account
	regInfo["email"] = ""
	regInfo["password"] = arrData.web_password
	regInfo["invitecode"] = ""
	regInfo["channel"] = arrData.channel
	regInfo["bindtype"] = g_humanDefine.bindType_phone
	regInfo["agent"] = 0
	regInfo["usertype"] = g_humanDefine.usertype_currency_agent
	regInfo["account"] = arrData.web_account
	regInfo["paylist"] = {}
	--查出来所有的通道
	local sqlCase = "select pay_id from dy_pay_info where is_lock = 0"
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlData = mysqlItem:fetch()
		if sqlData == nil then
			break
		end
		local tmp = {}
		tmp["payType"] = tonumber(sqlData)
		tmp["buy_free_rate"] = 0
		tmp["sell_free_rate"] = 0
		tmp["buy_common_rate"] = 0
		tmp["sell_common_rate"] = 0
		tmp["behalf_sell_fee_rate"] = 0
		tmp["behalf_buy_comm_rate"] = 0
		tmp["state"] = 1
		table.insert(regInfo["paylist"], tmp)
	end
	regInfo["commtype"] = 101
	regInfo["contactInfo"] = arrData.phonenum
	regInfo["userName"] = arrData.channel
	regInfo["isInvited"] = 0
	regInfo["dealCoinType"] = 0
	regInfo["teamName"] = ""
	regInfo["platformID"] = 0
	regInfo["isTeamAcceptOrder"] = 0
	local userid = UserInfoModel.CreateUser(regInfo)
	local uInfo = UserInfoModel.GetUserInfo(userid)
	if uInfo == nil then
		--创建角色失败
		retMsg['code'] = ReturnCode["human_create_user_err"][1]
		retMsg['msg'] = ReturnCode["human_create_user_err"][2]
		return luajson.encode(retMsg)		
	end
	
	--更新渠道对应的ID
	local sqlCase= "update dy_channel_info set userid="..uInfo.userid.." where id="..ret
	mysqlItem:execute(sqlCase)
	
	--外部渠道默认开启自动懂接单
	if channelInfo["isExternal"] == 1 then
		local cTime = TimeUtils.GetTimeString()
		local sqlCase = "insert into dy_vendor_order(userid,type,price_type,min_money,max_money,auto_switch,create_time,"
			.."channel,enable_status,user_type,channel_deal,rest_time,is_external,platform_id) " .."values("..uInfo.userid..",1,1,1,99999999,1,'"
			..cTime.."','"..uInfo.channel.."',1,200,"..channelInfo["channelDeal"]..",'"..cTime.."',1,"..uInfo.platformid..")"
		mysqlItem:execute(sqlCase)
		local sqlCase = "insert into dy_vendor_order(userid,type,price_type,min_money,max_money,auto_switch,create_time,"
			.."channel,enable_status,user_type,channel_deal,rest_time,is_external,platform_id) " .."values("..uInfo.userid..",0,1,1,99999999,1,'"
			..cTime.."','"..uInfo.channel.."',1,200,"..channelInfo["channelDeal"]..",'"..cTime.."',1,"..uInfo.platformid..")"
		mysqlItem:execute(sqlCase)
	end	
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end


--删除渠道
--id 						渠道id
--sign						签证
function deleteChannelAgent(rcvData) 
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	--检查渠道是否存在
	local sqlCase = "select * from dy_channel_info where id="..arrData.id
	mysqlItem:execute(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData ~= nil then
		retMsg['code'],retMsg['msg'] = 1, "渠道不存在"
		return luajson.encode(retMsg)	
	end
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end 

--设置汇差
--channel					渠道名		
--currency_type				币种类型 默认USDT
--add_buy_price				设置购买汇差
--minus_sell_price			设置出售汇差
--minus_coin_pay_price		设置币支付汇差
--is_fixed_buy_rate			是否固定购买汇率 0 否 1是	
--fixed_buy_rate			固定购买汇率
--is_fixed_sell_rate		是否固定出售汇率 0 否 ， 1是
--fixed_sell_rate			固定出售汇率 
--is_fixed_coin_pay_rate	是否固定币支付汇率 0 否 ， 1是
--fixed_coin_pay_rate		固定币支付汇率
--withdraw_coin_price_diff	提币价格差
--is_withdraw_coin_pay_rate	是否固定提币汇率 0 否 ， 1是
--withdraw_coin_rate	固定提币汇率
--sign						签证
function setRateDiff(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end

	--查询渠道号
	local sqlCase = "select * from dy_channel_info where channel='"..arrData.channel.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		retMsg['code'],retMsg['msg'] = 1, "渠道号不存在"
		return luajson.encode(retMsg)	
	end
	
	local minusCoinPayPrice = tonumber(arrData.minus_coin_pay_price) or 0
	local isFixedCoinPayRate = tonumber(arrData.is_fixed_coin_pay_rate) or 0
	local fixedCoinPayRate = tonumber(arrData.fixed_coin_pay_rate) or 0
	local withdrawCoinPriceDiff = tonumber(arrData.withdraw_coin_price_diff) or 0
	local isWithdrawCoinPayRate = tonumber(arrData.is_withdraw_coin_pay_rate) or 0
	local withdrawCoinRate = tonumber(arrData.withdraw_coin_rate) or 0
	
	local sqlCase = "update dy_coin_info set add_buy_price="..arrData.add_buy_price..", minus_sell_price="..arrData.minus_sell_price..", minus_coin_pay_price="..minusCoinPayPrice
				..", is_fixed_buy_rate="..arrData.is_fixed_buy_rate..", fixed_buy_rate="..arrData.fixed_buy_rate
				..", is_fixed_sell_rate="..arrData.is_fixed_sell_rate..", fixed_sell_rate="..arrData.fixed_sell_rate
				..", is_fixed_coin_pay_rate="..isFixedCoinPayRate..", fixed_coin_pay_rate="..fixedCoinPayRate
				..",withdraw_coin_price_diff="..withdrawCoinPriceDiff..", is_withdraw_coin_pay_rate="..isWithdrawCoinPayRate
				..", withdraw_coin_rate="..withdrawCoinRate
				.." where channel='"..arrData.channel.."' and coin_type='"..arrData.currency_type.."'"
	mysqlItem:execute(sqlCase)
	
	local sqlCase = "select userid from dy_user_info where channel='"..arrData.channel.."'"
	mysqlItem:executeQuery(sqlCase)
	
	local userList = OnlineModel.GetOnlineUserList()
	while true do 
		local sqlData = mysqlItem:fetch(sqlCase)
		if sqlData == nil then
			break
		end
		
		if true == OnlineModel.CheckOnline(tonumber(sqlData)) then
			table.insert(userList, tonumber(sqlData))
		end
	end
	for k,v in ipairs(userList) do 
		local gcmsg = msg_order2_pb.gcexchangerate()
		gcmsg.buyrate = tostring(CoinInfoService.Erc20USDTPrice(arrData.channel, g_marketDefine.hang_buy, g_marketDefine.currency_type.USDT, false, 0))
		gcmsg.sellrate = tostring(CoinInfoService.Erc20USDTPrice(arrData.channel, g_marketDefine.hang_sell, g_marketDefine.currency_type.USDT, false, 0))
		gcmsg.result = 0
		SendMessage(v, PacketCode[2050].client, gcmsg:ByteSize(),gcmsg:SerializeToString())
	end
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
	
end

--配置渠道信息	
--id 						渠道ID					
--channel					渠道名	
--phonenum   				联系方式
--office_url				独立域名
--is_close					是否关闭渠道 0开通 1=关闭 默认0			
--server_ip					服务器IP
--comm_type					返佣规则：101 = 按级差返佣，102=按保底返佣
--channel_deal				交易类型：0：公开，1:私有
--register_type_json
--currency_type				渠道币种 usdt,cnyb,eth,btc
--pay_json					支付通过及费率设置{8201:10,8101:20,8301:5}
--buy_comm_rate				平台手续费率
--add_buy_comm_rate			平台加收手续费率
--is_add_buy_comm 			是否收取手续费 0 不收，1 收
--orders_limit				接单限额 人民币
--deal_limit				交易限制 人民币
--is_currency_buy_rate 		是否收取币商手续费 0 不收，1 收
--currency_add_buy_rate 	币商买币加收收益费率
--buy_json					接提现费率设置{8201:10,8101:20,8301:5}
--behalf_buy_json			接代付费率设置{8201:10,8101:20,8301:5}
--is_transfer_review		转账是否需要审核0 需要 1 不需要
--sign						签证	
function setChannelInfo(rcvData)    
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	--渠道是否存在
	local sqlCase = "select userid from dy_channel_info where id="..arrData.id
	mysqlItem:execute(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		retMsg['code'],retMsg['msg'] = 1, "渠道不存在"
		return luajson.encode(retMsg)	
	end
	
	local uInfo = UserInfoModel.GetUserInfo(tonumber(sqlData))
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "渠道用户不存在"
		return luajson.encode(retMsg)	
	end
	
	--更新渠道对应的ID
	local sqlCase= "update dy_channel_info set is_close="..arrData.is_close..", office_url='"..arrData.office_url
					.."', server_ip='"..arrData.server_ip.."', comm_type="..arrData.comm_type..",currency_type='"..arrData.currency_type
					.."', buy_comm_rate='"..arrData.buy_comm_rate.."', sell_comm_rate='"..arrData.buy_comm_rate.."', add_buy_comm_rate="..arrData.add_buy_comm_rate
					..", add_sell_comm_rate="..arrData.add_buy_comm_rate..", register_type='"..arrData.register_type_json.."', orders_limit="..arrData.orders_limit
					..", deal_limit="..arrData.deal_limit..", is_add_buy_comm="..arrData.is_add_buy_comm..",is_currency_buy_rate="..(arrData.is_currency_buy_rate or 0)
					..",currency_add_buy_rate="..(arrData.currency_add_buy_rate or 0)..",is_transfer_review="..(arrData.is_transfer_review or 0).." where id="..arrData.id
	mysqlItem:execute(sqlCase)
	
	
	--查出来所有的通道
	local sqlCase = "select pay_id from dy_pay_info where is_lock = 0"
	mysqlItem:executeQuery(sqlCase)
	local payList = {}
	while true do 
		local sqlData = mysqlItem:fetch()
		if sqlData == nil then
			break
		end
		table.insert(payList, tonumber(sqlData))
	end
	--不存在就要渠道的用户都要插入一天
	local sqlCase = "select userid from dy_user_info where channel='"..uInfo.channel.."'"
	mysqlItem:executeQuery(sqlCase)
	local userList = {}
	while true do
		local sqlData = mysqlItem:fetch()
		if sqlData == nil then
			break
		end
		table.insert(userList, tonumber(sqlData))
	end
	
	local buyTypeList = luajson.decode(arrData.buy_json)
	local behalfBuyTypeList = luajson.decode(arrData.behalf_buy_json)
	local payTypeList = luajson.decode(arrData.pay_json)
	local tmp = {}
	for k,v in pairs(payTypeList) do 
		table.insert(tmp, tonumber(k))
	end
	
	for _, payType in ipairs(payList) do 
		
		local sqlCase= "select * from dy_user_rate where channel='"..uInfo.channel.."' and paytype="..payType
		mysqlItem:execute(sqlCase)
		local sqlData = mysqlItem:fetch()
		local isEx = false
		if sqlData ~= nil then
			isEx = true
		end
		local status = payTypeList[tostring(payType)] == nil and 0  or 1
		if isEx == false then
			for k, v in ipairs(userList) do 
				local sqlCase = "insert into dy_user_rate(userid,paytype,is_used,channel) values("..v..","..payType..","..status..",'"..uInfo.channel.."')"
				mysqlItem:execute(sqlCase)
			end
		else
			local sqlCase = "update dy_user_rate set is_used="..status.." where channel='"..uInfo.channel.."' and paytype="..payType
			mysqlItem:execute(sqlCase)
		end
		
		local sqlCase = "update dy_user_info set payidlist='"..luajson.encode(tmp).."',back_mobile='"..arrData.phonenum.."' where channel='"..uInfo.channel.."'"
		mysqlItem:execute(sqlCase)
		
		--设置自己的费率
		local isOk = false
		local sqlCase = "update dy_user_rate set "
		if payTypeList[tostring(payType)] ~= nil then
			local rate = tonumber(payTypeList[tostring(payType)])
			sqlCase = sqlCase.."sell_comm_rate="..rate
			UserInfoModel.SetUserFeeRate(uInfo.userid, payType, g_humanDefine.sell_comm_rate, rate)
			isOk = true
		end
		
		if buyTypeList[tostring(payType)] ~= nil then
			if isOk == true then
				sqlCase = sqlCase..","
			end
			local rate = tonumber(buyTypeList[tostring(payType)])
			sqlCase = sqlCase.."buy_comm_rate="..rate
			UserInfoModel.SetUserFeeRate(uInfo.userid, payType, g_humanDefine.buy_comm_rate, rate)
			isOk = true
		end
		
		if behalfBuyTypeList[tostring(payType)] ~= nil then
			if isOk == true then
				sqlCase = sqlCase..","
			end
			local rate = tonumber(behalfBuyTypeList[tostring(payType)])
			sqlCase = sqlCase.."behalf_buy_comm_rate="..rate
			UserInfoModel.SetUserFeeRate(uInfo.userid, payType, g_humanDefine.behalf_buy_comm_rate, rate)
			isOk = true
		end
		
		if isOk == true then
			sqlCase = sqlCase.." where userid="..uInfo.userid.." and paytype="..payType
			mysqlItem:execute(sqlCase)
		end
	end
	
	for k,v in ipairs(userList) do 
		local tmpInfo = UserInfoModel.GetUserInfo(v)
		
		if tmpInfo ~= nil then
			while #tmpInfo.paytypelist > 0 do 
				tmpInfo.paytypelist:remove(1)
			end
			
			for _, payType in ipairs(tmp) do 
				tmpInfo.paytypelist:append(payType)
			end
			
			UserInfoModel.SetUserInfo(tmpInfo)
		end
	end
	
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end

--锁定用户
--id			商户ID	
--is_lock					操作类型 0 解锁 1 锁定
--sign						签证	
function lockUser(rcvData)   
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--检查用户存不存在
	local uInfo = UserInfoModel.GetUserInfo(arrData.id)
	if uInfo == nil  then
		retMsg['code'],retMsg['msg'] = 1,"商户不存在"
		return luajson.encode(retMsg)	
	end

	uInfo.islock = tonumber(arrData.is_lock) or 0
	local status = uInfo.islock == 0 and 1 or 4
	UserInfoModel.SetUserInfo(uInfo)
	UserInfoModel.SendInfoList(uInfo,{"islock"})

	local sqlCase = "update dy_user_info set is_lock="..uInfo.islock..", status="..status.." where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
	
end

--编辑币商信息
--id 						用户ID
--phonenum 					手机号码
--is_hang_buy				是否允许挂买单，1=允许 2不允许
--is_hang_sell				是否允许挂卖单，1=允许 2不允许
--min_buy					最小出售金额
--max_buy					最大出售金额
--min_sell					最小出售金额
--max_sell					最大出售金额
--comm_type					收益类型：101=按级差进行收益，后期添加其他
--prohibit_login			禁止登陆 0 允许登陆 1 禁止登陆
--is_accept_order			是否允许接单 0 允许1 禁止
--user_type 				201 码商 202 币商
--sign						签证	
function editCurrencyDealer(rcvData) 

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--检查用户存不存在
	local uInfo = UserInfoModel.GetUserInfo(arrData.id)
	if uInfo == nil  then
		retMsg['code'],retMsg['msg'] = 1,"商户不存在"
		return luajson.encode(retMsg)	
	end

	uInfo.ishangbuy = tonumber(arrData.is_hang_buy) 
	uInfo.ishangsell = tonumber(arrData.is_hang_sell)
	uInfo.minbuy = tostring(arrData.min_buy)
	uInfo.maxbuy = tostring(arrData.max_buy)
	uInfo.minsell = tostring(arrData.min_sell)
	uInfo.maxsell = tostring(arrData.max_sell)
	uInfo.commtype = tonumber(arrData.comm_type)
	uInfo.prohibitlogin = tonumber(arrData.prohibit_login)
	uInfo.isacceptorder = tonumber(arrData.is_accept_order)
	uInfo.usertype = tonumber(arrData.user_type)
	UserInfoModel.SetUserInfo(uInfo)
	UserInfoModel.SendInfoList(uInfo,{"ishangbuy","ishangsell","prohibitlogin","isacceptorder"})
	
	local sqlCase = "update dy_user_info set is_hang_buy="..uInfo.ishangbuy..", is_hang_sell="..uInfo.ishangsell..", min_buy="..uInfo.minbuy
				..", max_buy="..uInfo.maxbuy..", min_sell="..uInfo.minsell..", max_sell="..uInfo.maxsell.." , comm_type="..uInfo.commtype
				..", prohibit_login="..uInfo.prohibitlogin..", is_accept_order="..uInfo.isacceptorder..",user_type="..uInfo.usertype.." where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	

end

--创建商户代理
--nickname 					商户名称	
--channel					渠道标识
--phone						手机号码
--account					账号		
--password 					密码	
--isInvited					是否允许开设代理，0允许，1禁止		
--sign						签证	
function createShopAgent(rcvData)   
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	--检查渠道是否存在
	local sqlCase = "select * from dy_channel_info where channel='"..arrData.channel.."'"
	mysqlItem:execute(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData ~= nil then
		retMsg['code'],retMsg['msg'] = 1, "渠道已存在"
		return luajson.encode(retMsg)	
	end
	
	--检查前台账号是否
	local sqlCase = "select * from dy_user_info where account='"..arrData.account.."' or phonenum='"..arrData.account.."' or email='"..arrData.account.."'"
	mysqlItem:execute(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData ~= nil then
		retMsg['code'],retMsg['msg'] = 1, "前端账号已存在"
		return luajson.encode(retMsg)	
	end
	
	local channelInfo = {}
	channelInfo["channelName"] = arrData.nickname
	channelInfo["channel"] = arrData.channel
	channelInfo["contactInfo"] = arrData.phone
	channelInfo["backAccount"] = ""
	channelInfo["backPwd"] = ""
	channelInfo["appAccount"] = ""
	channelInfo["appPwd"] = ""
	channelInfo["channelType"] = 300
	channelInfo["channelDeal"] = 0
	channelInfo["fundScheduling"] = 1
	channelInfo["isExternal"] = tonumber(arrData.is_external) or 0
	local ret = UserInfoModel.CreateChanel(channelInfo)
	if ret == nil or ret == 0 then
		retMsg['code'] = 1
		retMsg['msg'] = "创建渠道失败"
		return luajson.encode(retMsg)		
	end
	
	--创建渠道APP用户
	local regInfo = {}
	local tmp = string.split(arrData.account, "@") 
	local nickname = tmp[1]
	regInfo["nickname"] = nickname
	regInfo["phonenum"] = ""
	regInfo["email"] = arrData.account
	regInfo["password"] = arrData.password
	regInfo["invitecode"] = ""
	regInfo["channel"] = arrData.channel
	regInfo["bindtype"] = g_humanDefine.bindType_email
	regInfo["agent"] = 0
	regInfo["usertype"] = g_humanDefine.usertype_merchant_agent
	regInfo["account"] = arrData.account
	regInfo["paylist"] = {}
	local sqlCase = "select pay_id,merchant_min_rate from dy_pay_info where is_lock = 0"
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		local tmp = {}
		tmp["payType"] = tonumber(sqlData[1])
		tmp["buy_free_rate"] = tonumber(sqlData[2])
		tmp["sell_free_rate"] = tonumber(sqlData[2])
		tmp["buy_common_rate"] = 0
		tmp["sell_common_rate"] = 0
		tmp["behalf_sell_fee_rate"] = tonumber(sqlData[2])
		tmp["behalf_buy_comm_rate"] = 0
		tmp["state"] = 1
		table.insert(regInfo["paylist"], tmp)
	end
	regInfo["commtype"] = 101
	regInfo["contactInfo"] = arrData.phone
	regInfo["userName"] = arrData.nickname
	regInfo["isInvited"] = tonumber(arrData.isInvited) or 0
	regInfo["dealCoinType"] = 0
	regInfo["teamName"] = ""
	regInfo["platformID"] = 0
	regInfo["isTeamAcceptOrder"] = 0
	local userid = UserInfoModel.CreateUser(regInfo)
	local uInfo = UserInfoModel.GetUserInfo(userid)
	if uInfo == nil then
		--创建角色失败
		retMsg['code'] = ReturnCode["human_create_user_err"][1]
		retMsg['msg'] = ReturnCode["human_create_user_err"][2]
		return luajson.encode(retMsg)		
	end
	
	--更新渠道对应的ID
	local sqlCase= "update dy_channel_info set userid="..uInfo.userid.." where id="..ret
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
	
end

--设置商户代理费率
--id 						用户id	
--buy_json					{8201:10,8101:20,8301:5}  三个里面有至少有一个 8201银行卡，8101支付宝，8301微信 
--sell_json					{8201:10,8101:20,8301:5}  三个里面有至少有一个 8201银行卡，8101支付宝，8301微信 			
--behalf_sell_json			{8201:10,8101:20,8301:5}  三个里面有至少有一个 8201银行卡，8101支付宝，8301微信 
--is_otc					是否开启OTC交易 0 不开 1 开启	
--is_behalf_pay				是否开启代付交易 0 不开 1 开启
--sign						签证	
function setShopAgentRate(rcvData)  
 
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	--检查用户存不存在
	local uInfo = UserInfoModel.GetUserInfo(arrData.id)
	if uInfo == nil  then
		retMsg['code'],retMsg['msg'] = 1,"商户不存在"
		return luajson.encode(retMsg)	
	end
	
	if tonumber(arrData.is_otc) == 1 and tonumber(arrData.is_behalf_pay) == 1 then
		retMsg['code'],retMsg['msg'] = 1,"请至少保留一个提现"
		return luajson.encode(retMsg)	
	end
	
	local buyList = luajson.decode(arrData.buy_json)
	local sellList = luajson.decode(arrData.sell_json)
	local behalfSellList = luajson.decode(arrData.behalf_sell_json)

	local tmp = {}
	for k,v in pairs(buyList) do 
		local payType = tonumber(k)
		local rate = tonumber(v) or 0
		table.insert(tmp, payType)
		if rate >= 1 then
			retMsg['code'],retMsg['msg'] = 1,"参数错误"
			return luajson.encode(retMsg)		
		end
		
		--查看原本存不存在
		local sqlCase = "select * from dy_user_rate where userid="..uInfo.userid.." and paytype="..payType
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch()
		if sqlData == nil then
			--插入
			local sqlCase = "insert into dy_user_rate(userid, paytype,buy_fee_rate)"
				.. " values("..uInfo.userid..","..payType..", "..rate..")"
			mysqlItem:executeQuery(sqlCase)
		else
			--更新
			local sqlCase = "update dy_user_rate set buy_fee_rate="..rate.." where userid="..uInfo.userid.." and paytype="..payType
			mysqlItem:executeQuery(sqlCase)
		end
		
		UserInfoModel.SetUserFeeRate(uInfo.userid, payType, g_humanDefine.buy_fee_rate, rate)
	end

	for k,v in pairs(sellList) do 
		local payType = tonumber(k)
		local rate = tonumber(v) or 0
		
		if rate >= 1 then
			retMsg['code'],retMsg['msg'] = 1,"参数错误"
			return luajson.encode(retMsg)		
		end
		
		--查看原本存不存在
		local sqlCase = "select * from dy_user_rate where userid="..uInfo.userid.." and paytype="..payType
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch()
		if sqlData == nil then
			--插入
			local sqlCase = "insert into dy_user_rate(userid, paytype,sell_fee_rate)"
				.. " values("..uInfo.userid..","..payType..", "..rate..")"
			mysqlItem:executeQuery(sqlCase)
		else
			--更新
			local sqlCase = "update dy_user_rate set sell_fee_rate="..rate.." where userid="..uInfo.userid.." and paytype="..payType
			mysqlItem:executeQuery(sqlCase)
		end
		
		UserInfoModel.SetUserFeeRate(uInfo.userid, payType, g_humanDefine.sell_fee_rate, rate)
	end
	
	
	for k,v in pairs(behalfSellList) do 
		local payType = tonumber(k)
		local rate = tonumber(v) or 0
		
		if rate >= 1 then
			retMsg['code'],retMsg['msg'] = 1,"参数错误"
			return luajson.encode(retMsg)		
		end
		
		--查看原本存不存在
		local sqlCase = "select * from dy_user_rate where userid="..uInfo.userid.." and paytype="..payType
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch()
		if sqlData == nil then
			--插入
			local sqlCase = "insert into dy_user_rate(userid, paytype,behalf_sell_fee_rate)"
				.. " values("..uInfo.userid..","..payType..", "..rate..")"
			mysqlItem:executeQuery(sqlCase)
		else
			--更新
			local sqlCase = "update dy_user_rate set behalf_sell_fee_rate="..rate.." where userid="..uInfo.userid.." and paytype="..payType
			mysqlItem:executeQuery(sqlCase)
		end
		
		UserInfoModel.SetUserFeeRate(uInfo.userid, payType, g_humanDefine.behalf_sell_fee_rate, rate)
	end
	
	while #uInfo.paytypelist > 0 do 
		uInfo.paytypelist:remove(1)
	end
	
	for k,v in ipairs(tmp) do 
		uInfo.paytypelist:append(v)
	end
	
	uInfo.isotc = tonumber(arrData.is_otc) or 0
	uInfo.isbehalfpay = tonumber(arrData.is_behalf_pay) or 0
	UserInfoModel.SetUserInfo(uInfo)
	local sqlCase = "update dy_user_info set payidlist='"..luajson.encode(tmp).."',is_otc="..uInfo.isotc..", is_behalf_pay="..uInfo.isbehalfpay.."  where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end

--编辑商户信息
--channel 							渠道编号
--nickname							商户
--id   								用户编号
--phonenum 							手机号码
--email								邮箱
--min_buy  							最小购买金额
--max_buy							最大购买金额
--min_sell 			 				最小出售金额
--max_sell							最大出售金额
--is_lock   						是否允许登录 0允许 1锁定
--payList 							8101,8201,8301,8401
--statusList 						1,0,1,0,
--buyList 							0.02,0.02,0.02,0.02
--sellList 							0.02,0.02,0.02,0.02
--behalfSellList					0.02,0.02,0.02,0.02
--is_curency     					币支付类型  1打开 0关闭  
--is_up_payername					0 不用上传付款人 1 要上传付款人
--is_up_payvoucher					0 不用上传支付凭证 1 要上传支付凭证
--is_otc							是否开启OTC交易 0 不开 1 开启	
--is_behalf_pay						是否开启代付交易 0 不开 1 开启
--api_url							网关
--is_shop_price						是否使用商户价格 0 是 1 否 
--add_buy_price						充值汇差
--minus_sell_price					提现汇差
--minus_coin_pay_price				币支付汇差
--withdraw_coin_price_diff 			提币价格差
--extract_currency_rate_erc 		erc的提币手续费
--extract_currency_rate_trc 		trc的提币手续费
--is_fixed_buy_rate					是否固定购买汇率 0 否 1是	
--fixed_buy_rate					固定购买汇率
--is_fixed_sell_rate				是否固定出售汇率 0 否 ， 1是
--fixed_sell_rate					固定出售汇率 
--is_fixed_coin_pay_rate			是否固定币支付汇率 0 否 ， 1是
--fixed_coin_pay_rate				固定币支付汇率
--is_withdraw_coin_pay_rate			是否固定提币汇率 0 否 ， 1是
--withdraw_coin_rate				固定提币汇率
--sign								签证	 
function editShopAgent(rcvData)  
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local uInfo = UserInfoModel.GetUserInfo(arrData.id) 
	if uInfo == nil then 
		retMsg['code'],retMsg['msg'] = 1, "商户不存在"
		return luajson.encode(retMsg)	
	end
	
	if tonumber(arrData.is_otc) == 1 and tonumber(arrData.is_behalf_pay) == 1 then
		retMsg['code'],retMsg['msg'] = 1,"请至少保留一个提现"
		return luajson.encode(retMsg)	
	end
	
	local pInfo = UserInfoModel.GetPhoneByUser(arrData.phonenum, uInfo.channel)
	if pInfo ~= nil then
		if pInfo.userid ~= uInfo.userid then
			retMsg['code'],retMsg['msg'] = 1, "该手机号已被其他玩家绑定"
			return luajson.encode(retMsg)		
		end
	else
		--uInfo.phonenum = arrData.phonenum
	end
	
	--uInfo.email = arrData.email
	uInfo.minbuy = arrData.min_buy
	uInfo.maxbuy = arrData.max_buy
	uInfo.minsell = arrData.min_sell
	uInfo.maxsell = arrData.max_sell
	uInfo.prohibitlogin = tonumber(arrData.prohibit_login) or 0
	
	if arrData.payList ~= "" then
	
		local payList =  string.split(arrData.payList, ",")
		local buyList = string.split(arrData.buyList, ",")
		local sellList = string.split(arrData.sellList, ",")
		local stateList = string.split(arrData.statusList, ",")
		local behalfSellList = string.split(arrData.behalfSellList, ",")
		while #uInfo.paytypelist > 0 do
			uInfo.paytypelist:remove(1)
		end
		
		local tmp = {}
		for k,v in pairs(payList) do 
			local payType = tonumber(v)
			if tonumber(stateList[k]) == 1 then
				uInfo.paytypelist:append(payType)
				table.insert(tmp, payType)
			end
			
			local sqlCase= "select * from dy_user_rate where userid="..uInfo.userid.." and paytype="..payType
			mysqlItem:execute(sqlCase)
			
			local sqlData = mysqlItem:fetch({})
			local rate = v
			if sqlData == nil then
				local sqlCase = "insert into dy_user_rate(userid,paytype,buy_fee_rate,sell_fee_rate,behalf_sell_fee_rate,is_used) values("..uInfo.userid..","..payType..","..buyList[k]..","..sellList[k]..","..stateList[k]..","..behalfSellList[k]..")"
				mysqlItem:execute(sqlCase)
			else
				local sqlCase = "update dy_user_rate set buy_fee_rate="..buyList[k]..", sell_fee_rate="..sellList[k]..", behalf_sell_fee_rate="..behalfSellList[k]..", is_used="..stateList[k].." where userid="..uInfo.userid.." and paytype="..payType
				mysqlItem:execute(sqlCase)
			end
			UserInfoModel.SetUserFeeRate(uInfo.userid, payType, g_humanDefine.buy_fee_rate, buyList[k])
			UserInfoModel.SetUserFeeRate(uInfo.userid, payType, g_humanDefine.sell_fee_rate, sellList[k])
			UserInfoModel.SetUserFeeRate(uInfo.userid, payType, g_humanDefine.behalf_sell_fee_rate, behalfSellList[k])
		end
		local sqlCase = "update dy_user_info set payidlist='"..luajson.encode(tmp).."' where userid="..uInfo.userid
		mysqlItem:execute(sqlCase)
	end
	
	uInfo.isotc = tonumber(arrData.is_otc) or 0
	uInfo.isbehalfpay = tonumber(arrData.is_behalf_pay) or 0
	uInfo.extractcurrencyrateerc = tostring(tonumber(arrData.extract_currency_rate_erc) or 0)
	uInfo.extractcurrencyratetrc = tostring(tonumber(arrData.extract_currency_rate_trc) or 0)
	UserInfoModel.SetUserInfo(uInfo)
	local sqlCase = "update dy_user_info set phonenum='"..uInfo.phonenum.."',email='"..uInfo.email.."',min_buy="..uInfo.minbuy..", max_buy="..uInfo.maxbuy
			..", min_sell="..uInfo.minsell..", max_sell="..uInfo.maxsell
			..", prohibit_login="..uInfo.prohibitlogin..", is_up_payername="..arrData.is_up_payername..", is_up_payvoucher="..arrData.is_up_payvoucher
			..",is_otc="..uInfo.isotc..", is_behalf_pay="..uInfo.isbehalfpay..", extract_currency_rate_erc="..uInfo.extractcurrencyrateerc
			..", extract_currency_rate_trc="..uInfo.extractcurrencyratetrc.." where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
	
	redisItem:hdel( UserInfoModel.userinfo_phonenum, uInfo.phonenum.."_"..uInfo.channel, UserInfoModel.redis_index)
	
	local sqlCase = "update dy_user_api set api_url='"..(arrData.api_url or "").."' where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
	
	if (tonumber(arrData.is_shop_price) or 1) == 1 then
		local sqlCase = "update dy_coin_info set status=1 where channel='"..uInfo.userid.."'"
		mysqlItem:execute(sqlCase)
	else
		local sqlCase = "select * from dy_coin_info where channel='"..uInfo.userid.."'"
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch({})
		local buyPriceDiff = tonumber(arrData.add_buy_price) or 0
		local sellPriceDiff	= tonumber(arrData.minus_sell_price) or 0
		local coinPayPriceDiff	= tonumber(arrData.minus_coin_pay_price) or 0
		local withdrawCoinPriceDiff	= tonumber(arrData.withdraw_coin_price_diff) or 0
		local isFixedBuyRate	= tonumber(arrData.is_fixed_buy_rate) or 0
		local fixedBuyRate	= tonumber(arrData.fixed_buy_rate) or 0
		local isFixedSellRate	= tonumber(arrData.is_fixed_sell_rate) or 0
		local fixedSellRate	= tonumber(arrData.fixed_sell_rate) or 0
		local isFixedCoinPayRate	= tonumber(arrData.is_fixed_coin_pay_rate) or 0
		local fixedCoinPayRate	= tonumber(arrData.fixed_coin_pay_rate) or 0
		local isWithdrawCoinPayrate	= tonumber(arrData.is_withdraw_coin_pay_rate) or 0
		local withdrawcoinRate	= tonumber(arrData.withdraw_coin_rate) or 0
		if sqlData == nil then
			local sqlCase = "select coin_id,coin_name,coin_type,description,buy_price,sell_price,free_price from dy_coin_info where channel='ALL'"
			mysqlItem:executeQuery(sqlCase)
			local sqlData = mysqlItem:fetch({})
			if sqlData ~= nil then
				local sqlCase = "insert into dy_coin_info(coin_id,coin_name,coin_type,description,buy_price,"
					.."sell_price,free_price,add_buy_price,minus_sell_price,channel,minus_coin_pay_price,"
					.."withdraw_coin_price_diff,is_fixed_buy_rate,fixed_buy_rate,is_fixed_sell_rate,fixed_sell_rate,"
					.."is_fixed_coin_pay_rate,fixed_coin_pay_rate,is_withdraw_coin_pay_rate,withdraw_coin_rate) values("
					..sqlData[1]..",'"..sqlData[2].."','"..sqlData[3].."','"..sqlData[4].."',"..sqlData[5]..","..sqlData[6]
					..","..sqlData[7]..","..buyPriceDiff..","..sellPriceDiff..",'"..uInfo.userid.."',"..coinPayPriceDiff
					..","..withdrawCoinPriceDiff..","..isFixedBuyRate..","..fixedBuyRate..","..isFixedSellRate
					..","..fixedSellRate..","..isFixedCoinPayRate..","..fixedCoinPayRate..","..isWithdrawCoinPayrate
					..","..withdrawcoinRate..")"
				mysqlItem:execute(sqlCase)
			end
		else
			local sqlCase = "update dy_coin_info set add_buy_price="..buyPriceDiff..", minus_sell_price="..sellPriceDiff
				..", minus_coin_pay_price="..coinPayPriceDiff..", withdraw_coin_price_diff="..withdrawCoinPriceDiff
				..", is_fixed_buy_rate="..isFixedBuyRate..", fixed_buy_rate="..fixedBuyRate..", is_fixed_sell_rate="
				..isFixedSellRate..", fixed_sell_rate="..fixedSellRate..", is_fixed_coin_pay_rate="..isFixedCoinPayRate
				..", fixed_coin_pay_rate="..fixedCoinPayRate..", is_withdraw_coin_pay_rate="..isWithdrawCoinPayrate
				..", withdraw_coin_rate="..withdrawcoinRate..", status=0 where channel='"..uInfo.userid.."'"
			mysqlItem:execute(sqlCase)
		end
	end	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end

--创建商户
--optUserId 				操作者	
--agentID 					商户的上级
--merchantName 				商户名称	
--account					账号		
--pwd 						密码	
--phonenum						
--dealcointype				成交货币类型 0 数字货币 1 法币
--sign						签证	
function createshop(rcvData)   
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	--检查用户存不存在
	local uInfo = UserInfoModel.GetUserInfo(arrData.agentID)
	if uInfo == nil  then
		retMsg['code'],retMsg['msg'] = 1,"上级不存在"
		return luajson.encode(retMsg)	
	end
	
	if uInfo.usertype ~= 300  then
		retMsg['code'],retMsg['msg'] = 1,"只有商户代理才能创建商户"
		return luajson.encode(retMsg)	
	end
	
	
	--查看用户是否存在
	local sqlCase = "select * from dy_user_info where account='"..arrData.account.."' or phonenum='"..arrData.account.."' or email='"..arrData.account.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		retMsg['code'],retMsg['msg'] = 1,"该账户已存在！"
		return luajson.encode(retMsg)	
	end
	
	--根据手机号码和渠道号查找用户ID
	local pInfo = UserInfoModel.GetPhoneByUser(arrData.phonenum,uInfo.channel)
	if pInfo ~= nil then
		retMsg['code'],retMsg['msg'] = ReturnCode["human_phone_exist"][1], ReturnCode["human_phone_exist"][2]
		return luajson.encode(retMsg)
	end
	
	local regInfo = {}
	local tmp = string.split(arrData.account, "@") 
	local nickname = tmp[1]
	regInfo["nickname"] = nickname
	regInfo["phonenum"] = ""
	regInfo["email"] = arrData.account
	regInfo["password"] = arrData.pwd
	regInfo["invitecode"] = uInfo.invitecode
	regInfo["channel"] = uInfo.channel
	regInfo["bindtype"] = g_humanDefine.bindType_email
	regInfo["agent"] = uInfo.userid
	regInfo["usertype"] = g_humanDefine.usertype_merchant_dealer
	regInfo["account"] = arrData.account
	regInfo["paylist"] = {}
	local sqlCase = "select paytype,buy_fee_rate,sell_fee_rate,buy_comm_rate,sell_comm_rate,is_used,behalf_sell_fee_rate,behalf_buy_comm_rate from dy_user_rate where userid="..uInfo.userid
	mysqlItem:executeQuery(sqlCase)
	while true do
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		local tmp = {}
		tmp["payType"] = tonumber(sqlData[1])
		tmp["buy_free_rate"] = tonumber(sqlData[2])
		tmp["sell_free_rate"] = tonumber(sqlData[3])
		tmp["buy_common_rate"] = tonumber(sqlData[4])
		tmp["sell_common_rate"] = tonumber(sqlData[5])
		tmp["behalf_sell_fee_rate"] = tonumber(sqlData[7])
		tmp["behalf_buy_comm_rate"] = tonumber(sqlData[8])
		tmp["state"] = tonumber(sqlData[6])
		table.insert(regInfo["paylist"], tmp)
	end
	regInfo["commtype"] = uInfo.commtype
	regInfo["contactInfo"] = arrData.phonenum
	regInfo["userName"] = arrData.merchantName
	regInfo["isInvited"] = 1
	regInfo["dealCoinType"] = tonumber(arrData.dealcointype) or 0
	regInfo["teamName"] = uInfo.teamname or ""
	regInfo["platformID"] = uInfo.platformid or 0
	regInfo["isTeamAcceptOrder"] = uInfo.isteamacceptorder or 0
	local userid = UserInfoModel.CreateUser(regInfo)
	local uInfo = UserInfoModel.GetUserInfo(userid)
	if uInfo == nil then
		--创建角色失败
		retMsg['code'] = ReturnCode["human_create_user_err"][1]
		retMsg['msg'] = ReturnCode["human_create_user_err"][2]
		return luajson.encode(retMsg)		
	end
	UserInfoModel.SetUserInfo(uInfo)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end


--设置商户费率
--optUserId 				操作者		
--merchantUserID 			商户ID		
--payTypeList				通道类型列表 8001,8101		
--buyRateList				购买费率列表 0.02,0.03			
--sellRateList				出售费率列表 0.02,0.03			
--statusList				出售费率列表 0,1		
--behalfSellList			出售代付费率列表 0.02,0.02		
--minBuyAmountList			最小购买金额 		
--maxBuyAmountList			最小购买金额 		
--minSellAmountList			最小购买金额 		
--maxSellAmountList			最小购买金额 		
--sign						签证	
function setShopRate(rcvData)   
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end

	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	--检查用户存不存在
	local uInfo = UserInfoModel.GetUserInfo(arrData.optUserId)
	if uInfo == nil  then
		retMsg['code'],retMsg['msg'] = 1,"商户不存在"
		return luajson.encode(retMsg)	
	end
	
	if uInfo.usertype ~= 300  then
		retMsg['code'],retMsg['msg'] = 1,"只有商户代理才能创建商户"
		return luajson.encode(retMsg)	
	end
	
	local mInfo = UserInfoModel.GetUserInfo(arrData.merchantUserID)
	if mInfo == nil  then
		retMsg['code'],retMsg['msg'] = 1,"商户不存在"
		return luajson.encode(retMsg)	
	end
	
	if mInfo.agent ~= uInfo.userid then
		retMsg['code'],retMsg['msg'] = 1,"该用户不是你的直属代理无法设置"
		return luajson.encode(retMsg)	
	end
	
	while #mInfo.paytypelist > 0 do 
		mInfo.paytypelist:remove(1)
	end
	local tmp = {}
	if arrData.payTypeList ~= "" then

		local payList =  string.split(arrData.payTypeList, ",")
		local buyList = string.split(arrData.buyRateList, ",")
		local sellList = string.split(arrData.sellRateList, ",")
		local stateList = string.split(arrData.statusList, ",")
		local behalfSellList = string.split(arrData.behalfSellList, ",")
		
		for k,v in pairs(payList) do 
			local payType = tonumber(v)
			if tonumber(stateList[k]) == 1 then
				mInfo.paytypelist:append(payType)
				table.insert(tmp, payType)
			end
			
			local sqlCase= "select * from dy_user_rate where userid="..mInfo.userid.." and paytype="..payType
			mysqlItem:execute(sqlCase)
			
			local sqlData = mysqlItem:fetch({})
			local rate = v
			if sqlData == nil then
				local sqlCase = "insert into dy_user_rate(userid,paytype,buy_fee_rate,sell_fee_rate,behalf_sell_fee_rate,is_used) values("..mInfo.userid..","..payType..","..buyList[k]..","..sellList[k]..","..behalfSellList[k]..","..stateList[k]..")"
				mysqlItem:execute(sqlCase)
			else
				local sqlCase = "update dy_user_rate set buy_fee_rate="..buyList[k]..", sell_fee_rate="..sellList[k]..",behalf_sell_fee_rate="..behalfSellList[k]..", is_used="..stateList[k].." where userid="..mInfo.userid.." and paytype="..payType
				mysqlItem:execute(sqlCase)
			end
			UserInfoModel.SetUserFeeRate(mInfo.userid, payType, g_humanDefine.buy_fee_rate, buyList[k])
			UserInfoModel.SetUserFeeRate(mInfo.userid, payType, g_humanDefine.sell_fee_rate, sellList[k])
			UserInfoModel.SetUserFeeRate(mInfo.userid, payType, g_humanDefine.behalf_sell_fee_rate, behalfSellList[k])
		end
	end
	mInfo.minbuy = tostring(arrData.minBuyAmountList) or 0
	mInfo.maxbuy = tostring(arrData.maxBuyAmountList) or 0
	mInfo.minsell = tostring(arrData.minSellAmountList) or 0
	mInfo.maxsell = tostring(arrData.maxSellAmountList) or 0
	UserInfoModel.SetUserInfo(mInfo)
	local sqlCase = "update dy_user_info set min_buy="..mInfo.minbuy..", max_buy="..mInfo.maxbuy..", min_sell="
				..mInfo.minsell..", max_sell="..mInfo.maxsell..", payidlist='"..luajson.encode(tmp).."' where userid="..mInfo.userid
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end

--设置资金密码
--merchantUserID			商户ID	
--optType					操作类型 1 设置资金密码 2 修改资金密码 
--oldFundPwd				原资金密码								
--newFundPwd				新资金密码								
--sign						签证	
function setFundPwd(rcvData)   
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	--检查用户存不存在
	local uInfo = UserInfoModel.GetUserInfo(arrData.merchantUserID)
	if uInfo == nil  then
		retMsg['code'],retMsg['msg'] = 1,"商户不存在"
		return luajson.encode(retMsg)	
	end
	
	if tonumber(arrData.optType) == 1 then
		--设置密码
		if uInfo.isfundpassword == 1 then
			retMsg['code'],retMsg['msg'] = 1,"已设置了资金密码"
			return luajson.encode(retMsg)
		end
		
	else
		if uInfo.isfundpassword == 0 then
			retMsg['code'],retMsg['msg'] = 1,"还未设置资金密码"
			return luajson.encode(retMsg)
		end
		
		local sqlCase = "select fundpassword from dy_user_info where userid="..uInfo.userid 
		mysqlItem:execute(sqlCase)
		--[[
		local sqlData = mysqlItem:fetch()
		if sqlData ~= md5(arrData.oldFundPwd or "") then
			retMsg['code'],retMsg['msg'] = 1,"原密码不正确"
			return luajson.encode(retMsg)
		end
		]]
		if sqlData == md5(arrData.newFundPwd or "") then
			retMsg['code'],retMsg['msg'] = 1,"新密码与原密码一致"
			return luajson.encode(retMsg)
		end
		
	end
	local sqlCase = "update dy_user_info set fundpassword='"..md5(arrData.newFundPwd or "").."' where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
	
	uInfo.isfundpassword = 1 
	UserInfoModel.SetUserInfo(uInfo)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
	
end

--设置提币参数
--channel 					--渠道号
--coin_id					--币种ID
--extractCurrencyRate		--提币费率
--minExtractCurrencyCount	--最小提币数额
--maxExtractCurrencyCount	--最大提币数额
--transferCurrenyRate		--转币手续费
--minTransferCurrencyCount	--最小转币数额
--maxTransferCurrencyCount	--最大转币数额
--extract_currency_rate_trc	--trc提币手续费
--sign						--签证	
function setExtractCurrency(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	
	local sqlCase = "select * from dy_coin_info where coin_id="..arrData.coin_id.." and channel='"..arrData.channel.."'"
	mysqlItem:executeQuery(sqlCase)
	
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		retMsg['code'],retMsg['msg'] = 1, "未找到渠道币种"
		return luajson.encode(retMsg)
	end
	
	local sqlCase = "update dy_coin_info set "
					.."extract_currency_rate="..(arrData.extractCurrencyRate or 0)
					..", extract_currency_rate_trc="..(arrData.extract_currency_rate_trc or 0)
					..", min_extract_currency_count="..arrData.minExtractCurrencyCount
					..", max_extract_currency_count="..arrData.maxExtractCurrencyCount
					..", transfer_curreny_rate="..arrData.transferCurrenyRate
					..", min_transfer_curreny_count="..arrData.minTransferCurrencyCount
					..", max_transfer_curreny_count="..arrData.maxTransferCurrencyCount
					.." where coin_id="..arrData.coin_id.." and channel='"..arrData.channel.."'"
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)


end

--加减金币
--orderid            订单ID  
--check_userid      审核人ID
--check_username  审核人名字
--check_from    审核人来源，0=不审核直接通过，1=user_info，2=sub_user_info
--sign            签证    	
function httpaddercusdt(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	ThreadManager.sysOrderLock(arrData.orderid)
	--判断补款订单是否一斤完成
	local sqlCase = "select status,type,amount,userid,price,money,view_type,trans_order_id from dy_bill_repair where id="..arrData.orderid
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		retMsg['code'],retMsg['msg'] = 1,"订单不存在"
		return luajson.encode(retMsg)
	end
	
	if tonumber(sqlData[1]) ~= 1 then
		retMsg['code'],retMsg['msg'] = 1,"订单状态错误"
		return luajson.encode(retMsg)
	end 
	
	local optType = tonumber(sqlData[2])
	local amount = math.abs(tonumber(sqlData[3]))
	local userid = tonumber(sqlData[4])
	local price = tonumber(sqlData[5])
	local money = tonumber(sqlData[6]) or 0
	local viewType = tonumber(sqlData[7]) or 0
	local transOrderId = sqlData[8]
	
	if viewType == 2 and transOrderId ~= nil then
		local sqlCase = "select hosting_status from dy_block_chain_trans where id='"..transOrderId.."' and hosting_adds=1"
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch()
		
		if sqlData == nil then
			ThreadManager.sysOrderUnLock(arrData.orderid)
			retMsg['code'],retMsg['msg'] = 1,"未找到对应的，冷钱包记录"
			return luajson.encode(retMsg)
		end
		
		if tonumber(sqlData) ~= 4 then
			ThreadManager.sysOrderUnLock(arrData.orderid)
			retMsg['code'],retMsg['msg'] = 1,"冷钱包的记录已被处理完成， 审核失败"
			return luajson.encode(retMsg)
		end
	end
	
	
	
	if money == 0 then
		money = price * amount
	end
	
	local uInfo = UserInfoModel.GetUserInfo(userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1,"用户不存在"
		return luajson.encode(retMsg)
	end

	if optType == 1 then
		UserInfoModel.AddErcUsdtAmount(uInfo, amount, arrData.orderid, 0,"系统补款，增加余额",g_humanDefine.fund_details.type_sys_add,money)
		UserInfoModel.SendErcUsdtAmount(uInfo)
		
		if uInfo.dealcointype == 1 then
			--结算货币类型如果是法币把法币账户也结算一下
			UserInfoModel.AddErcFCAmount(uInfo, money, arrData.orderid, 0, "系统补款，增加余额", g_humanDefine.fund_details.type_sys_add)
		end
		
	elseif optType == 2 then
		if uInfo.dealcointype == 0 then
			if tonumber(uInfo.ercusdtamount) - tonumber(uInfo.ercusdtlockamount) < amount then
				ThreadManager.sysOrderUnLock(arrData.orderid)
				retMsg['code'],retMsg['msg'] = 1,"余额不足，扣款失败"
				return luajson.encode(retMsg)	
			end
		else
			if (tonumber(uInfo.ercfcamount) - tonumber(uInfo.ercfclockamount)) < money then
				ThreadManager.sysOrderUnLock(arrData.orderid)
				retMsg['code'],retMsg['msg'] = 1,"余额不足，扣款失败"
				return luajson.encode(retMsg)	
			end
		end
		UserInfoModel.DecErcUsdtAmount(uInfo, amount, arrData.orderid, 0,"系统扣款，减少余额",g_humanDefine.fund_details.type_sys_des,money)
		UserInfoModel.SendErcUsdtAmount(uInfo)
		
		if uInfo.dealcointype == 1 then
			--结算货币类型如果是法币把法币账户也结算一下
			UserInfoModel.DecErcFCAmount(uInfo, money, arrData.orderid, 0, "系统扣款，减少余额", g_humanDefine.fund_details.type_sys_des)
		end
	end
	local sqlCase = "update dy_bill_repair set status=3,check_from="..arrData.check_from..", check_userid="..arrData.check_userid
					..",check_username='"..arrData.check_username.."',check_time='"..TimeUtils.GetTimeString().."' where id="..arrData.orderid
	mysqlItem:execute(sqlCase)
	
	ThreadManager.sysOrderUnLock(arrData.orderid)
	
	if viewType == 1 then
		LogDispatch.sysAddOrDec(uInfo.userid, optType, amount)
	elseif viewType == 2 then
		LogDispatch.userRecharge(uInfo.userid, amount , 0, amount, true)
	end
	
	
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end

--修改昵称
--userid
--nickname					新的昵称
--sign						签证	
function updatenickname(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local uInfo = UserInfoModel.GetUserInfo(arrData.userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1,"用户不存在"
		return luajson.encode(retMsg)
	end
	
	local nickname = arrData.nickname
	
	--检查是否是属于那个玩家的
	local userid = UserInfoModel.checkNickName(nickname)
	if userid == 0 then
		uInfo.nickname =  nickname
		UserInfoModel.SetUserInfo(uInfo)
		--修改昵称
		local sqlCase = "update dy_user_info set nickname='"..uInfo.nickname.."' where userid="..uInfo.userid
		mysqlItem:execute(sqlCase)
		UserInfoModel.SendNickName(uInfo)
	elseif userid ~= uInfo.userid then
		retMsg['code'],retMsg['msg'] = 1,"昵称已经存在"
		return luajson.encode(retMsg)
	elseif userid == uInfo.userid then
		retMsg['code'],retMsg['msg'] = 1,"跟原昵称一致"
		return luajson.encode(retMsg)
	end
	
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end


--创建下级代理商
--optUserId						创建者
--agentID						代理商的上级
--meragentName					代理商名称
--account						账号
--pwd							密码
--phonenum						联系方式
--isInvited						是否允许开设代理，0允许，1禁止
--sign							签证	
function createagent(rcvData)   
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	--检查用户存不存在
	local uInfo = UserInfoModel.GetUserInfo(arrData.agentID)
	if uInfo == nil  then
		retMsg['code'],retMsg['msg'] = 1,"上级不存在"
		return luajson.encode(retMsg)	
	end
	
	if uInfo.usertype ~= 300 or uInfo.isinvited ~= 0 then
		retMsg['code'],retMsg['msg'] = 1,"您没有权限创建下级代理"
		return luajson.encode(retMsg)	
	end
	
	--查看用户是否存在
	local sqlCase = "select * from dy_user_info where account='"..arrData.account.."' or phonenum='"..arrData.account.."' or email='"..arrData.account.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		retMsg['code'],retMsg['msg'] = 1,"该账户已存在！"
		return luajson.encode(retMsg)	
	end
	
	local tmp = string.split(arrData.account, "@") 
	local nickname = tmp[1]
	local regInfo = {}
	regInfo["nickname"] = nickname
	regInfo["phonenum"] = ""
	regInfo["email"] = arrData.account
	regInfo["password"] = arrData.pwd
	regInfo["invitecode"] = uInfo.invitecode
	regInfo["channel"] = uInfo.channel
	regInfo["bindtype"] = g_humanDefine.bindType_email
	regInfo["agent"] = uInfo.userid
	regInfo["usertype"] = g_humanDefine.usertype_merchant_agent
	regInfo["account"] = arrData.account
	regInfo["paylist"] = {}
	local sqlCase = "select paytype,buy_fee_rate,sell_fee_rate,buy_comm_rate,sell_comm_rate,is_used,behalf_sell_fee_rate,behalf_buy_comm_rate from dy_user_rate where userid="..uInfo.userid
	mysqlItem:executeQuery(sqlCase)
	while true do
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		local tmp = {}
		tmp["payType"] = tonumber(sqlData[1])
		tmp["buy_free_rate"] = tonumber(sqlData[2])
		tmp["sell_free_rate"] = tonumber(sqlData[3])
		tmp["buy_common_rate"] = tonumber(sqlData[4])
		tmp["sell_common_rate"] = tonumber(sqlData[5])
		tmp["behalf_sell_fee_rate"] = tonumber(sqlData[7])
		tmp["behalf_buy_comm_rate"] = tonumber(sqlData[8])
		tmp["state"] = tonumber(sqlData[6])
		table.insert(regInfo["paylist"], tmp)
	end
	regInfo["commtype"] = uInfo.commtype
	regInfo["contactInfo"] = arrData.phonenum
	regInfo["userName"] = arrData.meragentName
	regInfo["isInvited"] = arrData.isInvited
	regInfo["dealCoinType"] = 0
	regInfo["teamName"] = uInfo.teamname or ""
	regInfo["platformID"] = uInfo.platformid or 0
	regInfo["isTeamAcceptOrder"] = uInfo.isteamacceptorder or 0
	local userid = UserInfoModel.CreateUser(regInfo)
	local uInfo = UserInfoModel.GetUserInfo(userid)
	if uInfo == nil then
		--创建角色失败
		retMsg['code'] = ReturnCode["human_create_user_err"][1]
		retMsg['msg'] = ReturnCode["human_create_user_err"][2]
		return luajson.encode(retMsg)		
	end
	UserInfoModel.SetUserInfo(uInfo)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end


--设置下级代理商费率
--meragentUserID 			操作者			
--payTypeList				通道类型列表 8001,8101		
--buyRateList				购买费率列表 0.02,0.03			
--sellRateList				出售费率列表 0.02,0.03			
--statusList				状态列表 0,1	
--behalfSellList			出售代付费率列表 0.02,0.03
--isInvited					
--sign						签证	
function setAgentRate(rcvData)   
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end

	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local mInfo = UserInfoModel.GetUserInfo(arrData.meragentUserID)
	if mInfo == nil  then
		retMsg['code'],retMsg['msg'] = 1,"商户不存在"
		return luajson.encode(retMsg)	
	end
	
	while #mInfo.paytypelist > 0 do 
		mInfo.paytypelist:remove(1)
	end
	
	if arrData.payTypeList ~= "" then

		local payList =  string.split(arrData.payTypeList, ",")
		local buyList = string.split(arrData.buyRateList, ",")
		local sellList = string.split(arrData.sellRateList, ",")
		local stateList = string.split(arrData.statusList, ",")
		local behalfSellList = string.split(arrData.behalfSellList, ",")
		local tmp = {}
		for k,v in pairs(payList) do 
			local payType = tonumber(v)
			
			if tonumber(stateList[k]) == 1 then
				mInfo.paytypelist:append(payType)
				table.insert(tmp, payType)
			end
			
			local sqlCase= "select * from dy_user_rate where userid="..mInfo.userid.." and paytype="..payType
			mysqlItem:execute(sqlCase)
			
			local sqlData = mysqlItem:fetch({})
			local rate = v
			if sqlData == nil then
				local sqlCase = "insert into dy_user_rate(userid,paytype,buy_fee_rate,sell_fee_rate,behalf_sell_fee_rate,is_used) values("..mInfo.userid..","..payType..","..buyList[k]..","..sellList[k]..","..stateList[k]..","..behalfSellList[k]..")"
				mysqlItem:execute(sqlCase)
			else
				local sqlCase = "update dy_user_rate set buy_fee_rate="..buyList[k]..", sell_fee_rate="..sellList[k]..", is_used="..stateList[k]..", behalf_sell_fee_rate="..behalfSellList[k].." where userid="..mInfo.userid.." and paytype="..payType
				mysqlItem:execute(sqlCase)
			end
			UserInfoModel.SetUserFeeRate(mInfo.userid, payType, g_humanDefine.buy_fee_rate, buyList[k])
			UserInfoModel.SetUserFeeRate(mInfo.userid, payType, g_humanDefine.sell_fee_rate, sellList[k])
			UserInfoModel.SetUserFeeRate(mInfo.userid, payType, g_humanDefine.behalf_sell_fee_rate, behalfSellList[k])
		end
		local sqlCase = "update dy_user_info set payidlist='"..luajson.encode(tmp).."' where userid="..mInfo.userid
		mysqlItem:execute(sqlCase)
	end
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end


--设置是否允许开设代理
--userid 					用户ID
--isInvited					是否允许开设代理，0允许，1禁止
--sign						签证	
function setIsInvited(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end

	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local uInfo = UserInfoModel.GetUserInfo(arrData.userid)
	if uInfo == nil  then
		retMsg['code'],retMsg['msg'] = 1,"商户不存在"
		return luajson.encode(retMsg)	
	end
	
	uInfo.isinvited = tonumber(arrData.isInvited) or 1
	UserInfoModel.SetUserInfo(uInfo)
	local sqlCase = "update dy_user_info set is_invited="..uInfo.isinvited.." where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end


--币商注册
--phone 				手机号	
--vercode 				验证码	
--password				密码		
--invite_code 			邀请码	
--channel				渠道号	
--sign						签证	
function registercurrencyagent(rcvData)   
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	if arrData.phone == nil or arrData.phone == "" 
	or arrData.vercode == nil or arrData.vercode == "" 
	or arrData.password == nil or arrData.password == "" 
	or arrData.invite_code == nil or arrData.invite_code == "" 
	or arrData.channel == nil or arrData.channel == "" then
		retMsg['code'],retMsg['msg'] = 1, "参数错误"
		return luajson.encode(retMsg)		
	end
	
	local ret, msg = HumanService.RegisterCurrencyAgent(arrData.phone,arrData.password,arrData.vercode,arrData.channel,arrData.phone,arrData.invite_code,nil)
	if ret ~= 0 then
		retMsg['code'] = ret
		retMsg['msg'] = msg
		return luajson.encode(retMsg)	
	end
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end


--创建币商
--optUserId 			创建者
--userName 				币商名称	
--account				账号，手机注册		
--pwd 					密码
--userType 				201 码商 202 币商
--teamname 				团队名称
--sign					签证	
function createcurrencyagent(rcvData)   
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	if arrData.optUserId == nil or arrData.optUserId == "" 
	or arrData.userName == nil or arrData.userName == "" 
	or arrData.account == nil or arrData.account == "" 
	or arrData.pwd == nil or arrData.pwd == "" then
		retMsg['code'],retMsg['msg'] = 1, "参数错误"
		return luajson.encode(retMsg)		
	end
	
	
	local sqlCase = "select * from dy_user_info where account='"..arrData.account.."' or phonenum='"..arrData.account.."' or email='"..arrData.account.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		retMsg['code'],retMsg['msg'] = ReturnCode["human_phone_exist"][1],ReturnCode["human_phone_exist"][2]
		return luajson.encode(retMsg)
	end
	
	if arrData.teamname == nil and arrData.teamname == "" then
		retMsg['code'],retMsg['msg'] = 1, "团队名称不能为空"
		return luajson.encode(retMsg)
	end
	--检查团队名称是否存在
	local sqlCase = "select * from dy_user_info where team_name='"..arrData.teamname.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		retMsg['code'],retMsg['msg'] = 1, "团队名称已经存在"
		return luajson.encode(retMsg)
	end
	
	--检查昵称是否存在
	local userid = UserInfoModel.checkNickName(arrData.account)
	if userid ~= 0 then
		retMsg['code'],retMsg['msg'] = 1,"昵称已经存在"
		return luajson.encode(retMsg)
	end
	
	local aInfo = UserInfoModel.GetUserInfo(arrData.optUserId)
	if aInfo == nil or (tonumber(aInfo.usertype) ~= g_humanDefine.usertype_currency_agent and tonumber(aInfo.usertype) ~= g_humanDefine.usertype_currency and tonumber(aInfo.usertype) ~= g_humanDefine.usertype_currency1)  then
		retMsg['code'],retMsg['msg'] = 1, "创建者不存在"
		return luajson.encode(retMsg)
	end

	local regInfo = {}
	regInfo["nickname"] = arrData.userName
	regInfo["phonenum"] = arrData.account
	regInfo["email"] = ""
	regInfo["password"] = arrData.pwd
	regInfo["invitecode"] = aInfo.invitecode
	regInfo["channel"] = aInfo.channel
	regInfo["bindtype"] = g_humanDefine.bindType_phone
	regInfo["agent"] = aInfo.userid
	regInfo["usertype"] = arrData.userType
	regInfo["account"] = arrData.account
	regInfo["paylist"] = {}
	regInfo["commtype"] = aInfo.commtype
	local sqlCase = "select paytype,buy_fee_rate,sell_fee_rate,buy_comm_rate,sell_comm_rate,is_used from dy_user_rate where userid="..aInfo.userid
	mysqlItem:executeQuery(sqlCase)
	while true do
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		local tmp = {}
		tmp["payType"] = tonumber(sqlData[1])
		tmp["buy_free_rate"] = 0
		tmp["sell_free_rate"] = 0
		tmp["buy_common_rate"] = 0
		tmp["sell_common_rate"] = 0
		tmp["behalf_sell_fee_rate"] = 0
		tmp["behalf_buy_comm_rate"] = 0
		tmp["state"] = tonumber(sqlData[6])
		table.insert(regInfo["paylist"], tmp)
	end
	regInfo["contactInfo"] = ""
	regInfo["userName"] = arrData.userName
	regInfo["isInvited"] = 1
	regInfo["dealCoinType"] = 0
	regInfo["teamName"] = arrData.teamname or ""
	regInfo["platformID"] = aInfo.platformid or 0
	regInfo["isTeamAcceptOrder"] = aInfo.isteamacceptorder or 0
	local userid = UserInfoModel.CreateUser(regInfo)
	
	if uInfo == nil then
		uInfo = st_human_pb.userinfo()
	end
	
	if nil == UserInfoModel.GetUserInfo(userid,uInfo) then
		retMsg['code'],retMsg['msg'] = ReturnCode["human_create_user_err"][1],ReturnCode["human_create_user_err"][2]
		return luajson.encode(retMsg)
	end
	
	UserInfoModel.SetUserInfo(uInfo)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end

--设置币商费率
--optUserID					操作者
--currencyUserID 			币商ID		
--payTypeList				通道类型列表 8001,8101		
--incomeRateList			收益费率列表 0.02,0.03						
--sign						签证	
function setcurrencyagentrate(rcvData)   
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end

	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local uInfo = UserInfoModel.GetUserInfo(arrData.currencyUserID)
	if uInfo == nil  then
		retMsg['code'],retMsg['msg'] = 1,"币商不存在"
		return luajson.encode(retMsg)	
	end
	
	if arrData.payTypeList ~= "" then
		local payList =  string.split(arrData.payTypeList, ",")
		local incomeRateList = string.split(arrData.incomeRateList, ",")
		
		--先检查一边有没有参数不符合的
		for k,v in pairs(payList) do 
			local payType = tonumber(v)
			local incomeRate = tonumber(incomeRateList[k])
			local aRate = UserInfoModel.getUserFeeRate(uInfo.agent, payType, g_humanDefine.sell_comm_rate, rate)
			if incomeRate < 0 or incomeRate > aRate then
				retMsg['code'],retMsg['msg'] = 1,"设置的参数不能大于上级"
				return luajson.encode(retMsg)	
			end
		end
		
		for k,v in pairs(payList) do 
			local payType = tonumber(v)
			local incomeRate = tonumber(incomeRateList[k])
		
			local sqlCase= "select * from dy_user_rate where userid="..uInfo.userid.." and paytype="..payType
			mysqlItem:execute(sqlCase)
			
			local sqlData = mysqlItem:fetch({})
			local rate = v
			if sqlData == nil then
				local sqlCase = "insert into dy_user_rate(userid,paytype,buy_comm_rate,sell_comm_rate) values("..uInfo.userid..","..payType..","..incomeRate..","..incomeRate..")"
				mysqlItem:execute(sqlCase)
			else
				local sqlCase = "update dy_user_rate set buy_comm_rate="..incomeRate..", sell_comm_rate="..incomeRate.." where userid="..uInfo.userid.." and paytype="..payType
				mysqlItem:execute(sqlCase)
			end
			UserInfoModel.SetUserFeeRate(uInfo.userid, payType, g_humanDefine.buy_comm_rate, incomeRate)
			UserInfoModel.SetUserFeeRate(uInfo.userid, payType, g_humanDefine.sell_comm_rate, incomeRate)
		end
	end
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end


--设置参数
--optUserID 				操作
--currencyUserID 			币商ID			
--optID						设置的参数 1 是否接单 2 是否允许登陆 
--optType					设置类型 0 允许 1 禁止				
--sign						签证	
function setparameter(rcvData)  

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end

	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local uInfo = UserInfoModel.GetUserInfo(arrData.currencyUserID)
	if uInfo == nil  then
		retMsg['code'],retMsg['msg'] = 1,"用户不存在"
		return luajson.encode(retMsg)	
	end
	
	if tonumber(arrData.optID) == 1 then
		uInfo.isacceptorder = tonumber(arrData.optType) or 0
	elseif tonumber(arrData.optID) == 2 then
		uInfo.prohibitlogin = tonumber(arrData.optType) or 0
	end
	
	UserInfoModel.SetUserInfo(uInfo)
	local sqlCase = "update dy_user_info set is_accept_order="..uInfo.isacceptorder..", prohibit_login="..uInfo.prohibitlogin.." where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	



end


--批量设置参数
--userIdList				用户ID逗号隔开
--optID						设置的参数 1 是否接单 2 是否允许登陆 
--sign						签证
function batchsetparameter(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	

	local userIdList = string.split(arrData.userIdList,",")
	
	for _, userid in ipairs(userIdList) do
	
		local uInfo = UserInfoModel.GetUserInfo(userid)
		if uInfo ~= nil  then
			if tonumber(arrData.optID) == 1 then
				uInfo.isacceptorder =(uInfo.isacceptorder + 1) % 2
			elseif tonumber(arrData.optID) == 2 then
				uInfo.prohibitlogin = (uInfo.prohibitlogin + 1) % 2
			end
			
			UserInfoModel.SetUserInfo(uInfo)
			local sqlCase = "update dy_user_info set is_accept_order="..uInfo.isacceptorder..", prohibit_login="..uInfo.prohibitlogin.." where userid="..uInfo.userid
			mysqlItem:execute(sqlCase)
		end
	end
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
	
end


--修改团队名称
--userID 					用户ID
--newTeamName				新的团队名称
--sign						签证
function modifyteamname(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	if arrData.newTeamName == nil and arrData.newTeamName == "" then
		retMsg['code'],retMsg['msg'] = 1, "团队名称不能为空"
		return luajson.encode(retMsg)
	end
	
	--检查新团队名称是否存在
	local sqlCase = "select * from dy_user_info where team_name='"..arrData.newTeamName.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		retMsg['code'],retMsg['msg'] = 1, "新团队名称已经存在"
		return luajson.encode(retMsg)
	end
	
	local uInfo = UserInfoModel.GetUserInfo(arrData.userID or 0)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "用户不存在"
		return luajson.encode(retMsg)
	end
	
	local aInfo = UserInfoModel.GetUserInfo(uInfo.agent)
	if aInfo == nil and (aInfo.usertype ~= 200 or aInfo.usertype ~= 400) then
		retMsg['code'],retMsg['msg'] = 1, "该用户不能修改团队名称"
		return luajson.encode(retMsg)
	end
	
	local userList = {uInfo.userid}
	local sqlCase = "select userid from ag_relation where bind_userid="..uInfo.userid
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlData = mysqlItem:fetch()
		if sqlData == nil then
			break
		end
		
		table.insert(userList, tonumber(sqlData))
	end
	
	for k,v in ipairs(userList) do 
		local pInfo = UserInfoModel.GetUserInfo(v)
		if pInfo ~= nil then
			pInfo.teamname = arrData.newTeamName
			UserInfoModel.SetUserInfo(pInfo)
			local sqlCase = "update dy_user_info set team_name='"..pInfo.teamname.."' where userid="..pInfo.userid
			mysqlItem:execute(sqlCase)
		end
	end
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
end


--设置是否禁止团队接单
--userID 					用户ID
--status					0-否 1-是
--sign						签证
function setteamname(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local userID = tonumber(arrData.userID) or 0
	local status = tonumber(arrData.status) or 0
	
	local uInfo = UserInfoModel.GetUserInfo(userID or 0)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "用户不存在"
		return luajson.encode(retMsg)
	end
	
	local aInfo = UserInfoModel.GetUserInfo(uInfo.agent)
	if aInfo == nil and (aInfo.usertype ~= 200 or aInfo.usertype ~= 400) then
		retMsg['code'],retMsg['msg'] = 1, "该用户不能修改团队接单状态"
		return luajson.encode(retMsg)
	end
	
	local userList = {uInfo.userid}
	local sqlCase = "select userid from ag_relation where bind_userid="..uInfo.userid
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlData = mysqlItem:fetch()
		if sqlData == nil then
			break
		end
		
		table.insert(userList, tonumber(sqlData))
	end
	
	for k,v in ipairs(userList) do 
		local pInfo = UserInfoModel.GetUserInfo(v)
		if pInfo ~= nil then
			pInfo.isteamacceptorder = status
			UserInfoModel.SetUserInfo(pInfo)
			local sqlCase = "update dy_user_info set is_team_accept_order='"..pInfo.isteamacceptorder.."' where userid="..pInfo.userid
			mysqlItem:execute(sqlCase)
		end
	end
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"	
	return luajson.encode(retMsg)	
end
