module("HttpPayInfo", package.seeall)


--设置收款账号开关
--payId						支付方式ID	
--switch					0 失效 1正常
--sign						签证
function setPayInfoSwitch(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local payId = tonumber(arrData.payId) or 0
	local switch = tonumber(arrData.switch) or 0
	
	local sqlCase = "select * from dy_user_pay where id="..payId
	mysqlItem:executeQuery(sqlCase)
	
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		retMsg['code'],retMsg['msg'] = 1, "支付账号不存在"
		return luajson.encode(retMsg)		
	end
	
	local userid = tonumber(sqlData[14])
	local payType = tonumber(sqlData[2])
	local status = tonumber(sqlData[15])
	
	if status == 2 then
		retMsg['code'],retMsg['msg'] = 1, "支付账号不存在"
		return luajson.encode(retMsg)
	end
	
	local payInfo = UserInfoModel.GetPayInfo(userid, payType, payId)
	payInfo.status = switch
	UserInfoModel.SetPayInfo(payInfo)
	local sqlCase = "update dy_user_pay set status="..payInfo.status.." where id="..payInfo.id
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end

--设置收款账号信息
--payId						支付方式ID	
--singlelimit				单笔限额
--daylimit					单日限额
--sign						签证
function setPayInfo(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local payId = tonumber(arrData.payId) or 0
	local singlelimit = tonumber(arrData.singlelimit) or 0
	local daylimit = tonumber(arrData.daylimit) or 0
	
	local sqlCase = "select * from dy_user_pay where id="..payId
	mysqlItem:executeQuery(sqlCase)
	
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		retMsg['code'],retMsg['msg'] = 1, "支付账号不存在"
		return luajson.encode(retMsg)		
	end
	
	local userid = tonumber(sqlData[14])
	local payType = tonumber(sqlData[2])
	local status = tonumber(sqlData[15])
	
	if status == 2 then
		retMsg['code'],retMsg['msg'] = 1, "支付账号不存在"
		return luajson.encode(retMsg)
	end
	
	local payInfo = UserInfoModel.GetPayInfo(userid, payType, payId)
	payInfo.singlelimit = tostring(singlelimit)
	payInfo.daylimit = tostring(daylimit)
	UserInfoModel.SetPayInfo(payInfo)
	local sqlCase = "update dy_user_pay set single_limit="..payInfo.singlelimit..", day_limit="..payInfo.daylimit.." where id="..payInfo.id
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end