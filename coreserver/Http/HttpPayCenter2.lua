
module("HttpPayCenter2", package.seeall)

--拆分挂单
--hangID 					--订单ID
--minMoney					--最小金额
--maxMoney					--最大金额
--sign						--签证
function splitpendingorder(rcvData)    	
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local hangID = tonumber(arrData.hangID) or 0
	local minMoney = tonumber(arrData.minMoney) or 0
	local maxMoney = tonumber(arrData.maxMoney) or 0
	
	if minMoney <= 0 or maxMoney < minMoney then
		retMsg['code'],retMsg['msg'] = 1, "输入的范围有误"
		return luajson.encode(retMsg)	
	end
	
	ThreadManager.OrderLock(hangID)
	local voItem = VendorOrderModel.GetVendorOrderInfo(hangID)
	if voItem == nil then
		ThreadManager.OrderUnLock(hangID)
		retMsg['code'],retMsg['msg'] = ReturnCode["deal_order_not_exist"][1], ReturnCode["deal_order_not_exist"][2]
		return luajson.encode(retMsg)	
	end
	
	if voItem.enablestatus == 0 then
		ThreadManager.OrderUnLock(hangID)
		retMsg['code'],retMsg['msg'] = 1,"挂单已失效"
		return luajson.encode(retMsg)	
	end
	
	if voItem.usertype ~= 300 then
		ThreadManager.OrderUnLock(hangID)
		retMsg['code'],retMsg['msg'] = 1,"该挂单不是提现单，不能拆分"
		return luajson.encode(retMsg)
	end
	
	if voItem.unsoldordernum > 0 then
		ThreadManager.OrderUnLock(hangID)
		retMsg['code'],retMsg['msg'] = 1,"该挂单还存在交易中的订单， 不能拆分"
		return luajson.encode(retMsg)
	end
	

	local remainingMoney =  tonumber(voItem.predictmoney) - tonumber(voItem.unsoldordermoney) - tonumber(voItem.dealordermoney)
	
	if remainingMoney < minMoney then
		ThreadManager.OrderUnLock(hangID)
		retMsg['code'],retMsg['msg'] = 1, "最小限额不能大于而已"
		return luajson.encode(retMsg)	
	end
	
	
	voItem.minmoney = tostring(minMoney)
	voItem.maxmoney = tostring(maxMoney)
	voItem.dealmodel = 0
	VendorOrderModel.SetVendorOrderInfo(voItem.hangid, voItem)
	local sqlCase = "update dy_vendor_order set min_money="..voItem.minmoney..", max_money="..voItem.maxmoney..",deal_mode="..voItem.dealmodel.." where id="..voItem.hangid
	mysqlItem:execute(sqlCase)
	
	ThreadManager.OrderUnLock(hangID)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
	
end

--创建空单
--userid					--币商ID
--money						--金额
--price						--价格
--income					--收益
--date						--创建时间
--payee						--收款人
--bankName					--收款银行
--bankID					--收款账号
--orderType					--0 买单 1 卖单
--sign						--签证
function createemptyorder(rcvData)    	
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local userid = tonumber(arrData.userid)	or 0
	local money = tonumber(arrData.money) or 0
	local price = tonumber(arrData.price) or 0	
	local income = tonumber(arrData.income)	or 0
	local date = arrData.date or ""
	local payee = arrData.payee or ""
	local bankName = arrData.bankName or ""
	local bankID = arrData.bankID or ""
	local orderType = tonumber(arrData.orderType)	or 0
	
	local uInfo = UserInfoModel.GetUserInfo(userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "币商不存在"
		return luajson.encode(retMsg)		
	end
	
	local cInfo = UserInfoModel.GetUserInfo(g_merchant_id)
	if cInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "商户不存在"
		return luajson.encode(retMsg)		
	end
	
	if money <= 0 then
		retMsg['code'],retMsg['msg'] = 1, "金额错误"
		return luajson.encode(retMsg)	
	end
	
	if price <= 0 then
		retMsg['code'],retMsg['msg'] = 1, "价格错误"
		return luajson.encode(retMsg)	
	end
	
	if income < 0 then
		retMsg['code'],retMsg['msg'] = 1, "收益错误"
		return luajson.encode(retMsg)	
	end
	
	if date == "" then
		retMsg['code'],retMsg['msg'] = 1, "时间错误"
		return luajson.encode(retMsg)	
	end
	
	if payee == "" then
		retMsg['code'],retMsg['msg'] = 1, "收款人不能为空"
		return luajson.encode(retMsg)	
	end
	
	if bankName == "" then
		retMsg['code'],retMsg['msg'] = 1, "银行不能为空"
		return luajson.encode(retMsg)	
	end
	
	if bankID == "" then
		retMsg['code'],retMsg['msg'] = 1, "银行卡号不能为空"
		return luajson.encode(retMsg)	
	end
	
	local num = CustomerOrderModel.AddOrderNum() + 10000
	num = tostring(num)
	num = string.sub(num, -3, -1)
	
	local tTable = TimeUtils.GetTableTime()
	local month = tTable.month > 9 and tTable.month or "0"..tTable.month
	local day = tTable.day > 9 and tTable.day or "0"..tTable.day
	local hour = tTable.hour > 9 and tTable.hour or "0"..tTable.hour
	local min = tTable.min > 9 and tTable.min or "0"..tTable.min
	local sec = tTable.sec > 9 and tTable.sec or "0"..tTable.sec
	local onlyMark = tTable.year..month..day..hour..min..sec..num
	
	local tTable = TimeUtils.GetTableTime()
	local rNum = ""
	for i = 1, 6 do 
		rNum = rNum..math.myrandom(0,9)
	end	
	local merchantOrderID = "A"..tTable.year..tTable.month..tTable.day..tTable.hour..tTable.min..tTable.sec..rNum
	
	local tmp = {}
	tmp["id"] = 0
	tmp["paytype"] = 8201
	tmp["account"] = bankID
	tmp["payee"] = payee
	tmp["qrcode"] = ""
	tmp["bankname"] = bankName
	tmp["bankaddr"] = ""
	tmp["singlelimit"] = ""
	tmp["daylimit"] = ""
	tmp["fourthpartyid"] = 0
	tmp["deallasttime"] = "2020-01-01 00:00:00"
	
	local createDate = TimeUtils.GetTimeString(TimeUtils.GetTime(date) - math.myrandom(300, 900))
	local amount = tonumber(string.format("%.10f",tostring(money / price)))
	local fee = amount * 0.015
	local arriveAmount = amount - fee
	income = amount * 0.008
	local dealType = orderType == 0 and 300 or 200
	local fromType = orderType == 0 and 3 or 2
	local customerInfo = orderType == 0 and uInfo  or cInfo
	local vendorInfo = orderType == 0 and cInfo or uInfo

	local sqlCase = "insert into dy_customer_order(type,vendor_order_id,customer_user_id,vendor_user_id,price,amount,money,status,"
		.."pay_type,pay_id,channel,from_type,create_time,deal_type,payee_account,payee_name,payee_bank,income,"
		.."only_mark,vendor_channel, platform_id,vendor_platform_id,update_time,merchant_order_id,pass_time,fee,get_amount) values(0,0,"..customerInfo.userid..","..vendorInfo.userid..","..price..","..amount..","..money..",7"
		..",'"..luajson.encode(tmp).."',8201,'"..customerInfo.channel.."',"..fromType..",'"..createDate.."',"..dealType..",'"..bankID.."','"..payee.."','"..bankName.."',"..income
		..",'"..onlyMark.."','"..vendorInfo.channel.."',"..customerInfo.platformid..","..vendorInfo.platformid..",'"..date.."','"..merchantOrderID
		.."','"..date.."',"..fee..","..arriveAmount..")"
	mysqlItem:execute(sqlCase)
	
	local sqlCase = "select id from dy_customer_order where only_mark='"..onlyMark.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		retMsg['code'],retMsg['msg'] = 1, "创建失败"
		return luajson.encode(retMsg)
	end
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
	
end


--修改订单信息
--orderID					--订单ID
--userid					--币商ID
--money						--金额
--price						--价格
--income					--收益
--date						--创建时间
--payee						--收款人
--bankName					--收款银行
--bankID					--收款账号
--sign						--签证
function updateorderinfo(rcvData)    	
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	
	local orderID = tonumber(arrData.orderID) or 0
	local userid = tonumber(arrData.userid)	or 0
	local money = tonumber(arrData.money) or 0
	local price = tonumber(arrData.price) or 0	
	local income = tonumber(arrData.income)	or 0
	local date = arrData.date or ""
	local payee = arrData.payee or ""
	local bankName = arrData.bankName or ""
	local bankID = arrData.bankID or ""
	
	local uInfo = UserInfoModel.GetUserInfo(userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "币商不存在"
		return luajson.encode(retMsg)		
	end
	
	if money <= 0 then
		retMsg['code'],retMsg['msg'] = 1, "金额错误"
		return luajson.encode(retMsg)	
	end
	
	if price <= 0 then
		retMsg['code'],retMsg['msg'] = 1, "价格错误"
		return luajson.encode(retMsg)	
	end
	
	if income < 0 then
		retMsg['code'],retMsg['msg'] = 1, "收益错误"
		return luajson.encode(retMsg)	
	end
	
	if date == "" then
		retMsg['code'],retMsg['msg'] = 1, "时间错误"
		return luajson.encode(retMsg)	
	end
	
	if payee == "" then
		retMsg['code'],retMsg['msg'] = 1, "收款人不能为空"
		return luajson.encode(retMsg)	
	end
	
	if bankName == "" then
		retMsg['code'],retMsg['msg'] = 1, "银行不能为空"
		return luajson.encode(retMsg)	
	end
	
	if bankID == "" then
		retMsg['code'],retMsg['msg'] = 1, "银行卡号不能为空"
		return luajson.encode(retMsg)	
	end
	
	ThreadManager.OrderLock(orderID)
	local coinInfo = CustomerOrderModel.GetCustomerOrderInfo(orderID)
	if coinInfo == nil then
		ThreadManager.OrderUnLock(orderID)
		retMsg['code'],retMsg['msg'] = 1, "订单不存在！"
		return luajson.encode(retMsg)	
	end
	
	if coinInfo.status ~= 7 and coinInfo.status ~= 11 then
		ThreadManager.OrderUnLock(orderID)
		retMsg['code'],retMsg['msg'] = 1, "订单不是成功的订单不能修改！"
		return luajson.encode(retMsg)
	end
	
	payInfo = luajson.decode(coinInfo.paytypelist)
	payInfo.payee = payee
	payInfo.bankname = bankName
	payInfo.account = bankID
	
	local sqlCase = "update dy_customer_order set vendor_user_id="..userid..", price="..price..", amount="..string.format("%.10f",tostring(money / price))
		..", money="..money..", payee_account='"..bankID.."', payee_name='"..payee.."', payee_bank='"..bankName
		.."', pay_type='"..luajson.encode(payInfo).."', create_time='"..date.."' where id="..orderID
	mysqlItem:execute(sqlCase)

	CustomerOrderModel.delCustomerOrderInfo(orderID)
	
	ThreadManager.OrderUnLock(orderID)

	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
	
end


--批量创建空单
--data   					数据列表
--sign						--签证
function batchcreateemptyorder(rcvData)    	
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	--[[
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	]]
	
	local function importData(dataList)
		local importSuccessList = {}
		for k,v in ipairs(dataList) do 
			local userid = tonumber(v.userid)	or 0
			local money = tonumber(v.money) or 0
			local price = tonumber(v.price) or 0	
			local income = tonumber(v.income)	or 0
			local date = tonumber(v.date) or 0
			local payee = v.payee or ""
			local bankName = v.bankName or ""
			local bankID = v.bankID or ""
			local orderType = tonumber(v.orderType)	or 0
			
			local uInfo = UserInfoModel.GetUserInfo(userid)
			if uInfo == nil then
				return 1, "第"..(k+1).."行数据, 币商不存在, 请重新导入", importSuccessList
			end
			
			local cInfo = UserInfoModel.GetUserInfo(g_merchant_id)
			if cInfo == nil then
				return 1, "第"..(k+1).."行数据, 商户不存在, 请重新导入", importSuccessList
			end
			
			if money <= 0 then
				return 1, "第"..(k+1).."行数据, 金额错误, 请重新导入", importSuccessList
			end
			
			if price <= 0 then
				return 1, "第"..(k+1).."行数据, 价格错误, 请重新导入", importSuccessList
			end
			
			if income < 0 then
				return 1, "第"..(k+1).."行数据, 收益错误, 请重新导入", importSuccessList
			end
			
			if date == 0 then
				return 1, "第"..(k+1).."行数据, 时间错误, 请重新导入", importSuccessList
			end
			
			if payee == "" then
				return 1, "第"..(k+1).."行数据, 收款人不能为空, 请重新导入", importSuccessList
			end
			
			if bankName == "" then
				return 1, "第"..(k+1).."行数据, 银行不能为空, 请重新导入", importSuccessList
			end
			
			if bankID == "" then
				return 1, "第"..(k+1).."行数据, 银行卡号不能为空, 请重新导入", importSuccessList
			end
			
			local num = CustomerOrderModel.AddOrderNum() + 10000
			num = tostring(num)
			num = string.sub(num, -3, -1)
			
			local tTable = TimeUtils.GetTableTime()
			local month = tTable.month > 9 and tTable.month or "0"..tTable.month
			local day = tTable.day > 9 and tTable.day or "0"..tTable.day
			local hour = tTable.hour > 9 and tTable.hour or "0"..tTable.hour
			local min = tTable.min > 9 and tTable.min or "0"..tTable.min
			local sec = tTable.sec > 9 and tTable.sec or "0"..tTable.sec
			local onlyMark = tTable.year..month..day..hour..min..sec..num
			
			local tTable = TimeUtils.GetTableTime()
			local rNum = ""
			for i = 1, 6 do 
				rNum = rNum..math.myrandom(0,9)
			end	
			local merchantOrderID = "A"..tTable.year..tTable.month..tTable.day..tTable.hour..tTable.min..tTable.sec..rNum
			
			local tmp = {}
			tmp["id"] = 0
			tmp["paytype"] = 8201
			tmp["account"] = bankID
			tmp["payee"] = payee
			tmp["qrcode"] = ""
			tmp["bankname"] = bankName
			tmp["bankaddr"] = ""
			tmp["singlelimit"] = ""
			tmp["daylimit"] = ""
			tmp["fourthpartyid"] = 0
			tmp["deallasttime"] = "2020-01-01 00:00:00"
			
			local createDate = TimeUtils.GetTimeString(date - math.myrandom(300, 900))
			local amount = tonumber(string.format("%.10f",tostring(money / price)))
			local fee = amount * 0.015
			local arriveAmount = amount - fee
			income = amount * 0.008
			local dealType = orderType == 0 and 300 or 200
			local fromType = orderType == 0 and 3 or 2
			local customerInfo = orderType == 0 and uInfo  or cInfo
			local vendorInfo = orderType == 0 and cInfo or uInfo

			local sqlCase = "insert into dy_customer_order(type,vendor_order_id,customer_user_id,vendor_user_id,price,amount,money,status,"
				.."pay_type,pay_id,channel,from_type,create_time,deal_type,payee_account,payee_name,payee_bank,income,"
				.."only_mark,vendor_channel, platform_id,vendor_platform_id,update_time,merchant_order_id,pass_time,fee,get_amount) values(0,0,"..customerInfo.userid..","..vendorInfo.userid..","..price..","..amount..","..money..",7"
				..",'"..luajson.encode(tmp).."',8201,'"..customerInfo.channel.."',"..fromType..",'"..createDate.."',"..dealType..",'"..bankID.."','"..payee.."','"..bankName.."',"..income
				..",'"..onlyMark.."','"..vendorInfo.channel.."',"..customerInfo.platformid..","..vendorInfo.platformid..",'"..TimeUtils.GetTimeString(date).."','"..merchantOrderID
				.."','"..TimeUtils.GetTimeString(date).."',"..fee..","..arriveAmount..")"
			mysqlItem:execute(sqlCase)
			
			local sqlCase = "select id from dy_customer_order where only_mark='"..onlyMark.."'"
			mysqlItem:executeQuery(sqlCase)
			local sqlData = mysqlItem:fetch()
			if sqlData == nil then
				return 1, "第"..(k+1).."行数据, 创建失败, 请重新导入", importSuccessList
			end
			
			table.insert(importSuccessList, merchantOrderID)
			
			local msg = "付款人："..(v.payer or "")
			ChatModel.InsertInto(sqlData,merchantOrderID,uInfo.userid,cInfo.userid,
			200,uInfo.channel,g_chatDefine.chat_type['customer_to_merchant'],1,msg)
		end
		
		return 0 , "", {}
	end
	
	local ret, msg,importSuccessList = importData(arrData["data"])
	if ret ~= 0 then
	
		for k,v in ipairs(importSuccessList) do 
			local sqlCase = "delete from dy_customer_order where merchant_order_id='"..v.."'"
			mysqlItem:execute(sqlCase)
			local sqlCase = "delete from dy_chat_data where pcorderid='"..v.."'"
			mysqlItem:execute(sqlCase)
		end
	
		retMsg['code'] = ret
		retMsg['msg'] = msg
		return luajson.encode(retMsg)	
	end
	
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
	
end