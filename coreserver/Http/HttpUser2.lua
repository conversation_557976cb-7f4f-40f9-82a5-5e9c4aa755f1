
module("HttpUser2", package.seeall)

--增加或减少能量值
--userid 					用户ID						
--opttype 					1-增加 2-减少						
--energyValue 				能量值						
--remarks 					备注						
--sign						签证	
function addenergyvalue(rcvData)   
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local uInfo = UserInfoModel.GetUserInfo(arrData.userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "用户不存在！"
		return luajson.encode(retMsg)	
	end
	
	local energyvalue = tonumber(arrData.energyValue)
	if energyvalue <= 0 then
		retMsg['code'],retMsg['msg'] = 1, "能量值数量错误"
		return luajson.encode(retMsg)	
	end
	
	local opttype = tonumber(arrData.opttype)
	if opttype == 1 then
		uInfo.energyvalue = tostring((tonumber(uInfo.energyvalue) or 0) + energyvalue)
	elseif opttype == 2 then
		if  energyvalue > (tonumber(uInfo.energyvalue) or 0) then
			retMsg['code'],retMsg['msg'] = 1, "能量值不足"
			return luajson.encode(retMsg)	
		end
		uInfo.energyvalue = tostring((tonumber(uInfo.energyvalue) or 0) - energyvalue)
	end
	
	uInfo.energyvalue = tonumber(uInfo.energyvalue) < 0 and "0" or uInfo.energyvalue
	UserInfoModel.SetUserInfo(uInfo)
	local sqlCase = "update dy_user_info set energy_value="..uInfo.energyvalue.." where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
	UserInfoModel.SendInfoList(uInfo, {"energyvalue"})
	
	local sqlCase = "insert into dy_transfer_energy_order(from_user_id,from_nickename,from_channel,from_platform_id,energy_value,remarks,opt_type) values("
		..uInfo.userid..",'"..uInfo.nickname.."','"..uInfo.channel.."',"..uInfo.platformid..","..energyvalue..",'"..(arrData.remarks or "").."',"..opttype..")"
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)	
end


--转账功能
--userid					用户ID
--touserid					目标用户ID
--amount 					数量
--remarks					备注
--optID						操作用户ID
--optname					操作用户名称
--sign						签证
function transfer(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local uInfo = UserInfoModel.GetUserInfo(arrData.userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "用户不存在"
		return luajson.encode(retMsg)		
	end
	local touInfo = UserInfoModel.GetUserInfo(arrData.touserid)
	if touInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "目标用户不存在"
		return luajson.encode(retMsg)		
	end
	
	local sysPrice = CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.deal_sell, g_marketDefine.currency_type.USDT, true, uInfo.userid)
	local wInfo = {}
	wInfo.toaddress = ""
	wInfo.currencytype = 2003
	wInfo.subject = ""
	wInfo.version = ""
	wInfo.notifyurl = ""
	wInfo.body = ""
	wInfo.outtradeno = ""
	wInfo.specifictype = 4
	wInfo.addrtype = 101
	wInfo.price = sysPrice
	wInfo.wallettype = 0
	
	if uInfo.dealcointype == 0 then
		wInfo.currencyamount = tonumber(arrData.amount) or 0
		wInfo.money = wInfo.currencyamount * wInfo.price
	else
		wInfo.money = tonumber(arrData.amount) or 0
		wInfo.currencyamount =  math.floor((wInfo.money / wInfo.price) * 1000000000) / 1000000000 
	end
	
	local ret, msg = VendorOrderService.WithdrawCurrency(uInfo, wInfo, 2, touInfo, false, arrData.optID, arrData.optname, arrData.remarks)
	if ret ~= 0 then
		retMsg['code'],retMsg['msg'] = ret, msg
		return luajson.encode(retMsg)		
	end
	
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)
	
end


--设置渠道冷钱包托管地址跟归拢地址地址
--channel 					渠道
--addrType 					链类型 101-erc   102 trc
--addrs						地址
--optType					操作类型 1-添加 2-关闭 3-删除 4-开启
--type						1-冷钱包地址 	2-归拢地址
--sign						签证
function setchanneladdr(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
		
	local channel = arrData.channel or ""
	local addrType = tonumber(arrData.addrType) or 0
	local addrs = arrData.addrs or ""
	local optType = tonumber(arrData.optType) or 0
	local type = tonumber(arrData.type) or 0
	
	if channel == "" or addrType == 0 or addrs == "" or optType == 0 or type == 0 then
		retMsg['code'],retMsg['msg'] = 1, "参数错误"
		return luajson.encode(retMsg)		
	end
	
	
	--检查渠道是否存在
	local sqlCase = "select * from dy_channel_info where channel='"..channel.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		retMsg['code'],retMsg['msg'] = 1, "渠道不存在"
		return luajson.encode(retMsg)
	end
	
	--检查渠道是否存在
	local sqlCase = "select status from dy_trusteeship_address_pre where channel='"..channel.."' and type="..type.." and addr_type="..addrType.." and coin_addr='"..addrs.."'"
	mysqlItem:executeQuery(sqlCase)
	local status = 2
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		status = tonumber(sqlData)
	end
	
	if optType == 1 then
		--添加
		if status ~= 2 then
			retMsg['code'],retMsg['msg'] = 1, "地址已存在"
			return luajson.encode(retMsg)
		end
		
		local sqlCase = "insert into dy_trusteeship_address_pre(coin_addr,addr_type,addr_name,channel,type)values('"..addrs.."',"..addrType..",'"..g_marketDefine.addr_name_list[addrType]
				.."','"..channel.."',"..type..")"
		mysqlItem:execute(sqlCase)
		
	elseif optType == 2 then
		--关闭
		if status ~= 0 then
			retMsg['code'],retMsg['msg'] = 1, "该地址已被关闭或删除"
			return luajson.encode(retMsg)
		end
		
		local sqlCase = "update dy_trusteeship_address_pre set status=1 where channel='"..channel.."' and type="..type.." and addr_type="..addrType.." and coin_addr='"..addrs.."'"
		mysqlItem:execute(sqlCase)
		
	elseif optType == 3 then
		--删除
		if status == 2 then
			retMsg['code'],retMsg['msg'] = 1, "该地址已被删除"
			return luajson.encode(retMsg)
		end
		
		local sqlCase = "delete from dy_trusteeship_address_pre where channel='"..channel.."' and type="..type.." and addr_type="..addrType.." and coin_addr='"..addrs.."'"
		mysqlItem:execute(sqlCase)
		
	elseif optType == 4 then
		--开启
		if status ~= 1 then
			retMsg['code'],retMsg['msg'] = 1, "该地址已开启或者删除"
			return luajson.encode(retMsg)
		end
		
		local sqlCase = "update dy_trusteeship_address_pre set status=0 where channel='"..channel.."' and type="..type.." and addr_type="..addrType.." and coin_addr='"..addrs.."'"
		mysqlItem:execute(sqlCase)
		
	end
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)

end

--设置商户币支付汇率
--userid 					商户ID
--exchangeRate				币支付汇率
--sign						签证
function setmerchantcoinpayrate(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local uInfo = UserInfoModel.GetUserInfo(arrData.userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "用户不存在！"
		return luajson.encode(retMsg)	
	end
	
	if uInfo.usertype ~= 301 then
		retMsg['code'],retMsg['msg'] = 1, "用户不是商户无法设置"
		return luajson.encode(retMsg)	
	end
	
	local exchangeRate = tonumber(arrData.exchangeRate) or 0
	if exchangeRate <= 0 then
		retMsg['code'],retMsg['msg'] = 1, "费率错误"
		return luajson.encode(retMsg)	
	end
	
	uInfo.coinpayrate = tostring(exchangeRate)
	UserInfoModel.SetUserInfo(uInfo)
	local sqlCase = "update dy_user_info set coin_pay_rate="..uInfo.coinpayrate.." where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)

end

--设置商户币支付erc20充币的状态
--userid 					商户ID
--status					0-关闭 1开启
--sign						签证
function setmerchanterc20status(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local uInfo = UserInfoModel.GetUserInfo(arrData.userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "用户不存在！"
		return luajson.encode(retMsg)	
	end
	
	if uInfo.usertype ~= 301 then
		retMsg['code'],retMsg['msg'] = 1, "用户不是商户无法设置"
		return luajson.encode(retMsg)	
	end
	
	local status = tonumber(arrData.status) 
	if status ~= 0 and status ~= 1 then
		retMsg['code'],retMsg['msg'] = 1, "状态错误"
		return luajson.encode(retMsg)	
	end
	
	uInfo.coinpayerc = status
	UserInfoModel.SetUserInfo(uInfo)
	local sqlCase = "update dy_user_info set coin_pay_erc="..uInfo.coinpayerc.." where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)

end

--设置商户币支付trc20充币的状态
--userid 					商户ID
--status					0-关闭 1开启
--sign						签证
function setmerchanttrc20status(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
		
	local uInfo = UserInfoModel.GetUserInfo(arrData.userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "用户不存在！"
		return luajson.encode(retMsg)	
	end
	
	if uInfo.usertype ~= 301 then
		retMsg['code'],retMsg['msg'] = 1, "用户不是商户无法设置"
		return luajson.encode(retMsg)	
	end
	
	local status = tonumber(arrData.status) 
	if status ~= 0 and status ~= 1 then
		retMsg['code'],retMsg['msg'] = 1, "状态错误"
		return luajson.encode(retMsg)	
	end
	
	uInfo.coinpaytrc = status
	UserInfoModel.SetUserInfo(uInfo)
	local sqlCase = "update dy_user_info set coin_pay_trc="..uInfo.coinpaytrc.." where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)

end

--设置商户币支付是否开启系统地址
--userid 					商户ID
--status					0-关闭 1开启
--sign						签证
function setmerchantsysaddr(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local uInfo = UserInfoModel.GetUserInfo(arrData.userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "用户不存在！"
		return luajson.encode(retMsg)	
	end
	
	if uInfo.usertype ~= 301 then
		retMsg['code'],retMsg['msg'] = 1, "用户不是商户无法设置"
		return luajson.encode(retMsg)	
	end
	
	local status = tonumber(arrData.status) 
	if status ~= 0 and status ~= 1 then
		retMsg['code'],retMsg['msg'] = 1, "状态错误"
		return luajson.encode(retMsg)	
	end
	
	uInfo.allowsysaddr = status
	UserInfoModel.SetUserInfo(uInfo)
	local sqlCase = "update dy_user_info set allow_sys_addr="..uInfo.allowsysaddr.." where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)

end

--设置商户币支付erc最小接单币数量
--userid 					商户ID
--amount					币数量
--sign						签证
function setmerchantsysercmin(rcvData)
	
	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
		
	local uInfo = UserInfoModel.GetUserInfo(arrData.userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "用户不存在！"
		return luajson.encode(retMsg)	
	end
	
	if uInfo.usertype ~= 301 then
		retMsg['code'],retMsg['msg'] = 1, "用户不是商户无法设置"
		return luajson.encode(retMsg)	
	end
	
	local amount = tonumber(arrData.amount) or 0
	if amount <= 0 then
		retMsg['code'],retMsg['msg'] = 1, "币数量错误"
		return luajson.encode(retMsg)	
	end
	
	uInfo.sysercmin = tostring(amount)
	UserInfoModel.SetUserInfo(uInfo)
	local sqlCase = "update dy_user_info set sys_erc_min="..uInfo.sysercmin.." where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)

end

--添加自备地址
--userid 					商户ID
--addrTypeList				地址类型 101-erc20  102-trc20
--addrsList					地址
--sign						签证
function addmerchantselfaddr(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	local uInfo = UserInfoModel.GetUserInfo(arrData.userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "用户不存在！"
		return luajson.encode(retMsg)	
	end
	
	if uInfo.usertype ~= 301 then
		retMsg['code'],retMsg['msg'] = 1, "用户不是商户无法设置"
		return luajson.encode(retMsg)	
	end
	
	if arrData.addrTypeList == "" or arrData.addrTypeList == nil then
		retMsg['code'],retMsg['msg'] = 1, "链类型错误"
		return luajson.encode(retMsg)	
	end
	
	if arrData.addrsList == "" or arrData.addrsList == nil then
		retMsg['code'],retMsg['msg'] = 1, "地址错误"
		return luajson.encode(retMsg)	
	end
	
	ThreadManager.MerchantSelfAddrLock(0)
	local addrTypeList = string.split(arrData.addrTypeList, ",")
	local addrsList = string.split(arrData.addrsList, ",")
	for i=1, #addrsList do
		addrsList[i] = string.lower(addrsList[i])
	end

	--检查地址有没有重复的
	local tmpList = {}
	local tmpStr = "("
	for k,v in ipairs(addrsList) do
		if tmpList[v] == nil then
			tmpList[v] = 0
		end
		tmpList[v] = tmpList[v] + 1
		tmpStr = tmpStr.." adds='"..v.."'"
		if k < #addrsList then 
			tmpStr=tmpStr.." or"
		
		end
	end
	tmpStr = tmpStr..")"
	for k,v in pairs(tmpList)do 
		if v > 1 then
			retMsg['code'],retMsg['msg'] = 1, "输入的地址有重复的"
			ThreadManager.MerchantSelfAddrUnLock(0)
			return luajson.encode(retMsg)
		end
	end
	
	
	--检查该地址是否已经存在
	local sqlCase = "select * from dy_user_pay where pay_id="..g_payCenterDefine.pay_id_list['bitpay'].." and "..tmpStr.." and status!=4"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		retMsg['code'],retMsg['msg'] = 1, "输入的地址已经存在"
		ThreadManager.MerchantSelfAddrUnLock(0)
		return luajson.encode(retMsg)
	end
	
	for k,v in ipairs(addrTypeList) do
		local sqlCase = "insert into dy_user_pay(pay_id,channel,platform_id,userid,status,owership,adds,addr_type,addr_name,nickname) values(8401,'"
		..uInfo.channel.."',"..uInfo.platformid..","..uInfo.userid..",3"..",1,'"..addrsList[k].."',"..v..",'"..g_marketDefine.addr_name_list[tonumber(v)].."','"..uInfo.nickname.."')"
		mysqlItem:execute(sqlCase)
	end
	
	ThreadManager.MerchantSelfAddrUnLock(0)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)

end

--审核自备地址
--userid 					商户ID
--ID						id
--review					审核结果 1-通过 4-不通过
--optid						审核ID
--optname					审核人名称
--remarks					备注
--sign						签证
function reviewmerchantselfaddr(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local uInfo = UserInfoModel.GetUserInfo(arrData.userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "用户不存在！"
		return luajson.encode(retMsg)	
	end
	
	if uInfo.usertype ~= 301 then
		retMsg['code'],retMsg['msg'] = 1, "用户不是商户无法设置"
		return luajson.encode(retMsg)	
	end
	
	local ID = tonumber(arrData.ID) or 0
	ThreadManager.MerchantSelfAddrLock(0)
	
	--检查该地址是否已经存在
	local sqlCase = "select status from dy_user_pay where id="..ID
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		ThreadManager.MerchantSelfAddrUnLock(0)
		retMsg['code'],retMsg['msg'] = 1, "ID不存在"
		return luajson.encode(retMsg)
	end
	
	local status = tonumber(sqlData)
	if status ~= 3 then
		ThreadManager.MerchantSelfAddrUnLock(0)
		retMsg['code'],retMsg['msg'] = 1, "该状态下无法审核"
		return luajson.encode(retMsg)
	end
	local review = tonumber(arrData.review) == 1 and 1 or 4
	local sqlCase = "update dy_user_pay set status="..review..", opt_id="..(arrData.optid or 0)..", opt_name='"..(arrData.optname or 0).."' ,remarks='"
			.."' where id="..ID
	mysqlItem:execute(sqlCase)
	
	ThreadManager.MerchantSelfAddrUnLock(0)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)

end

--删除地址
--userid 					商户ID
--id						地址id
--sign						签证
function delmerchantselfaddr(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	local uInfo = UserInfoModel.GetUserInfo(arrData.userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "用户不存在！"
		return luajson.encode(retMsg)	
	end
	
	if uInfo.usertype ~= 301 then
		retMsg['code'],retMsg['msg'] = 1, "用户不是商户无法设置"
		return luajson.encode(retMsg)	
	end
	
	ThreadManager.MerchantSelfAddrLock(0)

	--检查该地址是否已经存在
	local sqlCase = "select status from dy_user_pay where id="..arrData.id
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		if tonumber(sqlData) == 0 or tonumber(sqlData) == 1 then
			local sqlCase = "delete from dy_user_pay where id="..arrData.id
			mysqlItem:execute(sqlCase)
		end
	end
	
	ThreadManager.MerchantSelfAddrUnLock(0)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)

end


--币钱包划转到数字钱包
--userid 					商户ID
--amount					币数量
--sign						签证
function cionwallettobasiswallet(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	local uInfo = UserInfoModel.GetUserInfo(arrData.userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "用户不存在！"
		return luajson.encode(retMsg)	
	end
	
	local amount = tonumber(arrData.amount)
	if amount <= 0 then
		retMsg['code'],retMsg['msg'] = 1, "划转数量必须大于0"
		return luajson.encode(retMsg)
	end
	
	
	if amount > tonumber(uInfo.coinpayusdtamount) - tonumber(uInfo.coinpayusdtlockamount) then
		retMsg['code'],retMsg['msg'] = 1, "币钱包余额余额不足"
		return luajson.encode(retMsg)
	end
	
	if false == UserInfoModel.AddErcCoinPayUsdtLockAmount(uInfo, amount, 0, 0, "币钱包划转基础钱包, 冻结资金") then
		retMsg['code'],retMsg['msg'] = 1, "币钱包余额余额不足"
		return luajson.encode(retMsg)
	end
	
	local price = CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.deal_sell, g_marketDefine.currency_type.USDT, true, uInfo.userid)
	local money = math.floor(amount * price * 10000) / 10000
	
	UserInfoModel.DecErcCoinPayUsdtLockAmount(uInfo, amount, 0, 0, "币钱包划转基础钱包，解除冻结")
	UserInfoModel.DecErcCoinPayUsdtAmount(uInfo, amount, 0, 0, "币钱包划转基础钱包，扣除资金",g_humanDefine.fund_details.type_cionwallet_to_basiswallet, money)
	
	UserInfoModel.AddErcUsdtAmount(uInfo, amount, 0, 0, "币钱包划转基础钱包，增加资金",g_humanDefine.fund_details.type_cionwallet_to_basiswallet, money)
	UserInfoModel.SendErcUsdtAmount(uInfo)
			
	if uInfo.dealcointype == 1 then
		--结算货币类型如果是法币把法币账户也结算一下
		UserInfoModel.AddErcFCAmount(uInfo, money, 0, 0, "币钱包划转基础钱包，增加资金", g_humanDefine.fund_details.type_cionwallet_to_basiswallet)
	end
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)

end


--设置商户需不需要检查重复订单
--userid 					商户ID
--ischeck					0 检查 1 不检查
--sign						签证
function setcheckrepeatorder(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	local uInfo = UserInfoModel.GetUserInfo(arrData.userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "用户不存在！"
		return luajson.encode(retMsg)	
	end
	
	uInfo.ischeck = tonumber(arrData.ischeck) or 0
	UserInfoModel.SetUserInfo(uInfo)
	local sqlCase = "update dy_user_info set is_check="..uInfo.ischeck.." where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)

end

--删除用户
--userid 					用户ID
--sign						签证
function deluser(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	local uInfo = UserInfoModel.GetUserInfo(arrData.userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "用户不存在！"
		return luajson.encode(retMsg)	
	end
	
	if uInfo.usertype ~= 201 and uInfo.usertype ~= 202 and uInfo.usertype ~= 301 then
		retMsg['code'],retMsg['msg'] = 1, "暂时不支持删除代理级别的用户"
		return luajson.encode(retMsg)
	end  
	
	--先检查还有没有未完成订单
	local sqlCase = "select * from dy_customer_order where (customer_user_id="..uInfo.userid.." or vendor_user_id="..uInfo.userid..")"
		.."and (status=1 or status=3 or status=9 or status=17)"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		retMsg['code'],retMsg['msg'] = 1, "用户还有未完成的订单"
		return luajson.encode(retMsg)
	end
	
	--先检查还有没有未完成挂单
	local sqlCase = "select * from dy_vendor_order where userid="..uInfo.userid.."and (enable_status=1 or enable_status=2 or enable_status=5)"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		retMsg['code'],retMsg['msg'] = 1, "用户还有未完成的挂单"
		return luajson.encode(retMsg)
	end
	
	--先检查还有没有未完成提币订单
	local sqlCase = "select * from dy_block_chain_trans where specific_type=2 and userid="..uInfo.userid
		.."and (recd_status=101 or recd_status=103 or recd_status=105)"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		retMsg['code'],retMsg['msg'] = 1, "用户还有未完成的提币订单"
		return luajson.encode(retMsg)
	end
	
	--先检查还有没有未完成转账订单
	local sqlCase = "select * from dy_block_chain_trans where specific_type=4 and userid="..uInfo.userid
		.."and (recd_status=101 or recd_status=103 or recd_status=105)"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		retMsg['code'],retMsg['msg'] = 1, "用户还有未完成的转账订单"
		return luajson.encode(retMsg)
	end
	
	--先检查还有没有未完成托管钱包充值订单
	--[[
	local sqlCase = "select * from dy_trusteeship_wallet_order where userid="..uInfo.userid.." and status=0"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		retMsg['code'],retMsg['msg'] = 1, "用户还有未完成的托管钱包充值订单"
		return luajson.encode(retMsg)
	end
	]]
	
	local sqlCase = "update dy_user_info set status=5, is_lock=1, prohibit_login=1, is_accept_order=1," 
		.."is_allow_loginback=1,account='"..uInfo.userid.."', email='"..uInfo.userid.."', phonenum='"..uInfo.userid.."'"
		.." where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
	
	redisItem:del(UserInfoModel.userinfo_list..uInfo.userid, UserInfoModel.redis_index)
	
	local sqlCase = "delete from dy_user_conf where bind_type=102 and bind_userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
	
	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)

end

--获取商户可用余额
--userid 					用户ID
--sign						签证
function getuserbalance(rcvData)

	local retMsg = {}
	retMsg['code'] = 200;
	if rcvData == nil then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['data_is_nill'][1],g_payCenterDefine.retuen_code['data_is_nill'][2]
		return luajson.encode(retMsg)
	end
	local arrData = luajson.decode(rcvData)
	
	--开始进行签名
	local signStr = arrData['sign']
	arrData["sign"] = nil
	local encryptStr = EncryptWithMD5(arrData,"aaaaaaaaaaaaaaaa")
	if signStr ~= encryptStr then
		retMsg['code'],retMsg['msg'] = g_payCenterDefine.retuen_code['sign_error'][1],g_payCenterDefine.retuen_code['sign_error'][2]
		--return luajson.encode(retMsg)		
	end
	
	local uInfo = UserInfoModel.GetUserInfo(arrData.userid)
	if uInfo == nil then
		retMsg['code'],retMsg['msg'] = 1, "用户不存在！"
		return luajson.encode(retMsg)	
	end

	if uInfo.usertype ~= 300 and uInfo.usertype ~= 301 then
		retMsg['code'],retMsg['msg'] = 1, "暂时只支持商户查询"
		return luajson.encode(retMsg)	
	end
	
	local sqlCase = "select amount,(unsold_order_amount+deal_order_amount),predict_money,(unsold_order_money+deal_order_money),withdraw_type from dy_vendor_order where type=1 and enable_status=1 and wallet_type=0 and userid="..uInfo.userid
	mysqlItem:executeQuery(sqlCase)
	
	local orderInfoList = {}
	while true do 
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		local tmp = {}
		tmp['amount'] = tonumber(sqlData[1]) or 0
		tmp['orderAmount'] = tonumber(sqlData[2]) or 0
		tmp['money'] = tonumber(sqlData[3]) or 0
		tmp['orderMoney'] = tonumber(sqlData[4]) or 0
		tmp['withdrawType'] = tonumber(sqlData[5]) or 0
		table.insert(orderInfoList, tmp)
	end
	
	if uInfo.dealcointype == 0 then
		--数字货币
		local amount = 0
		local orderAmount = 0
		for k,v in ipairs(orderInfoList) do 
			local feeType = v.withdrawType == 0 and g_humanDefine.sell_fee_rate or g_humanDefine.behalf_sell_fee_rate
			local userRate = UserInfoModel.getUserFeeRate(uInfo.userid, 8201, feeType)
			amount = amount + (v.amount + (v.amount * userRate))
			orderAmount = orderAmount + (v.orderAmount + (v.orderAmount * userRate))
		end
		
		retMsg['data'] = {}
		retMsg['data']['balance'] = tostring(uInfo.ercusdtamount - uInfo.ercusdtlockamount - (amount - orderAmount))
		retMsg['data']['lockAmount'] = tostring(uInfo.ercusdtlockamount + (amount - orderAmount))
	else
		--法币
		local money = 0
		local orderMoney = 0
		for k,v in ipairs(orderInfoList) do 
			local feeType = v.withdrawType == 0 and g_humanDefine.sell_fee_rate or g_humanDefine.behalf_sell_fee_rate
			local userRate = UserInfoModel.getUserFeeRate(uInfo.userid, 8201, feeType)
			money = money + (v.money + (v.money * userRate))
			orderMoney = orderMoney + (v.orderMoney + (v.orderMoney * userRate))
		end
		
		retMsg['data'] = {}
		retMsg['data']['balance'] = tostring(uInfo.ercfcamount - uInfo.ercfclockamount - (money - orderMoney))
		retMsg['data']['lockAmount'] = tostring(uInfo.ercfclockamount + (money - orderMoney))
	end

	retMsg['code'] = 200
	retMsg['msg'] = "success"
	return luajson.encode(retMsg)
end