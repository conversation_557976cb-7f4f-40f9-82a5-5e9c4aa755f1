AgentAward = {}

--分佣模式
AgentAwardList = {
	["default"] = "OtcShopAward",
}

function AgentAward:GetAwardControll(channel)
	return _G[AgentAwardList[channel]] == nil and _G[AgentAwardList['default']] or _G[AgentAwardList[channel]]
end

--获取商户的分佣长度， 0 代表无限长
function AgentAward.GetAgentLevel(channel)
	local awardItem = AgentAward:GetAwardControll(channel)
	return awardItem['GetAgentLevel_merchant'] == nil and 0 or awardItem['GetAgentLevel_merchant']()
end

