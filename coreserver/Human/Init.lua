require("common.define.HumanDefine")
require("common.define.PayCenterDefine")
require("common.packet.packet_human")
require("common.st_human_pb")
require("common.msg_human_pb")
require("common.msg_human2_pb")


require("common.define.MarketDefine")

require("Human.Controller.AddPay")
require("Human.Controller.ForgetFundPwd")
require("Human.Controller.ForgetLoginPwd")
require("Human.Controller.Login")
require("Human.Controller.Register")
require("Human.Controller.SetFundPwd")
require("Human.Controller.UpdateLoginPwd")
require("Human.Controller.UpdateNickname")
require("Human.Controller.ModifyPay")
require("Human.Controller.DeletePay")
require("Human.Controller.PayStatus")
require("Human.Controller.Paylist")
require("Human.Controller.Heartbeat")
require("Human.Controller.ReConnect")
require("Human.Controller.MyPerformance")
require("Human.Controller.SetSubInfo")
require("Human.Controller.CheckFundPwd")
require("Human.Controller.GetUserInfo")
require("Human.Controller.TransferEnergyValue")


require("Human.Model.OnlineModel")
require("Human.Model.UserInfoModel")
require("Human.Model.UtilsModel")
require("Human.Model.UserPayModel")

require("Human.Services.HumanService")
require("Human.Services.NoticeServices")

require("Http.HttpPayCenter")
require("Http.HttpPayCenter2")
require("Http.HttpUser")
require("Http.HttpUser2")
require("Http.HttpPayInfo")
require("Http.HttpExchange")
require("Http.HttpPlatform")
require("Http.HttpGroup")

g_redisIndex[UserInfoModel.redis_index] = {index = g_redisInfo.redis_one, key = UserInfoModel.redis_index, link = 1} 
g_redisIndex[OnlineModel.redis_index] = {index = g_redisInfo.redis_one, key = OnlineModel.redis_index, link = 1} 

