module("GetUserInfo", package.seeall)

--查询用户信息
function execute(packetID, operateID, buffer)

	local cgmsg = msg_human_pb.cggetuserinfo()
	local gcmsg = msg_human_pb.gcgetuserinfo()

	cgmsg:ParseFromString(buffer)
	
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid,gcmsg.uinfo)
	if uInfo == nil then
		gcmsg.result = ReturnCode["human_user_not_exist"][1]
		gcmsg.msg = ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	--看看有没有地址
	local sqlCase = "select coin_addr,addr_type,addr_name from dy_user_address_pre where user_id="..cgmsg.userid.." and status=0"
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlDate = mysqlItem:fetch({})
		if sqlDate == nil then
			break
		end
		
		local addAddr = gcmsg.addrlist:add()
		addAddr.addrtype = tonumber(sqlDate[2])
		addAddr.addrname = sqlDate[3]
		addAddr.address = sqlDate[1]
	end
	
	gcmsg.result = 0
	return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()

end