module("UpdateNickname", package.seeall)

--修改昵称
function execute(packetID, operateID, buffer)

	local cgmsg = msg_human_pb.cgupdatenickname()
	local gcmsg = msg_human_pb.gcupdatenickname()

	cgmsg:ParseFromString(buffer)
	if cgmsg.nickname == "" then
		gcmsg.result = ReturnCode["human_nickname_err_1"][1]
		gcmsg.msg = ReturnCode["human_nickname_err_1"][2]
		return 0,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result = ReturnCode["human_user_not_exist"][1]
		gcmsg.msg = ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	
	local nickname = cgmsg.nickname
	
	--检查是否是属于那个玩家的
	local userid = UserInfoModel.checkNickName(nickname)
	if userid == 0 then
		uInfo.nickname = nickname
		UserInfoModel.SetUserInfo(uInfo)
		--修改昵称
		local sqlCase = "update dy_user_info set nickname='"..uInfo.nickname.."' where userid="..uInfo.userid
		mysqlItem:execute(sqlCase)
		UserInfoModel.SendNickName(uInfo)
	elseif userid ~= uInfo.userid then
		gcmsg.result = 1
		gcmsg.msg = "昵称已经存在"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	elseif userid == uInfo.userid then
		gcmsg.result = 1
		gcmsg.msg = "跟原昵称一致"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	gcmsg.result = 0
	return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()

end