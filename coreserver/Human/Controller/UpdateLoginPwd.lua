module("UpdateLoginPwd", package.seeall)

--修改登录密码
function execute(packetID, operateID, buffer)

	local cgmsg = msg_human_pb.cgupdateloginpwd()
	local gcmsg = msg_human_pb.gcupdateloginpwd()

	cgmsg:ParseFromString(buffer)

	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result = ReturnCode["human_user_not_exist"][1]
		gcmsg.msg = ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	if cgmsg.newpwd == "" then
		gcmsg.result = ReturnCode["human_pwd_err_3"][1]
		gcmsg.msg = ReturnCode["human_pwd_err_3"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	--检查验证码
	if false ==  UtilsModel.CheckAuthcode(uInfo.phonenum, cgmsg.authcode, uInfo.channel) then
		gcmsg.result = ReturnCode["human_authcode_not_exist"][1]
		gcmsg.msg = ReturnCode["human_authcode_not_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	--检查原密码
	local sqlCase = "select password from dy_user_info where userid="..uInfo.userid
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		if sqlData ~= md5(cgmsg.oldpwd) then
			gcmsg.result =  ReturnCode["human_pwd_err_1"][1]
			gcmsg.msg = ReturnCode["human_pwd_err_1"][2]
			return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
		end
		if sqlData == md5(cgmsg.newpwd) then
			gcmsg.result =  ReturnCode["human_pwd_err_2"][1]
			gcmsg.msg = ReturnCode["human_pwd_err_2"][2]
			return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
		end
	end
		
	
	--修改密码
	local sqlCase = "update dy_user_info set password='"..md5(cgmsg.newpwd).."' where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
	UserInfoModel.SetPhoneByUser(uInfo.phonenum, uInfo.channel, uInfo.userid, md5(cgmsg.newpwd))
	
	gcmsg.result = 0
	return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()

end