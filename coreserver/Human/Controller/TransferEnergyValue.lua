module("TransferEnergyValue", package.seeall)

--转让能量值
function execute(packetID, operateID, buffer)

	local cgmsg = msg_human2_pb.cgtransferenergyvalue()
	local gcmsg = msg_human2_pb.gctransferenergyvalue()

	cgmsg:ParseFromString(buffer)
	
	if cgmsg.userid == cgmsg.touserid then
		gcmsg.result,gcmsg.msg = 1, "不能自己给自己转入能量"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result,gcmsg.msg = ReturnCode["human_user_not_exist"][1],ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	local touInfo = UserInfoModel.GetUserInfo(cgmsg.touserid)
	if touInfo == nil then
		gcmsg.result,gcmsg.msg = 1, "转让用户不存在"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--检查资金密码
	local ret, msg = UserInfoModel.CheckFundPassword(uInfo,cgmsg.fundpwd)
	if ret ~= 0 then
		gcmsg.result, gcmsg.msg = ret,msg
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	local energyvalue = tonumber(cgmsg.energyvalue)
	if energyvalue <= 0 then
		gcmsg.result, gcmsg.msg = 1, "能量值数量错误"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	if  energyvalue > tonumber(uInfo.energyvalue) then
		gcmsg.result, gcmsg.msg = 1, "能量值不足"
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	uInfo.energyvalue = tostring((tonumber(uInfo.energyvalue) or 0) - energyvalue)
	uInfo.energyvalue = tonumber(uInfo.energyvalue) < 0 and "0" or uInfo.energyvalue
	UserInfoModel.SetUserInfo(uInfo)
	local sqlCase = "update dy_user_info set energy_value="..uInfo.energyvalue.." where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
	UserInfoModel.SendInfoList(uInfo, {"energyvalue"})
	
	touInfo.energyvalue = tostring((tonumber(touInfo.energyvalue) or 0) + energyvalue)
	UserInfoModel.SetUserInfo(touInfo)
	local sqlCase = "update dy_user_info set energy_value="..touInfo.energyvalue.." where userid="..touInfo.userid
	mysqlItem:execute(sqlCase)
	UserInfoModel.SendInfoList(touInfo, {"energyvalue"})
	
	local sqlCase = "insert into dy_transfer_energy_order(from_user_id,from_nickename,from_channel,from_platform_id,energy_value,to_user_id,to_nickename,to_channel,to_platform_id) values("
		..uInfo.userid..",'"..uInfo.nickname.."','"..uInfo.channel.."',"..uInfo.platformid..","..energyvalue..","..touInfo.userid..",'"..touInfo.nickname.."','"..touInfo.channel.."',"..touInfo.platformid..")"
	mysqlItem:execute(sqlCase)

	gcmsg.result = 0
	return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()

end