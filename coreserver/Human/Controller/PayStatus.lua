module("PayStatus", package.seeall)

--失效/生效收款方式
function execute(packetID, operateID, buffer)

	local cgmsg = msg_human_pb.cgpaystatus()
	local gcmsg = msg_human_pb.gcpaystatus()

	cgmsg:ParseFromString(buffer)
	
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result = ReturnCode["human_user_not_exist"][1]
		gcmsg.msg = ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	--查看是否已经绑定改信息
	local payInfo = UserInfoModel.GetPayInfo(cgmsg.userid, cgmsg.paytype, cgmsg.id)
	if payInfo == nil then
		gcmsg.result = ReturnCode["human_payinfo_not_exist"][1]
		gcmsg.msg = ReturnCode["human_payinfo_not_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	payInfo.status = payInfo.status == 0 and 1 or 0
	
	UserInfoModel.SetPayInfo(payInfo)
	local sqlCase = "update dy_user_pay set status="..payInfo.status.." where id="..payInfo.id
	mysqlItem:execute(sqlCase)
	
	gcmsg.result = 0
	return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()

end