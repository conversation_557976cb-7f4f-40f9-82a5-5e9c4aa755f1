module("ModifyPay", package.seeall)

--修改收款方式限额
function execute(packetID, operateID, buffer)

	local cgmsg = msg_human_pb.cgmodifypay()
	local gcmsg = msg_human_pb.gcmodifypay()

	cgmsg:ParseFromString(buffer)
	
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result = ReturnCode["human_user_not_exist"][1]
		gcmsg.msg = ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end

	if (tonumber(cgmsg.daylimit) or 0) < (tonumber(cgmsg.singlelimit) or 0)   then
		gcmsg.result = ReturnCode["human_parameter_error"][1]
		gcmsg.msg = ReturnCode["human_parameter_error"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
		
	end
	
	--查看是否已经绑定改信息
	local payInfo = UserInfoModel.GetPayInfo(cgmsg.userid, cgmsg.paytype, cgmsg.id)
	if payInfo == nil then
		gcmsg.result = ReturnCode["human_payinfo_not_exist"][1]
		gcmsg.msg = ReturnCode["human_payinfo_not_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	payInfo.singlelimit = tostring(tonumber(cgmsg.singlelimit) or 0)
	payInfo.daylimit = tostring(tonumber(cgmsg.daylimit) or 0)
	
	--修改缓存
	UserInfoModel.SetPayInfo(payInfo)
	local sqlCase = "update dy_user_pay set single_limit="..payInfo.singlelimit..", day_limit="..payInfo.daylimit
			.." where id="..payInfo.id
	mysqlItem:execute(sqlCase)
	
	gcmsg.result = 0
	return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()

end