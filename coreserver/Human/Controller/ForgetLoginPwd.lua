module("ForgetLoginPwd", package.seeall)

--修改登录密码
function execute(packetID, operateID, buffer)

	local cgmsg = msg_human_pb.cgforgetloginpwd()
	local gcmsg = msg_human_pb.gcforgetloginpwd()

	cgmsg:ParseFromString(buffer)
	
	if cgmsg.newpwd == "" then
		gcmsg.result = ReturnCode["human_pwd_err_3"][1]
		gcmsg.msg = ReturnCode["human_pwd_err_3"][2]
		return 0,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end

	--检查验证码
	if false == UtilsModel.CheckAuthcode(cgmsg.phonenum, cgmsg.authcode, cgmsg.channel) then
		gcmsg.result = ReturnCode["human_authcode_not_exist"][1]
		gcmsg.msg = ReturnCode["human_authcode_not_exist"][2]
		return 0,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	--根据手机号码和渠道号查找用户ID
	local pInfo = UserInfoModel.GetPhoneByUser(cgmsg.phonenum,cgmsg.channel)
	if pInfo == nil then
		gcmsg.result = ReturnCode["human_user_not_exist"][1]
		gcmsg.msg = ReturnCode["human_user_not_exist"][2]
		return 0, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
		
	
	if pInfo.password == md5(cgmsg.newpwd) then
		gcmsg.result =  ReturnCode["human_pwd_err_2"][1]
		gcmsg.msg = ReturnCode["human_pwd_err_2"][2]
		return 0, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	--修改密码
	local sqlCase = "update dy_user_info set password='"..md5(cgmsg.newpwd).."' where userid="..pInfo.userid
	mysqlItem:execute(sqlCase)
	UserInfoModel.SetPhoneByUser(cgmsg.phonenum, cgmsg.channel,pInfo.userid, md5(cgmsg.newpwd))
	gcmsg.result = 0
	return 0, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()

end