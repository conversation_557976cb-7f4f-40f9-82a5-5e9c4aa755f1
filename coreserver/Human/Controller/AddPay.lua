module("AddPay", package.seeall)

--添加收款方式
function execute(packetID, operateID, buffer)

	local cgmsg = msg_human_pb.cgaddpay()
	local gcmsg = msg_human_pb.gcaddpay()

	cgmsg:ParseFromString(buffer)
	
	if cgmsg.newpwd == "" then
		gcmsg.result = ReturnCode["human_pwd_err_3"][1]
		gcmsg.msg = ReturnCode["human_pwd_err_3"][2]
		return 0,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result = ReturnCode["human_user_not_exist"][1]
		gcmsg.msg = ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	cgmsg.account = cgmsg.account
	cgmsg.payee = cgmsg.payee
	cgmsg.qrcode = cgmsg.qrcode
	cgmsg.bankname = cgmsg.bankname
	cgmsg.bankaddr = cgmsg.bankaddr
	cgmsg.singlelimit = cgmsg.singlelimit
	cgmsg.daylimit = cgmsg.daylimit
	
	
	--检查玩家是否存在该种类型的支付方式
	local isEx = false 
	for k, v in ipairs(uInfo.paytypelist) do 
		if v == tonumber(cgmsg.paytype) then
			isEx = true
		end
	end
	
	if isEx == false then
		gcmsg.result = 1
		gcmsg.msg = "你未开通次支付方式"
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	--查看是否已经绑定改信息
	local sqlCase = "select * from dy_user_pay where account='"..cgmsg.account.."' and status!=2 and userid="..cgmsg.userid.." and pay_id="..cgmsg.paytype
	mysqlItem:executeQuery(sqlCase)
	local sqlDate = mysqlItem:fetch({})
	if sqlDate ~= nil then
		gcmsg.result = ReturnCode["human_payinfo_exist"][1]
		gcmsg.msg = ReturnCode["human_payinfo_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	local sqlCase = "insert into dy_user_pay(pay_id,name,account,bank_name,bank_addr,qr_code,single_limit,day_limit,userid, status,channel,platform_id) values("
			..cgmsg.paytype..",'"..cgmsg.payee.."','"..cgmsg.account.."','"..cgmsg.bankname.."','"..cgmsg.bankaddr.."','"..cgmsg.qrcode
			.."','"..cgmsg.singlelimit.."','"..cgmsg.daylimit.."',"..cgmsg.userid..", 1,'"..uInfo.channel.."',"..uInfo.platformid..")"
	mysqlItem:execute(sqlCase)
	
	local sqlCase = "select id from dy_user_pay where pay_id="..cgmsg.paytype.." and account='"..cgmsg.account
			.."' and userid="..cgmsg.userid.." order by id desc"
	mysqlItem:executeQuery(sqlCase)
	local sqlDate = mysqlItem:fetch()
	if sqlDate == nil then
		gcmsg.result = ReturnCode["human_add_err_1"][1]
		gcmsg.msg = ReturnCode["human_add_err_1"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	
	local payInfo = st_human_pb.payinfo()
	payInfo.paytype = cgmsg.paytype
	payInfo.account = cgmsg.account
	payInfo.payee = cgmsg.payee
	payInfo.qrcode = cgmsg.qrcode
	payInfo.bankname = cgmsg.bankname
	payInfo.bankaddr = cgmsg.bankaddr
	payInfo.singlelimit = cgmsg.singlelimit
	payInfo.daylimit = cgmsg.daylimit
	payInfo.id = tonumber(sqlDate)
	payInfo.status = 1
	payInfo.userid = cgmsg.userid
	payInfo.fourthpartyid = 0
	payInfo.deallasttime = "2020-01-01 00:00:00"
	
	UserInfoModel.AddPayInfoList(payInfo)
	
	gcmsg.result = 0
	return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()

end