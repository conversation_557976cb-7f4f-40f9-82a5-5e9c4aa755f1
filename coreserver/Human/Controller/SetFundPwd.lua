module("SetFundPwd", package.seeall)

--修改登录密码
function execute(packetID, operateID, buffer)

	local cgmsg = msg_human_pb.cgsetfundpwd()
	local gcmsg = msg_human_pb.gcsetfundpwd()

	cgmsg:ParseFromString(buffer)
	
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result = ReturnCode["human_user_not_exist"][1]
		gcmsg.msg = ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	if cgmsg.pwd == "" then
		gcmsg.result = ReturnCode["human_pwd_err_3"][1]
		gcmsg.msg = ReturnCode["human_pwd_err_3"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	if uInfo.isfundpassword == 1 then
		gcmsg.result = ReturnCode["human_fundpwd_err_1"][1]
		gcmsg.msg = ReturnCode["human_fundpwd_err_1"][2]
		return 0,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	--检查验证码
	if false == UtilsModel.CheckAuthcode(uInfo.phonenum, cgmsg.authcode, uInfo.channel) then
		gcmsg.result = ReturnCode["human_authcode_not_exist"][1]
		gcmsg.msg = ReturnCode["human_authcode_not_exist"][2]
		return 0,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	--修改密码
	local sqlCase = "update dy_user_info set fundpassword='"..md5(cgmsg.pwd).."' where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
	uInfo.isfundpassword = 1 
	UserInfoModel.SetUserInfo(uInfo)
	UserInfoModel.SendInfoList(uInfo, {"isfundpassword"})
	gcmsg.result = 0
	return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()

end