module("Paylist", package.seeall)

--我的收款方式
function execute(packetID, operateID, buffer)

	local cgmsg = msg_human_pb.cgpaylist()
	local gcmsg = msg_human_pb.gcpaylist()

	cgmsg:ParseFromString(buffer)
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result = ReturnCode["human_user_not_exist"][1]
		gcmsg.msg = ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	for _, paytype in ipairs(uInfo.paytypelist) do 
		local payList = UserInfoModel.GetPayInfoList(uInfo.userid, paytype)
		if payList ~= nil then
			for k,v in pairs(payList)do
				if k ~= "Placeholder" then
					local addPayInfo = gcmsg.paylist:add()
					addPayInfo:ParseFromString(v)
						
					--[[
					local money = '0'
					local sqlCase = "select sum(money) from dy_customer_order where user_pay_id="..addPayInfo.id
					mysqlItem:executeQuery(sqlCase)
					local sqlData = mysqlItem:fetch()
					if sqlData ~= nil then
						money = sqlData
					end
					addPayInfo.todaymoney = money
					]]
					addPayInfo.todaymoney = tostring(UserPayModel.GetDayLimit(addPayInfo.id))
				end
			end
		end 
	end
	gcmsg.result = 0
	return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()

end