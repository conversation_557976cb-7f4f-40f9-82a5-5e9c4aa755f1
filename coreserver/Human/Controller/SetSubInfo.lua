module("SetSubInfo", package.seeall)

--修改收款方式限额
function execute(packetID, operateID, buffer)
	
	local cgmsg = msg_human_pb.cgsetsubinfo()
	local gcmsg = msg_human_pb.gcsetsubinfo()

	cgmsg:ParseFromString(buffer)
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result = ReturnCode["human_user_not_exist"][1]
		gcmsg.msg = ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	local tInfo = UserInfoModel.GetUserInfo(cgmsg.targetuserid)
	if tInfo == nil then
		gcmsg.result = ReturnCode["human_user_not_exist"][1]
		gcmsg.msg = ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	if tInfo.agent ~= uInfo.userid then
		gcmsg.result = 1
		gcmsg.msg = "该用户不是您的直属下级"
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	tInfo.isacceptorder = cgmsg.isacceptorder
	tInfo.prohibitlogin = cgmsg.prohibitlogin
	
	local agentPayList = {}
	for k,v in ipairs(uInfo.paytypelist) do 
		agentPayList[tonumber(v)] = {}
		agentPayList[tonumber(v)]["payrate"] = UserInfoModel.getUserFeeRate(uInfo.userid, v, g_humanDefine.sell_comm_rate)
		agentPayList[tonumber(v)]["buyrate"] = UserInfoModel.getUserFeeRate(uInfo.userid, v, g_humanDefine.buy_comm_rate)
		agentPayList[tonumber(v)]["behalfbuyrate"] = UserInfoModel.getUserFeeRate(uInfo.userid, v, g_humanDefine.behalf_buy_comm_rate)
	end
	for k,v in ipairs(cgmsg.paytypelist) do 
		local payrate = tonumber(cgmsg.payratelist[k]) or 0
		if payrate < 0 or agentPayList[tonumber(v)]["payrate"] < payrate then
			gcmsg.result = 1
			gcmsg.msg = "费率设置错误"
			return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
		end
		
		local buyrate = tonumber(cgmsg.buyratelist[k]) or 0
		if buyrate < 0 or agentPayList[tonumber(v)]["buyrate"] < buyrate then
			gcmsg.result = 1
			gcmsg.msg = "费率设置错误"
			return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
		end
		local behalfbuyrate = tonumber(cgmsg.behalfbuyratelist[k]) or 0
		if behalfbuyrate < 0 or agentPayList[tonumber(v)]["behalfbuyrate"] < behalfbuyrate then
			gcmsg.result = 1
			gcmsg.msg = "费率设置错误"
			return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
		end
		
		agentPayList[tonumber(v)]["payrate"] = payrate
		agentPayList[tonumber(v)]["buyrate"] = buyrate
		agentPayList[tonumber(v)]["behalfbuyrate"] = behalfbuyrate
		gcmsg.paytypelist:append(tostring(v))
		gcmsg.payratelist:append(tostring(payrate))
		gcmsg.buyratelist:append(tostring(buyrate))
		gcmsg.behalfbuyratelist:append(tostring(behalfbuyrate))
	end
	
	for k,v in pairs(agentPayList) do 
		UserInfoModel.SetUserFeeRate(tInfo.userid, k, g_humanDefine.sell_comm_rate, v.payrate)
		UserInfoModel.SetUserFeeRate(tInfo.userid, k, g_humanDefine.buy_comm_rate, v.buyrate)
		UserInfoModel.SetUserFeeRate(tInfo.userid, k, g_humanDefine.behalf_buy_comm_rate, v.behalfbuyrate)
		local sqlCase = "update dy_user_rate set buy_comm_rate="..v.buyrate..", sell_comm_rate="..v.payrate..",behalf_buy_comm_rate="..v.behalfbuyrate.." where userid="..tInfo.userid.." and paytype="..k
		mysqlItem:execute(sqlCase)
		
	end
	
	UserInfoModel.SetUserInfo(tInfo)
	local sqlCase = "update dy_user_info set is_accept_order="..tInfo.isacceptorder..", prohibit_login="..tInfo.prohibitlogin.." where userid="..tInfo.userid
	mysqlItem:execute(sqlCase)
	
	gcmsg.targetuserid = cgmsg.userid
	gcmsg.isacceptorder = cgmsg.isacceptorder
	gcmsg.prohibitlogin = cgmsg.prohibitlogin
	
	gcmsg.userid = cgmsg.userid
	gcmsg.result = 0
	return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()

end