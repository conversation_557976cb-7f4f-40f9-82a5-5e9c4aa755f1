module("DeletePay", package.seeall)

--删除收款方式
function execute(packetID, operateID, buffer)

	local cgmsg = msg_human_pb.cgdeletepay()
	local gcmsg = msg_human_pb.gcdeletepay()

	cgmsg:ParseFromString(buffer)
	
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result = ReturnCode["human_user_not_exist"][1]
		gcmsg.msg = ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	--查看是否已经绑定改信息
	local payInfo = UserInfoModel.GetPayInfo(cgmsg.userid, cgmsg.paytype, cgmsg.id)
	if payInfo == nil then
		gcmsg.result = ReturnCode["human_payinfo_not_exist"][1]
		gcmsg.msg = ReturnCode["human_payinfo_not_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	--删除缓存
	UserInfoModel.DelPayInfo(cgmsg.userid, payInfo.paytype, payInfo.id)
	local sqlCase = "update dy_user_pay set status=2 where id="..payInfo.id
	mysqlItem:execute(sqlCase)
	
	gcmsg.result = 0
	return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()

end