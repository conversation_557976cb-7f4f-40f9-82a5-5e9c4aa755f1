module("ReConnect", package.seeall)

--重连
function execute(packetID, operateID, buffer)

	local cgmsg = msg_human_pb.cgreconnect()
	local gcmsg = msg_human_pb.gcreconnect()
	cgmsg:ParseFromString(buffer)
	

	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid, gcmsg.uinfo)
	if uInfo == nil then
		gcmsg.result = ReturnCode["human_user_not_exist"][1]
		gcmsg.msg = ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end
	
	OnlineModel.UserLogin(gcmsg.uinfo.userid)
	
	gcmsg.systime = TimeUtils.GetTime()
	gcmsg.result = 0
	return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
end