module("Login", package.seeall)

--登陆注册
function execute(packetID, operateID, buffer)

	local cgmsg = msg_human_pb.cglogin()
	local gcmsg = msg_human_pb.gclogin()

	cgmsg:ParseFromString(buffer)
	local userid = 0
    if cgmsg.bindtype == g_humanDefine.bindType_phone then
		--手机登陆
			
		--根据手机号码和渠道号查找用户ID
		local pInfo = UserInfoModel.GetPhoneByUser(cgmsg.phonenum,cgmsg.channel)
		if pInfo == nil then
			
			pInfo = UserInfoModel.GetAccountByUser(cgmsg.phonenum,cgmsg.channel)
			if pInfo == nil then
				pInfo = UserInfoModel.GetEmailByUser(cgmsg.phonenum,cgmsg.channel)
				if pInfo == nil then
					gcmsg.result = ReturnCode["human_phone_not_exist"][1]
					gcmsg.msg = ReturnCode["human_phone_not_exist"][2]
					return 0, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
				end
			end
		end
		if pInfo.password ~= md5(cgmsg.password) then
			gcmsg.result = ReturnCode["human_pass_err"][1]
			gcmsg.msg = ReturnCode["human_pass_err"][2]
			return 0, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
		end
		
		userid = pInfo.userid
	else
		
		--其他登陆方式
		gcmsg.result = ReturnCode["human_not_support"][1]
		gcmsg.resultmsg = ReturnCode["human_not_support"][2]
		return 0, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()
	end	

	if UserInfoModel.GetUserInfo(userid,gcmsg.uinfo) == nil then
		--玩家不存在
		gcmsg.result = ReturnCode["human_user_not_exist"][1]
		gcmsg.msg = ReturnCode["human_user_not_exist"][2]
		return 0,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end 
	
	--检查有没有被封号
	if gcmsg.uinfo.bantime == "-1" then
		gcmsg.result = 1
		gcmsg.msg = "账号已被永久封禁"
		return 0,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	local banTime = TimeUtils.GetTime(gcmsg.uinfo.bantime)
	local now = TimeUtils.GetTime()
	if banTime > now then
		gcmsg.result = 1
		gcmsg.msg = "账号已被封禁，解封时间为："..gcmsg.uinfo.bantime
		return 0,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	--检查该渠道是否有被关闭
	local sqlCase = "select is_lock from dy_channel_info where channel='"..gcmsg.uinfo.channel.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlDate = mysqlItem:fetch()
	if sqlDate ~= nil and tonumber(sqlDate) == 1 then
		gcmsg.result = 1
		gcmsg.msg = "渠道已被关闭"
		return 0,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	--检查玩家是否被禁止登陆
	if gcmsg.uinfo.prohibitlogin == 1 then
		gcmsg.result = 1
		gcmsg.msg = "该账号已被禁止登陆"
		return 0,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	
	--检查玩家是否在线，在就踢掉原来的
	if nil ~= OnlineModel.CheckOnline(gcmsg.uinfo.userid) then
		local gckit = msg_human_pb.gckituser()
		gckit.result = 0
		gckit.kittype = 1
		gckit.msg = "您的账号已经在别处登录！"
		SendMessage(gcmsg.uinfo.userid, PacketCode[1006].client, gckit:ByteSize(), gckit:SerializeToString())
		UserInfoModel.addKitUserList(gcmsg.uinfo.userid)
	end
	
	--看看有没有地址
	local addrList = {}
	local sqlCase = "select coin_addr,addr_type,addr_name from dy_user_address_pre where user_id="..gcmsg.uinfo.userid.." and status=0 and use_type=0"
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlDate = mysqlItem:fetch({})
		if sqlDate == nil then
			break
		end
		
		local tmp = {}
		tmp["address"] = sqlDate[1]
		tmp["addrtype"] = tonumber(sqlDate[2])
		tmp["addrname"] = sqlDate[3]
		table.insert(addrList, tmp)
	end
	
	for _, addrType in ipairs(g_marketDefine.addr_type_list) do 
		local isOk = false 
		for k,v in ipairs(addrList)do 
			if v.addrtype == addrType then
				isOk = true
			end
		end
		
		if isOk == false then
			if addrType == 101 then
				local address = AddressPreModel.GetNewAddress(gcmsg.uinfo.channel) or "" 
				if address ~= "" then
					AddressPreModel.UpdatePreAddress(address, gcmsg.uinfo.channel, gcmsg.uinfo.userid)
					local tmp = {}
					tmp["address"] = address
					tmp["addrtype"] = 101
					tmp["addrname"] = "ERC-20"
					table.insert(addrList, tmp)
				end
			elseif addrType == 102 then
				local address = AddressPreModel.GetNewTRCAddress(gcmsg.uinfo.channel) or "" 
				if address ~= "" then
					AddressPreModel.UpdatePreTRCAddress(address, gcmsg.uinfo.channel, gcmsg.uinfo.userid)
					local tmp = {}
					tmp["address"] = address
					tmp["addrtype"] = 102
					tmp["addrname"] = "TRC-20"
					table.insert(addrList, tmp)
				end
			end
		end
	end
	
	--登陆
	gcmsg.uinfo.penulttime = gcmsg.uinfo.lasttime
	gcmsg.uinfo.lasttime = TimeUtils.GetTime()
	UserInfoModel.SetUserInfo(gcmsg.uinfo)
	local sqlCase = "update dy_user_info set penulttime="..gcmsg.uinfo.penulttime..",lasttime="..gcmsg.uinfo.lasttime.." where userid="..gcmsg.uinfo.userid
	mysqlItem:execute(sqlCase)
	OnlineModel.UserLogin(gcmsg.uinfo.userid)
	for k,v in ipairs(addrList) do
		if v.addrtype == 101 then
			local addrInfo = gcmsg.addrlist:add()
			addrInfo.address = v.address
			addrInfo.addrtype = v.addrtype
			addrInfo.addrname = v.addrname
		end
	end
	gcmsg.uinfo.ethaddress = ""
	
	local sqlCase = "select coin_addr,addr_type,addr_name from dy_trusteeship_address_pre where status=0 and type=1 and channel='"..gcmsg.uinfo.channel.."'"
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlDate = mysqlItem:fetch({})
		if sqlDate == nil then
			break
		end
		local addrInfo = gcmsg.trusteeshipaddresslist:add()
		addrInfo.address = ""
		addrInfo.addrtype = tonumber(sqlDate[2])
		addrInfo.addrname = sqlDate[3]
	end

	
	gcmsg.result = 0
	gcmsg.systime = TimeUtils.GetTime()
	return gcmsg.uinfo.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()

end