module("Register", package.seeall)

--手机号注册
function execute(packetID, operateID, buffer)

	local cgmsg = msg_human_pb.cgregister()
	local gcmsg = msg_human_pb.gcregister()

	cgmsg:ParseFromString(buffer)
	if cgmsg.nickname == "" 
	or cgmsg.phonenum == "" 
	or cgmsg.password == "" 
	or cgmsg.authcode == "" 
	or cgmsg.channel == "" 
	or cgmsg.invitecode == "" 
	then
		gcmsg.result = ReturnCode["human_parameter_error"][1]
		gcmsg.msg = ReturnCode["human_parameter_error"][2]
		return 0,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	
	local ret, msg = HumanService.RegisterCurrencyAgent(cgmsg.phonenum,cgmsg.password,cgmsg.authcode,cgmsg.channel,cgmsg.nickname,cgmsg.invitecode, gcmsg.uinfo)
	if ret ~= 0 then
		gcmsg.result = ret
		gcmsg.msg = msg
		return 0,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	gcmsg.result = 0	
	return userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()

end