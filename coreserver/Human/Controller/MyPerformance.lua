module("MyPerformance", package.seeall)

--我的业绩
function execute(packetID, operateID, buffer)

	local cgmsg = msg_human_pb.cgmyperformance()
	local gcmsg = msg_human_pb.gcmyperformance()

	cgmsg:ParseFromString(buffer)
	
	local uInfo = UserInfoModel.GetUserInfo(cgmsg.userid)
	if uInfo == nil then
		gcmsg.result = ReturnCode["human_user_not_exist"][1]
		gcmsg.msg = ReturnCode["human_user_not_exist"][2]
		return cgmsg.userid,0,gcmsg:ByteSize(),gcmsg:SerializeToString()
	end
	
	local t = TimeUtils.GetTime()
	local today = TimeUtils.GetDayString()
	local yestoday = TimeUtils.GetDayString(t - 86400)
	local sqlCase = "select dateid, reward_amount from log_user_daily where	userid="..uInfo.userid.." and (dateid='"..today.."' or dateid='"..yestoday.."')"
	mysqlItem:executeQuery(sqlCase)
	while true do
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		if today == sqlData[1] then
			gcmsg.todayincome = sqlData[2]
		elseif yestoday == sqlData[1] then
			gcmsg.yesterdayincome = sqlData[2]
		end
	end
	
	
	local day = cgmsg.timetype == 0 and today or yestoday 
	
	local sqlCase = "select tem_deal_count, fee_count from log_paytype_daily where userid="..uInfo.userid.." and paytype="..cgmsg.paytype.." and dateid='"..day.."'"
	mysqlItem:executeQuery(sqlCase)
	local mysqlData = mysqlItem:fetch({})
	if mysqlData ~= nil then
		gcmsg.performancecount = mysqlData[1]
		gcmsg.incomecount = mysqlData[2]
	end
	
	for k,v in ipairs(uInfo.paytypelist) do
		gcmsg.paytypelist:append(tostring(v))
		local payrate = UserInfoModel.getUserFeeRate(uInfo.userid, v, g_humanDefine.sell_comm_rate)
		gcmsg.payratelist:append(tostring(payrate))
		local buyrate = UserInfoModel.getUserFeeRate(uInfo.userid, v, g_humanDefine.buy_comm_rate)
		gcmsg.buyratelist:append(tostring(buyrate))
		local behalfbuyrate = UserInfoModel.getUserFeeRate(uInfo.userid, v, g_humanDefine.behalf_buy_comm_rate)
		gcmsg.behalfbuyratelist:append(tostring(behalfbuyrate))
	end
	
	local startPos = (cgmsg.pagenum - 1) * cgmsg.pagesize
	local sqlCase = "select userid,nickname,is_accept_order,prohibit_login from dy_user_info where agent="..uInfo.userid.." limit "..startPos..", "..cgmsg.pagesize
	mysqlItem:executeQuery(sqlCase)
	
	while true do 	
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		local addSubInfo = gcmsg.infolist:add()
		addSubInfo.userid = tonumber(sqlData[1])
		addSubInfo.nickname = sqlData[2]
		addSubInfo.isacceptorder = tonumber(sqlData[3])
		addSubInfo.prohibitlogin = tonumber(sqlData[4])
	end
	for k,v in ipairs(gcmsg.infolist) do
		for _,payType in ipairs(gcmsg.paytypelist) do 
			v.paytypelist:append(tostring(payType))
			local payrate = UserInfoModel.getUserFeeRate(v.userid, payType, g_humanDefine.sell_comm_rate)
			v.payratelist:append(tostring(payrate))
			local buyrate = UserInfoModel.getUserFeeRate(v.userid, payType, g_humanDefine.buy_comm_rate)
			v.buyratelist:append(tostring(buyrate))
			local behalfbuyrate = UserInfoModel.getUserFeeRate(v.userid, payType, g_humanDefine.behalf_buy_comm_rate)
			v.behalfbuyratelist:append(tostring(behalfbuyrate))
		end
		
		local sqlCase = "select tem_deal_count, agen_fee_count,buy_count from log_paytype_daily where userid="..v.userid.." and paytype="..cgmsg.paytype.." and dateid='"..day.."'"
		mysqlItem:executeQuery(sqlCase)
		local mysqlData = mysqlItem:fetch({})
		if mysqlData ~= nil then
			v.teamperformance = mysqlData[1]
			v.myperformance = mysqlData[2]
			v.buycount = mysqlData[3]
		end
	end
	
	gcmsg.paytype = cgmsg.paytype
	gcmsg.pagenum = cgmsg.pagenum
	gcmsg.pagesize = cgmsg.pagesize
	gcmsg.userid = cgmsg.userid
	gcmsg.result = 0
	return cgmsg.userid, 0, gcmsg:ByteSize(), gcmsg:SerializeToString()

end