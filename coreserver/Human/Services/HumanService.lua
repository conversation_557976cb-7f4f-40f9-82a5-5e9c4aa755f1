	

HumanService = {}

function HumanService.Init()
	
	--检查系统用户是否存在
	
	local sqlCase = "select userid from dy_user_info where user_type=100"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		local cid = UserInfoModel.GetRandomCID()
		local sqlCase = "insert into dy_user_info(cid,account,password,eth_address,nickname,user_type,username,payidlist) values('','','','','系统用户',100,'系统用户','[]')"
		mysqlItem:execute(sqlCase)
	end
	--[[
	LogDispatch.del("logteambalance")
	
	--同步用户团队余额
	local sqlCase = "select userid from dy_user_info"
	mysqlItem:executeQuery(sqlCase)
	local teamList = {}
	while true do 
		local sqlDase = mysqlItem:fetch()
		if sqlDase == nil then
			break
		end
		table.insert(teamList, tonumber(sqlDase))
	end
	
	for _, userid in ipairs(teamList) do 
		local sqlCase = "select userid from ag_relation where bind_userid="..userid
		mysqlItem:executeQuery(sqlCase)
		local userList = {userid}
		while true do 
			local sqlDase = mysqlItem:fetch()
			if sqlDase == nil then
				break
			end
			table.insert(userList, tonumber(sqlDase))
		end
		local amount = 0
		local money = 0
		for k, v in ipairs(userList) do 
			local pInfo = UserInfoModel.GetUserInfo(v)
			if pInfo ~= nil then
				amount = amount + tonumber(pInfo.ercusdtamount)
				money = money + tonumber(pInfo.ercfcamount)
			end
		end
		
		local sqlCase = "update log_user set team_balance_amount="..amount..", team_balance_money="..money.." where userid="..userid
		mysqlItem:execute(sqlCase)
	end
	]]
end


function HumanService.ServerLoop()
    
	
	--更新玩家货币
	local ercusdtamountList = redisItem:hkeys(UserInfoModel.userinfo_ercusdtamountt_update, UserInfoModel.redis_index)
	redisItem:del(UserInfoModel.userinfo_ercusdtamountt_update, UserInfoModel.redis_index)
	
	if #ercusdtamountList ~= 0  then
		local getAll =  redisItem:hmget(UserInfoModel.userinfo_ercusdtamount, ercusdtamountList, UserInfoModel.redis_index)
		for k_all, v_all in pairs(getAll) do
			local sqlCase = "update dy_user_info set erc_usdt_amount="..v_all.." where userid="..ercusdtamountList[k_all]
			mysqlItem:execute(sqlCase)
		end
	end

	local ercusdtlockamountList = redisItem:hkeys(UserInfoModel.userinfo_ercusdtlockamount_update, UserInfoModel.redis_index)
	redisItem:del(UserInfoModel.userinfo_ercusdtlockamount_update, UserInfoModel.redis_index)
	
	if #ercusdtlockamountList ~= 0  then
		local getAll =  redisItem:hmget(UserInfoModel.userinfo_ercusdtlockamount, ercusdtlockamountList, UserInfoModel.redis_index)
		for k_all, v_all in pairs(getAll) do
			local sqlCase = "update dy_user_info set erc_usdt_lock_amount="..v_all.." where userid="..ercusdtlockamountList[k_all]
			mysqlItem:execute(sqlCase)
		end
	end
	
	local ercfcamountList = redisItem:hkeys(UserInfoModel.userinfo_ercfcamount_update, UserInfoModel.redis_index)
	redisItem:del(UserInfoModel.userinfo_ercfcamount_update, UserInfoModel.redis_index)
	
	if #ercfcamountList ~= 0  then
		local getAll =  redisItem:hmget(UserInfoModel.userinfo_ercfcamount, ercfcamountList, UserInfoModel.redis_index)
		for k_all, v_all in pairs(getAll) do
			local sqlCase = "update dy_user_info set erc_fc_amount="..v_all.." where userid="..ercfcamountList[k_all]
			mysqlItem:execute(sqlCase)
		end
	end

	local ercfclockamountList = redisItem:hkeys(UserInfoModel.userinfo_ercfclockamount_update, UserInfoModel.redis_index)
	redisItem:del(UserInfoModel.userinfo_ercfclockamount_update, UserInfoModel.redis_index)
	
	if #ercfclockamountList ~= 0  then
		local getAll =  redisItem:hmget(UserInfoModel.userinfo_ercfclockamount, ercfclockamountList, UserInfoModel.redis_index)
		for k_all, v_all in pairs(getAll) do
			local sqlCase = "update dy_user_info set erc_fc_lock_amount="..v_all.." where userid="..ercfclockamountList[k_all]
			mysqlItem:execute(sqlCase)
		end
	end
	
	local coinpayusdtamountList = redisItem:hkeys(UserInfoModel.userinfo_coinpayusdtamountt_update, UserInfoModel.redis_index)
	redisItem:del(UserInfoModel.userinfo_coinpayusdtamountt_update, UserInfoModel.redis_index)
	
	if #coinpayusdtamountList ~= 0  then
		local getAll =  redisItem:hmget(UserInfoModel.userinfo_coinpayusdtamount, coinpayusdtamountList, UserInfoModel.redis_index)
		for k_all, v_all in pairs(getAll) do
			local sqlCase = "update dy_user_info set coin_pay_usdt_amount="..v_all.." where userid="..coinpayusdtamountList[k_all]
			mysqlItem:execute(sqlCase)
		end
	end

	local coinpayusdtlockamountList = redisItem:hkeys(UserInfoModel.userinfo_coinpayusdtlockamount_update, UserInfoModel.redis_index)
	redisItem:del(UserInfoModel.userinfo_coinpayusdtlockamount_update, UserInfoModel.redis_index)
	
	if #coinpayusdtlockamountList ~= 0  then
		local getAll =  redisItem:hmget(UserInfoModel.userinfo_coinpayusdtlockamount, coinpayusdtlockamountList, UserInfoModel.redis_index)
		for k_all, v_all in pairs(getAll) do
			local sqlCase = "update dy_user_info set coin_pay_usdt_lock_amount="..v_all.." where userid="..coinpayusdtlockamountList[k_all]
			mysqlItem:execute(sqlCase)
		end
	end
	
end

--闭上注册
function HumanService.RegisterCurrencyAgent(phonenum,password,authcode,channel,nickname,invitecode, uInfo)
    
	
	--检查验证码
	if false ==  UtilsModel.CheckAuthcode(phonenum, authcode,channel) then
		return ReturnCode["human_authcode_not_exist"][1],ReturnCode["human_authcode_not_exist"][2]
	end
			
	local sqlCase = "select * from dy_user_info where account='"..phonenum.."' or phonenum='"..phonenum.."' or email='"..phonenum.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		return ReturnCode["human_phone_exist"][1],ReturnCode["human_phone_exist"][2]
	end
	
	--检查昵称是否存在
	local userid = UserInfoModel.checkNickName(nickname)
	if userid ~= 0 then
		return 1,"昵称已经存在"
	end
	
	--检查邀请码
	local sqlCase = "select userid from dy_user_info where invite_code='"..invitecode.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlDate = mysqlItem:fetch()
	if sqlDate == nil then
		return ReturnCode["human_invitecode_error"][1], ReturnCode["human_invitecode_error"][2]
	end
	
	local aInfo = UserInfoModel.GetUserInfo(sqlDate)
	if aInfo == nil or (tonumber(aInfo.usertype) ~= g_humanDefine.usertype_currency_agent and tonumber(aInfo.usertype) ~= g_humanDefine.usertype_currency 
				and tonumber(aInfo.usertype) ~= g_humanDefine.usertype_currency1 )  then
		return ReturnCode["human_invitecode_error"][1],ReturnCode["human_invitecode_error"][2]
	end
	if aInfo.channel ~= channel then
		return ReturnCode["human_invitecode_error"][1],ReturnCode["human_invitecode_error"][2]
	end
	
	local usertype = aInfo.usertype == 202 and 202 or 201
	local regInfo = {}
	regInfo["nickname"] = nickname
	regInfo["phonenum"] = phonenum
	regInfo["email"] = ""
	regInfo["password"] = password
	regInfo["invitecode"] = invitecode
	regInfo["channel"] = aInfo.channel
	regInfo["bindtype"] = g_humanDefine.bindType_phone
	regInfo["agent"] = aInfo.userid
	regInfo["usertype"] = usertype
	regInfo["account"] = regInfo["phonenum"]
	regInfo["paylist"] = {}
	regInfo["commtype"] = aInfo.commtype
	local sqlCase = "select paytype,buy_fee_rate,sell_fee_rate,buy_comm_rate,sell_comm_rate,is_used from dy_user_rate where userid="..aInfo.userid
	mysqlItem:executeQuery(sqlCase)
	while true do
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			break
		end
		local tmp = {}
		tmp["payType"] = tonumber(sqlData[1])
		tmp["buy_free_rate"] = 0
		tmp["sell_free_rate"] = 0
		tmp["buy_common_rate"] = 0
		tmp["sell_common_rate"] = 0
		tmp["behalf_sell_fee_rate"] = 0
		tmp["behalf_buy_comm_rate"] = 0
		tmp["state"] = tonumber(sqlData[6])
		table.insert(regInfo["paylist"], tmp)
	end
	regInfo["contactInfo"] = ""
	regInfo["userName"] = ""
	regInfo["isInvited"] = 1
	regInfo["dealCoinType"] = 0
	regInfo["teamName"] = aInfo.teamname or ""
	regInfo["platformID"] = aInfo.platformid or 0
	regInfo["isTeamAcceptOrder"] = aInfo.isteamacceptorder or 0
	local userid = UserInfoModel.CreateUser(regInfo)
	
	if uInfo == nil then
		uInfo = st_human_pb.userinfo()
	end
	
	if nil == UserInfoModel.GetUserInfo(userid,uInfo) then
		return ReturnCode["human_create_user_err"][1],ReturnCode["human_create_user_err"][2]
	end
	
	UserInfoModel.SetUserInfo(uInfo)
	
	return 0, ""
end


--通渠道到下的商户迁移迁到别的代理商
function HumanService.MerchantMigrateAgent(migrateUser, toUserid)
	
	
	local uInfo = UserInfoModel.GetUserInfo(toUserid) 
	if uInfo == nil then
		return 1, "目标用户不存在"
	end
	
	local mInfo = UserInfoModel.GetUserInfo(migrateUser) 
	if mInfo == nil then
		return 1, "迁移用户不存在"
	end
	
	--处理迁移用户的上级ID
	local old_agent = mInfo.agent
	mInfo.agent = uInfo.userid
	UserInfoModel.SetUserInfo(mInfo)
	local sqlCase = "update dy_user_info set agent="..mInfo.agent.." where userid="..mInfo.userid
	mysqlItem:execute(sqlCase)
	
	--处理代理关系
	local function relationshipProcessing(userid, agent, level)
		
		local aInfo = UserInfoModel.GetUserInfo(agent)
		if aInfo == nil then
			return 
		end
			
		local sqlCase = "insert into ag_relation(userid, bind_userid, level) values("..userid..","..aInfo.userid..","..level..")"
		mysqlItem:execute(sqlCase)
		
		relationshipProcessing(userid, aInfo.agent, level+1)
	end
	
	local sqlCase = "delete from ag_relation where userid="..mInfo.userid
	mysqlItem:execute(sqlCase)
			
	relationshipProcessing(mInfo.userid, mInfo.agent, 1)
	
	--旧的上级人数减1
	local sqlCase = "update dy_user_info set team_user_num=team_user_num-1, dir_user_num=dir_user_num-1 where userid="..old_agent
	mysqlItem:execute(sqlCase)
	--新得上级人数加1
	local sqlCase = "update dy_user_info set team_user_num=team_user_num+1, dir_user_num=dir_user_num+1 where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)

end

















