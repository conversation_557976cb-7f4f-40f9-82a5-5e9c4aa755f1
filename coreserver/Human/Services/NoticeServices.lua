NoticeServices = {}


function NoticeServices.NoticeHangSellOut(userList)
	
	--通知挂卖单的人，有卖单，需要付款
	--暂时只刷新大厅的带处理列表
	for k,v in ipairs(userList) do 
		NoticeServices.UpdateWorkingList(v)
	end
	
end


function NoticeServices.NoticeOrderPass(userList)
	--已经付款。通知买方放币
	--暂时只刷新大厅的带处理列表
	for k,v in ipairs(userList) do 
		NoticeServices.UpdateWorkingList(v)
	end
end

function NoticeServices.DealCancel(userList)
	--通知卖方，交易被取消
	--通知用户，币已经被解冻
	--暂时只刷新大厅的带处理列表
	
	for k,v in ipairs(userList) do 
		NoticeServices.UpdateWorkingList(v)
	end
end

function NoticeServices.OrderCarryOut(userList)
	--暂时只刷新大厅的带处理列表
	for k,v in ipairs(userList) do 
		NoticeServices.UpdateWorkingList(v)
	end	
end

function NoticeServices.updateOrderInfo(cInfo)
	
	local userList = {cInfo.customeruserid,cInfo.vendoruserid}
	
	local gcUpdate = msg_order2_pb.gcdetail()
	for k,v in ipairs(userList) do 
		gcUpdate.coinfo:ParseFromString(cInfo:SerializeToString())
		--给前端转化，前端只认对于这个这个用户说是买还是卖
		gcUpdate.coinfo.type = CoinInfoService.orderTypeConversion(gcUpdate.coinfo.type, gcUpdate.coinfo.customeruserid,
										gcUpdate.coinfo.vendoruserid, v)							
		gcUpdate.result = 0
		SendMessage(v, PacketCode[2030].client, gcUpdate:ByteSize(), gcUpdate:SerializeToString())
	end
end



function NoticeServices.UpdateWorkingList(userid)
	
	local UpdateWorkingList = msg_order2_pb.gcworkinglist()
	
	local sqlCase = "select * from dy_customer_order where (customer_user_id="..userid.." or vendor_user_id="..userid
					..") and (status="..g_marketDefine.deal_status_wait.." or status="..g_marketDefine.deal_status_pay.." or status="..g_marketDefine.deal_status_appeal
					.. ") order by id desc"
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlDate = mysqlItem:fetch({})
		if sqlDate == nil then
			break
		end
		local addgInfo = UpdateWorkingList.infolist:add()
		addgInfo.dealid = tonumber(sqlDate[1])
		addgInfo.type = tonumber(sqlDate[2])
		addgInfo.vendororderid = tonumber(sqlDate[3])
		addgInfo.customeruserid = tonumber(sqlDate[4])
		addgInfo.vendoruserid = tonumber(sqlDate[5])
		addgInfo.price = sqlDate[6]
		addgInfo.amount = sqlDate[7]
		addgInfo.money = sqlDate[8]
		addgInfo.status = tonumber(sqlDate[9])
		addgInfo.merchantorderid = sqlDate[10]
		addgInfo.feerate = sqlDate[11]
		addgInfo.fee = sqlDate[12]
		
		if sqlDate[13] ~= nil and sqlDate[13] ~= "" then
			local proofUrlList = string.split(sqlDate[13], ",")
			for k,v in ipairs(proofUrlList) do
				addgInfo.proofurl:append(v)
			end
		end
		addgInfo.paytypelist = sqlDate[14]
		addgInfo.payidlist = sqlDate[15]
		addgInfo.createtime = sqlDate[16]
		addgInfo.createtime = sqlDate[16]
		addgInfo.paytime = sqlDate[18]
		addgInfo.passtime = sqlDate[19]
		addgInfo.channel = sqlDate[20]
		addgInfo.publicprice = sqlDate[21]
		addgInfo.fromtype = tonumber(sqlDate[22])
		addgInfo.updatetime = sqlDate[17]
		addgInfo.getamount = sqlDate[42]
		addgInfo.dealtype = tonumber(sqlDate[32])
		addgInfo.income = sqlDate[44]
		addgInfo.withdrawtype = tonumber(sqlDate[47])
		
		--给前端转化，前端只认对于这个这个用户说是买还是卖
		addgInfo.type = CoinInfoService.orderTypeConversion(addgInfo.type, addgInfo.customeruserid, addgInfo.vendoruserid, userid)
	end
	
	for k,v in ipairs(UpdateWorkingList.infolist) do 
		local uInfo = UserInfoModel.GetUserInfo(v.customeruserid)
		if uInfo ~= nil then
			v.customerusernickname = uInfo.nickname
		end
		local uInfo = UserInfoModel.GetUserInfo(v.vendoruserid)
		if uInfo ~= nil then
			v.vendorusernickname = uInfo.nickname
		end
	end
	
	UpdateWorkingList.result = 0
	SendMessage(userid, PacketCode[2042].client, UpdateWorkingList:ByteSize(), UpdateWorkingList:SerializeToString())
end


