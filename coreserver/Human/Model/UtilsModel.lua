UtilsModel = {}

function UtilsModel.CheckAuthcode(phonenum, authcode, channel)
	
	if channel == "UTO" then
		return true
	end
	
	--检查验证码是否存在
	local sqlCase = "select id, timemark, state from dy_authcode where account='"..phonenum.."' and code='"..authcode.."' and channel = '"..channel
					.."' and type=0 and state = 0 order by id desc"
    mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		return false
	end
			
	--检查验证码是否使用过
	local state = tonumber(sqlData[3])
    if state == 1 then
		return false
    end

	--检查验证码是否过期
	local timemark = tonumber(sqlData[2])
	local newTime = TimeUtils.GetTime()
	if newTime - timemark > 600 then
		return false
	end			
	
	local id = tonumber(sqlData[1])
			
	--修改验证码状态
	--[[
	local sqlCase = "update  dy_authcode set state = 1 where id = "..id
    mysqlItem:execute(sqlCase)		
	]]

	return true
end
