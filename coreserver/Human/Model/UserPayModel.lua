UserPayModel = {}

UserPayModel.userpay_day_dael_count = "userpay_day_dael_count"									--卡的单日交易量

function UserPayModel.GetDayLimit(payid)
	
	if payid == 0 or payid == "" then
		return 0
	end
	
	local nowDay = TimeUtils.GetDayString()
	local tmpData = redisItem:hget(UserPayModel.userpay_day_dael_count, payid, UserPayModel.redis_index)
	local getNum = 0
	if tmpData ~= nil then
		tmpData = luajson.decode(tmpData)
		if tmpData["date"] == nowDay then
			getNum = tmpData["num"]
		end
	end
	return getNum == nil and 0 or getNum
	
end



--在挂买单的时候，需要先检查收款方式是否存在,改函数需要实现
--返回true/false  
function UserPayModel.CheckUserPayExist(uInfo)
	if uInfo == nil then
		return false
	end
	
	local isEx = false
	
	for _, paytype in ipairs(uInfo.paytypelist) do 
		local payList = UserInfoModel.GetPayInfoList(uInfo.userid, paytype)
		if payList ~= nil then
			for k,v in pairs(payList)do
				if k ~= "Placeholder" then
					local PayInfo = st_human_pb.payinfo()
					PayInfo:ParseFromString(v)
					if PayInfo.status == 1 then
						return true
					end
				end
			end
		end 
	end
	
	return false
end


--在钱包以及钱包的交易中，只支持银行卡和支付宝，如果两个都有，那边两个都返回，入两个都没有，那么两个都不返回
function UserPayModel.GetBankAndAliPayInfo(uInfo, payType,moneyCount)
	
	local payTypeList = {}
	local payList = UserInfoModel.GetPayInfoList(uInfo.userid, payType)
	if payList ~= nil then
		local isEx = false
		for k,v in pairs(payList)do
			if k ~= "Placeholder" then
				local PayInfo = st_human_pb.payinfo()
				PayInfo:ParseFromString(v)
				if PayInfo.status == 1 and moneyCount <= (tonumber(PayInfo.singlelimit) or 0)  and (moneyCount+ UserInfoModel.GetDayLimit(PayInfo.id)) <= (tonumber(PayInfo.daylimit) or 0) then
					local tmp = {}
					tmp["id"] = PayInfo.id
					tmp["paytype"] = PayInfo.paytype
					tmp["account"] = PayInfo.account
					tmp["payee"] = PayInfo.payee
					tmp["qrcode"] = PayInfo.qrcode
					tmp["bankname"] = PayInfo.bankname
					tmp["bankaddr"] = PayInfo.bankaddr
					tmp["singlelimit"] = PayInfo.singlelimit
					tmp["daylimit"] = PayInfo.daylimit
					tmp["fourthpartyid"] = PayInfo.fourthpartyid
					tmp["deallasttime"] = PayInfo.deallasttime
					table.insert(payTypeList, tmp)
				end
			end
		end
	end 

	return payTypeList
	--返回收款列表
end

function UserPayModel.GetAvailablePayType(uInfo, payType,moneyCount,orderList)
	
	local payTypeList = {payType}
	if payType == 8111 then
		table.insert(payTypeList, 8112)
	end
	for _,v in ipairs(payTypeList) do 
		local payList = UserInfoModel.GetPayInfoList(uInfo.userid, v)
		if payList ~= nil then
			local isEx = false
			for _,info in pairs(payList)do
				if _ ~= "Placeholder" then
					local PayInfo = st_human_pb.payinfo()
					PayInfo:ParseFromString(info)
					if orderList[PayInfo.account] ~= uInfo.userid and PayInfo.status == 1 and moneyCount <= (tonumber(PayInfo.singlelimit) or 0) and (moneyCount+ UserInfoModel.GetDayLimit(PayInfo.id)) <= (tonumber(PayInfo.daylimit) or 0) then
						return v
					end
				end
			end
		end 
	end

	return 0
	--返回收款列表
end
