
UserInfoModel = {}

UserInfoModel.redis_index = "redis_userinfo"


UserInfoModel.userinfo_list = "userinfo_"  												--保存用户信息的ID，以userid为key，存管的probuf结构字符串
UserInfoModel.userinfo_account = "userinfo_account_list"  								--保存用户信息的，以account为key，存管json的字符串 userID 跟 password
UserInfoModel.userinfo_phonenum = "userinfo_phonenum_list"  							--保存用户信息的，以phonenum为key，存管json的字符串 userID 跟 password    
UserInfoModel.userinfo_email = "userinfo_userinfo_email_list"  							--保存用户信息的，以phonenum为key，存管json的字符串 userID 跟 password    
UserInfoModel.userinfo_ethaddr = "userinfo_ethaddr"  									--保存用户信息的，以ethaddr为key，存管userID 
UserInfoModel.payinfo_list = "payinfo_list_"   											--用户绑定的支付信息     
UserInfoModel.userinfo_ercusdtamount = "userinfo_ercusdtamount"   						--用户erc20的usdt的数量
UserInfoModel.userinfo_ercusdtamountt_update = "userinfo_ercusdtamount_update"   		--标志用户需要更新数据库的erc20的usdt的数量
UserInfoModel.userinfo_ercusdtlockamount = "userinfo_ercusdtlockamount"   				--erc20的usdt的锁定数量
UserInfoModel.userinfo_ercusdtlockamount_update = "userinfo_ercusdtlockamount_update"   --标志用户需要更新数据库的erc20的usdt的锁定数量
UserInfoModel.userinfo_kit_userList = "userinfo_kit_userList"							--标记被踢下线的玩家
UserInfoModel.userinfo_fee_rate = "userinfo_fee_rate"									--保存用户的费率
UserInfoModel.userinfo_day_limit = "userinfo_day_limit"									--单日限额
UserInfoModel.userinfo_sys_user = "userinfo_sys_user" 									--系统用户
UserInfoModel.userinfo_ercfcamount = "userinfo_ercfcamount"   							--用户erc20的法币的数量
UserInfoModel.userinfo_ercfcamount_update = "userinfo_ercfcamount_update"   			--标志用户需要更新数据库的法币的数量
UserInfoModel.userinfo_ercfclockamount = "userinfo_ercfclockamount"   					--erc20的usdt的锁定数量
UserInfoModel.userinfo_ercfclockamount_update = "userinfo_ercfclockamount_update"  		 --标志用户需要更新数据库的法币的锁定数量
UserInfoModel.userinfo_coinpayusdtamount = "userinfo_coinpayusdtamount"   						--用户erc20的usdt的数量
UserInfoModel.userinfo_coinpayusdtamountt_update = "userinfo_coinpayusdtamountt_update"   		--标志用户需要更新数据库的erc20的usdt的数量
UserInfoModel.userinfo_coinpayusdtlockamount = "userinfo_coinpayusdtlockamount"   				--erc20的usdt的锁定数量
UserInfoModel.userinfo_coinpayusdtlockamount_update = "userinfo_coinpayusdtlockamount_update"   --标志用户需要更新数据库的erc20的usdt的锁定数量

--通过手机获取userid
function UserInfoModel.GetPhoneByUser(phonenum,channel)
	
	if phonenum == nil or phonenum == "" then
		return nil
	end
	
	--[[
	if true == redisItem:hexists( UserInfoModel.userinfo_phonenum, phonenum.."_"..channel,  UserInfoModel.redis_index) then
		local str = redisItem:hget( UserInfoModel.userinfo_phonenum, phonenum.."_"..channel, UserInfoModel.redis_index )
		return luajson.decode(str)
	end
	]]
	local sqlCase = "select userid, password from dy_user_info where phonenum='"..phonenum.."' and channel = '"..channel.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData ~= nil then
		return UserInfoModel.SetPhoneByUser(phonenum, channel,tonumber(sqlData[1]), sqlData[2])
	end
	return nil
end

--更新缓存中手机号与对象的信息
function UserInfoModel.SetPhoneByUser(phonenum, channel,userid, password)
	
	local pInfo = {}
	pInfo['userid'] = userid
	pInfo['password'] = password
	redisItem:hset( UserInfoModel.userinfo_phonenum, phonenum.."_"..channel, luajson.encode(pInfo), UserInfoModel.redis_index )
	return pInfo
end

--通过account获取userid
function UserInfoModel.GetAccountByUser(account,channel)
	
	if account == nil or account == "" then
		return nil
	end
	
	--[[
	if true == redisItem:hexists( UserInfoModel.userinfo_account, account.."_"..channel,  UserInfoModel.redis_index) then
		local str = redisItem:hget( UserInfoModel.userinfo_account, account.."_"..channel, UserInfoModel.redis_index )
		return luajson.decode(str)
	end
	]]
	local sqlCase = "select userid, password from dy_user_info where account='"..account.."' and channel = '"..channel.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData ~= nil then
		return UserInfoModel.SetAccountByUser(account, channel,tonumber(sqlData[1]), sqlData[2])
	end
	return nil
end

--更新缓存中account与对象的信息
function UserInfoModel.SetAccountByUser(account, channel,userid, password)
	
	local pInfo = {}
	pInfo['userid'] = userid
	pInfo['password'] = password
	redisItem:hset( UserInfoModel.userinfo_account, account.."_"..channel, luajson.encode(pInfo), UserInfoModel.redis_index )
	return pInfo
end


--通过手机获取userid
function UserInfoModel.GetEmailByUser(Email,channel)
	
	if Email == nil or Email == "" then
		return nil
	end
	
	--[[
	if true == redisItem:hexists( UserInfoModel.userinfo_email, Email.."_"..channel,  UserInfoModel.redis_index) then
		local str = redisItem:hget( UserInfoModel.userinfo_email, Email.."_"..channel, UserInfoModel.redis_index )
		return luajson.decode(str)
	end
	]]
	local sqlCase = "select userid, password from dy_user_info where email='"..Email.."' and channel = '"..channel.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData ~= nil then
		return UserInfoModel.SetEmailByUser(Email, channel,tonumber(sqlData[1]), sqlData[2])
	end
	return nil
end

--更新缓存中手机号与对象的信息
function UserInfoModel.SetEmailByUser(Email, channel,userid, password)
	
	local pInfo = {}
	pInfo['userid'] = userid
	pInfo['password'] = password
	redisItem:hset( UserInfoModel.userinfo_email, Email.."_"..channel, luajson.encode(pInfo), UserInfoModel.redis_index )
	return pInfo
end

function UserInfoModel.GetRandomCID()
	local strList = {'0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g'
	,'h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z'}
	local ret = ""
	for i = 1,8 do
		local len = math.myrandom(1,#strList)
		ret = ret..strList[len]
	end
	return ret
end

function UserInfoModel.GetInitFaceID(Sex)

	local boyList =  {'90006_1001','90007_1001','90008_1001','90009_1001','90010_1001'}	
	local girlList = {'90001_1001','90002_1001','90003_1001','90004_1001','90005_1001'}
	
	local neutralList = {'90001_1001','90002_1001','90003_1001','90004_1001','90005_1001','90006_1001','90007_1001','90008_1001','90009_1001','90010_1001'}	
	
	local faceID
	if Sex == 1 then
		local indexNum = math.myrandom(1,#boyList)
		faceID = boyList[indexNum]
	elseif Sex == 2 then
		local indexNum = math.myrandom(1,#girlList)
		faceID = girlList[indexNum]
	else
		local indexNum = math.myrandom(1,#neutralList)
		faceID = neutralList[indexNum]
	end	
	
	return faceID
end


--注册渠道
function UserInfoModel.CreateChanel(channelInfo)
	
	--创建后台渠道后台用户
	local sqlCase= "insert into dy_channel_info(account, password, web_account, web_password,channel,phonenum,channel_type,channle_name,channel_deal,fund_scheduling,is_external) values('"..channelInfo.backAccount.."','"
				..channelInfo.backPwd.."','"..channelInfo.appAccount.."','"..channelInfo.appPwd.."','"..channelInfo.channel.."','"..channelInfo.contactInfo.."',"
				..channelInfo.channelType..",'"..channelInfo.channelName.."',"..channelInfo.channelDeal..","..channelInfo.fundScheduling..","..channelInfo.isExternal..")"
	mysqlItem:execute(sqlCase)
	
	local sqlCase= "select id from dy_channel_info where channel='"..channelInfo.channel.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch(sqlCase)
	if sqlData == nil then
		return 0
	end
	
	local channelID = tonumber(sqlData) or 0
	--设置汇率
	local sqlCase = "select * from dy_coin_info where channel='ALL' and coin_id=2003"
	mysqlItem:executeQuery(sqlCase)
	local coinInfo = mysqlItem:fetch({})
	if coinInfo ~= nil then
		local sqlCase = "insert into dy_coin_info(coin_id,coin_name,coin_type,description,buy_price,sell_price,free_price,channel) values("
						..coinInfo[2]..",'"..coinInfo[3].."','"..coinInfo[4].."','"..coinInfo[5].."',"..coinInfo[6]..","..coinInfo[7]..","..coinInfo[8]
						..",'"..channelInfo.channel.."')"
		mysqlItem:execute(sqlCase)
	end
	
	if tonumber(channelInfo.channelType) == 200 or tonumber(channelInfo.channelType) == 400 then
		local sqlCase = "insert into dy_web_conf(channel,channel_name) values('"..channelInfo.channel.."','"..channelInfo.channelName.."')"
		mysqlItem:execute(sqlCase)
	end
	
	mysqlItem:execute(sqlCase)
	return channelID
end


--注册用户
function UserInfoModel.CreateUser(regInfo)
	
	local password = md5(regInfo.password)
	local nickname = regInfo.nickname
	local phonenum = regInfo.phonenum
	local face1 = UserInfoModel.GetInitFaceID()
	local channel = regInfo.channel
	local invitecode = regInfo.invitecode
	local cid = UserInfoModel.GetRandomCID()
	local agent =  regInfo.agent
	local usertype = regInfo.usertype
	local account = regInfo.account
	
	local tmp = {'A','B','C','D','E','F','G','H','I','J','K','L','N','M','O','P','Q','R','S','T','U','V','W','X','Y','Z',
				 '1','2','3','4','6','7','7','8','9'}
	local invite_code = ""
	
	local num = 1000000
	while true do	
		local tmpInviteCode = tmp[math.myrandom(1,#tmp)]..tmp[math.myrandom(1,#tmp)]..tmp[math.myrandom(1,#tmp)]..tmp[math.myrandom(1,#tmp)]
		local sqlCase = "select * from	dy_user_info where invite_code='"..tmpInviteCode.."'"
		mysqlItem:executeQuery(sqlCase)
		local sqlDate = mysqlItem:fetch()
		if sqlDate == nil then
			invite_code = tmpInviteCode
			num = -1
		else	
			num = num - 1
		end
		
		if num < 0 then
			break
		end
	end
	
	if invite_code == "" then
		return 0	
	end
	
	local payidlist = {}
	for k,v in ipairs(regInfo.paylist)do 
		if v.state == 1 then
			table.insert(payidlist, v.payType)
		end
	end 
	local is_allow_loginback = (usertype==200 or usertype ==400) and 0 or 1
	local test = g_money == nil and '0.**********' or g_money
	local sqlCase = "insert into dy_user_info(cid,account,password,nickname,face_1,phonenum,channel,bind_code,bindtype,invite_code,payidlist,erc_usdt_amount,agent,user_type,comm_type,back_mobile,username,email,is_invited,is_allow_loginback,deal_coin_type,team_name,platform_id,is_team_accept_order)"
			.." values('"..cid.."','"..account.. "','"..password.."','"..nickname.."','"..face1.."','"..phonenum.."','"..channel.."','"..invitecode
			.."',"..regInfo.bindtype..",'"..invite_code.."','"..luajson.encode(payidlist).."','"..test.."',"..agent..","..usertype..","..regInfo.commtype
			..",'"..regInfo.contactInfo.."','"..regInfo.userName.."','"..regInfo.email.."',"..regInfo.isInvited..","..is_allow_loginback..","..regInfo.dealCoinType
			..",'"..regInfo.teamName.."',"..regInfo.platformID..","..regInfo.isTeamAcceptOrder..")"
	local ret = mysqlItem:execute(sqlCase)
	if ret == nil then
		LogFile("player", sqlCase)
		return 0
	end
	--查找userid
	local sqlCase = "select userid from dy_user_info where account='"..account.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		
		local userid = tonumber(sqlData)
		
		--创建给Userid的自增值加20到50
		local addNum = math.myrandom(20, 50)
		local sqlCase = "alter table dy_user_info AUTO_INCREMENT="..userid+addNum
		mysqlItem:execute(sqlCase)
		
		UserInfoModel.SetPhoneByUser(phonenum, channel,tonumber(sqlData), password)
		
		for _, addrType in ipairs(g_marketDefine.addr_type_list) do 
			if addrType == 101 then
				local address = AddressPreModel.GetNewAddress(channel) or "" 
				if address ~= "" then
					AddressPreModel.UpdatePreAddress(address, channel, userid)
				end
			elseif addrType == 102 then
				local address = AddressPreModel.GetNewTRCAddress(channel) or "" 
				if address ~= "" then
					AddressPreModel.UpdatePreTRCAddress(address, channel, userid)
				end
			end
		end
		
		--插入费率表
		for k,v in ipairs(regInfo.paylist) do 
			
			local sqlCase = "insert into dy_user_rate(userid,paytype,buy_fee_rate,sell_fee_rate,buy_comm_rate,sell_comm_rate,channel,is_used,behalf_sell_fee_rate,behalf_buy_comm_rate) values("
				..tonumber(sqlData)..","..v.payType..","..v.buy_free_rate..","..v.sell_free_rate..","..v.buy_common_rate..","..v.sell_common_rate..",'"..channel.."',"..v.state..","..v.behalf_sell_fee_rate..","..v.behalf_buy_comm_rate..")"
			mysqlItem:execute(sqlCase)
		end
		
		--插入log用户表
		sqlCase = "insert into log_user(userid,channel) values("..tonumber(sqlData)..",'"..channel.."')"
		mysqlItem:execute(sqlCase)
		
		if tonumber(usertype) == g_humanDefine.usertype_merchant_dealer then
			local sqlCase = "insert into dy_user_api(userid) values("..userid..")"
			mysqlItem:execute(sqlCase)
		end
		
		--统计新增用户	
		LogDispatch.newUserCount(userid, channel, nickname, usertype)
		
		return userid
	end
	return 0
end

--获取缓存玩家信息
function UserInfoModel.GetUserInfo(userID, uInfo)

	if uInfo == nil then
		uInfo = st_human_pb.userinfo()
	end
	if true == redisItem:exists(UserInfoModel.userinfo_list..userID, UserInfoModel.redis_index) then
		local strGet = redisItem:get(UserInfoModel.userinfo_list..userID, UserInfoModel.redis_index)
		uInfo:ParseFromString(strGet)
		uInfo.ercusdtamount = UserInfoModel.GetErcUsdtAmount(userID)
		uInfo.ercusdtlockamount = UserInfoModel.GetErcUsdtLockAmount(userID)
		uInfo.ercfcamount = UserInfoModel.GetErcFCAmount(userID)
		uInfo.ercfclockamount = UserInfoModel.GetErcFCLockAmount(userID)
		uInfo.coinpayusdtamount = UserInfoModel.GetErcCoinPayUsdtAmount(userID)
		uInfo.coinpayusdtlockamount = UserInfoModel.GetErcCoinPayUsdtLockAmount(userID)
		return uInfo
	end
	
	return UserInfoModel.LoadUserInfo(userID, uInfo)
end

--把玩家信息保存缓存
function UserInfoModel.SetUserInfo(uInfo)
	redisItem:set(UserInfoModel.userinfo_list..uInfo.userid, uInfo:SerializeToString(), UserInfoModel.redis_index)
end

--从数据库加载玩家信息
function UserInfoModel.LoadUserInfo(userID, uInfo)
	
	local sqlCase = "select * from dy_user_info where userid="..userID
	
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch({})
	if sqlData == nil then
		return nil
	end
	
	if uInfo == nil then
		uInfo = st_human_pb.userinfo()
	end
	
	uInfo.userid = tonumber(sqlData[1]) or 0
	uInfo.cid = sqlData[2] or ""
	uInfo.account = sqlData[3] or ""
	uInfo.password = ""
	uInfo.nickname = sqlData[5] or ""
	uInfo.ethaddress = sqlData[6] or ""
	uInfo.face1 = sqlData[7] or ""
	uInfo.regdate = sqlData[8] or ""
	uInfo.sex = tonumber(sqlData[9]) or 0
	uInfo.age = tonumber(sqlData[10]) or 0
	uInfo.email = sqlData[11] or ""
	uInfo.phonenum = sqlData[12] or ""
	uInfo.ethamount = sqlData[13] or "0"
	uInfo.ercusdtamount = sqlData[14] or "0"
	uInfo.ercusdtlockamount = sqlData[15] or "0"
	uInfo.channel = sqlData[16] or "0"
	uInfo.invitecode = sqlData[17] or ""
	uInfo.bindcode = sqlData[18] or ""
	uInfo.imei = sqlData[19] or ""
	uInfo.devname = sqlData[20] or ""
	uInfo.macname = sqlData[21] or ""
	uInfo.mobiletype = tonumber(sqlData[22]) or 0
	uInfo.lasttime = tonumber(sqlData[23]) or 0
	uInfo.penulttime = tonumber(sqlData[24]) or 0
	uInfo.isban = tonumber(sqlData[25]) or 0
	uInfo.description = sqlData[26] or ""
	uInfo.blacklist = tonumber(sqlData[27]) or 0	
	uInfo.ip = sqlData[28] or ""
	uInfo.province = sqlData[29] or ""
	uInfo.city = sqlData[30] or ""
	if sqlData[31] ~= nil and sqlData[31] ~= "" then
		local paytypelist = luajson.decode(sqlData[31])
		for k,v in ipairs(paytypelist)do 
			uInfo.paytypelist:append(v)
		end
	end
	uInfo.bindtype = tonumber(sqlData[32]) or 0	
	uInfo.isfundpassword = sqlData[33] ~= "" and 1 or 0
	uInfo.usertype = tonumber(sqlData[34])
	uInfo.minbuy = sqlData[45]
	uInfo.maxbuy = sqlData[46]
	uInfo.minsell = sqlData[47]
	uInfo.maxsell = sqlData[48]
	uInfo.ishangbuy = tonumber(sqlData[43]) or 0
	uInfo.ishangsell = tonumber(sqlData[44]) or 0
	uInfo.commtype = tonumber(sqlData[49]) or 0
	uInfo.prohibitlogin = tonumber(sqlData[50]) or 0
	uInfo.isacceptorder = tonumber(sqlData[52]) or 0
	uInfo.islock = tonumber(sqlData[39]) or 0
	uInfo.deallasttime = sqlData[53]
	uInfo.bantime = sqlData[56]
	uInfo.isinvited = tonumber(sqlData[54])
	uInfo.isotc = tonumber(sqlData[61])
	uInfo.isbehalfpay = tonumber(sqlData[62])
	uInfo.ercfcamount = tostring(sqlData[64])
	uInfo.ercfclockamount = tostring(sqlData[65])
	uInfo.dealcointype = tonumber(sqlData[66])
	uInfo.energyvalue = sqlData[67]
	uInfo.weightsvalue = sqlData[68]
	uInfo.teamname = sqlData[69]
	uInfo.platformid = tonumber(sqlData[70])
	uInfo.isteamacceptorder = tonumber(sqlData[71])
	uInfo.preauthorization = sqlData[72]
	uInfo.coinpayrate = sqlData[73]
	uInfo.coinpayerc = tonumber(sqlData[74])
	uInfo.coinpaytrc = tonumber(sqlData[75])
	uInfo.allowsysaddr = tonumber(sqlData[76])
	uInfo.sysercmin = sqlData[77]
	uInfo.coinpayusdtamount = sqlData[78]
	uInfo.coinpayusdtlockamount = sqlData[79]
	uInfo.ischeck = tonumber(sqlData[80])
	uInfo.extractcurrencyrateerc = sqlData[81]
	uInfo.extractcurrencyratetrc = sqlData[82]
	
	uInfo.agent = tonumber(sqlData[42])
	local sqlCase = "select id from dy_vendor_order where userid="..userID.." and type=1 and auto_switch=1 and enable_status=1"
	mysqlItem:executeQuery(sqlCase)
	local id = mysqlItem:fetch()
	if id == nil then
		uInfo.autosell = 0
	else
		uInfo.autosell = tonumber(id)
	end
	
	redisItem:hset(UserInfoModel.userinfo_ercusdtamount, uInfo.userid, uInfo.ercusdtamount, UserInfoModel.redis_index)
	redisItem:hset(UserInfoModel.userinfo_ercusdtlockamount, uInfo.userid, uInfo.ercusdtlockamount, UserInfoModel.redis_index)
	
	redisItem:hset(UserInfoModel.userinfo_ercfcamount, uInfo.userid, uInfo.ercfcamount, UserInfoModel.redis_index)
	redisItem:hset(UserInfoModel.userinfo_ercfclockamount, uInfo.userid, uInfo.ercfclockamount, UserInfoModel.redis_index)
	
	redisItem:hset(UserInfoModel.userinfo_coinpayusdtamount, uInfo.userid, uInfo.coinpayusdtamount, UserInfoModel.redis_index)
	redisItem:hset(UserInfoModel.userinfo_coinpayusdtlockamount, uInfo.userid, uInfo.coinpayusdtlockamount, UserInfoModel.redis_index)
	
	UserInfoModel.SetUserInfo(uInfo)
	return uInfo
end

function UserInfoModel.AddPayInfoList(payInfo)
	
	redisItem:hset(UserInfoModel.payinfo_list..payInfo.userid.."_"..payInfo.paytype, payInfo.id, payInfo:SerializeToString(), UserInfoModel.redis_index)

end

function UserInfoModel.SetPayInfo(payInfo)

	UserInfoModel.AddPayInfoList(payInfo)
	
end

function UserInfoModel.DelPayInfo(userID, payType, id)

	return redisItem:hdel(UserInfoModel.payinfo_list..userID.."_"..payType, id, UserInfoModel.redis_index)
	
end

function UserInfoModel.GetPayInfo(userID, payType, id, payInfo)
	
	if payInfo == nil then
		payInfo = st_human_pb.payinfo() 
		
	end
	local tmp = redisItem:hget(UserInfoModel.payinfo_list..userID.."_"..payType, id, UserInfoModel.redis_index)
	if tmp ~= nil then
		payInfo:ParseFromString(tmp)
		return payInfo
	else	
		local sqlCase = "select * from dy_user_pay where id="..id.." and status!=2"
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch()
		if sqlData ~= nil then
			local payInfo = st_human_pb.payinfo()
			payInfo.paytype = tonumber(sqlDate[2])
			payInfo.account = sqlDate[5]
			payInfo.payee = sqlDate[4]
			payInfo.qrcode = sqlDate[8]
			payInfo.bankname = sqlDate[6]
			payInfo.bankaddr = sqlDate[7]
			payInfo.singlelimit = sqlDate[11]
			payInfo.daylimit = sqlDate[13]
			payInfo.id = tonumber(sqlDate[1])
			payInfo.status = tonumber(sqlDate[15])
			payInfo.userid = tonumber(sqlDate[14])
			payInfo.fourthpartyid = tonumber(sqlDate[24])
			payInfo.deallasttime = sqlDate[25]
			UserInfoModel.AddPayInfoList(payInfo)
		end
	end
	
	return nil
end

function UserInfoModel.GetPayInfoList(userID, payType)
	
	if true == redisItem:exists(UserInfoModel.payinfo_list..userID.."_"..payType, UserInfoModel.redis_index) then
		return redisItem:hgetall(UserInfoModel.payinfo_list..userID.."_"..payType, UserInfoModel.redis_index)
	end
	
	
	UserInfoModel.LoadPayInfoList(userID, payType)
	return redisItem:hgetall(UserInfoModel.payinfo_list..userID.."_"..payType, UserInfoModel.redis_index)
end

function UserInfoModel.LoadPayInfoList(userID, payType)
	
	local sqlCase = "select * from dy_user_pay where userid="..userID.." and status!=2 and pay_id="..payType
	mysqlItem:executeQuery(sqlCase)
	while true do
		local sqlDate = mysqlItem:fetch({})
		if sqlDate == nil then
			break
		end
		local payInfo = st_human_pb.payinfo()
		payInfo.paytype = tonumber(sqlDate[2])
		payInfo.account = sqlDate[5]
		payInfo.payee = sqlDate[4]
		payInfo.qrcode = sqlDate[8]
		payInfo.bankname = sqlDate[6]
		payInfo.bankaddr = sqlDate[7]
		payInfo.singlelimit = sqlDate[11]
		payInfo.daylimit = sqlDate[13]
		payInfo.id = tonumber(sqlDate[1])
		payInfo.status = tonumber(sqlDate[15])
		payInfo.userid = tonumber(sqlDate[14])
		payInfo.fourthpartyid = tonumber(sqlDate[24])
		payInfo.deallasttime = sqlDate[25]
		UserInfoModel.AddPayInfoList(payInfo)
	end
	
	--优化添加一个占位的数据，避免每次都查询数据库
	redisItem:hset(UserInfoModel.payinfo_list..userID.."_"..payType, "Placeholder", 1, UserInfoModel.redis_index)
	
end

function UserInfoModel.AddErcUsdtAmount(uInfo, amount, order_id, order_type,remarks, type,money)
	if amount == 0 then
		return 
	end
	
	amount =  string.format("%.10f", amount)
	ThreadManager.UserAmountLock(uInfo.userid)
	local pAmount = redisItem:hget(UserInfoModel.userinfo_ercusdtamount, uInfo.userid, UserInfoModel.redis_index)
	pAmount = pAmount or 0
	pAmount = pAmount + amount
	uInfo.ercusdtamount = string.format("%.10f", pAmount)
	redisItem:hset(UserInfoModel.userinfo_ercusdtamount, uInfo.userid, uInfo.ercusdtamount, UserInfoModel.redis_index)
	ThreadManager.UserAmountUnLock(uInfo.userid)
	redisItem:hset(UserInfoModel.userinfo_ercusdtamountt_update, uInfo.userid, 1, UserInfoModel.redis_index)
	
	--资金变化记录
	UserInfoModel.CurrencyVarietyLog(uInfo.userid, 2003, 0, amount, uInfo.ercusdtamount, order_id, order_type, remarks, uInfo.channel, 0)
	--资金流水
	UserInfoModel.CapitalFlow(uInfo.userid, 2003, amount, uInfo.ercusdtamount, order_id, type, uInfo.channel,uInfo.usertype, remarks, money, uInfo.ercfcamount, uInfo.platformid,0)
	
	LogDispatch.teamBalance(uInfo.userid, 1, amount)
end

function UserInfoModel.DecErcUsdtAmount(uInfo, amount,order_id, order_type,remarks, type,money)
	if amount == 0 then
		return 
	end
	
	amount =  string.format("%.10f", amount)
	ThreadManager.UserAmountLock(uInfo.userid)
	local pAmount = redisItem:hget(UserInfoModel.userinfo_ercusdtamount, uInfo.userid, UserInfoModel.redis_index)
	pAmount = pAmount or 0
	pAmount = pAmount - amount
	uInfo.ercusdtamount = string.format("%.10f", pAmount)
	redisItem:hset(UserInfoModel.userinfo_ercusdtamount, uInfo.userid, uInfo.ercusdtamount, UserInfoModel.redis_index)
	ThreadManager.UserAmountUnLock(uInfo.userid)
	redisItem:hset(UserInfoModel.userinfo_ercusdtamountt_update, uInfo.userid, 1, UserInfoModel.redis_index)
	
	--资金变化记录
	UserInfoModel.CurrencyVarietyLog(uInfo.userid, 2003, 0, -amount, uInfo.ercusdtamount, order_id, order_type, remarks, uInfo.channel,0)
	--资金流水
	UserInfoModel.CapitalFlow(uInfo.userid, 2003, -amount, uInfo.ercusdtamount, order_id, type, uInfo.channel,uInfo.usertype, remarks, money, uInfo.ercfcamount, uInfo.platformid,0)
	
	LogDispatch.teamBalance(uInfo.userid, 1, -amount)
end

function UserInfoModel.GetErcUsdtAmount(userid)

	return tostring(redisItem:hget(UserInfoModel.userinfo_ercusdtamount, userid, UserInfoModel.redis_index))
end

function UserInfoModel.AddErcUsdtLockAmount(uInfo, amount, order_id, order_type, remarks, isExternal)
	if amount == 0 then
		return 
	end
	isExternal = isExternal or 0
	amount =  string.format("%.10f", amount)
	ThreadManager.UserAmountLock(uInfo.userid)
	local pAmount = redisItem:hget(UserInfoModel.userinfo_ercusdtlockamount, uInfo.userid, UserInfoModel.redis_index)
	pAmount = pAmount or 0
	pAmount = pAmount + amount
	
	if uInfo.dealcointype == 0 and isExternal == 0 then
		local Count = redisItem:hget(UserInfoModel.userinfo_ercusdtamount, uInfo.userid, UserInfoModel.redis_index)
		if pAmount > tonumber(Count) then
			ThreadManager.UserAmountUnLock(uInfo.userid)
			return false
		end
	end
	
	uInfo.ercusdtlockamount = string.format("%.10f", pAmount)
	redisItem:hset(UserInfoModel.userinfo_ercusdtlockamount, uInfo.userid, uInfo.ercusdtlockamount, UserInfoModel.redis_index)
	ThreadManager.UserAmountUnLock(uInfo.userid)
	redisItem:hset(UserInfoModel.userinfo_ercusdtlockamount_update, uInfo.userid, 1, UserInfoModel.redis_index)
	
	--资金变化记录
	UserInfoModel.CurrencyVarietyLog(uInfo.userid, 2003, 1, amount, uInfo.ercusdtlockamount, order_id, order_type, remarks, uInfo.channel,0)
	
	return true
end

function UserInfoModel.DecErcUsdtLockAmount(uInfo, amount, order_id, order_type, remarks)
	if amount == 0 then
		return 
	end
	
	amount =  string.format("%.10f", amount)
	ThreadManager.UserAmountLock(uInfo.userid)
	local pAmount = redisItem:hget(UserInfoModel.userinfo_ercusdtlockamount, uInfo.userid, UserInfoModel.redis_index)
	pAmount = pAmount or 0
	pAmount = pAmount - amount
	pAmount = pAmount < 0 and 0 or pAmount
	uInfo.ercusdtlockamount = string.format("%.10f", pAmount)
	redisItem:hset(UserInfoModel.userinfo_ercusdtlockamount, uInfo.userid, uInfo.ercusdtlockamount, UserInfoModel.redis_index)
	ThreadManager.UserAmountUnLock(uInfo.userid)
	redisItem:hset(UserInfoModel.userinfo_ercusdtlockamount_update, uInfo.userid, 1, UserInfoModel.redis_index)
	
	--资金变化记录
	UserInfoModel.CurrencyVarietyLog(uInfo.userid, 2003, 1, -amount, uInfo.ercusdtlockamount, order_id, order_type, remarks, uInfo.channel,0)
end

function UserInfoModel.GetErcUsdtLockAmount(userid)

	return tostring(redisItem:hget(UserInfoModel.userinfo_ercusdtlockamount, userid, UserInfoModel.redis_index))
end

function UserInfoModel.addKitUserList(userid)
	
	redisItem:sadd(UserInfoModel.userinfo_kit_userList, userid, UserInfoModel.redis_index)
	
end

function UserInfoModel.isUserKit(userid)
	
	return redisItem:srem(UserInfoModel.userinfo_kit_userList, userid, UserInfoModel.redis_index)
	
end

function UserInfoModel.SendErcUsdtAmount(uInfo)
	
	local gcUpdate = msg_human_pb.gcupdateuserinfo()

	gcUpdate.typelist:append("ercusdtamount")
	gcUpdate.valuelist:append(tostring(uInfo.ercusdtamount))
	gcUpdate.result = 0
	SendMessage(uInfo.userid, PacketCode[1032].client, gcUpdate:ByteSize(), gcUpdate:SerializeToString())

end

function UserInfoModel.SendErcUsdtLockAmount(uInfo)
	
	local gcUpdate = msg_human_pb.gcupdateuserinfo()

	gcUpdate.typelist:append("ercusdtlockamount")
	gcUpdate.valuelist:append(tostring(uInfo.ercusdtlockamount))
	gcUpdate.result = 0
	SendMessage(uInfo.userid, PacketCode[1032].client, gcUpdate:ByteSize(), gcUpdate:SerializeToString())

end



function UserInfoModel.SendNickName(uInfo)
	
	local gcUpdate = msg_human_pb.gcupdateuserinfo()
	
	gcUpdate.typelist:append("nickname")
	gcUpdate.valuelist:append(uInfo.nickname)
	gcUpdate.result = 0
	SendMessage(uInfo.userid, PacketCode[1032].client, gcUpdate:ByteSize(), gcUpdate:SerializeToString())

end

function UserInfoModel.SendInfoList(uInfo, InfoList)
	
	local gcUpdate = msg_human_pb.gcupdateuserinfo()
	for k,v in ipairs(InfoList) do 
		gcUpdate.typelist:append(v)
		gcUpdate.valuelist:append(tostring(uInfo[v]))
	end
	gcUpdate.result = 0
	SendMessage(uInfo.userid, PacketCode[1032].client, gcUpdate:ByteSize(), gcUpdate:SerializeToString())

end

function UserInfoModel.SendAutoSell(uInfo)
	
	local gcUpdate = msg_human_pb.gcupdateuserinfo()
	
	gcUpdate.typelist:append("autosell")
	gcUpdate.valuelist:append(tostring(uInfo.autosell))
	gcUpdate.result = 0
	SendMessage(uInfo.userid, PacketCode[1032].client, gcUpdate:ByteSize(), gcUpdate:SerializeToString())

end



function UserInfoModel.getUserFeeRate(userid, paytype, ratetype)
	
	if true == redisItem:hexists(UserInfoModel.userinfo_fee_rate, userid.."_"..paytype.."_"..ratetype, UserInfoModel.redis_index) then
		local rate = redisItem:hget(UserInfoModel.userinfo_fee_rate, userid.."_"..paytype.."_"..ratetype, UserInfoModel.redis_index)
		return tonumber(rate) or 0
	end
	
	return UserInfoModel.LoadUserFeeRate(userid, paytype, ratetype)
end

function UserInfoModel.LoadUserFeeRate(userid, paytype, ratetype)
	
	local msg = ""
	if ratetype == g_humanDefine.buy_fee_rate then
		msg = "buy_fee_rate"
	elseif ratetype == g_humanDefine.sell_fee_rate then
		msg = "sell_fee_rate"
	elseif ratetype == g_humanDefine.buy_comm_rate then
		msg = "buy_comm_rate"
	elseif ratetype == g_humanDefine.sell_comm_rate then
		msg = "sell_comm_rate"
	elseif ratetype == g_humanDefine.behalf_sell_fee_rate then
		msg = "behalf_sell_fee_rate"
	elseif ratetype == g_humanDefine.behalf_buy_comm_rate then
		msg = "behalf_buy_comm_rate"
	end
	
	local sqlCase = "select "..msg.." from dy_user_rate where userid="..userid.." and paytype="..paytype
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		UserInfoModel.SetUserFeeRate(userid, paytype, ratetype, tonumber(sqlData))
		return tonumber(sqlData)
	end
	
	UserInfoModel.SetUserFeeRate(userid, paytype, ratetype, 0)
	return 0
end

function UserInfoModel.SetUserFeeRate(userid, paytype, ratetype, rate)
	
	redisItem:hset(UserInfoModel.userinfo_fee_rate, userid.."_"..paytype.."_"..ratetype, rate, UserInfoModel.redis_index)
	
end

function UserInfoModel.CheckFundPassword(uInfo, fundPwd)
	
	--检查资金密码
	if uInfo.isfundpassword == 0 then
		return ReturnCode["human_fundpassword_not_exist"][1],ReturnCode["human_fundpassword_not_exist"][2]
	end
	
	if fundPwd ~= nil then
		--检查资金密码对不对
		local sqlCase = "select * from dy_user_info where userid="..uInfo.userid.." and fundpassword='"..md5(fundPwd).."'"
		mysqlItem:execute(sqlCase)
		local sqlDate = mysqlItem:fetch(sqlCase)
		if sqlDate == nil then
			return ReturnCode["human_pass_err"][1],ReturnCode["human_pass_err"][2]
		end
	end
	
	return 0, ""
end


function UserInfoModel.CurrencyVarietyLog(userid, currency_type, is_lock, amount, use_amount, order_id, order_type, remarks, channel,walletType)

	local sqlCase = "insert into log_amount_details(userid,currency_type,is_lock,amount,use_amount,order_id,order_type,remarks,channel,wallet_type)"
			.." values("..userid..","..currency_type..","..is_lock..","..amount..","..use_amount..",'"..order_id.."',"..order_type..",'"
			..remarks.."','"..channel.."',"..walletType..")"
	LogModel.PushGameSqlServer(sqlCase)
end

function UserInfoModel.CapitalFlow(userid, currency_type, amount, after_amount, order_id, order_type, channel,user_type,remarks, money, ercfcamount, platformid,walletType)

	local sqlCase = "insert into log_fund_details(userid,currency_type,amount,before_amount,after_amount,order_id,order_type,channel,user_type,remarks,money,after_money,platform_id,wallet_type)"
			.." values("..userid..","..currency_type..","..amount..","..(after_amount-amount)..","..after_amount..",'"..order_id.."',"..order_type..",'"
			..channel.."',"..user_type..",'"..remarks.."',"..money..","..ercfcamount..","..platformid..","..walletType..")"
	LogModel.PushGameSqlServer(sqlCase)
end


function UserInfoModel.SetDayLimit(payid, money)
	
	if payid == 0 or money == 0 then
		return 
	end

	local nowDay = TimeUtils.GetDayString()
	local tmpData = redisItem:hget(UserInfoModel.userinfo_day_limit, payid, UserInfoModel.redis_index)
	if tmpData == nil then
		tmpData = {}
		tmpData["date"] = nowDay
		tmpData["num"] = 0
	else
		tmpData = luajson.decode(tmpData)
	end
	
	if tmpData["date"] == nowDay then
		tmpData["num"] = tmpData["num"] + money
		
	else
		tmpData["date"] = nowDay
		tmpData["num"] = money
	end
	
	redisItem:hset(UserInfoModel.userinfo_day_limit, payid, luajson.encode(tmpData), UserInfoModel.redis_index)
end


function UserInfoModel.GetDayLimit(payid)
	
	if payid == 0 or payid == "" then
		return 0
	end
	
	local nowDay = TimeUtils.GetDayString()
	local tmpData = redisItem:hget(UserInfoModel.userinfo_day_limit, payid, UserInfoModel.redis_index)
	local getNum = 0
	if tmpData ~= nil then
		tmpData = luajson.decode(tmpData)
		if tmpData["date"] == nowDay then
			getNum = tmpData["num"]
		end
	end
	return getNum == nil and 0 or getNum
	
end

function UserInfoModel.checkNickName(nickName)


	local sqlCase = "select userid from dy_user_info where nickname='"..nickName.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		return tonumber(sqlData) or 0
	end
	
	return 0
end


function UserInfoModel.GetSysUserInfo(platformID)
	
	platformID = platformID or 0
	if true == redisItem:exists(UserInfoModel.userinfo_sys_user.."_"..platformID, UserInfoModel.redis_index) then
		local userid = redisItem:get(UserInfoModel.userinfo_sys_user.."_"..platformID, UserInfoModel.redis_index)
		return UserInfoModel.GetUserInfo(tonumber(userid))
	end
	
	local sqlCase = "select userid from dy_user_info where user_type=100 and platform_id="..platformID
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		redisItem:set(UserInfoModel.userinfo_sys_user.."_"..platformID, UserInfoModel.redis_index)
		return UserInfoModel.GetUserInfo(tonumber(sqlData))
	end

	return nil
end


function UserInfoModel.AddErcFCAmount(uInfo, amount, order_id, order_type,remarks, type )
	if amount == 0 then
		return 
	end
	
	amount =  string.format("%.4f", amount)
	ThreadManager.UserFCAmountLock(uInfo.userid)
	local pAmount = redisItem:hget(UserInfoModel.userinfo_ercfcamount, uInfo.userid, UserInfoModel.redis_index)
	pAmount = pAmount or 0
	pAmount = pAmount + amount
	uInfo.ercfcamount = string.format("%.4f", pAmount)
	redisItem:hset(UserInfoModel.userinfo_ercfcamount, uInfo.userid, uInfo.ercfcamount, UserInfoModel.redis_index)
	ThreadManager.UserFCAmountUnLock(uInfo.userid)
	redisItem:hset(UserInfoModel.userinfo_ercfcamount_update, uInfo.userid, 1, UserInfoModel.redis_index)
	
	--资金变化记录
	UserInfoModel.CurrencyVarietyLog(uInfo.userid, 1001, 0, amount, uInfo.ercfcamount, order_id, order_type, remarks, uInfo.channel,0)
	--资金流水
	UserInfoModel.CapitalFlow(uInfo.userid, 1001, amount, uInfo.ercfcamount, order_id, type, uInfo.channel,uInfo.usertype, remarks,0,0,uInfo.platformid,0)

	LogDispatch.teamBalance(uInfo.userid, 2, amount)
end

function UserInfoModel.DecErcFCAmount(uInfo, amount,order_id, order_type,remarks, type)
	if amount == 0 then
		return 
	end
	
	amount =  string.format("%.4f", amount)
	ThreadManager.UserFCAmountLock(uInfo.userid)
	local pAmount = redisItem:hget(UserInfoModel.userinfo_ercfcamount, uInfo.userid, UserInfoModel.redis_index)
	pAmount = pAmount or 0
	pAmount = pAmount - amount
	uInfo.ercfcamount = string.format("%.4f", pAmount)
	redisItem:hset(UserInfoModel.userinfo_ercfcamount, uInfo.userid, uInfo.ercfcamount, UserInfoModel.redis_index)
	ThreadManager.UserFCAmountUnLock(uInfo.userid)
	redisItem:hset(UserInfoModel.userinfo_ercfcamount_update, uInfo.userid, 1, UserInfoModel.redis_index)
	
	--资金变化记录
	UserInfoModel.CurrencyVarietyLog(uInfo.userid, 1001, 0, -amount, uInfo.ercfcamount, order_id, order_type, remarks, uInfo.channel,0)
	--资金流水
	UserInfoModel.CapitalFlow(uInfo.userid, 1001, -amount, uInfo.ercfcamount, order_id, type, uInfo.channel,uInfo.usertype, remarks,0,0,uInfo.platformid,0)

	LogDispatch.teamBalance(uInfo.userid, 2, -amount)
end

function UserInfoModel.GetErcFCAmount(userid)
	if true == redisItem:hexists(UserInfoModel.userinfo_ercfcamount, userid, UserInfoModel.redis_index) then
		return tostring(redisItem:hget(UserInfoModel.userinfo_ercfcamount, userid, UserInfoModel.redis_index))
	end
	
	local ercFCAmount = 0
	local sqlCase = "select erc_fc_amount from dy_user_info where userid="..userid
	mysqlItem:executeQuery(sqlCase)
	
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		ercFCAmount = tonumber(sqlData) 
		redisItem:hset(UserInfoModel.userinfo_ercfcamount, userid,ercFCAmount, UserInfoModel.redis_index)
	end
	
	return tostring(ercFCAmount)
end

function UserInfoModel.AddErcFCLockAmount(uInfo, amount, order_id, order_type, remarks)
	if amount == 0 then
		return 
	end
	
	amount =  string.format("%.4f", amount)
	ThreadManager.UserFCAmountLock(uInfo.userid)
	local pAmount = redisItem:hget(UserInfoModel.userinfo_ercfclockamount, uInfo.userid, UserInfoModel.redis_index)
	pAmount = pAmount or 0
	pAmount = pAmount + amount
	
	local Count = redisItem:hget(UserInfoModel.userinfo_ercfcamount, uInfo.userid, UserInfoModel.redis_index)
	if pAmount > tonumber(Count) then
		ThreadManager.UserFCAmountUnLock(uInfo.userid)
		return false
	end
	
	uInfo.ercfclockamount = string.format("%.4f", pAmount)
	redisItem:hset(UserInfoModel.userinfo_ercfclockamount, uInfo.userid, uInfo.ercfclockamount, UserInfoModel.redis_index)
	ThreadManager.UserFCAmountUnLock(uInfo.userid)
	redisItem:hset(UserInfoModel.userinfo_ercfclockamount_update, uInfo.userid, 1, UserInfoModel.redis_index)
	
	--资金变化记录
	UserInfoModel.CurrencyVarietyLog(uInfo.userid, 1001, 1, amount, uInfo.ercfclockamount, order_id, order_type, remarks, uInfo.channel,0)
	
	return true
end

function UserInfoModel.DecErcFCLockAmount(uInfo, amount, order_id, order_type, remarks)
	if amount == 0 then
		return 
	end
	
	amount =  string.format("%.4f", amount)
	ThreadManager.UserFCAmountLock(uInfo.userid)
	local pAmount = redisItem:hget(UserInfoModel.userinfo_ercfclockamount, uInfo.userid, UserInfoModel.redis_index)
	pAmount = pAmount or 0
	pAmount = pAmount - amount
	pAmount = pAmount < 0 and 0 or pAmount
	uInfo.ercfclockamount = string.format("%.4f", pAmount)
	redisItem:hset(UserInfoModel.userinfo_ercfclockamount, uInfo.userid, uInfo.ercfclockamount, UserInfoModel.redis_index)
	ThreadManager.UserFCAmountUnLock(uInfo.userid)
	redisItem:hset(UserInfoModel.userinfo_ercfclockamount_update, uInfo.userid, 1, UserInfoModel.redis_index)
	
	--资金变化记录
	UserInfoModel.CurrencyVarietyLog(uInfo.userid, 1001, 1, -amount, uInfo.ercfclockamount, order_id, order_type, remarks, uInfo.channel,0)
end

function UserInfoModel.GetErcFCLockAmount(userid)
		
	if true == redisItem:hexists(UserInfoModel.userinfo_ercfclockamount, userid, UserInfoModel.redis_index) then
		return tostring(redisItem:hget(UserInfoModel.userinfo_ercfclockamount, userid, UserInfoModel.redis_index))
	end
	
	local ercFCLocAmount = 0
	local sqlCase = "select erc_fc_lock_amount from dy_user_info where userid="..userid
	mysqlItem:executeQuery(sqlCase)
	
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		ercFCLocAmount = tonumber(sqlData)
		redisItem:hset(UserInfoModel.userinfo_ercfclockamount, userid,ercFCLocAmount, UserInfoModel.redis_index)
	end
	
	return tostring(ercFCLocAmount)
end

function UserInfoModel.AddErcCoinPayUsdtAmount(uInfo, amount, order_id, order_type,remarks, type, money)
	if amount == 0 then
		return 
	end
	
	amount =  string.format("%.10f", amount)
	ThreadManager.UserCoinPayAmountLock(uInfo.userid)
	local pAmount = redisItem:hget(UserInfoModel.userinfo_coinpayusdtamount, uInfo.userid, UserInfoModel.redis_index)
	pAmount = pAmount or 0
	pAmount = pAmount + amount
	uInfo.coinpayusdtamount = string.format("%.10f", pAmount)
	redisItem:hset(UserInfoModel.userinfo_coinpayusdtamount, uInfo.userid, uInfo.coinpayusdtamount, UserInfoModel.redis_index)
	ThreadManager.UserCoinPayAmountUnLock(uInfo.userid)
	redisItem:hset(UserInfoModel.userinfo_coinpayusdtamountt_update, uInfo.userid, 1, UserInfoModel.redis_index)
	
	--资金变化记录
	UserInfoModel.CurrencyVarietyLog(uInfo.userid, 2003, 0, amount, uInfo.coinpayusdtamount, order_id, order_type, remarks, uInfo.channel,1)
	--资金流水
	UserInfoModel.CapitalFlow(uInfo.userid, 2003, amount, uInfo.coinpayusdtamount, order_id, type, uInfo.channel,uInfo.usertype, remarks, money, uInfo.ercfcamount, uInfo.platformid,1)
	
	LogDispatch.teamBalance(uInfo.userid, 1, amount)
end

function UserInfoModel.DecErcCoinPayUsdtAmount(uInfo, amount,order_id, order_type,remarks, type,money)
	if amount == 0 then
		return 
	end
	
	amount =  string.format("%.10f", amount)
	ThreadManager.UserCoinPayAmountLock(uInfo.userid)
	local pAmount = redisItem:hget(UserInfoModel.userinfo_coinpayusdtamount, uInfo.userid, UserInfoModel.redis_index)
	pAmount = pAmount or 0
	pAmount = pAmount - amount
	uInfo.coinpayusdtamount = string.format("%.10f", pAmount)
	redisItem:hset(UserInfoModel.userinfo_coinpayusdtamount, uInfo.userid, uInfo.coinpayusdtamount, UserInfoModel.redis_index)
	ThreadManager.UserCoinPayAmountUnLock(uInfo.userid)
	redisItem:hset(UserInfoModel.userinfo_coinpayusdtamountt_update, uInfo.userid, 1, UserInfoModel.redis_index)
	
	--资金变化记录
	UserInfoModel.CurrencyVarietyLog(uInfo.userid, 2003, 0, -amount, uInfo.coinpayusdtamount, order_id, order_type, remarks, uInfo.channel,1)
	--资金流水
	UserInfoModel.CapitalFlow(uInfo.userid, 2003, -amount, uInfo.coinpayusdtamount, order_id, type, uInfo.channel,uInfo.usertype, remarks, money, uInfo.ercfcamount, uInfo.platformid,1)
	
	LogDispatch.teamBalance(uInfo.userid, 1, -amount)
end

function UserInfoModel.GetErcCoinPayUsdtAmount(userid)

	return tostring(redisItem:hget(UserInfoModel.userinfo_coinpayusdtamount, userid, UserInfoModel.redis_index) or 0)
end

function UserInfoModel.AddErcCoinPayUsdtLockAmount(uInfo, amount, order_id, order_type, remarks)
	if amount == 0 then
		return 
	end

	amount =  string.format("%.10f", amount)
	ThreadManager.UserCoinPayAmountLock(uInfo.userid)
	local pAmount = redisItem:hget(UserInfoModel.userinfo_coinpayusdtlockamount, uInfo.userid, UserInfoModel.redis_index)
	pAmount = pAmount or 0
	pAmount = pAmount + amount
	
	local Count = redisItem:hget(UserInfoModel.userinfo_ercusdtamount, uInfo.userid, UserInfoModel.redis_index)
	if pAmount > tonumber(Count) then
		ThreadManager.UserCoinPayAmountUnLock(uInfo.userid)
		return false
	end
	
	uInfo.coinpayusdtlockamount = string.format("%.10f", pAmount)
	redisItem:hset(UserInfoModel.userinfo_coinpayusdtlockamount, uInfo.userid, uInfo.coinpayusdtlockamount, UserInfoModel.redis_index)
	ThreadManager.UserCoinPayAmountUnLock(uInfo.userid)
	redisItem:hset(UserInfoModel.userinfo_coinpayusdtlockamount_update, uInfo.userid, 1, UserInfoModel.redis_index)
	
	--资金变化记录
	UserInfoModel.CurrencyVarietyLog(uInfo.userid, 2003, 1, amount, uInfo.coinpayusdtlockamount, order_id, order_type, remarks, uInfo.channel,1)
	
	return true
end

function UserInfoModel.DecErcCoinPayUsdtLockAmount(uInfo, amount, order_id, order_type, remarks)
	if amount == 0 then
		return 
	end
	
	amount =  string.format("%.10f", amount)
	ThreadManager.UserCoinPayAmountLock(uInfo.userid)
	local pAmount = redisItem:hget(UserInfoModel.userinfo_coinpayusdtlockamount, uInfo.userid, UserInfoModel.redis_index)
	pAmount = pAmount or 0
	pAmount = pAmount - amount
	pAmount = pAmount < 0 and 0 or pAmount
	uInfo.coinpayusdtlockamount = string.format("%.10f", pAmount)
	redisItem:hset(UserInfoModel.userinfo_coinpayusdtlockamount, uInfo.userid, uInfo.coinpayusdtlockamount, UserInfoModel.redis_index)
	ThreadManager.UserCoinPayAmountUnLock(uInfo.userid)
	redisItem:hset(UserInfoModel.userinfo_coinpayusdtlockamount_update, uInfo.userid, 1, UserInfoModel.redis_index)
	
	--资金变化记录
	UserInfoModel.CurrencyVarietyLog(uInfo.userid, 2003, 1, -amount, uInfo.coinpayusdtlockamount, order_id, order_type, remarks, uInfo.channel,1)
end

function UserInfoModel.GetErcCoinPayUsdtLockAmount(userid)

	return tostring(redisItem:hget(UserInfoModel.userinfo_coinpayusdtlockamount, userid, UserInfoModel.redis_index) or 0) 
end
