
OnlineModel = {}

OnlineModel.redis_index = "redis_online"
OnlineModel.online_userlist = "online_userlist_"  --玩家在线列表


function OnlineModel.Init()
	redisItem:del(OnlineModel.online_userlist, OnlineModel.redis_index)
end


function OnlineModel.UserLogin(userID)
	
    redisItem:hset(OnlineModel.online_userlist, userID, 0, OnlineModel.redis_index)
	luaPrint(userID.." login")
	
end

function OnlineModel.UserExit(userID)
   
	if UserInfoModel.isUserKit(userID) == 0 then
		redisItem:hdel(OnlineModel.online_userlist, userID, OnlineModel.redis_index)
	end
	luaPrint(userID.." exit")
	
end

function OnlineModel.CheckOnline(userID)
	
	return redisItem:hexists(OnlineModel.online_userlist, userID, OnlineModel.redis_index)
	
end

function OnlineModel.GetOnlineUserList()
	
	return redisItem:hkeys(OnlineModel.online_userlist, OnlineModel.redis_index)
	
end
