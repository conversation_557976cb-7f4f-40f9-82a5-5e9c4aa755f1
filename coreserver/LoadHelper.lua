
LoadHelper = {}

require("trdlib.Init")  --先加载第三方库
require("conf.serverConf")
require("conf.redisConf")
require("Public.Init")
require("LogServer.Init")
require("Human.Init")
require("Notice.Init")
require("Market.Init")
require("Agent.Init")

require("Chain.Init")
require("Chat.Init")

require("RedisClear.Init")


function LoadHelper.HlydReload()
	if g_isDebug ~= 1 then
		return nil
	end
end

function LoadHelper.HttpReload()
	
end