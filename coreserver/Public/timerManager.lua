module('timerManager', package.seeall)


function timerManager.Init(currTime, timerType)
		
	g_markTime.curr = TimeUtils.GetTableTime()
	
	if timerType == "gametimer" then
		OnlineModel.Init()
		LogServer.Init()
		AddressPreModel.Init()
		SynchroChainDataModel.Init()
		HumanService.Init()
	elseif timerType == "utilstimer" then
		
		--RaceInfoService.Init()
		--ExpertService.Init()
		
	end
	
	g_markTime.last = g_markTime.curr
	
	print(timerType.." init end")
end


function timerManager:execute(currTime, timerType)
	
	if g_markTime.last == nil then
		g_markTime.last = TimeUtils.GetTableTime()
		print("================"..timerType)
		return
	end
	
	--上报信息功能
	ReportServerInfoService.ServerLoop()	
	
	--每分钟更新一次服务器的数据	
	g_markTime.curr = TimeUtils.GetTableTime()
	
	if timerType == "gametimer" then
		HumanService.ServerLoop()
		ChainServices.ServerLoop()
		ChatServices.ServerLoop()
		VendorOrderService.ServerLoop()
		LogServer.ServerLoop()
		CoinInfoService.ServerLoop()
		LogServer.UtilsLoop()
		
		VendorOrderService.matchOrderLoop(currTime)
	elseif timerType == "utilstimer" then
		
		--RaceInfoService.UtilsLoop()
		--ExpertService.UtilsLoop()
		
	end
	
	g_markTime.last = g_markTime.curr
	
end

function timerManager:createOnceTimer(strIndex)
	return _G[strIndex]
end


