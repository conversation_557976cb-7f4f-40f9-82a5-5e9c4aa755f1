
module('httpManager', package.seeall)




n_httpController = {

	["paycenter"] = "HttpPayCenter",
	["paycenter2"] = "HttpPayCenter2",
	["user"] = "HttpUser",
	["user2"] = "HttpUser2",
	["payinfo"] = "HttpPayInfo",
	["exchange"] = "HttpExchange",
	["platform"] = "HttpPlatform",
	["group"] = "HttpGroup",
}


function httpManager:createController(packetid)

	--local controller = _G[n_httpController[packetid]]
	return _G[n_httpController[packetid]]
	
end



