
--该类是用于处理C++传过来多线程的问题，
--包括共享内存锁的机制
--对于线程的锁，已经要记住，lock的时候一定要unlock
ThreadManager = {}
ThreadManager.redis_index = "redis_thread"

function ThreadManager.OrderLock(index)
	local redisKey = "lock_order_"..index
	while true do
		local ok = redisItem:setnx(redisKey, "1", ThreadManager.redis_index)
		if ok == true then
			redisItem:expire(redisKey, 30, ThreadManager.redis_index) --设置超时是3秒
			break
		end
	end	
end

function ThreadManager.OrderUnLock(index)
	local redisKey = "lock_order_"..index
	redisItem:del(redisKey, ThreadManager.redis_index)	
end


function ThreadManager.DealLock(index)
	local redisKey = "lock_deal_"..index
	while true do
		local ok = redisItem:setnx(redisKey, "1", ThreadManager.redis_index)
		if ok == true then
			redisItem:expire(redisK<PERSON>, 30, ThreadManager.redis_index) --设置超时是3秒
			break
		end
	end	
end

function ThreadManager.DealUnLock(index)
	local redisKey = "lock_deal_"..index
	redisItem:del(redisKey, ThreadManager.redis_index)	
end


function ThreadManager.UserAmountLock(index)
	local redisKey = "lock_userAmount_"..index
	while true do
		local ok = redisItem:setnx(redisKey, "1", ThreadManager.redis_index)
		if ok == true then
			redisItem:expire(redisKey, 30, ThreadManager.redis_index) --设置超时是3秒
			break
		end
	end	
end

function ThreadManager.UserAmountUnLock(index)
	local redisKey = "lock_userAmount_"..index
	redisItem:del(redisKey, ThreadManager.redis_index)	
end

function ThreadManager.AppealLock(index)
	local redisKey = "lock_appeal_"..index
	while true do
		local ok = redisItem:setnx(redisKey, "1", ThreadManager.redis_index)
		if ok == true then
			redisItem:expire(redisKey, 30, ThreadManager.redis_index) --设置超时是3秒
			break
		end
	end	
end

function ThreadManager.AppealUnLock(index)
	local redisKey = "lock_appeal_"..index
	redisItem:del(redisKey, ThreadManager.redis_index)	
end


function ThreadManager.sysOrderLock(index)
	local redisKey = "lock_sysOrder_"..index
	while true do
		local ok = redisItem:setnx(redisKey, "1", ThreadManager.redis_index)
		if ok == true then
			redisItem:expire(redisKey, 30, ThreadManager.redis_index) --设置超时是3秒
			break
		end
	end	
end

function ThreadManager.sysOrderUnLock(index)
	local redisKey = "lock_sysOrder_"..index
	redisItem:del(redisKey, ThreadManager.redis_index)	
end


function ThreadManager.UserFCAmountLock(index)
	local redisKey = "lock_userFCAmount_"..index
	while true do
		local ok = redisItem:setnx(redisKey, "1", ThreadManager.redis_index)
		if ok == true then
			redisItem:expire(redisKey, 30, ThreadManager.redis_index) --设置超时是3秒
			break
		end
	end	
end

function ThreadManager.UserFCAmountUnLock(index)
	local redisKey = "lock_userFCAmount_"..index
	redisItem:del(redisKey, ThreadManager.redis_index)	
end

function ThreadManager.UserWithdrawLock(index)
	local redisKey = "lock_userwithdraw_"..index
	while true do
		local ok = redisItem:setnx(redisKey, "1", ThreadManager.redis_index)
		if ok == true then
			redisItem:expire(redisKey, 30, ThreadManager.redis_index) --设置超时是3秒
			break
		end
	end	
end

function ThreadManager.UserWithdrawUnLock(index)
	local redisKey = "lock_userwithdraw_"..index
	redisItem:del(redisKey, ThreadManager.redis_index)	
end

function ThreadManager.MerchantSelfAddrLock(index)
	local redisKey = "lock_merchantselfaddr_"..index
	while true do
		local ok = redisItem:setnx(redisKey, "1", ThreadManager.redis_index)
		if ok == true then
			redisItem:expire(redisKey, 30, ThreadManager.redis_index) --设置超时是3秒
			break
		end
	end	
end

function ThreadManager.MerchantSelfAddrUnLock(index)
	local redisKey = "lock_merchantselfaddr_"..index
	redisItem:del(redisKey, ThreadManager.redis_index)	
end


function ThreadManager.UserCoinPayAmountLock(index)
	local redisKey = "lock_userCoinPayAmount_"..index
	while true do
		local ok = redisItem:setnx(redisKey, "1", ThreadManager.redis_index)
		if ok == true then
			redisItem:expire(redisKey, 30, ThreadManager.redis_index) --设置超时是3秒
			break
		end
	end	
end

function ThreadManager.UserCoinPayAmountUnLock(index)
	local redisKey = "lock_userCoinPayAmount_"..index
	redisItem:del(redisKey, ThreadManager.redis_index)	
end