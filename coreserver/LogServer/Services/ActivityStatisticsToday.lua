module("ActivityStatisticsToday", package.seeall)

function work(buffer)
	
	LogModel.DelLiushuiRanking_today()
	
	local date = ""
	local tab = LogServer.GetTodayDate()
	date = tab.year.."-"..tab.month.."-"..tab.day
	local sqlCase = "select userid, achamount from log_playerdaily where achamount > 0 and dateid ='"..date.."' order by achamount desc limit 30" 
	mysqlLog:executeQuery(sqlCase)
	
	local rankList = st_human_pb.activityliushuiranking()
	for i = 1,100 do
		local sqlData = mysqlLog:fetch({})
		if sqlData == nil then
			break
		end	
		local pInfo = PlayerModel.GetPlayerInfo(sqlData[1])
		if pInfo ~= nil then
			rankList.ranking:append(i)
			rankList.userid:append(pInfo.userid)
			rankList.nickname:append(pInfo.nickname)
			rankList.jifen:append(sqlData[2])
		end
	end
	LogModel.SetLiushuiRanking_today(rankList)
end