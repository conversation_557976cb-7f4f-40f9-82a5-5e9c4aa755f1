LogServer = {}



function LogServer.Init()
	
end


function LogServer.ServerLoop()
	
	for k,v in pairs( LogDispatch.log_dispatch ) do
		
		local len = redisItem:llen( v, LogDispatch.redis_index )
		
		if tonumber(len) > 0 then
			processWork(k,len)
		end
	end	
end


function LogServer.UtilsLoop()
	--这一个线程是放在Utils实例中执行，所以是每秒都会执行。
	
	local tm = TimeUtils.GetTableTime()
	processWork("SqlUpdateSec","sec")
	
	if g_markTime.curr.hour == 23 and g_markTime.curr.min > 59 and g_markTime.curr.sec == 59 then
		local dayStr = g_markTime.curr.year.."-"..g_markTime.curr.month.."-"..g_markTime.curr.day
		processWork("UpdatePayDayDealCount",dayStr)   
		--结算子平台日服务费
		processWork("logplatformfee",dayStr)   
	end
	
	if g_markTime.curr.sec == 0 then
		processWork("CheckUserFund","0")   
	end
	
end


























