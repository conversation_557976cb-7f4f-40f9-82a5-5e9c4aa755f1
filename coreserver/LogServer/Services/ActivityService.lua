ActivityService = {}

function ActivityService.ServerLoop()
	
	local t = TimeUtils.GetTableTime()
	if tonumber(t.sec) == 0 then
		processWork("ActivityStatisticsToday", "Today")
	end
	
	if LogModel.GetLiushuiRanking_yesterday() == nil then
		processWork("ActivityStatisticsYesterday", "Yesterday")
	end
	
	if tonumber(t.hour) == 0 and tonumber(t.min) == 0 and tonumber(t.sec) == 0 then
		processWork("ActivityStatisticsYesterday", "Yesterday")
	end

end





