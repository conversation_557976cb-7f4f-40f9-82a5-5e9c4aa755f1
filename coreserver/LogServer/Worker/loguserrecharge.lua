module("loguserrecharge", package.seeall)

function work(buffer)
	--通知下注的这里，不需要加入锁，这里只是一个通知的过程
	--暂时不考虑分段来处理，后面看业务量的多少来觉得，是否需要用到limit
	--执行完毕
	
	local dayStr = TimeUtils.GetDayString()
	local dayWeek = TimeUtils.GetWeekString()
	
	local len = buffer
	for i = 1, len do
		
		local data = redisItem:rpop( LogDispatch.log_dispatch["loguserrecharge"], LogDispatch.redis_index )
		
		if data == nil then
			break
		end
		
		local dataArr = luajson.decode(data)
			
		local tAmount = 0		--托管钱包充值
		local tNum = 0			
		local pAmount = 0		--私人钱包充值
		local pNum = 0			
		if dataArr.isT == false then
			pAmount = dataArr.amount
			pNum = 1
		else
			tAmount = dataArr.amount
			tNum = 1
		end	
		
		LogModel.CheckSys("ALL") 
		local sqlCase = "update log_sys set recharge_amount=recharge_amount+"..dataArr.getAmount..", hosting_coin_num=hosting_coin_num+"..tNum..", hosting_coin_amount=hosting_coin_amount+"..tAmount
				..", recharge_coin_num=recharge_coin_num+"..pNum..", recharge_coin_amount=recharge_coin_amount+"..pAmount.." where channel='ALL'"
		mysqlItem:execute(sqlCase)
		
		LogModel.CheckSysDaily("ALL", dayStr) 
		local sqlCase = "update log_sys_daily set recharge_amount=recharge_amount+"..dataArr.getAmount..", hosting_coin_num=hosting_coin_num+"..tNum..", hosting_coin_amount=hosting_coin_amount+"..tAmount
				..", recharge_coin_num=recharge_coin_num+"..pNum..", recharge_coin_amount=recharge_coin_amount+"..pAmount.." where dateid='"..dayStr.."' and channel='ALL'"
		mysqlItem:execute(sqlCase)	
		
		LogModel.CheckSysWeek("ALL", dayWeek) 
		local sqlCase = "update log_sys_week set recharge_amount=recharge_amount+"..dataArr.getAmount..", hosting_coin_num=hosting_coin_num+"..tNum..", hosting_coin_amount=hosting_coin_amount+"..tAmount
				..", recharge_coin_num=recharge_coin_num+"..pNum..", recharge_coin_amount=recharge_coin_amount+"..pAmount.." where dateid='"..dayWeek.."' and channel='ALL'"
		mysqlItem:execute(sqlCase)
		
		LogModel.CheckSub("ALL")
		local sqlCase = "update log_sub set hosting_coin_num=hosting_coin_num+"..tNum..", hosting_coin_amount=hosting_coin_amount+"..tAmount
			..", recharge_coin_num=recharge_coin_num+"..pNum..", recharge_coin_amount=recharge_coin_amount+"..pAmount.." where platform_id='ALL'"
		mysqlItem:execute(sqlCase)
		
		LogModel.CheckSubDaily("ALL", dayStr)
		local sqlCase = "update log_sub_daily set hosting_coin_num=hosting_coin_num+"..tNum..", hosting_coin_amount=hosting_coin_amount+"..tAmount
			..", recharge_coin_num=recharge_coin_num+"..pNum..", recharge_coin_amount=recharge_coin_amount+"..pAmount.." where platform_id='ALL' and dateid='"..dayStr.."'"
		mysqlItem:execute(sqlCase)
		
		LogModel.CheckSubWeek("ALL", dayWeek)
		local sqlCase = "update log_sub_week set hosting_coin_num=hosting_coin_num+"..tNum..", hosting_coin_amount=hosting_coin_amount+"..tAmount
			..", recharge_coin_num=recharge_coin_num+"..pNum..", recharge_coin_amount=recharge_coin_amount+"..pAmount.." where platform_id='ALL' and dateid='"..dayWeek.."'"
		mysqlItem:execute(sqlCase)
		
		local uInfo = UserInfoModel.GetUserInfo(dataArr.userid)
		if uInfo ~= nil then
			LogModel.CheckUserDaily(uInfo.userid, uInfo.channel, dayStr) 
			
			LogModel.CheckSys(uInfo.channel) 
			local sqlCase = "update log_sys set recharge_amount=recharge_amount+"..dataArr.getAmount..", hosting_coin_num=hosting_coin_num+"..tNum..", hosting_coin_amount=hosting_coin_amount+"..tAmount
				..", recharge_coin_num=recharge_coin_num+"..pNum..", recharge_coin_amount=recharge_coin_amount+"..pAmount.." where channel='"..uInfo.channel.."'"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSysDaily(uInfo.channel, dayStr) 
			local sqlCase = "update log_sys_daily set recharge_amount=recharge_amount+"..dataArr.getAmount..", hosting_coin_num=hosting_coin_num+"..tNum..", hosting_coin_amount=hosting_coin_amount+"..tAmount
				..", recharge_coin_num=recharge_coin_num+"..pNum..", recharge_coin_amount=recharge_coin_amount+"..pAmount.." where dateid='"..dayStr.."' and channel='"..uInfo.channel.."'"
			mysqlItem:execute(sqlCase)	
			
			LogModel.CheckSysWeek(uInfo.channel, dayWeek) 
			local sqlCase = "update log_sys_week set recharge_amount=recharge_amount+"..dataArr.getAmount..", hosting_coin_num=hosting_coin_num+"..tNum..", hosting_coin_amount=hosting_coin_amount+"..tAmount
				..", recharge_coin_num=recharge_coin_num+"..pNum..", recharge_coin_amount=recharge_coin_amount+"..pAmount.." where dateid='"..dayWeek.."' and channel='"..uInfo.channel.."'"
			mysqlItem:execute(sqlCase)	
			
			
			local sqlCase = "update log_user set recharge_amount=recharge_amount+"..dataArr.getAmount.." where userid="..uInfo.userid
			mysqlItem:execute(sqlCase)
			local sqlCase = "update log_user_daily set recharge_amount=recharge_amount+"..dataArr.getAmount.." where id > 0 and userid="..uInfo.userid.." and dateid='"..dayStr.."'"
			mysqlItem:execute(sqlCase)	
			
			LogModel.CheckSub(uInfo.platformid)
			local sqlCase = "update log_sub set hosting_coin_num=hosting_coin_num+"..tNum..", hosting_coin_amount=hosting_coin_amount+"..tAmount
				..", recharge_coin_num=recharge_coin_num+"..pNum..", recharge_coin_amount=recharge_coin_amount+"..pAmount.." where platform_id='"..uInfo.platformid.."'"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSubDaily(uInfo.platformid, dayStr)
			local sqlCase = "update log_sub_daily set hosting_coin_num=hosting_coin_num+"..tNum..", hosting_coin_amount=hosting_coin_amount+"..tAmount
				..", recharge_coin_num=recharge_coin_num+"..pNum..", recharge_coin_amount=recharge_coin_amount+"..pAmount.." where platform_id='"..uInfo.platformid.."' and dateid='"..dayStr.."'"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSubWeek(uInfo.platformid, dayWeek)
			local sqlCase = "update log_sub_week set hosting_coin_num=hosting_coin_num+"..tNum..", hosting_coin_amount=hosting_coin_amount+"..tAmount
				..", recharge_coin_num=recharge_coin_num+"..pNum..", recharge_coin_amount=recharge_coin_amount+"..pAmount.." where platform_id='"..uInfo.platformid.."' and dateid='"..dayWeek.."'"
			mysqlItem:execute(sqlCase)
		end
	end	
end




