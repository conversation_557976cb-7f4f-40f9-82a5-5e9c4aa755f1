module("logteambalance", package.seeall)

function work(buffer)
	--通知下注的这里，不需要加入锁，这里只是一个通知的过程
	--暂时不考虑分段来处理，后面看业务量的多少来觉得，是否需要用到limit
	--执行完毕
	
	local dayStr = TimeUtils.GetDayString()
	
	--统计出售
	local function AddTeamBalance(userID, coinType, count)
		local uInfo = UserInfoModel.GetUserInfo(userID)
		if uInfo == nil then
			return 
		end
		
		
		LogModel.CheckUserDaily(uInfo.userid, uInfo.channel, dayStr) 
		if coinType == 1 then
			--数字货币
			local sqlCase = "update log_user set team_balance_amount=team_balance_amount+"..count.." where userid="..uInfo.userid
			mysqlItem:execute(sqlCase)
		else
			--法币
			local sqlCase = "update log_user set team_balance_money=team_balance_money+"..count.." where userid="..uInfo.userid
			mysqlItem:execute(sqlCase)
		end
		
		AddTeamBalance(uInfo.agent, coinType, count)
	end
	
	
	local len = buffer
	for i = 1, len do
		
		local data = redisItem:rpop( LogDispatch.log_dispatch["logteambalance"], LogDispatch.redis_index )
		if data == nil then
			break
		end
		
		local dataArr = luajson.decode(data)
		
		AddTeamBalance(dataArr.userid, dataArr.coinType, dataArr.count)

	end	
end




