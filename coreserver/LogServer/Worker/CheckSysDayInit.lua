--没天凌晨的时候，这个线程来处理分佣的工作。

--在每天的11点50分以上，每一分钟就会调用该结构，检查对应的表是否存在，如果不存在，那么就插入到数据库中
--每次系统启动的时候，就会调用该函数，检查对应的表是否存在

--首先是每日的，然后是每周的，最后是每月的

module("CheckSysDayInit", package.seeall)

function work(buffer)
		
	--[[
	local sqlCase = "select channel from du_channel where state=1"
	mysqlItem:executeQuery(sqlCase)
	
	local channelList = {}
	for i = 1, 1000 do
		local sqlData = mysqlItem:fetch()
		if sqlData == nil then
			break
		end
		table.insert(channelList, sqlData)
	end
	]]
end




