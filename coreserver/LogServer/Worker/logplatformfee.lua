module("logplatformfee", package.seeall)

function work(buffer)
	--结算子平台日服务费
	
	local sqlCase = "select id,withdraw_rate_list,behalf_rate_list,recharge_rate_list,take_coin_rate,close_rate  from dy_subsystem_info"
	mysqlItem:executeQuery(sqlCase)
	local subSystemRateList = {}
	while true do
		local sqlDate = mysqlItem:fetch({})
		if sqlDate == nil then
			break
		end
		
		local tmp = {}
		tmp['1'] =  luajson.decode((sqlDate[2] == "") and "{}" or sqlDate[2])
		tmp['2'] =  luajson.decode((sqlDate[3] == "") and "{}" or sqlDate[3])
		tmp['3'] =  luajson.decode((sqlDate[4] == "") and "{}" or sqlDate[4])
		local tmp1 = {}
		tmp1['0'] = sqlDate[5]
		tmp['4'] =  tmp1
		local tmp2 = {}
		tmp2['0'] = sqlDate[6]
		tmp['5'] =  tmp2
		subSystemRateList[tostring(sqlDate[1])] = tmp
	end
	
	--拉出所有的当天记录
	local sqlCase = "select id,platform_id,deal_type,payid,deal_count from log_subsystem_daily where dateid='"..buffer.."'"
	mysqlItem:executeQuery(sqlCase)
	local subSystemList = {}
	while true do
		local sqlDate = mysqlItem:fetch({})
		if sqlDate == nil then
			break
		end
		
		local tmp = {}
		tmp["id"] = tonumber(sqlDate[1])
		tmp["platform_id"] = tonumber(sqlDate[2])
		tmp["deal_type"] = tonumber(sqlDate[3])
		tmp["payid"] = tonumber(sqlDate[4])
		tmp["deal_count"] = tonumber(sqlDate[5])
		
		table.insert(subSystemList, tmp)
	end
	
	for k,v in ipairs(subSystemList) do 
		local rate = subSystemRateList[tostring(v.platform_id)][tostring(v.deal_type)][tostring(v.payid)] 
		rate = tonumber(rate) or 0
		local fee = v.deal_count * rate
		
		local sqlCase = "update log_subsystem_daily set fee="..fee..", fee_rate="..rate.." where id="..v.id
		mysqlItem:execute(sqlCase)
	end
end
