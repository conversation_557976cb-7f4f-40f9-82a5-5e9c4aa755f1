
module("loguserpumpcount", package.seeall)

function work(buffer)
	
	for i=1,tonumber(buffer) do
		
		local data = redisItem:rpop( LogDispatch.log_dispatch["loguserpumpcount"], LogDispatch.redis_index )
		if data == nil then
			break
		end
		local dataArr = luajson.decode(data)
		local orderID = dataArr.orderid
		local info = CustomerOrderModel.GetCustomerOrderInfo(orderID)
		if info ~= nil then
			if info.dealtype == 100 then
				commissionMode100(info)
			elseif info.dealtype == 200 then
				commissionMode200(info)
			elseif info.dealtype == 300 then
				commissionMode300(info)
			end
		end
	end
end

--分佣 
function  AddToAgentAch(userid, feeRate, orderInfo, allReturnMoney, rateType,level,dayStr, subUser, dealType, weekStr)
	
	local uInfo = UserInfoModel.GetUserInfo(userid)
	if uInfo == nil then
		return allReturnMoney
	end
	
	if orderInfo.commissionfee - allReturnMoney <= 0 then
		return allReturnMoney
	end
	
	local agentLevel = AgentAward.GetAgentLevel(uInfo.channel)
	if agentLevel > 0 and level > agentLevel then
		return allReturnMoney
	end
	
	local newFeeRate = UserInfoModel.getUserFeeRate(uInfo.userid, orderInfo.paytype, rateType)
	newFeeRate = newFeeRate == 0 and feeRate or newFeeRate
	
	if math.floor(uInfo.usertype/100) == 2 then
		if feeRate > newFeeRate then
			newFeeRate = feeRate
		end
	elseif math.floor(uInfo.usertype/100) == 3 then
		if feeRate < newFeeRate then
			newFeeRate = feeRate
		end
	end
	local rate = math.abs(feeRate - newFeeRate) 
	--费率差就是这个用户能拿到的奖励
	local commission =  orderInfo.amount * rate
	--不能大于分剩下的
	commission = commission < (orderInfo.fee - allReturnMoney) and commission or (orderInfo.fee - allReturnMoney)
	--先直接加到用户的余额中
	local sysPrice = CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.deal_buy, g_marketDefine.currency_type.USDT, true, uInfo.userid)
	UserInfoModel.AddErcUsdtAmount(uInfo, commission, orderInfo.orderID,orderInfo.orderType, "分佣，增加资金",g_humanDefine.fund_details.type_fee,sysPrice*commission)
	UserInfoModel.SendErcUsdtAmount(uInfo)
	allReturnMoney = allReturnMoney + commission
	
	LogDispatch.teamIncome(uInfo.userid, commission)

	--统计佣金
	LogModel.CheckUserDaily(uInfo.userid, uInfo.channel, dayStr) 
	
	local tmp = "personal_income_sell=personal_income_sell+"
	if dealType == g_marketDefine.deal_buy then
		tmp = "personal_income_buy=personal_income_buy+"
	
	end
	
	local sqlCase = "update log_user set reward_amount=reward_amount+"..commission..", "..tmp..commission.." where userid="..uInfo.userid
	mysqlItem:execute(sqlCase)
		
	local sqlCase = "update log_user_daily set reward_amount=reward_amount+"..commission..", "..tmp..commission.." where userid="..uInfo.userid.." and dateid='"..dayStr.."'"
	mysqlItem:execute(sqlCase)
		
		
	local dealType = 0
	if orderInfo.dealType == 100 then
		dealType = 4
	elseif orderInfo.dealType == 200 then
		dealType = 0
	elseif orderInfo.dealType == 300 then
		dealType = 1
	end

	local sqlCase = "insert into log_commission_details(order_id,order_type,currency_type,source_userid,source_nick,target_userid,target_nick,amount,"
		.."dea_price,money_count,free_rate,free_count,commission_rate,commission_count,platform_id) values("..orderInfo.orderID..","..dealType..",2003,"
		..orderInfo.fromUser..",'"..orderInfo.fromNick.."',"..uInfo.userid..",'"..uInfo.nickname.."',"..orderInfo.amount..","..orderInfo.price..","
		..orderInfo.money..","..orderInfo.rate..","..orderInfo.fee..","..rate..","..commission..","..orderInfo.platformid..")"
	mysqlItem:execute(sqlCase)
	
	
	LogModel.CheckPayTypeDaily(uInfo.userid, uInfo.channel, dayStr, orderInfo.paytype) 
	local sqlCase = "update log_paytype_daily set fee_count=fee_count+"..commission.." where userid="..uInfo.userid
	.." and dateid='"..dayStr.."' and paytype="..orderInfo.paytype
	mysqlItem:execute(sqlCase)
	LogModel.CheckPayType(uInfo.userid, uInfo.channel, orderInfo.paytype) 
	local sqlCase = "update log_paytype set fee_count=fee_count+"..commission.." where userid="..uInfo.userid
	.." and paytype="..orderInfo.paytype
	mysqlItem:execute(sqlCase)
	
	local subInfo = UserInfoModel.GetUserInfo(subUser)
	if subInfo ~= nil then
		LogModel.CheckPayTypeDaily(subInfo.userid, subInfo.channel, dayStr, orderInfo.paytype) 
		local sqlCase = "update log_paytype_daily set agen_fee_count=agen_fee_count+"..commission.." where userid="..subInfo.userid
		.." and dateid='"..dayStr.."' and paytype="..orderInfo.paytype
		mysqlItem:execute(sqlCase)
		LogModel.CheckPayType(subInfo.userid, subInfo.channel, orderInfo.paytype) 
		local sqlCase = "update log_paytype set agen_fee_count=agen_fee_count+"..commission.." where userid="..subInfo.userid
		.." and paytype="..orderInfo.paytype
		mysqlItem:execute(sqlCase)
	end
	
	level = level + 1 
	
	 return  AddToAgentAch(uInfo.agent, newFeeRate, orderInfo, allReturnMoney, rateType,level,dayStr,uInfo.userid, weekStr)
	
end


function commissionMode100(info)
	local dayStr = TimeUtils.GetDayString()
	local weekStr = TimeUtils.GetWeekString()
	local freeCount = 0
	local orderInfo = {}
	orderInfo["orderType"] = tonumber(info.type)
	orderInfo["orderID"] = tonumber(info.dealid)
	orderInfo["money"] = tonumber(info.money)
	orderInfo["amount"] = tonumber(info.amount)
	orderInfo["rate"] = tonumber(info.feerate)
	orderInfo["fee"] = tonumber(info.fee)
	orderInfo["price"] = tonumber(info.price)
	orderInfo["fromUser"] = tonumber(info.customeruserid)
	orderInfo["fromNick"] = info.customerusernickname
	orderInfo["commissionfee"] = tonumber(info.buyfee)
	orderInfo["paytype"] = tonumber(info.payidlist)
	orderInfo["dealType"] = info.dealtype
	orderInfo["platformid"] = 0
	local vInfo = UserInfoModel.GetUserInfo(info.vendoruserid)
	if vInfo ~= nil then 
		orderInfo["platformid"] = vInfo.platformid
		freeCount = AddToAgentAch(vInfo.userid, 0, orderInfo, freeCount, g_humanDefine.sell_comm_rate, 0, dayStr, 0, g_marketDefine.deal_sell, weekStr)
		LogModel.CheckSys(vInfo.channel) 
		local sqlCase = "update log_sys set commission_amount=commission_amount+"..freeCount.." where channel='"..vInfo.channel.."'"
		mysqlItem:execute(sqlCase)
		
		LogModel.CheckSysDaily(vInfo.channel, dayStr) 
		local sqlCase = "update log_sys_daily set commission_amount=commission_amount+"..freeCount.." where channel='"..vInfo.channel.."' and dateid='"..dayStr.."'"
		mysqlItem:execute(sqlCase)
		
		LogModel.CheckSysWeek(vInfo.channel, weekStr) 
		local sqlCase = "update log_sys_week set commission_amount=commission_amount+"..freeCount.." where channel='"..vInfo.channel.."' and dateid='"..weekStr.."'"
		mysqlItem:execute(sqlCase)
	end
	
	--如果是UTokan的渠道需要分0.008给购买方的直属上级
	local cInfo = UserInfoModel.GetUserInfo(info.customeruserid)
	if cInfo ~=  nil and cInfo.channel == "UTO" then
		
		local aInfo = UserInfoModel.GetUserInfo(cInfo.agent)
		if aInfo ~= nil then
		
			local commission =  orderInfo.amount * 0.008
			--不能大于分剩下的
			commission = commission < (orderInfo.fee - freeCount) and commission or (orderInfo.fee - freeCount)
			--先直接加到用户的余额中
			local sysPrice = CoinInfoService.Erc20USDTPrice(aInfo.channel, g_marketDefine.deal_buy, g_marketDefine.currency_type.USDT, true, aInfo.userid)
			UserInfoModel.AddErcUsdtAmount(aInfo, commission, orderInfo.orderID,orderInfo.orderType, "特色渠道分佣，增加资金",g_humanDefine.fund_details.type_fee,sysPrice*commission)
			UserInfoModel.SendErcUsdtAmount(aInfo)
			freeCount = freeCount + commission

			--统计佣金
			LogModel.CheckUserDaily(aInfo.userid, aInfo.channel, dayStr) 
			local sqlCase = "update log_user set reward_amount=reward_amount+"..commission.." where userid="..aInfo.userid
			mysqlItem:execute(sqlCase)
				
			local sqlCase = "update log_user_daily set reward_amount=reward_amount+"..commission.." where userid="..aInfo.userid.." and dateid='"..dayStr.."'"
			mysqlItem:execute(sqlCase)
				
			local sqlCase = "insert into log_commission_details(order_id,order_type,currency_type,source_userid,source_nick,target_userid,target_nick,amount,"
				.."dea_price,money_count,free_rate,free_count,commission_rate,commission_count,platform_id) values("..orderInfo.orderID..",4,2003,"
				..orderInfo.fromUser..",'"..orderInfo.fromNick.."',"..aInfo.userid..",'"..aInfo.nickname.."',"..orderInfo.amount..","..orderInfo.price..","
				..orderInfo.money..","..orderInfo.rate..","..orderInfo.fee..",0.008,"..commission..","..cInfo.platformid..")"
			mysqlItem:execute(sqlCase)
	
		end
	end
	
	
	local sysFee = info.fee - freeCount
	LogModel.CheckSys("ALL") 
	local sqlCase = "update log_sys set commission_amount=commission_amount+"..freeCount..", sys_commission_amount=sys_commission_amount+"..sysFee
				.." where channel='ALL'"
	mysqlItem:execute(sqlCase)
	
	LogModel.CheckSysDaily("ALL", dayStr) 
	local sqlCase = "update log_sys_daily set commission_amount=commission_amount+"..freeCount..", sys_commission_amount=sys_commission_amount+"..sysFee
				.." where channel='ALL' and dateid='"..dayStr.."'"
	mysqlItem:execute(sqlCase)
	
	LogModel.CheckSysWeek("ALL", weekStr) 
	local sqlCase = "update log_sys_week set commission_amount=commission_amount+"..freeCount..", sys_commission_amount=sys_commission_amount+"..sysFee
				.." where channel='ALL' and dateid='"..weekStr.."'"
	mysqlItem:execute(sqlCase)
	
	local sysInfo = UserInfoModel.GetSysUserInfo(orderInfo.platformid)
	if sysInfo ~= nil then
		local allPrice = CoinInfoService.Erc20USDTPrice("ALL", g_marketDefine.deal_buy, g_marketDefine.currency_type.USDT, false, 0)
		UserInfoModel.AddErcUsdtAmount(sysInfo, sysFee, orderInfo["orderID"], 0, "系统分佣， 归入系统用户", g_humanDefine.fund_details.type_fee,allPrice*sysFee)
		local sqlCase = "insert into log_commission_details(order_id,order_type,currency_type,source_userid,source_nick,target_userid,target_nick,amount,"
		.."dea_price,money_count,free_rate,free_count,commission_rate,commission_count,platform_id) values("..orderInfo.orderID..",4,2003,"
		..orderInfo.fromUser..",'"..orderInfo.fromNick.."',"..sysInfo.userid..",'"..sysInfo.nickname.."',"..orderInfo.amount..","..orderInfo.price..","
		..orderInfo.money..","..orderInfo.rate..","..orderInfo.fee..",0,"..sysFee..","..orderInfo.platformid..")"
		mysqlItem:execute(sqlCase)
		
		LogDispatch.sysIncome(orderInfo.fromUser, sysFee)
	end
		
end

function commissionMode300(info)
	local dayStr = TimeUtils.GetDayString()
	local weekStr = TimeUtils.GetWeekString()
	local freeCount = 0
	local freeCount2 = 0
	local orderInfo = {}
	orderInfo["orderType"] = info.type
	orderInfo["orderID"] = info.dealid
	orderInfo["money"] = info.money
	orderInfo["amount"] = info.amount
	orderInfo["rate"] = info.feerate
	orderInfo["fee"] = info.fee
	orderInfo["price"] = info.price
	orderInfo["fromUser"] = info.vendoruserid
	orderInfo["fromNick"] = info.vendorusernickname
	orderInfo["commissionfee"] = info.sellfee
	orderInfo["paytype"] = tonumber(info.payidlist)
	orderInfo["dealType"] = info.dealtype
	local tInfo = UserInfoModel.GetUserInfo(orderInfo.fromUser)
	orderInfo["platformid"] = tInfo.platformid
	local vInfo = UserInfoModel.GetUserInfo(info.vendoruserid)
	if vInfo ~= nil then
		local rateType = info.withdrawtype == 0 and g_humanDefine.sell_fee_rate or g_humanDefine.behalf_sell_fee_rate 
		local rate = UserInfoModel.getUserFeeRate(vInfo.userid, orderInfo.paytype, rateType)
		freeCount = AddToAgentAch(vInfo.agent, rate, orderInfo, freeCount, rateType, 0, dayStr, vInfo.userid, g_marketDefine.deal_sell,weekStr)
		freeCount2 = freeCount
		LogModel.CheckSys(vInfo.channel) 
		local sqlCase = "update log_sys set commission_amount=commission_amount+"..freeCount.." where channel='"..vInfo.channel.."'"
		mysqlItem:execute(sqlCase)
		
		LogModel.CheckSysDaily(vInfo.channel, dayStr) 
		local sqlCase = "update log_sys_daily set commission_amount=commission_amount+"..freeCount.." where channel='"..vInfo.channel.."' and dateid='"..dayStr.."'"
		mysqlItem:execute(sqlCase)
		
		LogModel.CheckSysWeek(vInfo.channel, weekStr) 
		local sqlCase = "update log_sys_week set commission_amount=commission_amount+"..freeCount.." where channel='"..vInfo.channel.."' and dateid='"..weekStr.."'"
		mysqlItem:execute(sqlCase)

	end
	
	
	local cInfo = UserInfoModel.GetUserInfo(info.customeruserid)
	if cInfo ~= nil then
		local rateType = info.withdrawtype == 0 and g_humanDefine.buy_comm_rate or g_humanDefine.behalf_buy_comm_rate
		freeCount = AddToAgentAch(cInfo.userid, 0, orderInfo, freeCount, rateType, 0,dayStr, 0, g_marketDefine.deal_buy, weekStr)
		local free = freeCount - freeCount2
		LogModel.CheckSys(cInfo.channel) 
		local sqlCase = "update log_sys set commission_amount=commission_amount+"..free.." where channel='"..cInfo.channel.."'"
		mysqlItem:execute(sqlCase)

		LogModel.CheckSysDaily(cInfo.channel, dayStr) 
		local sqlCase = "update log_sys_daily set commission_amount=commission_amount+"..free.." where channel='"..cInfo.channel.."' and dateid='"..dayStr.."'"
		mysqlItem:execute(sqlCase)
		
		LogModel.CheckSysWeek(cInfo.channel, weekStr) 
		local sqlCase = "update log_sys_week set commission_amount=commission_amount+"..free.." where channel='"..cInfo.channel.."' and dateid='"..weekStr.."'"
		mysqlItem:execute(sqlCase)
	end
	
	local sysFee = info.fee - freeCount
	LogModel.CheckSys("ALL") 
	local sqlCase = "update log_sys set commission_amount=commission_amount+"..freeCount..", sys_commission_amount=sys_commission_amount+"..sysFee
				.." where channel='ALL'"
	mysqlItem:execute(sqlCase)

	LogModel.CheckSysDaily("ALL", dayStr) 
	local sqlCase = "update log_sys_daily set commission_amount=commission_amount+"..freeCount..", sys_commission_amount=sys_commission_amount+"..sysFee
				.." where channel='ALL' and dateid='"..dayStr.."'"
				
	LogModel.CheckSysWeek("ALL", weekStr) 
	local sqlCase = "update log_sys_week set commission_amount=commission_amount+"..freeCount..", sys_commission_amount=sys_commission_amount+"..sysFee
				.." where channel='ALL' and dateid='"..weekStr.."'"
	mysqlItem:execute(sqlCase)
	local sysInfo = UserInfoModel.GetSysUserInfo(orderInfo.platformid)
	if sysInfo ~= nil then
		local allPrice = CoinInfoService.Erc20USDTPrice("ALL", g_marketDefine.deal_buy, g_marketDefine.currency_type.USDT, false, 0)
		UserInfoModel.AddErcUsdtAmount(sysInfo, sysFee, orderInfo["orderID"], 0, "系统分佣， 归入系统用户", g_humanDefine.fund_details.type_fee,allPrice*sysFee)
		local sqlCase = "insert into log_commission_details(order_id,order_type,currency_type,source_userid,source_nick,target_userid,target_nick,amount,"
		.."dea_price,money_count,free_rate,free_count,commission_rate,commission_count,platform_id) values("..orderInfo.orderID..",1,2003,"
		..orderInfo.fromUser..",'"..orderInfo.fromNick.."',"..sysInfo.userid..",'"..sysInfo.nickname.."',"..orderInfo.amount..","..orderInfo.price..","
		..orderInfo.money..","..orderInfo.rate..","..orderInfo.fee..",0,"..sysFee..","..orderInfo.platformid..")"
		mysqlItem:execute(sqlCase)
		LogDispatch.sysIncome(orderInfo.fromUser, sysFee)
	end
	
end

function commissionMode200(info)
	local dayStr = TimeUtils.GetDayString()
	local weekStr = TimeUtils.GetWeekString()
	local freeCount = 0
	local freeCount2 = 0
	local orderInfo = {}
	orderInfo["orderType"] = info.type
	orderInfo["orderID"] = info.dealid
	orderInfo["money"] = info.money
	orderInfo["amount"] = info.amount
	orderInfo["rate"] = info.feerate
	orderInfo["fee"] = info.fee
	orderInfo["price"] = info.price
	orderInfo["fromUser"] = info.customeruserid
	orderInfo["fromNick"] = info.customerusernickname
	orderInfo["commissionfee"] = info.buyfee
	orderInfo["paytype"] = tonumber(info.payidlist)
	orderInfo["dealType"] = info.dealtype
	local tInfo = UserInfoModel.GetUserInfo(orderInfo.fromUser)
	orderInfo["platformid"] = tInfo.platformid
	local cInfo = UserInfoModel.GetUserInfo(info.customeruserid)
	if cInfo ~= nil then
		--出续费方费率
		local rate = UserInfoModel.getUserFeeRate(cInfo.userid, orderInfo.paytype, g_humanDefine.buy_fee_rate)
		freeCount = AddToAgentAch(cInfo.agent, rate, orderInfo, freeCount, g_humanDefine.buy_fee_rate, 0,dayStr, cInfo.userid,g_marketDefine.deal_buy, weekStr)
		freeCount2 = freeCount
		LogModel.CheckSys(cInfo.channel) 
		local sqlCase = "update log_sys set commission_amount=commission_amount+"..freeCount.." where channel='"..cInfo.channel.."'"
		mysqlItem:execute(sqlCase)
		
		LogModel.CheckSysDaily(cInfo.channel, dayStr) 
		local sqlCase = "update log_sys_daily set commission_amount=commission_amount+"..freeCount.." where channel='"..cInfo.channel.."' and dateid='"..dayStr.."'"
		mysqlItem:execute(sqlCase)
		
		LogModel.CheckSysWeek(cInfo.channel, weekStr) 
		local sqlCase = "update log_sys_week set commission_amount=commission_amount+"..freeCount.." where channel='"..cInfo.channel.."' and dateid='"..weekStr.."'"
		mysqlItem:execute(sqlCase)
	end
	
	--收手续费方分佣
	
	local vInfo = UserInfoModel.GetUserInfo(info.vendoruserid)
		if vInfo ~= nil then
			freeCount = AddToAgentAch(vInfo.userid, 0, orderInfo, freeCount, g_humanDefine.sell_comm_rate, 0,dayStr, 0,g_marketDefine.deal_sell, weekStr)
			local free = freeCount - freeCount2
			LogModel.CheckSys(vInfo.channel)
			local sqlCase = "update log_sys set commission_amount=commission_amount+"..free.." where channel='"..vInfo.channel.."'"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSysDaily(vInfo.channel, dayStr) 
			local sqlCase = "update log_sys_daily set commission_amount=commission_amount+"..free.." where channel='"..vInfo.channel.."' and dateid='"..dayStr.."'"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSysWeek(vInfo.channel, weekStr) 
			local sqlCase = "update log_sys_week set commission_amount=commission_amount+"..free.." where channel='"..vInfo.channel.."' and dateid='"..weekStr.."'"
			mysqlItem:execute(sqlCase)
		end
	
	local sysFee = orderInfo.fee - freeCount
	LogModel.CheckSys("ALL") 
	local sqlCase = "update log_sys set commission_amount=commission_amount+"..freeCount..", sys_commission_amount=sys_commission_amount+"..sysFee
				.." where channel='ALL'"
	mysqlItem:execute(sqlCase)
	
	LogModel.CheckSysDaily("ALL", dayStr) 
	local sqlCase = "update log_sys_daily set commission_amount=commission_amount+"..freeCount..", sys_commission_amount=sys_commission_amount+"..sysFee
				.." where channel='ALL' and dateid='"..dayStr.."'"
	mysqlItem:execute(sqlCase)
	
	LogModel.CheckSysWeek("ALL", weekStr) 
	local sqlCase = "update log_sys_week set commission_amount=commission_amount+"..freeCount..", sys_commission_amount=sys_commission_amount+"..sysFee
				.." where channel='ALL' and dateid='"..weekStr.."'"
	mysqlItem:execute(sqlCase)
	
	local sysInfo = UserInfoModel.GetSysUserInfo(orderInfo.platformid)
	if sysInfo ~= nil then
		local allPrice = CoinInfoService.Erc20USDTPrice("ALL", g_marketDefine.deal_buy, g_marketDefine.currency_type.USDT, false, 0)
		UserInfoModel.AddErcUsdtAmount(sysInfo, sysFee, orderInfo["orderID"], 0, "系统分佣， 归入系统用户", g_humanDefine.fund_details.type_fee,allPrice*sysFee)
		local sqlCase = "insert into log_commission_details(order_id,order_type,currency_type,source_userid,source_nick,target_userid,target_nick,amount,"
		.."dea_price,money_count,free_rate,free_count,commission_rate,commission_count,platform_id) values("..orderInfo.orderID..",0,2003,"
		..orderInfo.fromUser..",'"..orderInfo.fromNick.."',"..sysInfo.userid..",'"..sysInfo.nickname.."',"..orderInfo.amount..","..orderInfo.price..","
		..orderInfo.money..","..orderInfo.rate..","..orderInfo.fee..",0,"..sysFee..","..orderInfo.platformid..")"
		mysqlItem:execute(sqlCase)
		LogDispatch.sysIncome(orderInfo.fromUser, sysFee)
	end
	
end

