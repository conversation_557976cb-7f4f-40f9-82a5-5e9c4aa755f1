module("loguserdealordercount", package.seeall)

function work(buffer)
	--通知下注的这里，不需要加入锁，这里只是一个通知的过程
	--暂时不考虑分段来处理，后面看业务量的多少来觉得，是否需要用到limit
	--执行完毕
	
	local dayStr = TimeUtils.GetDayString()
	local weekStr = TimeUtils.GetWeekString()
	
	function AddOrderCount(userid, orderType)
		
		local pInfo = UserInfoModel.GetUserInfo(userid)
		if pInfo == nil then
			return 
		end
		local tmp = "team_count_sell=team_count_sell+"
		if orderType == g_marketDefine.deal_buy then
			tmp = "team_count_buy=team_count_buy+"
		end
		
		local sqlCase = "update log_user set "..tmp.."1 where userid="..pInfo.userid
		mysqlItem:execute(sqlCase)

		local sqlCase = "update log_user_daily set "..tmp.."1 where id > 0 and userid="..pInfo.userid.." and dateid='"..dayStr.."'"
		mysqlItem:execute(sqlCase)	
		
		AddOrderCount(pInfo.agent, orderType)
		
	end
	
	local len = buffer
	for i = 1, len do
		
		local data = redisItem:rpop( LogDispatch.log_dispatch["loguserdealordercount"], LogDispatch.redis_index )
		
		if data == nil then
			break
		end
		
		local dataArr = luajson.decode(data)
		LogModel.CheckSys("ALL") 
		local sqlCase = "update log_sys set total_count_buy=total_count_buy+1,total_count_sell=total_count_sell+1 where channel='ALL'"
		mysqlItem:execute(sqlCase)
		
		LogModel.CheckSysDaily("ALL", dayStr) 
		local sqlCase = "update log_sys_daily set total_count_buy=total_count_buy+1,total_count_sell=total_count_sell+1 where dateid='"..dayStr.."' and channel='ALL'"
		mysqlItem:execute(sqlCase)	
		
		LogModel.CheckSysWeek("ALL", weekStr) 
		local sqlCase = "update log_sys_week set total_count_buy=total_count_buy+1,total_count_sell=total_count_sell+1 where dateid='"..weekStr.."' and channel='ALL'"
		mysqlItem:execute(sqlCase)	
		
		
		local sellInfo = UserInfoModel.GetUserInfo(dataArr.sell_user_id)
		if sellInfo ~= nil then
			LogModel.CheckUserDaily(sellInfo.userid, sellInfo.channel, dayStr) 
			
			LogModel.CheckSys(sellInfo.channel) 
			local sqlCase = "update log_sys set total_count_sell=total_count_sell+1 where channel='"..sellInfo.channel.."'"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSysDaily(sellInfo.channel, dayStr) 
			local sqlCase = "update log_sys_daily set total_count_sell=total_count_sell+1 where dateid='"..dayStr.."' and channel='"..sellInfo.channel.."'"
			mysqlItem:execute(sqlCase)	
			
			LogModel.CheckSysWeek(sellInfo.channel, weekStr) 
			local sqlCase = "update log_sys_week set total_count_sell=total_count_sell+1 where dateid='"..weekStr.."' and channel='"..sellInfo.channel.."'"
			mysqlItem:execute(sqlCase)
			
			local sqlCase = "update log_user set total_count_sell=total_count_sell+1 where userid="..sellInfo.userid
			mysqlItem:execute(sqlCase)

			local sqlCase = "update log_user_daily set total_count_sell=total_count_sell+1 where id > 0 and userid="..sellInfo.userid.." and dateid='"..dayStr.."'"
			mysqlItem:execute(sqlCase)		

			AddOrderCount(sellInfo.userid, g_marketDefine.deal_sell)
		end

		local buyInfo = UserInfoModel.GetUserInfo(dataArr.buy_user_id)
		if buyInfo ~= nil then
			LogModel.CheckUserDaily(buyInfo.userid, buyInfo.channel, dayStr) 	
			
			LogModel.CheckSys(buyInfo.channel) 
			local sqlCase = "update log_sys set total_count_buy=total_count_buy+1 where channel='"..buyInfo.channel.."'"
			mysqlItem:execute(sqlCase)

			LogModel.CheckSysDaily(buyInfo.channel, dayStr) 
			local sqlCase = "update log_sys_daily set total_count_buy=total_count_buy+1 where dateid='"..dayStr.."' and channel='"..buyInfo.channel.."'"
			mysqlItem:execute(sqlCase)	
			
			LogModel.CheckSysWeek(buyInfo.channel, weekStr) 
			local sqlCase = "update log_sys_week set total_count_buy=total_count_buy+1 where dateid='"..weekStr.."' and channel='"..buyInfo.channel.."'"
			mysqlItem:execute(sqlCase)	

			local sqlCase = "update log_user set total_count_buy=total_count_buy+1 where userid="..buyInfo.userid
			mysqlItem:execute(sqlCase)

			local sqlCase = "update log_user_daily set total_count_buy=total_count_buy+1  where id > 0 and userid="..buyInfo.userid.." and dateid='"..dayStr.."'"
			mysqlItem:execute(sqlCase)
			
			AddOrderCount(buyInfo.userid, g_marketDefine.deal_buy)
		end
		
		

	end	
end




