module("logteamincome", package.seeall)

function work(buffer)
	--通知下注的这里，不需要加入锁，这里只是一个通知的过程
	--暂时不考虑分段来处理，后面看业务量的多少来觉得，是否需要用到limit
	--执行完毕
	
	local dayStr = TimeUtils.GetDayString()
	
	--统计出售
	local function AddTeamIncome(userID, count)
		local uInfo = UserInfoModel.GetUserInfo(userID)
		if uInfo == nil then
			return 
		end
		
		
		LogModel.CheckUserDaily(uInfo.userid, uInfo.channel, dayStr) 
		
		local sqlCase = "update log_user set team_income_amount=team_income_amount+"..count.." where userid="..uInfo.userid
		mysqlItem:execute(sqlCase)
		
		local sqlCase = "update log_user_daily set team_income_amount=team_income_amount+"..count.." where userid="..uInfo.userid.." and  dateid='"..dayStr.."'"
		mysqlItem:execute(sqlCase)
		
		AddTeamIncome(uInfo.agent, count)
	end
	
	
	local len = buffer
	for i = 1, len do
		
		local data = redisItem:rpop( LogDispatch.log_dispatch["logteamincome"], LogDispatch.redis_index )
		if data == nil then
			break
		end
		
		local dataArr = luajson.decode(data)
		
		AddTeamIncome(dataArr.userid, dataArr.count)

	end	
end




