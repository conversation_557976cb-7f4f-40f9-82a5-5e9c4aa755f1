module("loguserextractcurrency", package.seeall)

function work(buffer)
	--通知下注的这里，不需要加入锁，这里只是一个通知的过程
	--暂时不考虑分段来处理，后面看业务量的多少来觉得，是否需要用到limit
	--执行完毕
	
	local dayStr = TimeUtils.GetDayString()
	local dayWeek = TimeUtils.GetWeekString()
	
	local len = buffer
	for i = 1, len do
		
		local data = redisItem:rpop( LogDispatch.log_dispatch["loguserextractcurrency"], LogDispatch.redis_index )
		
		if data == nil then
			break
		end
		
		local dataArr = luajson.decode(data)
		LogModel.CheckSys("ALL") 
		local sqlCase = "update log_sys set extract_amount=extract_amount+"..dataArr.amount..", extract_coin_num=extract_coin_num+1, extract_coin_amount=extract_coin_amount+"..dataArr.amount
			.." where channel='ALL'"
		mysqlItem:execute(sqlCase)
		
		LogModel.CheckSysDaily("ALL", dayStr) 
		local sqlCase = "update log_sys_daily set extract_amount=extract_amount+"..dataArr.amount..", extract_coin_num=extract_coin_num+1, extract_coin_amount=extract_coin_amount+"..dataArr.amount
			.." where dateid='"..dayStr.."' and channel='ALL'"
		mysqlItem:execute(sqlCase)	
		
		LogModel.CheckSysWeek("ALL", dayWeek) 
		local sqlCase = "update log_sys_week set extract_amount=extract_amount+"..dataArr.amount..", extract_coin_num=extract_coin_num+1, extract_coin_amount=extract_coin_amount+"..dataArr.amount
			.." where dateid='"..dayWeek.."' and channel='ALL'"
		mysqlItem:execute(sqlCase)	
		
		LogModel.CheckSub("ALL")
		local sqlCase = "update log_sub set extract_coin_num=extract_coin_num+1, extract_coin_amount=extract_coin_amount+"..dataArr.amount
			.." where platform_id='ALL'"
		mysqlItem:execute(sqlCase)
		
		LogModel.CheckSubDaily("ALL", dayStr)
		local sqlCase = "update log_sub_daily set extract_coin_num=extract_coin_num+1, extract_coin_amount=extract_coin_amount+"..dataArr.amount
			.." where platform_id='ALL' and dateid='"..dayStr.."'"
		mysqlItem:execute(sqlCase)
		
		LogModel.CheckSubWeek("ALL", dayWeek)
		local sqlCase = "update log_sub_week set extract_coin_num=extract_coin_num+1, extract_coin_amount=extract_coin_amount+"..dataArr.amount
			.." where platform_id='ALL' and dateid='"..dayWeek.."'"
		mysqlItem:execute(sqlCase)
		
		
		local uInfo = UserInfoModel.GetUserInfo(dataArr.userid)
		if uInfo ~= nil then
			LogModel.CheckUserDaily(uInfo.userid, uInfo.channel, dayStr) 
			
			LogModel.CheckSys(uInfo.channel) 
			local sqlCase = "update log_sys set extract_amount=extract_amount+"..dataArr.amount..", extract_coin_num=extract_coin_num+1, extract_coin_amount=extract_coin_amount+"..dataArr.amount
			.." where channel='"..uInfo.channel.."'"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSysDaily(uInfo.channel, dayStr) 
			local sqlCase = "update log_sys_daily set extract_amount=extract_amount+"..dataArr.amount..", extract_coin_num=extract_coin_num+1, extract_coin_amount=extract_coin_amount+"..dataArr.amount
			.." where dateid='"..dayStr.."' and channel='"..uInfo.channel.."'"
			mysqlItem:execute(sqlCase)

			LogModel.CheckSysWeek(uInfo.channel, dayWeek) 
			local sqlCase = "update log_sys_week set extract_amount=extract_amount+"..dataArr.amount..", extract_coin_num=extract_coin_num+1, extract_coin_amount=extract_coin_amount+"..dataArr.amount
			.." where dateid='"..dayWeek.."' and channel='"..uInfo.channel.."'"
			mysqlItem:execute(sqlCase)
			
			local sqlCase = "update log_user set extract_amount=extract_amount+"..dataArr.amount.." where userid="..uInfo.userid
			mysqlItem:execute(sqlCase)
			local sqlCase = "update log_user_daily set extract_amount=extract_amount+"..dataArr.amount.." where id > 0 and userid="..uInfo.userid.." and dateid='"..dayStr.."'"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSub(uInfo.platformid)
			local sqlCase = "update log_sub set extract_coin_num=extract_coin_num+1, extract_coin_amount=extract_coin_amount+"..dataArr.amount
				.." where platform_id='"..uInfo.platformid.."'"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSubDaily(uInfo.platformid, dayStr)
			local sqlCase = "update log_sub_daily set extract_coin_num=extract_coin_num+1, extract_coin_amount=extract_coin_amount+"..dataArr.amount
				.." where platform_id='"..uInfo.platformid.."' and dateid='"..dayStr.."'"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSubWeek(uInfo.platformid, dayWeek)
			local sqlCase = "update log_sub_week set extract_coin_num=extract_coin_num+1, extract_coin_amount=extract_coin_amount+"..dataArr.amount
				.." where platform_id='"..uInfo.platformid.."' and dateid='"..dayWeek.."'"
			mysqlItem:execute(sqlCase)
			
			
			if uInfo.platformid ~= 0 then
				local dealType = 4
				LogModel.CheckPlatformDaily(uInfo.platformid, dayStr, dealType, 0) 
			
				local sqlCase = "update log_subsystem_daily set deal_count=deal_count+1 where platform_id="
					..uInfo.platformid.." and dateid='"..dayStr.."' and deal_type="..dealType.." and payid=0"
				mysqlItem:execute(sqlCase)
			end
		end

	end	
end




