module("loguserpassordercount", package.seeall)

function work(buffer)
	--通知下注的这里，不需要加入锁，这里只是一个通知的过程
	--暂时不考虑分段来处理，后面看业务量的多少来觉得，是否需要用到limit
	--执行完毕
	
	local dayStr = TimeUtils.GetDayString()
	local dayWeek = TimeUtils.GetWeekString()
	
	--统计出售
	local function AddDealCount(userID, amount, payType, money, dealType)
		local uInfo = UserInfoModel.GetUserInfo(userID)
		if uInfo == nil then
			return 
		end
		LogModel.CheckPayTypeDaily(uInfo.userid, uInfo.channel, dayStr, payType) 
		local sqlCase = "update log_paytype_daily set tem_deal_count=tem_deal_count+"..amount.." where userid="..uInfo.userid
		.." and dateid='"..dayStr.."' and paytype="..payType
		mysqlItem:execute(sqlCase)
		LogModel.CheckPayType(uInfo.userid, uInfo.channel, payType) 
		local sqlCase = "update log_paytype set tem_deal_count=tem_deal_count+"..amount.." where userid="..uInfo.userid
		.." and paytype="..payType
		mysqlItem:execute(sqlCase)
		
		local rechargeAmount = 0
		local rechargeMoney = 0
		if dealType == 200 then
			rechargeAmount = amount
			rechargeMoney = money
		end
		
		local sqlCase = "update log_user set team_amount_sell=team_amount_sell+"..amount..", team_money_sell=team_money_sell+"..money
			..", team_complete_count_sell=team_complete_count_sell+1, team_recharge_amount=team_recharge_amount+"..rechargeAmount
			..", team_recharge_money=team_recharge_money+"..rechargeMoney.." where userid="..uInfo.userid
		mysqlItem:execute(sqlCase)

		local sqlCase = "update log_user_daily set team_amount_sell=team_amount_sell+"..amount..", team_money_sell=team_money_sell+"..money
			..", team_complete_count_sell=team_complete_count_sell+1, team_recharge_amount=team_recharge_amount+"..rechargeAmount
			..", team_recharge_money=team_recharge_money+"..rechargeMoney.." where id > 0 and userid="..uInfo.userid.." and dateid='"..dayStr.."'"
		mysqlItem:execute(sqlCase)	
		
		return  AddDealCount(uInfo.agent, amount, payType,money, dealType)
	end
	
	--统计购买量
	local function AddBuyCount(userID, amount, payType, money, dealType)
		local uInfo = UserInfoModel.GetUserInfo(userID)
		if uInfo == nil then
			return 
		end
		LogModel.CheckPayTypeDaily(uInfo.userid, uInfo.channel, dayStr, payType) 
		local sqlCase = "update log_paytype_daily set buy_count=buy_count+"..amount.." where userid="..uInfo.userid
		.." and dateid='"..dayStr.."' and paytype="..payType
		mysqlItem:execute(sqlCase)
		LogModel.CheckPayType(uInfo.userid, uInfo.channel, payType) 
		local sqlCase = "update log_paytype set buy_count=buy_count+"..amount.." where userid="..uInfo.userid
		.." and paytype="..payType
		mysqlItem:execute(sqlCase)
		
		local withdrawAmount = 0
		local withdrawMoney = 0
		if dealType == 300 then
			withdrawAmount = amount
			withdrawMoney = money
		end

		local sqlCase = "update log_user set team_amount_buy=team_amount_buy+"..amount..", team_money_buy=team_money_buy+"..money
			..", team_complete_count_buy=team_complete_count_buy+1, team_withdraw_amount=team_withdraw_amount+"..withdrawAmount
			..", team_withdraw_money=team_withdraw_money+"..withdrawMoney.." where userid="..uInfo.userid
		mysqlItem:execute(sqlCase)

		local sqlCase = "update log_user_daily set team_amount_buy=team_amount_buy+"..amount..", team_money_buy=team_money_buy+"..money
			..", team_complete_count_buy=team_complete_count_buy+1, team_withdraw_amount=team_withdraw_amount+"..withdrawAmount
			..", team_withdraw_money=team_withdraw_money+"..withdrawMoney.." where id > 0 and userid="..uInfo.userid.." and dateid='"..dayStr.."'"
		mysqlItem:execute(sqlCase)	
		
		return  AddBuyCount(uInfo.agent, amount, payType,money, dealType)
	end
	
	
	local len = buffer
	for i = 1, len do
		
		local data = redisItem:rpop( LogDispatch.log_dispatch["loguserpassordercount"], LogDispatch.redis_index )
		if data == nil then
			break
		end
		
		local dataArr = luajson.decode(data)
		local sellInfo = UserInfoModel.GetUserInfo(dataArr.sell_user_id)
		local buyInfo = UserInfoModel.GetUserInfo(dataArr.buy_user_id)
		local rAmount = 0		--充值数量
		local rNum = 0			
		local wAmount = 0		--提现
		local wNum = 0			
		local platformID = nil
		if dataArr.dealType == 200 then
			rAmount = dataArr.amount
			rNum = 1
			if buyInfo ~= nil then
				platformID = buyInfo.platformid
			end
		elseif dataArr.dealType == 300 then
			wAmount = dataArr.amount
			wNum = 1
			if sellInfo ~= nil then
				platformID = sellInfo.platformid
			end
		end	
		
		local rechargeDealNum = 0
		local rechargeDealAmount = 0
		local rCoinPaySys = 0
		local rCoinPayShop = 0
		local rCoinPayDealNum = 0
		if tonumber(dataArr.payType) == 8401 then
			rCoinPayDealNum = 1
			if dataArr.owership == 0 then
					rCoinPaySys = dataArr.amount
			else
				rCoinPayShop = dataArr.amount
			end
		else
			rechargeDealNum = 1
			rechargeDealAmount = dataArr.amount
		end
		
		LogModel.CheckSys("ALL") 
		local sqlCase = "update log_sys set finish_count_buy=finish_count_buy+1, finish_count_sell=finish_count_sell+1, sell_amount=sell_amount+"..dataArr.amount
			..", buy_amount=buy_amount+"..(dataArr.amount-dataArr.fee)..", fee_amount=fee_amount+"..dataArr.fee..", recharge_deal_num=recharge_deal_num+"..rNum..", recharge_deal_amount=recharge_deal_amount+"..rAmount
				..", withdraw_deal_num=withdraw_deal_num+"..wNum..", withdraw_deal_amount=withdraw_deal_amount+"..wAmount.." where channel='ALL'"
		mysqlItem:execute(sqlCase)

		LogModel.CheckSysDaily("ALL", dayStr) 
		local sqlCase = "update log_sys_daily set finish_count_buy=finish_count_buy+1, finish_count_sell=finish_count_sell+1, sell_amount=sell_amount+"..dataArr.amount
			..", buy_amount=buy_amount+"..(dataArr.amount-dataArr.fee)..", fee_amount=fee_amount+"
		..dataArr.fee..", recharge_deal_num=recharge_deal_num+"..rNum..", recharge_deal_amount=recharge_deal_amount+"..rAmount
				..", withdraw_deal_num=withdraw_deal_num+"..wNum..", withdraw_deal_amount=withdraw_deal_amount+"..wAmount.." where dateid='"..dayStr.."' and channel='ALL'"
		mysqlItem:execute(sqlCase)
		
		LogModel.CheckSysWeek("ALL", dayWeek) 
		local sqlCase = "update log_sys_week set finish_count_buy=finish_count_buy+1, finish_count_sell=finish_count_sell+1, sell_amount=sell_amount+"..dataArr.amount
			..", buy_amount=buy_amount+"..(dataArr.amount-dataArr.fee)..", fee_amount=fee_amount+"
		..dataArr.fee..", recharge_deal_num=recharge_deal_num+"..rNum..", recharge_deal_amount=recharge_deal_amount+"..rAmount
				..", withdraw_deal_num=withdraw_deal_num+"..wNum..", withdraw_deal_amount=withdraw_deal_amount+"..wAmount.." where dateid='"..dayWeek.."' and channel='ALL'"
		mysqlItem:execute(sqlCase)

		if sellInfo ~= nil then
			LogModel.CheckUserDaily(sellInfo.userid, sellInfo.channel, dayStr) 
			
			LogModel.CheckSys(sellInfo.channel) 
			local sqlCase = "update log_sys set finish_count_sell=finish_count_sell+1, sell_amount=sell_amount+"..dataArr.amount
			if dataArr.dealType == 300 then
				sqlCase = sqlCase..", withdraw_deal_num=withdraw_deal_num+1, withdraw_deal_amount=withdraw_deal_amount+"..dataArr.amount
			end
			sqlCase = sqlCase.." where channel='"..sellInfo.channel.."'"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSysDaily(sellInfo.channel, dayStr) 
			local sqlCase = "update log_sys_daily set finish_count_sell=finish_count_sell+1, sell_amount=sell_amount+"..dataArr.amount
			if dataArr.dealType == 300 then
				sqlCase = sqlCase..", withdraw_deal_num=withdraw_deal_num+1, withdraw_deal_amount=withdraw_deal_amount+"..dataArr.amount
			end
			sqlCase = sqlCase.." where dateid='"..dayStr.."' and channel='"..sellInfo.channel.."'"
			mysqlItem:execute(sqlCase)	
			
			LogModel.CheckSysWeek(sellInfo.channel, dayWeek) 
			local sqlCase = "update log_sys_week set finish_count_sell=finish_count_sell+1, sell_amount=sell_amount+"..dataArr.amount
			if dataArr.dealType == 300 then
				sqlCase = sqlCase..", withdraw_deal_num=withdraw_deal_num+1, withdraw_deal_amount=withdraw_deal_amount+"..dataArr.amount
			end
			sqlCase = sqlCase.." where dateid='"..dayWeek.."' and channel='"..sellInfo.channel.."'"
			mysqlItem:execute(sqlCase)	

			local sqlCase = "update log_user set sell_amount=sell_amount+"..dataArr.amount..", finish_count_sell=finish_count_sell+1, confirm_time=confirm_time+"..dataArr.confirm_time
						..", sell_money=sell_money+"..dataArr.money.." where userid="..sellInfo.userid
			mysqlItem:execute(sqlCase)

			local sqlCase = "update log_user_daily set sell_amount=sell_amount+"..dataArr.amount..", finish_count_sell=finish_count_sell+1, confirm_time=confirm_time+"..dataArr.confirm_time
						..", sell_money=sell_money+"..dataArr.money.." where id > 0 and userid="..sellInfo.userid.." and dateid='"..dayStr.."'"
			mysqlItem:execute(sqlCase)	

			AddDealCount(sellInfo.userid, dataArr.amount, dataArr.payType, dataArr.money, dataArr.dealType)	
		end
		
		if buyInfo ~= nil then
			LogModel.CheckUserDaily(buyInfo.userid, buyInfo.channel, dayStr) 

			LogModel.CheckSys(buyInfo.channel) 
			local sqlCase = "update log_sys set finish_count_buy=finish_count_buy+1, buy_amount=buy_amount+"..(dataArr.amount-dataArr.fee)
					..", fee_amount=fee_amount+"..dataArr.fee
			if dataArr.dealType == 200 then
				sqlCase = sqlCase..", recharge_deal_num=recharge_deal_num+"..rechargeDealNum..", recharge_deal_amount=recharge_deal_amount+"..rechargeDealAmount
						..", r_coin_pay_sys=r_coin_pay_sys+"..rCoinPaySys..", r_coin_pay_shop=r_coin_pay_shop+"..rCoinPayShop..", r_coin_pay_deal_num=r_coin_pay_deal_num+"..rCoinPayDealNum
			end
			sqlCase = sqlCase.." where channel='"..buyInfo.channel.."'"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSysDaily(buyInfo.channel, dayStr) 
			local sqlCase = "update log_sys_daily set finish_count_buy=finish_count_buy+1, buy_amount=buy_amount+"..(dataArr.amount-dataArr.fee)
					..", fee_amount=fee_amount+"..dataArr.fee
			if dataArr.dealType == 200 then
				sqlCase = sqlCase..", recharge_deal_num=recharge_deal_num+"..rechargeDealNum..", recharge_deal_amount=recharge_deal_amount+"..rechargeDealAmount
						..", r_coin_pay_sys=r_coin_pay_sys+"..rCoinPaySys..", r_coin_pay_shop=r_coin_pay_shop+"..rCoinPayShop..", r_coin_pay_deal_num=r_coin_pay_deal_num+"..rCoinPayDealNum
			end
			sqlCase = sqlCase.." where dateid='"..dayStr.."' and channel='"..buyInfo.channel.."'"
			mysqlItem:execute(sqlCase)	
			
			LogModel.CheckSysWeek(buyInfo.channel, dayWeek) 
			local sqlCase = "update log_sys_week set finish_count_buy=finish_count_buy+1, buy_amount=buy_amount+"..(dataArr.amount-dataArr.fee)
					..", fee_amount=fee_amount+"..dataArr.fee
			if dataArr.dealType == 200 then
				sqlCase = sqlCase..", recharge_deal_num=recharge_deal_num+"..rechargeDealNum..", recharge_deal_amount=recharge_deal_amount+"..rechargeDealAmount
						..", r_coin_pay_sys=r_coin_pay_sys+"..rCoinPaySys..", r_coin_pay_shop=r_coin_pay_shop+"..rCoinPayShop..", r_coin_pay_deal_num=r_coin_pay_deal_num+"..rCoinPayDealNum
			end
			sqlCase = sqlCase.." where dateid='"..dayWeek.."' and channel='"..buyInfo.channel.."'"
			mysqlItem:execute(sqlCase)	

			local sqlCase = "update log_user set buy_amount=buy_amount+"..(dataArr.amount-dataArr.fee)..", fee_amount=fee_amount+"..dataArr.fee
				..", finish_count_buy=finish_count_buy+1, confirm_time=confirm_time+"..dataArr.confirm_time..", buy_money=buy_money+"..dataArr.money
				..", r_coin_pay_sys=r_coin_pay_sys+"..rCoinPaySys..", r_coin_pay_shop=r_coin_pay_shop+"..rCoinPayShop..", r_coin_pay_deal_num=r_coin_pay_deal_num+"..rCoinPayDealNum
				.." where userid="..buyInfo.userid
			mysqlItem:execute(sqlCase)

			local sqlCase = "update log_user_daily set buy_amount=buy_amount+"..(dataArr.amount-dataArr.fee)..", fee_amount=fee_amount+"..dataArr.fee
				..", finish_count_buy=finish_count_buy+1, confirm_time=confirm_time+"..dataArr.confirm_time..", buy_money=buy_money+"..dataArr.money
				..", r_coin_pay_sys=r_coin_pay_sys+"..rCoinPaySys..", r_coin_pay_shop=r_coin_pay_shop+"..rCoinPayShop..", r_coin_pay_deal_num=r_coin_pay_deal_num+"..rCoinPayDealNum
				.." where id > 0 and userid="..buyInfo.userid.." and dateid='"..dayStr.."'"
			mysqlItem:execute(sqlCase)	
			
			AddBuyCount(buyInfo.userid, dataArr.amount, dataArr.payType, dataArr.money, dataArr.dealType)	
		end
		
		if platformID ~= nil then
			LogModel.CheckSub("ALL")
			local sqlCase = "update log_sub set recharge_deal_num=recharge_deal_num+"..rNum..", recharge_deal_amount=recharge_deal_amount+"..rAmount
				..", withdraw_deal_num=withdraw_deal_num+"..wNum..", withdraw_deal_amount=withdraw_deal_amount+"..wAmount
				..", r_coin_pay_sys=r_coin_pay_sys+"..rCoinPaySys..", r_coin_pay_shop=r_coin_pay_shop+"..rCoinPayShop..", r_coin_pay_deal_num=r_coin_pay_deal_num+"..rCoinPayDealNum
				.." where platform_id='ALL'"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSubDaily("ALL", dayStr)
			local sqlCase = "update log_sub_daily set recharge_deal_num=recharge_deal_num+"..rNum..", recharge_deal_amount=recharge_deal_amount+"..rAmount
				..", withdraw_deal_num=withdraw_deal_num+"..wNum..", withdraw_deal_amount=withdraw_deal_amount+"..wAmount
				..", r_coin_pay_sys=r_coin_pay_sys+"..rCoinPaySys..", r_coin_pay_shop=r_coin_pay_shop+"..rCoinPayShop..", r_coin_pay_deal_num=r_coin_pay_deal_num+"..rCoinPayDealNum
				.." where platform_id='ALL' and dateid='"..dayStr.."'"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSubWeek("ALL", dayWeek)
			local sqlCase = "update log_sub_week set recharge_deal_num=recharge_deal_num+"..rNum..", recharge_deal_amount=recharge_deal_amount+"..rAmount
				..", withdraw_deal_num=withdraw_deal_num+"..wNum..", withdraw_deal_amount=withdraw_deal_amount+"..wAmount
				..", r_coin_pay_sys=r_coin_pay_sys+"..rCoinPaySys..", r_coin_pay_shop=r_coin_pay_shop+"..rCoinPayShop..", r_coin_pay_deal_num=r_coin_pay_deal_num+"..rCoinPayDealNum
				.." where platform_id='ALL' and dateid='"..dayWeek.."'"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSub(platformID)
			local sqlCase = "update log_sub set recharge_deal_num=recharge_deal_num+"..rNum..", recharge_deal_amount=recharge_deal_amount+"..rAmount
				..", withdraw_deal_num=withdraw_deal_num+"..wNum..", withdraw_deal_amount=withdraw_deal_amount+"..wAmount
				..", r_coin_pay_sys=r_coin_pay_sys+"..rCoinPaySys..", r_coin_pay_shop=r_coin_pay_shop+"..rCoinPayShop..", r_coin_pay_deal_num=r_coin_pay_deal_num+"..rCoinPayDealNum
				.." where platform_id='"..platformID.."'"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSubDaily(platformID, dayStr)
			local sqlCase = "update log_sub_daily set recharge_deal_num=recharge_deal_num+"..rNum..", recharge_deal_amount=recharge_deal_amount+"..rAmount
				..", withdraw_deal_num=withdraw_deal_num+"..wNum..", withdraw_deal_amount=withdraw_deal_amount+"..wAmount
				..", r_coin_pay_sys=r_coin_pay_sys+"..rCoinPaySys..", r_coin_pay_shop=r_coin_pay_shop+"..rCoinPayShop..", r_coin_pay_deal_num=r_coin_pay_deal_num+"..rCoinPayDealNum
				.." where platform_id='"..platformID.."' and dateid='"..dayStr.."'"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSubWeek(platformID, dayWeek)
			local sqlCase = "update log_sub_week set recharge_deal_num=recharge_deal_num+"..rNum..", recharge_deal_amount=recharge_deal_amount+"..rAmount
				..", withdraw_deal_num=withdraw_deal_num+"..wNum..", withdraw_deal_amount=withdraw_deal_amount+"..wAmount
				..", r_coin_pay_sys=r_coin_pay_sys+"..rCoinPaySys..", r_coin_pay_shop=r_coin_pay_shop+"..rCoinPayShop..", r_coin_pay_deal_num=r_coin_pay_deal_num+"..rCoinPayDealNum
				.." where platform_id='"..platformID.."' and dateid='"..dayWeek.."'"
			mysqlItem:execute(sqlCase)
		end
		
		--统计子系统交易量
		local platformid = 0
		if buyInfo ~= nil then
			if buyInfo.platformid ~= 0 then
				platformid = buyInfo.platformid
			end
		end
		
		if sellInfo ~= nil then
			if sellInfo.platformid ~= 0 then
				platformid = sellInfo.platformid
			end
		end
		
		if platformid ~= nil then
			local dealType = 0
			if dataArr.dealType == 200 then
				dealType = 3
			
			elseif dataArr.dealType == 300 then
				if dataArr.withdrawType == 0 then
					dealType = 1
				elseif dataArr.withdrawType == 1 then
					dealType = 2
				end
			end
			if dealType ~= 0 then
				LogModel.CheckPlatformDaily(platformid, dayStr, dealType, dataArr.paytype) 
				
				local sqlCase = "update log_subsystem_daily set deal_count=deal_count+"..dataArr.amount..", deal_money=deal_money+"..dataArr.money
					.." where platform_id="..platformid.." and  dateid='"..dayStr.."' and deal_type="..dealType.." and payid="..dataArr.paytype
				mysqlItem:execute(sqlCase)
			end
		end

		--统计支付ID的交易量
		if dataArr.payID ~= 0 and dataArr.money ~= 0 then
			local nowDay = TimeUtils.GetDayString()
			local tmpData = redisItem:hget(UserPayModel.userpay_day_dael_count, dataArr.payID, UserPayModel.redis_index)
			if tmpData == nil then
				tmpData = {}
				tmpData["date"] = nowDay
				tmpData["num"] = 0
			else
				tmpData = luajson.decode(tmpData)
			end
			
			if tmpData["date"] == nowDay then
				tmpData["num"] = tmpData["num"] + dataArr.money
				
			else
				tmpData["date"] = nowDay
				tmpData["num"] = dataArr.money
			end
			
			local coinPayDealAmount = 0
			if tonumber(dataArr.payType) == 8401 then
				coinPayDealAmount = dataArr.amount
			end
			redisItem:hset(UserPayModel.userpay_day_dael_count, dataArr.payID, luajson.encode(tmpData), UserPayModel.redis_index)
			local sqlCase = "update dy_user_pay set day_deal_money="..tmpData["num"]..",coin_pay_deal_amount=coin_pay_deal_amount+"
				..coinPayDealAmount.." where id="..dataArr.payID
			mysqlItem:execute(sqlCase)	
		end

	end	
end




