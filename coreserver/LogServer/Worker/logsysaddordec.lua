module("logsysaddordec", package.seeall)

function work(buffer)
	--通知下注的这里，不需要加入锁，这里只是一个通知的过程
	--暂时不考虑分段来处理，后面看业务量的多少来觉得，是否需要用到limit
	--执行完毕
	
	local dayStr = TimeUtils.GetDayString()
	local dayWeek = TimeUtils.GetWeekString()
	
	
	local len = buffer
	for i = 1, len do
		
		local data = redisItem:rpop( LogDispatch.log_dispatch["logsysaddordec"], LogDispatch.redis_index )
		if data == nil then
			break
		end
		
		local dataArr = luajson.decode(data)
		
		local sysAdd = 0
		local sysDec = 0
		if dataArr.optType == 1 then
			sysAdd = dataArr.count
		elseif dataArr.optType == 2 then
			sysDec = dataArr.count
		end
		
		LogModel.CheckSub("ALL")
		local sqlCase = "update log_sub set sys_add=sys_add+"..sysAdd..", sys_dec=sys_dec+"..sysDec.." where platform_id='ALL'"
		mysqlItem:execute(sqlCase)

		LogModel.CheckSubDaily("ALL", dayStr)
		local sqlCase = "update log_sub_daily set sys_add=sys_add+"..sysAdd..", sys_dec=sys_dec+"..sysDec.." where platform_id='ALL' and dateid='"..dayStr.."'"
		mysqlItem:execute(sqlCase)

		LogModel.CheckSubWeek("ALL", dayWeek)
		local sqlCase = "update log_sub_week set sys_add=sys_add+"..sysAdd..", sys_dec=sys_dec+"..sysDec.." where platform_id='ALL' and dateid='"..dayWeek.."'"
		mysqlItem:execute(sqlCase)

		local uInfo = UserInfoModel.GetUserInfo(dataArr.userid)
		if uInfo ~= nil then
			LogModel.CheckSub(uInfo.platformid)
			local sqlCase = "update log_sub set sys_add=sys_add+"..sysAdd..", sys_dec=sys_dec+"..sysDec.." where platform_id='"..uInfo.platformid.."'"
			mysqlItem:execute(sqlCase)

			LogModel.CheckSubDaily(uInfo.platformid, dayStr)
			local sqlCase = "update log_sub_daily set sys_add=sys_add+"..sysAdd..", sys_dec=sys_dec+"..sysDec.." where platform_id='"..uInfo.platformid.."' and dateid='"..dayStr.."'"
			mysqlItem:execute(sqlCase)

			LogModel.CheckSubWeek(uInfo.platformid, dayWeek)
			local sqlCase = "update log_sub_week set sys_add=sys_add+"..sysAdd..", sys_dec=sys_dec+"..sysDec.." where platform_id='"..uInfo.platformid.."' and dateid='"..dayWeek.."'"
			mysqlItem:execute(sqlCase)

		end
		
		LogModel.CheckSys("ALL")
		local sqlCase = "update log_sys set sys_add=sys_add+"..sysAdd..", sys_dec=sys_dec+"..sysDec.." where channel='ALL'"
		mysqlItem:execute(sqlCase)
		
		LogModel.CheckSysDaily("ALL", dayStr)
		local sqlCase = "update log_sys_daily set sys_add=sys_add+"..sysAdd..", sys_dec=sys_dec+"..sysDec.." where channel='ALL' and dateid='"..dayStr.."'"
		mysqlItem:execute(sqlCase)
		
		LogModel.CheckSysWeek("ALL", dayWeek)
		local sqlCase = "update log_sys_week set sys_add=sys_add+"..sysAdd..", sys_dec=sys_dec+"..sysDec.." where channel='ALL' and dateid='"..dayWeek.."'"
		mysqlItem:execute(sqlCase)
		
		local uInfo = UserInfoModel.GetUserInfo(dataArr.userid)
		if uInfo ~= nil then
			LogModel.CheckSys(uInfo.channel)
			local sqlCase = "update log_sys set sys_add=sys_add+"..sysAdd..", sys_dec=sys_dec+"..sysDec.." where channel='"..uInfo.channel.."'"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSysDaily(uInfo.channel, dayStr)
			local sqlCase = "update log_sys_daily set sys_add=sys_add+"..sysAdd..", sys_dec=sys_dec+"..sysDec.." where channel='"..uInfo.channel.."' and dateid='"..dayStr.."'"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSysWeek(uInfo.channel, dayWeek)
			local sqlCase = "update log_sys_week set sys_add=sys_add+"..sysAdd..", sys_dec=sys_dec+"..sysDec.." where channel='"..uInfo.channel.."' and dateid='"..dayWeek.."'"
			mysqlItem:execute(sqlCase)
			
		end

	end	
end




