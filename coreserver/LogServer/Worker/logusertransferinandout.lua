module("logusertransferinandout", package.seeall)

--统计新增玩家

function work(buffer)
	--通知下注的这里，不需要加入锁，这里只是一个通知的过程
	--暂时不考虑分段来处理，后面看业务量的多少来觉得，是否需要用到limit
	--执行完毕
	
	local dayStr = TimeUtils.GetDayString()
	local dayWeek = TimeUtils.GetWeekString()
	local len = tonumber(buffer)
	for i = 1, len do
		
		local data = redisItem:rpop( LogDispatch.log_dispatch['logusertransferinandout'], LogDispatch.redis_index )
		
		if data == nil then
			break
		end
		
		local dataArr = luajson.decode( data )
		
		LogModel.CheckSys("ALL") 
		local sqlCase = "update log_sys set transfer_in_amount=transfer_in_amount+"..dataArr.getAmount
			..", transfer_out_amount=transfer_out_amount+"..dataArr.amount.." where channel='ALL'"
		mysqlItem:execute(sqlCase)
		
		LogModel.CheckSysDaily("ALL", dayStr) 
		local sqlCase = "update log_sys_daily set transfer_in_amount=transfer_in_amount+"..dataArr.getAmount
			..", transfer_out_amount=transfer_out_amount+"..dataArr.amount.." where dateid='"..dayStr.."' and channel='ALL'"
			
		LogModel.CheckSysWeek("ALL", dayWeek) 
		local sqlCase = "update log_sys_week set transfer_in_amount=transfer_in_amount+"..dataArr.getAmount
			..", transfer_out_amount=transfer_out_amount+"..dataArr.amount.." where dateid='"..dayWeek.."' and channel='ALL'"
		mysqlItem:execute(sqlCase)	
		
		local fromInfo = UserInfoModel.GetUserInfo(dataArr.fromUserID)
		if fromInfo ~= nil then
			LogModel.CheckUserDaily(fromInfo.userid, fromInfo.channel, dayStr) 
			
			LogModel.CheckSys(fromInfo.channel) 
			local sqlCase = "update log_sys set transfer_out_amount=transfer_out_amount+"..dataArr.amount.." where channel='"..fromInfo.channel.."'"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSysDaily(fromInfo.channel, dayStr) 
			local sqlCase = "update log_sys_daily set transfer_out_amount=transfer_out_amount+"..dataArr.amount.." where dateid='"..dayStr.."' and channel='"..fromInfo.channel.."'"
			mysqlItem:execute(sqlCase)	
			
			LogModel.CheckSysWeek(fromInfo.channel, dayWeek) 
			local sqlCase = "update log_sys_week set transfer_out_amount=transfer_out_amount+"..dataArr.amount.." where dateid='"..dayWeek.."' and channel='"..fromInfo.channel.."'"
			mysqlItem:execute(sqlCase)
			
			local sqlCase = "update log_user set transfer_out_amount=transfer_out_amount+"..dataArr.amount.." where userid="..fromInfo.userid
			mysqlItem:execute(sqlCase)
			local sqlCase = "update log_user_daily set transfer_out_amount=transfer_out_amount+"..dataArr.amount.." where id > 0 and userid="..fromInfo.userid.." and dateid='"..dayStr.."'"
			mysqlItem:execute(sqlCase)	
		end
			
		local toInfo = UserInfoModel.GetUserInfo(dataArr.toUserID)
		if toInfo ~= nil then
			LogModel.CheckUserDaily(toInfo.userid, toInfo.channel, dayStr) 
			
			LogModel.CheckSys(toInfo.channel) 
			local sqlCase = "update log_sys set transfer_in_amount=transfer_in_amount+"..dataArr.getAmount.." where channel='"..toInfo.channel.."'"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSysDaily(toInfo.channel, dayStr) 
			local sqlCase = "update log_sys_daily set transfer_in_amount=transfer_in_amount+"..dataArr.getAmount.." where dateid='"..dayStr.."' and channel='"..toInfo.channel.."'"
			mysqlItem:execute(sqlCase)	
			
			LogModel.CheckSysWeek(toInfo.channel, dayWeek) 
			local sqlCase = "update log_sys_week set transfer_in_amount=transfer_in_amount+"..dataArr.getAmount.." where dateid='"..dayWeek.."' and channel='"..toInfo.channel.."'"
			mysqlItem:execute(sqlCase)	
			
			local sqlCase = "update log_user set transfer_in_amount=transfer_in_amount+"..dataArr.getAmount.." where userid="..toInfo.userid
			mysqlItem:execute(sqlCase)
			local sqlCase = "update log_user_daily set transfer_in_amount=transfer_in_amount+"..dataArr.getAmount.." where id > 0 and userid="..toInfo.userid.." and dateid='"..dayStr.."'"
			mysqlItem:execute(sqlCase)	
		end
	end

end




