module("CheckUserFund", package.seeall)

function work(buffer)
	
	local strDay = TimeUtils.GetDayString()
	local strWeek = TimeUtils.GetWeekString()
		
	--统计所有的资金
	--统计所有的资金
	local amount = 0
	local money = 0
	local moneyUsdt = 0
	local sysUserusdt = 0
	local sqlCase = "select sum(erc_usdt_amount) from dy_user_info where deal_coin_type=0"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch(sqlCase)
	if sqlData ~= nil then
		amount = tonumber(sqlData)
	end
	
	local sqlCase = "select sum(erc_fc_amount) from dy_user_info where deal_coin_type=1"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch(sqlCase)
	if sqlData ~= nil then
		money = tonumber(sqlData)
	end
	
	local sqlCase = "select sum(erc_usdt_amount) from dy_user_info where deal_coin_type=1"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch(sqlCase)
	if sqlData ~= nil then
		moneyUsdt = tonumber(sqlData)
	end
	
	local sqlCase = "select sum(erc_usdt_amount) from dy_user_info where user_type=100"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch(sqlCase)
	if sqlData ~= nil then
		sysUserusdt = tonumber(sqlData)
	end
	
	LogModel.CheckSub("ALL") 
	local sqlCase = "update log_sub set usdt_amount="..amount..", money_amount="..money..", money_usdt="..moneyUsdt..", sys_user_amount="..sysUserusdt.." where platform_id='ALL'"
	mysqlItem:execute(sqlCase)
	
	LogModel.CheckSubDaily("ALL", strDay) 
	local sqlCase = "update log_sub_daily set usdt_amount="..amount..", money_amount="..money..", money_usdt="..moneyUsdt..", sys_user_amount="..sysUserusdt.." where platform_id='ALL' and dateid='"..strDay.."'"
	mysqlItem:execute(sqlCase)

	LogModel.CheckSubWeek("ALL", strWeek) 
	local sqlCase = "update log_sub_week set usdt_amount="..amount..", money_amount="..money..", money_usdt="..moneyUsdt..", sys_user_amount="..sysUserusdt.." where platform_id='ALL' and dateid='"..strWeek.."'"
	mysqlItem:execute(sqlCase)
	
	local platformList = {0}
	local sqlCase = "select id from dy_subsystem_info"
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlData = mysqlItem:fetch()
		if sqlData == nil then
			break
		end
		
		table.insert(platformList, tonumber(sqlData))
	end
	
	for k,v in ipairs(platformList) do 
		local amount = 0
		local money = 0
		local moneyUsdt = 0
		local sysUserusdt = 0
		local sqlCase = "select sum(erc_usdt_amount) from dy_user_info where deal_coin_type=0 and platform_id="..v
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch(sqlCase)
		if sqlData ~= nil then
			amount = tonumber(sqlData)
		end
		
		local sqlCase = "select sum(erc_fc_amount) from dy_user_info where deal_coin_type=1 and platform_id="..v
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch(sqlCase)
		if sqlData ~= nil then
			money = tonumber(sqlData)
		end
		
		local sqlCase = "select sum(erc_usdt_amount) from dy_user_info where deal_coin_type=1 and platform_id="..v
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch(sqlCase)
		if sqlData ~= nil then
			moneyUsdt = tonumber(sqlData)
		end
		
		local sqlCase = "select sum(erc_usdt_amount) from dy_user_info where user_type=100 and platform_id="..v
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch(sqlCase)
		if sqlData ~= nil then
			sysUserusdt = tonumber(sqlData)
		end
		
		LogModel.CheckSub(v) 
		local sqlCase = "update log_sub set usdt_amount="..amount..", money_amount="..money..", money_amount="..money..", money_usdt="..moneyUsdt..", sys_user_amount="..sysUserusdt.." where platform_id='"..v.."'"
		mysqlItem:execute(sqlCase)
		
		LogModel.CheckSubDaily(v, strDay) 
		local sqlCase = "update log_sub_daily set usdt_amount="..amount..", money_amount="..money..", money_amount="..money..", money_usdt="..moneyUsdt..", sys_user_amount="..sysUserusdt.." where platform_id='"..v.."' and dateid='"..strDay.."'"
		mysqlItem:execute(sqlCase)

		LogModel.CheckSubWeek(v, strWeek) 
		local sqlCase = "update log_sub_week set usdt_amount="..amount..", money_amount="..money..", money_amount="..money..", money_usdt="..moneyUsdt..", sys_user_amount="..sysUserusdt.." where platform_id='"..v.."' and dateid='"..strWeek.."'"
		mysqlItem:execute(sqlCase)
	
	end
	
	LogModel.CheckSys("ALL") 
	local sqlCase = "update log_sys set usdt_amount="..amount..", money_amount="..money..", money_usdt="..moneyUsdt.." where channel='ALL'"
	mysqlItem:execute(sqlCase)
	
	LogModel.CheckSysDaily("ALL", strDay) 
	local sqlCase = "update log_sys_daily set usdt_amount="..amount..", money_amount="..money..", money_usdt="..moneyUsdt.." where channel='ALL' and dateid='"..strDay.."'"
	mysqlItem:execute(sqlCase)
	
	LogModel.CheckSysWeek("ALL", strWeek) 
	local sqlCase = "update log_sys_week set usdt_amount="..amount..", money_amount="..money..", money_usdt="..moneyUsdt.." where channel='ALL' and dateid='"..strWeek.."'"
	mysqlItem:execute(sqlCase)
	
	local channelList = {}
	local sqlCase = "select channel from dy_channel_info"
	mysqlItem:executeQuery(sqlCase)
	while true do 
		local sqlData = mysqlItem:fetch()
		if sqlData == nil then
			break
		end
		
		table.insert(channelList, sqlData)
	end
	
	for k,v in ipairs(channelList) do 
		local amount = 0
		local money = 0
		local moneyUsdt = 0
		local sqlCase = "select sum(erc_usdt_amount) from dy_user_info where deal_coin_type=0 and channel='"..v.."'"
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch(sqlCase)
		if sqlData ~= nil then
			amount = tonumber(sqlData)
		end
		
		local sqlCase = "select sum(erc_fc_amount) from dy_user_info where deal_coin_type=1 and channel='"..v.."'"
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch(sqlCase)
		if sqlData ~= nil then
			money = tonumber(sqlData)
		end
		
		local sqlCase = "select sum(erc_usdt_amount) from dy_user_info where deal_coin_type=1 and channel='"..v.."'"
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch(sqlCase)
		if sqlData ~= nil then
			moneyUsdt = tonumber(sqlData)
		end
	
		LogModel.CheckSys(v) 
		local sqlCase = "update log_sys set usdt_amount="..amount..", money_amount="..money..", money_usdt="..moneyUsdt.." where channel='"..v.."'"
		mysqlItem:execute(sqlCase)
		
		LogModel.CheckSysDaily(v, strDay) 
		local sqlCase = "update log_sys_daily set usdt_amount="..amount..", money_amount="..money..", money_usdt="..moneyUsdt.." where channel='"..v.."' and dateid='"..strDay.."'"
		mysqlItem:execute(sqlCase)

		LogModel.CheckSysWeek(v, strWeek) 
		local sqlCase = "update log_sys_week set usdt_amount="..amount..", money_amount="..money..", money_usdt="..moneyUsdt.." where channel='"..v.."' and dateid='"..strWeek.."'"
		mysqlItem:execute(sqlCase)
	
	end

end




