module("lognewusercount", package.seeall)

--统计新增玩家

function work(buffer)
	--通知下注的这里，不需要加入锁，这里只是一个通知的过程
	--暂时不考虑分段来处理，后面看业务量的多少来觉得，是否需要用到limit
	--执行完毕
	
	--分佣 
	local function AddNewPlayer(userid)
		
		if userid == 0 then
			return
		end
		
		local aInfo = UserInfoModel.GetUserInfo(userid)
		if aInfo == nil then
			return
		end
		
		local sqlCase = "update dy_user_info set team_user_num=team_user_num+1 where userid="..aInfo.userid
		mysqlItem:execute(sqlCase)
		AddNewPlayer(aInfo.agent)
	
	end
	
	local dayStr = TimeUtils.GetDayString()
	local weekStr = TimeUtils.GetWeekString()
	local len = tonumber(buffer)
	for i = 1, len do
		
		local data = redisItem:rpop( LogDispatch.log_dispatch['lognewusercount'], LogDispatch.redis_index )
		
		if data == nil then
			break
		end
		
		local dataArr = luajson.decode( data )
	
		--看看是否在这里触发代理的级别关系
		if dataArr.usertype == g_humanDefine.usertype_merchant then
			LogModel.CheckSys("ALL") 
			LogModel.CheckSys(dataArr.channel) 
			local sqlCase = "update log_sys set merchant_num=merchant_num+1 where id>0 and (channel='ALL' or channel='"..dataArr.channel.."')"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSysDaily("ALL", dayStr) 
			LogModel.CheckSysDaily(dataArr.channel, dayStr) 
			local sqlCase = "update log_sys_daily set merchant_num=merchant_num+1 where id>0 and dateid='"..dayStr.."' and (channel='ALL' or channel='"..dataArr.channel.."')"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSysWeek("ALL", weekStr) 
			LogModel.CheckSysWeek(dataArr.channel, weekStr) 
			local sqlCase = "update log_sys_week set merchant_num=merchant_num+1 where id>0 and dateid='"..weekStr.."' and (channel='ALL' or channel='"..dataArr.channel.."')"
			mysqlItem:execute(sqlCase)
		else 
			LogModel.CheckSys("ALL") 
			LogModel.CheckSys(dataArr.channel) 
			local sqlCase = "update log_sys set dealer_dealer_num=dealer_dealer_num+1 where id>0 and (channel='ALL' or channel='"..dataArr.channel.."')"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSysDaily("ALL", dayStr) 
			LogModel.CheckSysDaily(dataArr.channel, dayStr) 
			local sqlCase = "update log_sys_daily set dealer_dealer_num=dealer_dealer_num+1 where id>0 and dateid='"..dayStr.."' and (channel='ALL' or channel='"..dataArr.channel.."')"
			mysqlItem:execute(sqlCase)
			
			LogModel.CheckSysWeek("ALL", weekStr) 
			LogModel.CheckSysWeek(dataArr.channel, weekStr) 
			local sqlCase = "update log_sys_week set dealer_dealer_num=dealer_dealer_num+1 where id>0 and dateid='"..weekStr.."' and (channel='ALL' or channel='"..dataArr.channel.."')"
			mysqlItem:execute(sqlCase)
		end
		
		local uInfo = UserInfoModel.GetUserInfo(dataArr.userid)
		if uInfo ~= nil then
			local sqlCase = "update dy_user_info set team_user_num=team_user_num+1, dir_user_num=dir_user_num+1 where userid="..uInfo.agent
			mysqlItem:execute(sqlCase)
			local aInfo = UserInfoModel.GetUserInfo(uInfo.agent)
			if aInfo ~= nil then
				AddNewPlayer(aInfo.agent)
			end
		end
		
		local userid = dataArr.userid
		local level = 0
		local agent = userid
		while true do 
			local uInfo = UserInfoModel.GetUserInfo(agent)
			if uInfo == nil or uInfo.agent == 0 then
				break
			end 
			
			agent = uInfo.agent
			level = level + 1
			local sqlCase = "insert into ag_relation(userid, bind_userid, level) values("..userid..","..agent..","..level..")"
			mysqlItem:execute(sqlCase)
		end 
		
		
	end

end




