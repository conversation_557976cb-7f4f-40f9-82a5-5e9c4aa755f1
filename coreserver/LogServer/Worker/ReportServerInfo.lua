
module("ReportServerInfo", package.seeall)

--[[
function GetServerInfo()    --获取改游戏的参数
	--返回6个参数
	--controller最大线程数，controller空闲线程数，controller待处理事务总数，worker最大线程数，worker空闲线程数，worker待处理事务总数
	return c_getServerInfo()
end

function GetSysInfo()     --获取系统参数
	--返回4个参数：
	--CPU总数，CPU使用率，内存总量，可使用内存总量
	return c_getSysInfo(g_processName == nil and "game" or g_processName)
end--]]

function work(buffer)
	--[[
	local currTime = TimeUtils.GetTimeString();
	local timestamp = TimeUtils.GetTime();
	local TotalCPU,RateCPU,TotalMem,RemainMem = GetSysInfo()
	local controllerMaxThread,controllerFreeThread,controllerListNums,workerMaxThread,workerFreeThread,workerListNums = GetServerInfo()
	
	local sqlCase = "insert into log_serverinfo(time,processName,TotalCPU,RateCPU,TotalMem,RemainMem,controllerMaxThread,controllerFreeThread,controllerListNums,workerMaxThread,workerFreeThread,workerListNums,timestamp) values('"..
	currTime.."','"..(g_processName == nil and "game" or g_processName).."',"..TotalCPU..","..RateCPU..","..TotalMem..","..RemainMem..","..
	controllerMaxThread..","..controllerFreeThread..","..controllerListNums..","..workerMaxThread..","..workerFreeThread..","..workerListNums..","..timestamp..")"

	mysqlLog:execute(sqlCase)
	]]
end