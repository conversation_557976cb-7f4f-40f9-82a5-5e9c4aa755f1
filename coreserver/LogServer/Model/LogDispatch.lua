
LogDispatch = {}

LogDispatch.log_dispatch = {}

LogDispatch.log_dispatch['loguserpumpcount'] = "loguserpumpcount"				--计算佣金的任务
LogDispatch.log_dispatch['lognewusercount'] = "lognewusercount"					--统计新增用户的任务
LogDispatch.log_dispatch['loguserdealordercount'] = "loguserdealordercount"		--统计用户下单的任务
LogDispatch.log_dispatch['logusercancelordercount'] = "logusercancelordercount"	--统计用户取消订单的任务
LogDispatch.log_dispatch['loguserpassordercount'] = "loguserpassordercount"		--统计订单完成的任务
LogDispatch.log_dispatch['loguserrecharge'] = "loguserrecharge"					--统计充币
LogDispatch.log_dispatch['loguserextractcurrency'] = "loguserextractcurrency"	--统计提币
LogDispatch.log_dispatch['logusertransferinandout'] = "logusertransferinandout"	--统计转入转出
LogDispatch.log_dispatch['logteambalance'] = "logteambalance"					--统计团队余额
LogDispatch.log_dispatch['logteamincome'] = "logteamincome"						--统计团队收益
LogDispatch.log_dispatch['logsysincome'] = "logsysincome"						--统计系统收益
LogDispatch.log_dispatch['logsysaddordec'] = "logsysaddordec"					--统计系统调账

function LogDispatch.Push(strindex,jsonStr)
	redisItem:lpush( LogDispatch.log_dispatch[strindex], jsonStr, LogDispatch.redis_index )
end

function LogDispatch.del(strindex)
	redisItem:del( LogDispatch.log_dispatch[strindex], LogDispatch.redis_index )
end

--新增玩家
function LogDispatch.newUserCount(userid, channel, nickname, usertype)
	local data = {}
	data['userid'] = userid
	data['channel'] = channel
	data['nickname'] = nickname
	data['usertype'] = usertype
	
	LogDispatch.Push("lognewusercount", luajson.encode(data))
end

--下单
function LogDispatch.userDealOrderCount(buy_user_id, sell_user_id)
	local data = {}
	data['buy_user_id'] = buy_user_id							--买币的用户
	data['sell_user_id'] = sell_user_id							--卖币的用户
	
	LogDispatch.Push("loguserdealordercount", luajson.encode(data))
end

--取消订单
function LogDispatch.userCancelOrderCount(buy_user_id, sell_user_id)
	local data = {}
	data['buy_user_id'] = buy_user_id							--买币的用户
	data['sell_user_id'] = sell_user_id							--卖币的用户
	
	LogDispatch.Push("logusercancelordercount", luajson.encode(data))
end

--完成订单
function LogDispatch.userPassOrderCount(buy_user_id, sell_user_id, amount, fee, confirm_time, payType, money, payID, dealtype, withdrawtype,paytype,owership)
	local data = {}
	data['buy_user_id'] = buy_user_id							--买币的用户
	data['sell_user_id'] = sell_user_id							--卖币的用户
	data['amount'] = amount										--成交数量
	data['fee'] = fee											--手续费
	data['confirm_time'] = confirm_time							--手续费
	data['payType'] = payType									--手续费
	data['money'] = money										--人民币
	data['payID'] = payID										--人民币
	data['dealType'] = dealtype									--人民币
	data['withdrawType'] = withdrawtype							--0 otc  1 代付
	data['paytype'] = paytype									--0 otc  1 代付
	data['owership'] = owership									--0-系统地址 1-商户自备地址
	LogDispatch.Push("loguserpassordercount", luajson.encode(data))
end

--充币
function LogDispatch.userRecharge(userid, amount, fee,getAmount, isT)
	local data = {}
	data['userid'] = userid									--充币用户
	data['amount'] = amount									--充币数量
	data['fee'] = fee										--手续费
	data['getAmount'] = getAmount							--到账数量
	data['isT'] = isT							--到账数量
	
	LogDispatch.Push("loguserrecharge", luajson.encode(data))
end

--提币
function LogDispatch.userExtractCurrency(userid, amount, fee, getAmount)
	local data = {}
	data['userid'] = userid									--提币用户
	data['amount'] = amount									--提币数量
	data['fee'] = fee										--提币手续费
	data['getAmount'] = getAmount							--到账数量
	
	LogDispatch.Push("loguserextractcurrency", luajson.encode(data))
end

--转入转出
function LogDispatch.userTransferInAndOut(fromUserID, toUserID, amount, fee, getAmount)
	local data = {}
	data['fromUserID'] = fromUserID							--转出用户
	data['toUserID'] = toUserID								--转入用户
	data['amount'] = amount									--成交数量
	data['fee'] = fee										--手续费
	data['getAmount'] = getAmount							--到账
	
	LogDispatch.Push("logusertransferinandout", luajson.encode(data))
end

--分佣
function LogDispatch.userPumpCount(dealid)
	local data = {}
	data['orderid'] = dealid					--订单ID
	LogDispatch.Push("loguserpumpcount", luajson.encode(data))
end

--团队余额
function LogDispatch.teamBalance(userid, coinType, count)
	local data = {}
	data['userid'] = userid					--用户ID
	data['coinType'] = coinType				--钱包类型 1-数字货币 2-法币
	data['count'] = count					--变化金额
	LogDispatch.Push("logteambalance", luajson.encode(data))
end

--团队收益
function LogDispatch.teamIncome(userid, count)
	local data = {}
	data['userid'] = userid					--用户ID
	data['count'] = count					--变化金额
	LogDispatch.Push("logteamincome", luajson.encode(data))
end

--系统收益
function LogDispatch.sysIncome(userid, count)
	local data = {}
	data['userid'] = userid					--用户ID
	data['count'] = count					--变化金额
	LogDispatch.Push("logsysincome", luajson.encode(data))
end

--系统调账
function LogDispatch.sysAddOrDec(userid, optType, count)
	local data = {}
	data['userid'] = userid					--用户ID
	data['optType'] = optType				--1-系统补款 2-系统扣款
	data['count'] = count					--变化金额
	LogDispatch.Push("logsysaddordec", luajson.encode(data))
end