LogModel = {}

LogModel.redis_index = "redis_logmodel"
LogModel.sql_exec = "sql_exec" 												--排队写入数据库
LogModel.check_sub = "check_sub"					
LogModel.check_sub_daily = "check_sub_daily"					
LogModel.check_sub_week = "check_sub_week"		
LogModel.check_sys = "check_sys"					
LogModel.check_sys_daily = "check_sys_daily"					
LogModel.check_sys_week = "check_sys_week"				

function LogModel.PushGameSqlServer(sqlCase)
	
	if sqlCase == nil then
		return
	end
	redisItem:rpush(LogModel.sql_exec, sqlCase, LogModel.redis_index)
	
end

function LogModel.PopGameSqlServer(sqlCase)
	
	return redisItem:lpop(LogModel.sql_exec, LogModel.redis_index)
	
end


function LogModel.CheckUserDaily(userid, channel, dateid) 
	
	local sqlCase = "select id from log_user_daily where userid="..userid.." and  dateid='"..dateid.."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		sqlCase = "insert into log_user_daily(userid,dateid,channel) values("..userid..",'"..dateid.."','"..channel.."')"
		mysqlItem:execute(sqlCase)		
	end
	
end

function LogModel.CheckPayTypeDaily(userid, channel, dateid, paytype) 
	
	local sqlCase = "select id from log_paytype_daily where userid="..userid.." and  dateid='"..dateid.."' and paytype="..paytype
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		sqlCase = "insert into log_paytype_daily(userid,dateid,channel,paytype) values("..userid..",'"..dateid.."','"..channel.."',"..paytype..")"
		mysqlItem:execute(sqlCase)	
	end
	
end

function LogModel.CheckPayType(userid, channel, paytype) 
	
	local sqlCase = "select id from log_paytype where userid="..userid.." and paytype="..paytype
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		sqlCase = "insert into log_paytype(userid,channel,paytype) values("..userid..",'"..channel.."',"..paytype..")"
		mysqlItem:execute(sqlCase)
	end
	
end


function LogModel.CheckPlatformDaily(platformid, dateid, dealType, payID) 
	
	local sqlCase = "select id from log_subsystem_daily where platform_id="..platformid.." and  dateid='"..dateid.."' and deal_type="..dealType.." and payid="..payID
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		local sqlCase = "select platform_name from dy_subsystem_info where id="..platformid
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch()
		local platformName = ""
		if sqlData ~= nil then
			platformName = sqlData
		end
		sqlCase = "insert into log_subsystem_daily(platform_id,dateid,deal_type,payid,platform_name) values("..platformid..",'"..dateid.."',"..dealType..","..payID..",'"..platformName.."')"
		mysqlItem:execute(sqlCase)		
	end
	
end

function LogModel.CheckSub(platformID) 
	
	local ret = redisItem:hget(LogModel.check_sub, platformID, LogModel.redis_index)
	if ret ~= "1" then
		local sqlCase = "select * from log_sub where platform_id='"..platformID.."'"
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			local platformName = ""
			local sqlCase = "select platform_name from dy_subsystem_info where id="..platformID
			mysqlItem:executeQuery(sqlCase)
			local sqlData = mysqlItem:fetch()
			if sqlData ~= nil then
				platformName = sqlData
			end
			
			if tonumber(platformID) == 0 then
				platformName = "系统平台"
			end
		
			local sqlCase = "insert into log_sub(platform_id,platform_name) values('"..platformID.."','"..platformName.."')"
			mysqlItem:execute(sqlCase)		
		end
		
		redisItem:hset(LogModel.check_sub, platformID, 1, LogModel.redis_index)
	end
	
end

function LogModel.CheckSubDaily(platformID, dateid) 
	local ret = redisItem:hget(LogModel.check_sub_daily, platformID, LogModel.redis_index)
	if ret ~= dateid then
		local sqlCase = "select * from log_sub_daily where platform_id='"..platformID.."' and dateid='"..dateid.."'"
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			local platformName = ""
			local sqlCase = "select platform_name from dy_subsystem_info where id="..platformID
			mysqlItem:executeQuery(sqlCase)
			local sqlData = mysqlItem:fetch()
			if sqlData ~= nil then
				platformName = sqlData
			end
			
			if tonumber(platformID) == 0 then
				platformName = "系统平台"
			end
		
			local sqlCase = "insert into log_sub_daily(platform_id,platform_name, dateid) values('"..platformID.."','"..platformName.."','"..dateid.."')"
			mysqlItem:execute(sqlCase)	
		end
		
		redisItem:hset(LogModel.check_sub_daily, platformID, dateid, LogModel.redis_index)
	end
end

function LogModel.CheckSubWeek(platformID, dateid) 
	local ret = redisItem:hget(LogModel.check_sub_week, platformID, LogModel.redis_index)
	if  ret ~= dateid then
		local sqlCase = "select * from log_sub_week where platform_id='"..platformID.."' and dateid='"..dateid.."'"
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			local platformName = ""
			local sqlCase = "select platform_name from dy_subsystem_info where id="..platformID
			mysqlItem:executeQuery(sqlCase)
			local sqlData = mysqlItem:fetch()
			if sqlData ~= nil then
				platformName = sqlData
			end
			
			if tonumber(platformID) == 0 then
				platformName = "系统平台"
			end
			
			local tableTime = TimeUtils.GetTableTime()
			local tTime = TimeUtils.GetTime()
			local wday = tableTime.wday == 1 and 7 or tableTime.wday - 1
			startTime = tTime - ((wday - 1) * 86400)
			endTime = tTime + ((7 - wday) * 86400)
			local startStr = TimeUtils.GetDayString(startTime)
			local endStr = TimeUtils.GetDayString(endTime)
		
			local sqlCase = "insert into log_sub_week(platform_id,platform_name, dateid,dateid_start,dateid_end) values('"..platformID
				.."','"..platformName.."','"..dateid.."','"..startStr.."','"..endStr.."')"
			mysqlItem:execute(sqlCase)	
		end
		
		redisItem:hset(LogModel.check_sub_week, platformID, dateid, LogModel.redis_index)
	end
end

function LogModel.CheckSys(channel) 

	local ret = redisItem:hget(LogModel.check_sys, channel, LogModel.redis_index)
	if ret ~= "1" then
		local sqlCase = "select * from log_sys where channel='"..channel.."'"
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			local platformID = 0
			local sqlCase = "select platform_id from dy_channel_info where channel='"..channel.."'"
			mysqlItem:executeQuery(sqlCase)
			local sqlData = mysqlItem:fetch()
			if sqlData ~= nil then
				platformID = tonumber(sqlData)
			end
			local platformName = ""
			local sqlCase = "select platform_name from dy_subsystem_info where id="..platformID
			mysqlItem:executeQuery(sqlCase)
			local sqlData = mysqlItem:fetch()
			if sqlData ~= nil then
				platformName = sqlData
			end
			
			if tonumber(platformID) == 0 then
				platformName = "系统平台"
			end
		
			local sqlCase = "insert into log_sys(channel,platform_id,platform_name) values('"..channel.."','"..platformID.."','"..platformName.."')"
			mysqlItem:execute(sqlCase)	
		end
		
		redisItem:hset(LogModel.check_sys, channel, 1, LogModel.redis_index)
	end
	
end

function LogModel.CheckSysDaily(channel, dateid) 

	local ret = redisItem:hget(LogModel.check_sys_daily, channel, LogModel.redis_index)
	if ret ~= dateid then
		local sqlCase = "select * from log_sys_daily where channel='"..channel.."' and dateid='"..dateid.."'"
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			local platformID = 0 
			local sqlCase = "select platform_id from dy_channel_info where channel='"..channel.."'"
			mysqlItem:executeQuery(sqlCase)
			local sqlData = mysqlItem:fetch()
			if sqlData ~= nil then
				platformID = tonumber(sqlData)
			end
			local platformName = ""
			local sqlCase = "select platform_name from dy_subsystem_info where id="..platformID
			mysqlItem:executeQuery(sqlCase)
			local sqlData = mysqlItem:fetch()
			if sqlData ~= nil then
				platformName = sqlData
			end
			
			if tonumber(platformID) == 0 then
				platformName = "系统平台"
			end
		
			local sqlCase = "insert into log_sys_daily(channel,platform_id,platform_name, dateid) values('"..channel.."','"..platformID.."','"..platformName.."','"..dateid.."')"
			mysqlItem:execute(sqlCase)	
		end
		
		redisItem:hset(LogModel.check_sys_daily, channel, dateid, LogModel.redis_index)
	end
end

function LogModel.CheckSysWeek(channel, dateid) 

	local ret = redisItem:hget(LogModel.check_sys_week, channel, LogModel.redis_index)
	if  ret ~= dateid then
		local sqlCase = "select * from log_sys_week where channel='"..channel.."' and dateid='"..dateid.."'"
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch({})
		if sqlData == nil then
			local platformID = 0 
			local sqlCase = "select platform_id from dy_channel_info where channel='"..channel.."'"
			mysqlItem:executeQuery(sqlCase)
			local sqlData = mysqlItem:fetch()
			if sqlData ~= nil then
				platformID = tonumber(sqlData)
			end
			
			local platformName = ""
			local sqlCase = "select platform_name from dy_subsystem_info where id="..platformID
			mysqlItem:executeQuery(sqlCase)
			local sqlData = mysqlItem:fetch()
			if sqlData ~= nil then
				platformName = sqlData
			end
			
			if tonumber(platformID) == 0 then
				platformName = "系统平台"
			end
			
			local tableTime = TimeUtils.GetTableTime()
			local tTime = TimeUtils.GetTime()
			local wday = tableTime.wday == 1 and 7 or tableTime.wday - 1
			startTime = tTime - ((wday - 1) * 86400)
			endTime = tTime + ((7 - wday) * 86400)
			local startStr = TimeUtils.GetDayString(startTime)
			local endStr = TimeUtils.GetDayString(endTime)
		
			local sqlCase = "insert into log_sys_week(channel,platform_id,platform_name, dateid,dateid_start,dateid_end) values('"..channel.."','"..platformID
				.."','"..platformName.."','"..dateid.."','"..startStr.."','"..endStr.."')"
			mysqlItem:execute(sqlCase)	
		end
		
		redisItem:hset(LogModel.check_sys_week, channel, dateid, LogModel.redis_index)
	end
end