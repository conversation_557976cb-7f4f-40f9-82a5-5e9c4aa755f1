-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
module('st_notice_pb')


local NOTICEINFO = protobuf.Descriptor();
local NOTICEINFO_ID_FIELD = protobuf.FieldDescriptor();
local NOTICEINFO_TITLE_FIELD = protobuf.FieldDescriptor();
local NOTICEINFO_CONTENT_FIELD = protobuf.FieldDescriptor();
local NOTICEINFO_NOTICETYPE_FIELD = protobuf.FieldDescriptor();
local NOTICEINFO_EXPIRATIONTIME_FIELD = protobuf.FieldDescriptor();
local NOTICEINFO_REMARK_FIELD = protobuf.FieldDescriptor();
local MESSAGEINFO = protobuf.Descriptor();
local MESSAGEINFO_ID_FIELD = protobuf.FieldDescriptor();
local MESSAGEINFO_MESSAGETYPE_FIELD = protobuf.FieldDescriptor();
local MESSAGEINFO_TITLE_FIELD = protobuf.FieldDescriptor();
local MESSAGEINFO_CONTENT_FIELD = protobuf.FieldDescriptor();
local MESSAGEINFO_REMARK_FIELD = protobuf.FieldDescriptor();
local MESSAGEINFO_ISREAD_FIELD = protobuf.FieldDescriptor();

NOTICEINFO_ID_FIELD.name = "id"
NOTICEINFO_ID_FIELD.full_name = ".prootc.noticeinfo.id"
NOTICEINFO_ID_FIELD.number = 1
NOTICEINFO_ID_FIELD.index = 0
NOTICEINFO_ID_FIELD.label = 1
NOTICEINFO_ID_FIELD.has_default_value = false
NOTICEINFO_ID_FIELD.default_value = 0
NOTICEINFO_ID_FIELD.type = 5
NOTICEINFO_ID_FIELD.cpp_type = 1

NOTICEINFO_TITLE_FIELD.name = "title"
NOTICEINFO_TITLE_FIELD.full_name = ".prootc.noticeinfo.title"
NOTICEINFO_TITLE_FIELD.number = 2
NOTICEINFO_TITLE_FIELD.index = 1
NOTICEINFO_TITLE_FIELD.label = 1
NOTICEINFO_TITLE_FIELD.has_default_value = false
NOTICEINFO_TITLE_FIELD.default_value = ""
NOTICEINFO_TITLE_FIELD.type = 9
NOTICEINFO_TITLE_FIELD.cpp_type = 9

NOTICEINFO_CONTENT_FIELD.name = "content"
NOTICEINFO_CONTENT_FIELD.full_name = ".prootc.noticeinfo.content"
NOTICEINFO_CONTENT_FIELD.number = 3
NOTICEINFO_CONTENT_FIELD.index = 2
NOTICEINFO_CONTENT_FIELD.label = 1
NOTICEINFO_CONTENT_FIELD.has_default_value = false
NOTICEINFO_CONTENT_FIELD.default_value = ""
NOTICEINFO_CONTENT_FIELD.type = 9
NOTICEINFO_CONTENT_FIELD.cpp_type = 9

NOTICEINFO_NOTICETYPE_FIELD.name = "noticetype"
NOTICEINFO_NOTICETYPE_FIELD.full_name = ".prootc.noticeinfo.noticetype"
NOTICEINFO_NOTICETYPE_FIELD.number = 4
NOTICEINFO_NOTICETYPE_FIELD.index = 3
NOTICEINFO_NOTICETYPE_FIELD.label = 1
NOTICEINFO_NOTICETYPE_FIELD.has_default_value = false
NOTICEINFO_NOTICETYPE_FIELD.default_value = 0
NOTICEINFO_NOTICETYPE_FIELD.type = 5
NOTICEINFO_NOTICETYPE_FIELD.cpp_type = 1

NOTICEINFO_EXPIRATIONTIME_FIELD.name = "expirationtime"
NOTICEINFO_EXPIRATIONTIME_FIELD.full_name = ".prootc.noticeinfo.expirationtime"
NOTICEINFO_EXPIRATIONTIME_FIELD.number = 5
NOTICEINFO_EXPIRATIONTIME_FIELD.index = 4
NOTICEINFO_EXPIRATIONTIME_FIELD.label = 1
NOTICEINFO_EXPIRATIONTIME_FIELD.has_default_value = false
NOTICEINFO_EXPIRATIONTIME_FIELD.default_value = 0
NOTICEINFO_EXPIRATIONTIME_FIELD.type = 5
NOTICEINFO_EXPIRATIONTIME_FIELD.cpp_type = 1

NOTICEINFO_REMARK_FIELD.name = "remark"
NOTICEINFO_REMARK_FIELD.full_name = ".prootc.noticeinfo.remark"
NOTICEINFO_REMARK_FIELD.number = 6
NOTICEINFO_REMARK_FIELD.index = 5
NOTICEINFO_REMARK_FIELD.label = 1
NOTICEINFO_REMARK_FIELD.has_default_value = false
NOTICEINFO_REMARK_FIELD.default_value = ""
NOTICEINFO_REMARK_FIELD.type = 9
NOTICEINFO_REMARK_FIELD.cpp_type = 9

NOTICEINFO.name = "noticeinfo"
NOTICEINFO.full_name = ".prootc.noticeinfo"
NOTICEINFO.nested_types = {}
NOTICEINFO.enum_types = {}
NOTICEINFO.fields = {NOTICEINFO_ID_FIELD, NOTICEINFO_TITLE_FIELD, NOTICEINFO_CONTENT_FIELD, NOTICEINFO_NOTICETYPE_FIELD, NOTICEINFO_EXPIRATIONTIME_FIELD, NOTICEINFO_REMARK_FIELD}
NOTICEINFO.is_extendable = false
NOTICEINFO.extensions = {}
MESSAGEINFO_ID_FIELD.name = "id"
MESSAGEINFO_ID_FIELD.full_name = ".prootc.messageinfo.id"
MESSAGEINFO_ID_FIELD.number = 1
MESSAGEINFO_ID_FIELD.index = 0
MESSAGEINFO_ID_FIELD.label = 1
MESSAGEINFO_ID_FIELD.has_default_value = false
MESSAGEINFO_ID_FIELD.default_value = 0
MESSAGEINFO_ID_FIELD.type = 5
MESSAGEINFO_ID_FIELD.cpp_type = 1

MESSAGEINFO_MESSAGETYPE_FIELD.name = "messagetype"
MESSAGEINFO_MESSAGETYPE_FIELD.full_name = ".prootc.messageinfo.messagetype"
MESSAGEINFO_MESSAGETYPE_FIELD.number = 2
MESSAGEINFO_MESSAGETYPE_FIELD.index = 1
MESSAGEINFO_MESSAGETYPE_FIELD.label = 1
MESSAGEINFO_MESSAGETYPE_FIELD.has_default_value = false
MESSAGEINFO_MESSAGETYPE_FIELD.default_value = 0
MESSAGEINFO_MESSAGETYPE_FIELD.type = 5
MESSAGEINFO_MESSAGETYPE_FIELD.cpp_type = 1

MESSAGEINFO_TITLE_FIELD.name = "title"
MESSAGEINFO_TITLE_FIELD.full_name = ".prootc.messageinfo.title"
MESSAGEINFO_TITLE_FIELD.number = 3
MESSAGEINFO_TITLE_FIELD.index = 2
MESSAGEINFO_TITLE_FIELD.label = 1
MESSAGEINFO_TITLE_FIELD.has_default_value = false
MESSAGEINFO_TITLE_FIELD.default_value = ""
MESSAGEINFO_TITLE_FIELD.type = 9
MESSAGEINFO_TITLE_FIELD.cpp_type = 9

MESSAGEINFO_CONTENT_FIELD.name = "content"
MESSAGEINFO_CONTENT_FIELD.full_name = ".prootc.messageinfo.content"
MESSAGEINFO_CONTENT_FIELD.number = 4
MESSAGEINFO_CONTENT_FIELD.index = 3
MESSAGEINFO_CONTENT_FIELD.label = 1
MESSAGEINFO_CONTENT_FIELD.has_default_value = false
MESSAGEINFO_CONTENT_FIELD.default_value = ""
MESSAGEINFO_CONTENT_FIELD.type = 9
MESSAGEINFO_CONTENT_FIELD.cpp_type = 9

MESSAGEINFO_REMARK_FIELD.name = "remark"
MESSAGEINFO_REMARK_FIELD.full_name = ".prootc.messageinfo.remark"
MESSAGEINFO_REMARK_FIELD.number = 5
MESSAGEINFO_REMARK_FIELD.index = 4
MESSAGEINFO_REMARK_FIELD.label = 1
MESSAGEINFO_REMARK_FIELD.has_default_value = false
MESSAGEINFO_REMARK_FIELD.default_value = ""
MESSAGEINFO_REMARK_FIELD.type = 9
MESSAGEINFO_REMARK_FIELD.cpp_type = 9

MESSAGEINFO_ISREAD_FIELD.name = "isread"
MESSAGEINFO_ISREAD_FIELD.full_name = ".prootc.messageinfo.isread"
MESSAGEINFO_ISREAD_FIELD.number = 6
MESSAGEINFO_ISREAD_FIELD.index = 5
MESSAGEINFO_ISREAD_FIELD.label = 1
MESSAGEINFO_ISREAD_FIELD.has_default_value = false
MESSAGEINFO_ISREAD_FIELD.default_value = 0
MESSAGEINFO_ISREAD_FIELD.type = 5
MESSAGEINFO_ISREAD_FIELD.cpp_type = 1

MESSAGEINFO.name = "messageinfo"
MESSAGEINFO.full_name = ".prootc.messageinfo"
MESSAGEINFO.nested_types = {}
MESSAGEINFO.enum_types = {}
MESSAGEINFO.fields = {MESSAGEINFO_ID_FIELD, MESSAGEINFO_MESSAGETYPE_FIELD, MESSAGEINFO_TITLE_FIELD, MESSAGEINFO_CONTENT_FIELD, MESSAGEINFO_REMARK_FIELD, MESSAGEINFO_ISREAD_FIELD}
MESSAGEINFO.is_extendable = false
MESSAGEINFO.extensions = {}

messageinfo = protobuf.Message(MESSAGEINFO)
noticeinfo = protobuf.Message(NOTICEINFO)

----------nimol modify---------
ST_NOTICE_PB_MESSAGEINFO = MESSAGEINFO
ST_NOTICE_PB_NOTICEINFO = NOTICEINFO
