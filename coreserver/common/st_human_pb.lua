-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
module('st_human_pb')


local USERINFO = protobuf.Descriptor();
local USERINFO_USERID_FIELD = protobuf.FieldDescriptor();
local USERINFO_CID_FIELD = protobuf.FieldDescriptor();
local USERINFO_ACCOUNT_FIELD = protobuf.FieldDescriptor();
local USERINFO_PASSWORD_FIELD = protobuf.FieldDescriptor();
local USERINFO_NICKNAME_FIELD = protobuf.FieldDescriptor();
local USERINFO_ETHADDRESS_FIELD = protobuf.FieldDescriptor();
local USERINFO_FACE1_FIELD = protobuf.FieldDescriptor();
local USERINFO_REGDATE_FIELD = protobuf.FieldDescriptor();
local USERINFO_SEX_FIELD = protobuf.FieldDescriptor();
local USERINFO_AGE_FIELD = protobuf.FieldDescriptor();
local USERINFO_EMAIL_FIELD = protobuf.FieldDescriptor();
local USERINFO_PHONENUM_FIELD = protobuf.FieldDescriptor();
local USERINFO_ETHAMOUNT_FIELD = protobuf.FieldDescriptor();
local USERINFO_ERCUSDTAMOUNT_FIELD = protobuf.FieldDescriptor();
local USERINFO_ERCUSDTLOCKAMOUNT_FIELD = protobuf.FieldDescriptor();
local USERINFO_CHANNEL_FIELD = protobuf.FieldDescriptor();
local USERINFO_INVITECODE_FIELD = protobuf.FieldDescriptor();
local USERINFO_BINDCODE_FIELD = protobuf.FieldDescriptor();
local USERINFO_IMEI_FIELD = protobuf.FieldDescriptor();
local USERINFO_DEVNAME_FIELD = protobuf.FieldDescriptor();
local USERINFO_MACNAME_FIELD = protobuf.FieldDescriptor();
local USERINFO_MOBILETYPE_FIELD = protobuf.FieldDescriptor();
local USERINFO_LASTTIME_FIELD = protobuf.FieldDescriptor();
local USERINFO_PENULTTIME_FIELD = protobuf.FieldDescriptor();
local USERINFO_ISBAN_FIELD = protobuf.FieldDescriptor();
local USERINFO_DESCRIPTION_FIELD = protobuf.FieldDescriptor();
local USERINFO_BLACKLIST_FIELD = protobuf.FieldDescriptor();
local USERINFO_IP_FIELD = protobuf.FieldDescriptor();
local USERINFO_PROVINCE_FIELD = protobuf.FieldDescriptor();
local USERINFO_CITY_FIELD = protobuf.FieldDescriptor();
local USERINFO_BINDTYPE_FIELD = protobuf.FieldDescriptor();
local USERINFO_ISFUNDPASSWORD_FIELD = protobuf.FieldDescriptor();
local USERINFO_PAYTYPELIST_FIELD = protobuf.FieldDescriptor();
local USERINFO_AGENT_FIELD = protobuf.FieldDescriptor();
local USERINFO_USERTYPE_FIELD = protobuf.FieldDescriptor();
local USERINFO_AUTOSELL_FIELD = protobuf.FieldDescriptor();
local USERINFO_MINBUY_FIELD = protobuf.FieldDescriptor();
local USERINFO_MAXBUY_FIELD = protobuf.FieldDescriptor();
local USERINFO_MINSELL_FIELD = protobuf.FieldDescriptor();
local USERINFO_MAXSELL_FIELD = protobuf.FieldDescriptor();
local USERINFO_ISLOCK_FIELD = protobuf.FieldDescriptor();
local USERINFO_ISHANGBUY_FIELD = protobuf.FieldDescriptor();
local USERINFO_ISHANGSELL_FIELD = protobuf.FieldDescriptor();
local USERINFO_COMMTYPE_FIELD = protobuf.FieldDescriptor();
local USERINFO_PROHIBITLOGIN_FIELD = protobuf.FieldDescriptor();
local USERINFO_ISACCEPTORDER_FIELD = protobuf.FieldDescriptor();
local USERINFO_DEALLASTTIME_FIELD = protobuf.FieldDescriptor();
local USERINFO_BANTIME_FIELD = protobuf.FieldDescriptor();
local USERINFO_ISINVITED_FIELD = protobuf.FieldDescriptor();
local USERINFO_ISOTC_FIELD = protobuf.FieldDescriptor();
local USERINFO_ISBEHALFPAY_FIELD = protobuf.FieldDescriptor();
local USERINFO_ERCFCAMOUNT_FIELD = protobuf.FieldDescriptor();
local USERINFO_ERCFCLOCKAMOUNT_FIELD = protobuf.FieldDescriptor();
local USERINFO_DEALCOINTYPE_FIELD = protobuf.FieldDescriptor();
local USERINFO_ENERGYVALUE_FIELD = protobuf.FieldDescriptor();
local USERINFO_WEIGHTSVALUE_FIELD = protobuf.FieldDescriptor();
local USERINFO_TEAMNAME_FIELD = protobuf.FieldDescriptor();
local USERINFO_PLATFORMID_FIELD = protobuf.FieldDescriptor();
local USERINFO_ISTEAMACCEPTORDER_FIELD = protobuf.FieldDescriptor();
local USERINFO_PREAUTHORIZATION_FIELD = protobuf.FieldDescriptor();
local USERINFO_COINPAYRATE_FIELD = protobuf.FieldDescriptor();
local USERINFO_COINPAYERC_FIELD = protobuf.FieldDescriptor();
local USERINFO_COINPAYTRC_FIELD = protobuf.FieldDescriptor();
local USERINFO_ALLOWSYSADDR_FIELD = protobuf.FieldDescriptor();
local USERINFO_SYSERCMIN_FIELD = protobuf.FieldDescriptor();
local USERINFO_COINPAYUSDTAMOUNT_FIELD = protobuf.FieldDescriptor();
local USERINFO_COINPAYUSDTLOCKAMOUNT_FIELD = protobuf.FieldDescriptor();
local USERINFO_ISCHECK_FIELD = protobuf.FieldDescriptor();
local USERINFO_EXTRACTCURRENCYRATEERC_FIELD = protobuf.FieldDescriptor();
local USERINFO_EXTRACTCURRENCYRATETRC_FIELD = protobuf.FieldDescriptor();
local COININFO = protobuf.Descriptor();
local COININFO_COINID_FIELD = protobuf.FieldDescriptor();
local COININFO_COINNAME_FIELD = protobuf.FieldDescriptor();
local COININFO_COINAMOUNT_FIELD = protobuf.FieldDescriptor();
local COININFO_COINLOCKAMOUNT_FIELD = protobuf.FieldDescriptor();
local COININFO_COINFREEAMOUNT_FIELD = protobuf.FieldDescriptor();
local COININFO_STATUS_FIELD = protobuf.FieldDescriptor();
local COININFO_BUYRATE_FIELD = protobuf.FieldDescriptor();
local COININFO_SELLRATE_FIELD = protobuf.FieldDescriptor();
local COINADDR = protobuf.Descriptor();
local COINADDR_ADDRTYPE_FIELD = protobuf.FieldDescriptor();
local COINADDR_ADDRNAME_FIELD = protobuf.FieldDescriptor();
local COINADDR_ADDRESS_FIELD = protobuf.FieldDescriptor();
local PAYINFO = protobuf.Descriptor();
local PAYINFO_PAYTYPE_FIELD = protobuf.FieldDescriptor();
local PAYINFO_ACCOUNT_FIELD = protobuf.FieldDescriptor();
local PAYINFO_PAYEE_FIELD = protobuf.FieldDescriptor();
local PAYINFO_QRCODE_FIELD = protobuf.FieldDescriptor();
local PAYINFO_BANKNAME_FIELD = protobuf.FieldDescriptor();
local PAYINFO_BANKADDR_FIELD = protobuf.FieldDescriptor();
local PAYINFO_SINGLELIMIT_FIELD = protobuf.FieldDescriptor();
local PAYINFO_DAYLIMIT_FIELD = protobuf.FieldDescriptor();
local PAYINFO_ID_FIELD = protobuf.FieldDescriptor();
local PAYINFO_STATUS_FIELD = protobuf.FieldDescriptor();
local PAYINFO_USERID_FIELD = protobuf.FieldDescriptor();
local PAYINFO_TODAYMONEY_FIELD = protobuf.FieldDescriptor();
local PAYINFO_FOURTHPARTYID_FIELD = protobuf.FieldDescriptor();
local PAYINFO_DEALLASTTIME_FIELD = protobuf.FieldDescriptor();
local SUBINFO = protobuf.Descriptor();
local SUBINFO_USERID_FIELD = protobuf.FieldDescriptor();
local SUBINFO_NICKNAME_FIELD = protobuf.FieldDescriptor();
local SUBINFO_TEAMPERFORMANCE_FIELD = protobuf.FieldDescriptor();
local SUBINFO_MYPERFORMANCE_FIELD = protobuf.FieldDescriptor();
local SUBINFO_ISACCEPTORDER_FIELD = protobuf.FieldDescriptor();
local SUBINFO_PROHIBITLOGIN_FIELD = protobuf.FieldDescriptor();
local SUBINFO_PAYTYPELIST_FIELD = protobuf.FieldDescriptor();
local SUBINFO_PAYRATELIST_FIELD = protobuf.FieldDescriptor();
local SUBINFO_BUYCOUNT_FIELD = protobuf.FieldDescriptor();
local SUBINFO_BUYRATELIST_FIELD = protobuf.FieldDescriptor();
local SUBINFO_BEHALFBUYRATELIST_FIELD = protobuf.FieldDescriptor();
local TWALLETORDERINFO = protobuf.Descriptor();
local TWALLETORDERINFO_ORDER_ID_FIELD = protobuf.FieldDescriptor();
local TWALLETORDERINFO_CHANNEL_FIELD = protobuf.FieldDescriptor();
local TWALLETORDERINFO_USERID_FIELD = protobuf.FieldDescriptor();
local TWALLETORDERINFO_NICKNAME_FIELD = protobuf.FieldDescriptor();
local TWALLETORDERINFO_CURRENCY_TYPE_FIELD = protobuf.FieldDescriptor();
local TWALLETORDERINFO_AMOUNT_FIELD = protobuf.FieldDescriptor();
local TWALLETORDERINFO_ACTUALAMOUNT_FIELD = protobuf.FieldDescriptor();
local TWALLETORDERINFO_TRUSTEESHIP_FIELD = protobuf.FieldDescriptor();
local TWALLETORDERINFO_USERTXID_FIELD = protobuf.FieldDescriptor();
local TWALLETORDERINFO_CREATE_FIELD = protobuf.FieldDescriptor();
local TWALLETORDERINFO_REMARKS_FIELD = protobuf.FieldDescriptor();
local TWALLETORDERINFO_STATUS_FIELD = protobuf.FieldDescriptor();
local TWALLETORDERINFO_COINID_FIELD = protobuf.FieldDescriptor();
local TWALLETORDERINFO_COINNAME_FIELD = protobuf.FieldDescriptor();
local TWALLETORDERINFO_ADDRTYPE_FIELD = protobuf.FieldDescriptor();
local TWALLETORDERINFO_ADDRNAME_FIELD = protobuf.FieldDescriptor();

USERINFO_USERID_FIELD.name = "userid"
USERINFO_USERID_FIELD.full_name = ".prootc.userinfo.userid"
USERINFO_USERID_FIELD.number = 1
USERINFO_USERID_FIELD.index = 0
USERINFO_USERID_FIELD.label = 1
USERINFO_USERID_FIELD.has_default_value = false
USERINFO_USERID_FIELD.default_value = 0
USERINFO_USERID_FIELD.type = 5
USERINFO_USERID_FIELD.cpp_type = 1

USERINFO_CID_FIELD.name = "cid"
USERINFO_CID_FIELD.full_name = ".prootc.userinfo.cid"
USERINFO_CID_FIELD.number = 2
USERINFO_CID_FIELD.index = 1
USERINFO_CID_FIELD.label = 1
USERINFO_CID_FIELD.has_default_value = false
USERINFO_CID_FIELD.default_value = ""
USERINFO_CID_FIELD.type = 9
USERINFO_CID_FIELD.cpp_type = 9

USERINFO_ACCOUNT_FIELD.name = "account"
USERINFO_ACCOUNT_FIELD.full_name = ".prootc.userinfo.account"
USERINFO_ACCOUNT_FIELD.number = 3
USERINFO_ACCOUNT_FIELD.index = 2
USERINFO_ACCOUNT_FIELD.label = 1
USERINFO_ACCOUNT_FIELD.has_default_value = false
USERINFO_ACCOUNT_FIELD.default_value = ""
USERINFO_ACCOUNT_FIELD.type = 9
USERINFO_ACCOUNT_FIELD.cpp_type = 9

USERINFO_PASSWORD_FIELD.name = "password"
USERINFO_PASSWORD_FIELD.full_name = ".prootc.userinfo.password"
USERINFO_PASSWORD_FIELD.number = 4
USERINFO_PASSWORD_FIELD.index = 3
USERINFO_PASSWORD_FIELD.label = 1
USERINFO_PASSWORD_FIELD.has_default_value = false
USERINFO_PASSWORD_FIELD.default_value = ""
USERINFO_PASSWORD_FIELD.type = 9
USERINFO_PASSWORD_FIELD.cpp_type = 9

USERINFO_NICKNAME_FIELD.name = "nickname"
USERINFO_NICKNAME_FIELD.full_name = ".prootc.userinfo.nickname"
USERINFO_NICKNAME_FIELD.number = 5
USERINFO_NICKNAME_FIELD.index = 4
USERINFO_NICKNAME_FIELD.label = 1
USERINFO_NICKNAME_FIELD.has_default_value = false
USERINFO_NICKNAME_FIELD.default_value = ""
USERINFO_NICKNAME_FIELD.type = 9
USERINFO_NICKNAME_FIELD.cpp_type = 9

USERINFO_ETHADDRESS_FIELD.name = "ethaddress"
USERINFO_ETHADDRESS_FIELD.full_name = ".prootc.userinfo.ethaddress"
USERINFO_ETHADDRESS_FIELD.number = 6
USERINFO_ETHADDRESS_FIELD.index = 5
USERINFO_ETHADDRESS_FIELD.label = 1
USERINFO_ETHADDRESS_FIELD.has_default_value = false
USERINFO_ETHADDRESS_FIELD.default_value = ""
USERINFO_ETHADDRESS_FIELD.type = 9
USERINFO_ETHADDRESS_FIELD.cpp_type = 9

USERINFO_FACE1_FIELD.name = "face1"
USERINFO_FACE1_FIELD.full_name = ".prootc.userinfo.face1"
USERINFO_FACE1_FIELD.number = 7
USERINFO_FACE1_FIELD.index = 6
USERINFO_FACE1_FIELD.label = 1
USERINFO_FACE1_FIELD.has_default_value = false
USERINFO_FACE1_FIELD.default_value = ""
USERINFO_FACE1_FIELD.type = 9
USERINFO_FACE1_FIELD.cpp_type = 9

USERINFO_REGDATE_FIELD.name = "regdate"
USERINFO_REGDATE_FIELD.full_name = ".prootc.userinfo.regdate"
USERINFO_REGDATE_FIELD.number = 8
USERINFO_REGDATE_FIELD.index = 7
USERINFO_REGDATE_FIELD.label = 1
USERINFO_REGDATE_FIELD.has_default_value = false
USERINFO_REGDATE_FIELD.default_value = ""
USERINFO_REGDATE_FIELD.type = 9
USERINFO_REGDATE_FIELD.cpp_type = 9

USERINFO_SEX_FIELD.name = "sex"
USERINFO_SEX_FIELD.full_name = ".prootc.userinfo.sex"
USERINFO_SEX_FIELD.number = 9
USERINFO_SEX_FIELD.index = 8
USERINFO_SEX_FIELD.label = 1
USERINFO_SEX_FIELD.has_default_value = false
USERINFO_SEX_FIELD.default_value = 0
USERINFO_SEX_FIELD.type = 5
USERINFO_SEX_FIELD.cpp_type = 1

USERINFO_AGE_FIELD.name = "age"
USERINFO_AGE_FIELD.full_name = ".prootc.userinfo.age"
USERINFO_AGE_FIELD.number = 10
USERINFO_AGE_FIELD.index = 9
USERINFO_AGE_FIELD.label = 1
USERINFO_AGE_FIELD.has_default_value = false
USERINFO_AGE_FIELD.default_value = 0
USERINFO_AGE_FIELD.type = 5
USERINFO_AGE_FIELD.cpp_type = 1

USERINFO_EMAIL_FIELD.name = "email"
USERINFO_EMAIL_FIELD.full_name = ".prootc.userinfo.email"
USERINFO_EMAIL_FIELD.number = 11
USERINFO_EMAIL_FIELD.index = 10
USERINFO_EMAIL_FIELD.label = 1
USERINFO_EMAIL_FIELD.has_default_value = false
USERINFO_EMAIL_FIELD.default_value = ""
USERINFO_EMAIL_FIELD.type = 9
USERINFO_EMAIL_FIELD.cpp_type = 9

USERINFO_PHONENUM_FIELD.name = "phonenum"
USERINFO_PHONENUM_FIELD.full_name = ".prootc.userinfo.phonenum"
USERINFO_PHONENUM_FIELD.number = 12
USERINFO_PHONENUM_FIELD.index = 11
USERINFO_PHONENUM_FIELD.label = 1
USERINFO_PHONENUM_FIELD.has_default_value = false
USERINFO_PHONENUM_FIELD.default_value = ""
USERINFO_PHONENUM_FIELD.type = 9
USERINFO_PHONENUM_FIELD.cpp_type = 9

USERINFO_ETHAMOUNT_FIELD.name = "ethamount"
USERINFO_ETHAMOUNT_FIELD.full_name = ".prootc.userinfo.ethamount"
USERINFO_ETHAMOUNT_FIELD.number = 13
USERINFO_ETHAMOUNT_FIELD.index = 12
USERINFO_ETHAMOUNT_FIELD.label = 1
USERINFO_ETHAMOUNT_FIELD.has_default_value = false
USERINFO_ETHAMOUNT_FIELD.default_value = ""
USERINFO_ETHAMOUNT_FIELD.type = 9
USERINFO_ETHAMOUNT_FIELD.cpp_type = 9

USERINFO_ERCUSDTAMOUNT_FIELD.name = "ercusdtamount"
USERINFO_ERCUSDTAMOUNT_FIELD.full_name = ".prootc.userinfo.ercusdtamount"
USERINFO_ERCUSDTAMOUNT_FIELD.number = 14
USERINFO_ERCUSDTAMOUNT_FIELD.index = 13
USERINFO_ERCUSDTAMOUNT_FIELD.label = 1
USERINFO_ERCUSDTAMOUNT_FIELD.has_default_value = false
USERINFO_ERCUSDTAMOUNT_FIELD.default_value = ""
USERINFO_ERCUSDTAMOUNT_FIELD.type = 9
USERINFO_ERCUSDTAMOUNT_FIELD.cpp_type = 9

USERINFO_ERCUSDTLOCKAMOUNT_FIELD.name = "ercusdtlockamount"
USERINFO_ERCUSDTLOCKAMOUNT_FIELD.full_name = ".prootc.userinfo.ercusdtlockamount"
USERINFO_ERCUSDTLOCKAMOUNT_FIELD.number = 15
USERINFO_ERCUSDTLOCKAMOUNT_FIELD.index = 14
USERINFO_ERCUSDTLOCKAMOUNT_FIELD.label = 1
USERINFO_ERCUSDTLOCKAMOUNT_FIELD.has_default_value = false
USERINFO_ERCUSDTLOCKAMOUNT_FIELD.default_value = ""
USERINFO_ERCUSDTLOCKAMOUNT_FIELD.type = 9
USERINFO_ERCUSDTLOCKAMOUNT_FIELD.cpp_type = 9

USERINFO_CHANNEL_FIELD.name = "channel"
USERINFO_CHANNEL_FIELD.full_name = ".prootc.userinfo.channel"
USERINFO_CHANNEL_FIELD.number = 16
USERINFO_CHANNEL_FIELD.index = 15
USERINFO_CHANNEL_FIELD.label = 1
USERINFO_CHANNEL_FIELD.has_default_value = false
USERINFO_CHANNEL_FIELD.default_value = ""
USERINFO_CHANNEL_FIELD.type = 9
USERINFO_CHANNEL_FIELD.cpp_type = 9

USERINFO_INVITECODE_FIELD.name = "invitecode"
USERINFO_INVITECODE_FIELD.full_name = ".prootc.userinfo.invitecode"
USERINFO_INVITECODE_FIELD.number = 17
USERINFO_INVITECODE_FIELD.index = 16
USERINFO_INVITECODE_FIELD.label = 1
USERINFO_INVITECODE_FIELD.has_default_value = false
USERINFO_INVITECODE_FIELD.default_value = ""
USERINFO_INVITECODE_FIELD.type = 9
USERINFO_INVITECODE_FIELD.cpp_type = 9

USERINFO_BINDCODE_FIELD.name = "bindcode"
USERINFO_BINDCODE_FIELD.full_name = ".prootc.userinfo.bindcode"
USERINFO_BINDCODE_FIELD.number = 18
USERINFO_BINDCODE_FIELD.index = 17
USERINFO_BINDCODE_FIELD.label = 1
USERINFO_BINDCODE_FIELD.has_default_value = false
USERINFO_BINDCODE_FIELD.default_value = ""
USERINFO_BINDCODE_FIELD.type = 9
USERINFO_BINDCODE_FIELD.cpp_type = 9

USERINFO_IMEI_FIELD.name = "imei"
USERINFO_IMEI_FIELD.full_name = ".prootc.userinfo.imei"
USERINFO_IMEI_FIELD.number = 19
USERINFO_IMEI_FIELD.index = 18
USERINFO_IMEI_FIELD.label = 1
USERINFO_IMEI_FIELD.has_default_value = false
USERINFO_IMEI_FIELD.default_value = ""
USERINFO_IMEI_FIELD.type = 9
USERINFO_IMEI_FIELD.cpp_type = 9

USERINFO_DEVNAME_FIELD.name = "devname"
USERINFO_DEVNAME_FIELD.full_name = ".prootc.userinfo.devname"
USERINFO_DEVNAME_FIELD.number = 20
USERINFO_DEVNAME_FIELD.index = 19
USERINFO_DEVNAME_FIELD.label = 1
USERINFO_DEVNAME_FIELD.has_default_value = false
USERINFO_DEVNAME_FIELD.default_value = ""
USERINFO_DEVNAME_FIELD.type = 9
USERINFO_DEVNAME_FIELD.cpp_type = 9

USERINFO_MACNAME_FIELD.name = "macname"
USERINFO_MACNAME_FIELD.full_name = ".prootc.userinfo.macname"
USERINFO_MACNAME_FIELD.number = 21
USERINFO_MACNAME_FIELD.index = 20
USERINFO_MACNAME_FIELD.label = 1
USERINFO_MACNAME_FIELD.has_default_value = false
USERINFO_MACNAME_FIELD.default_value = ""
USERINFO_MACNAME_FIELD.type = 9
USERINFO_MACNAME_FIELD.cpp_type = 9

USERINFO_MOBILETYPE_FIELD.name = "mobiletype"
USERINFO_MOBILETYPE_FIELD.full_name = ".prootc.userinfo.mobiletype"
USERINFO_MOBILETYPE_FIELD.number = 22
USERINFO_MOBILETYPE_FIELD.index = 21
USERINFO_MOBILETYPE_FIELD.label = 1
USERINFO_MOBILETYPE_FIELD.has_default_value = false
USERINFO_MOBILETYPE_FIELD.default_value = 0
USERINFO_MOBILETYPE_FIELD.type = 5
USERINFO_MOBILETYPE_FIELD.cpp_type = 1

USERINFO_LASTTIME_FIELD.name = "lasttime"
USERINFO_LASTTIME_FIELD.full_name = ".prootc.userinfo.lasttime"
USERINFO_LASTTIME_FIELD.number = 23
USERINFO_LASTTIME_FIELD.index = 22
USERINFO_LASTTIME_FIELD.label = 1
USERINFO_LASTTIME_FIELD.has_default_value = false
USERINFO_LASTTIME_FIELD.default_value = 0
USERINFO_LASTTIME_FIELD.type = 5
USERINFO_LASTTIME_FIELD.cpp_type = 1

USERINFO_PENULTTIME_FIELD.name = "penulttime"
USERINFO_PENULTTIME_FIELD.full_name = ".prootc.userinfo.penulttime"
USERINFO_PENULTTIME_FIELD.number = 24
USERINFO_PENULTTIME_FIELD.index = 23
USERINFO_PENULTTIME_FIELD.label = 1
USERINFO_PENULTTIME_FIELD.has_default_value = false
USERINFO_PENULTTIME_FIELD.default_value = 0
USERINFO_PENULTTIME_FIELD.type = 5
USERINFO_PENULTTIME_FIELD.cpp_type = 1

USERINFO_ISBAN_FIELD.name = "isban"
USERINFO_ISBAN_FIELD.full_name = ".prootc.userinfo.isban"
USERINFO_ISBAN_FIELD.number = 25
USERINFO_ISBAN_FIELD.index = 24
USERINFO_ISBAN_FIELD.label = 1
USERINFO_ISBAN_FIELD.has_default_value = false
USERINFO_ISBAN_FIELD.default_value = 0
USERINFO_ISBAN_FIELD.type = 5
USERINFO_ISBAN_FIELD.cpp_type = 1

USERINFO_DESCRIPTION_FIELD.name = "description"
USERINFO_DESCRIPTION_FIELD.full_name = ".prootc.userinfo.description"
USERINFO_DESCRIPTION_FIELD.number = 26
USERINFO_DESCRIPTION_FIELD.index = 25
USERINFO_DESCRIPTION_FIELD.label = 1
USERINFO_DESCRIPTION_FIELD.has_default_value = false
USERINFO_DESCRIPTION_FIELD.default_value = ""
USERINFO_DESCRIPTION_FIELD.type = 9
USERINFO_DESCRIPTION_FIELD.cpp_type = 9

USERINFO_BLACKLIST_FIELD.name = "blacklist"
USERINFO_BLACKLIST_FIELD.full_name = ".prootc.userinfo.blacklist"
USERINFO_BLACKLIST_FIELD.number = 27
USERINFO_BLACKLIST_FIELD.index = 26
USERINFO_BLACKLIST_FIELD.label = 1
USERINFO_BLACKLIST_FIELD.has_default_value = false
USERINFO_BLACKLIST_FIELD.default_value = 0
USERINFO_BLACKLIST_FIELD.type = 5
USERINFO_BLACKLIST_FIELD.cpp_type = 1

USERINFO_IP_FIELD.name = "ip"
USERINFO_IP_FIELD.full_name = ".prootc.userinfo.ip"
USERINFO_IP_FIELD.number = 28
USERINFO_IP_FIELD.index = 27
USERINFO_IP_FIELD.label = 1
USERINFO_IP_FIELD.has_default_value = false
USERINFO_IP_FIELD.default_value = ""
USERINFO_IP_FIELD.type = 9
USERINFO_IP_FIELD.cpp_type = 9

USERINFO_PROVINCE_FIELD.name = "province"
USERINFO_PROVINCE_FIELD.full_name = ".prootc.userinfo.province"
USERINFO_PROVINCE_FIELD.number = 29
USERINFO_PROVINCE_FIELD.index = 28
USERINFO_PROVINCE_FIELD.label = 1
USERINFO_PROVINCE_FIELD.has_default_value = false
USERINFO_PROVINCE_FIELD.default_value = ""
USERINFO_PROVINCE_FIELD.type = 9
USERINFO_PROVINCE_FIELD.cpp_type = 9

USERINFO_CITY_FIELD.name = "city"
USERINFO_CITY_FIELD.full_name = ".prootc.userinfo.city"
USERINFO_CITY_FIELD.number = 30
USERINFO_CITY_FIELD.index = 29
USERINFO_CITY_FIELD.label = 1
USERINFO_CITY_FIELD.has_default_value = false
USERINFO_CITY_FIELD.default_value = ""
USERINFO_CITY_FIELD.type = 9
USERINFO_CITY_FIELD.cpp_type = 9

USERINFO_BINDTYPE_FIELD.name = "bindtype"
USERINFO_BINDTYPE_FIELD.full_name = ".prootc.userinfo.bindtype"
USERINFO_BINDTYPE_FIELD.number = 31
USERINFO_BINDTYPE_FIELD.index = 30
USERINFO_BINDTYPE_FIELD.label = 1
USERINFO_BINDTYPE_FIELD.has_default_value = false
USERINFO_BINDTYPE_FIELD.default_value = 0
USERINFO_BINDTYPE_FIELD.type = 5
USERINFO_BINDTYPE_FIELD.cpp_type = 1

USERINFO_ISFUNDPASSWORD_FIELD.name = "isfundpassword"
USERINFO_ISFUNDPASSWORD_FIELD.full_name = ".prootc.userinfo.isfundpassword"
USERINFO_ISFUNDPASSWORD_FIELD.number = 32
USERINFO_ISFUNDPASSWORD_FIELD.index = 31
USERINFO_ISFUNDPASSWORD_FIELD.label = 1
USERINFO_ISFUNDPASSWORD_FIELD.has_default_value = false
USERINFO_ISFUNDPASSWORD_FIELD.default_value = 0
USERINFO_ISFUNDPASSWORD_FIELD.type = 5
USERINFO_ISFUNDPASSWORD_FIELD.cpp_type = 1

USERINFO_PAYTYPELIST_FIELD.name = "paytypelist"
USERINFO_PAYTYPELIST_FIELD.full_name = ".prootc.userinfo.paytypelist"
USERINFO_PAYTYPELIST_FIELD.number = 33
USERINFO_PAYTYPELIST_FIELD.index = 32
USERINFO_PAYTYPELIST_FIELD.label = 3
USERINFO_PAYTYPELIST_FIELD.has_default_value = false
USERINFO_PAYTYPELIST_FIELD.default_value = {}
USERINFO_PAYTYPELIST_FIELD.type = 5
USERINFO_PAYTYPELIST_FIELD.cpp_type = 1

USERINFO_AGENT_FIELD.name = "agent"
USERINFO_AGENT_FIELD.full_name = ".prootc.userinfo.agent"
USERINFO_AGENT_FIELD.number = 34
USERINFO_AGENT_FIELD.index = 33
USERINFO_AGENT_FIELD.label = 1
USERINFO_AGENT_FIELD.has_default_value = false
USERINFO_AGENT_FIELD.default_value = 0
USERINFO_AGENT_FIELD.type = 5
USERINFO_AGENT_FIELD.cpp_type = 1

USERINFO_USERTYPE_FIELD.name = "usertype"
USERINFO_USERTYPE_FIELD.full_name = ".prootc.userinfo.usertype"
USERINFO_USERTYPE_FIELD.number = 35
USERINFO_USERTYPE_FIELD.index = 34
USERINFO_USERTYPE_FIELD.label = 1
USERINFO_USERTYPE_FIELD.has_default_value = false
USERINFO_USERTYPE_FIELD.default_value = 0
USERINFO_USERTYPE_FIELD.type = 5
USERINFO_USERTYPE_FIELD.cpp_type = 1

USERINFO_AUTOSELL_FIELD.name = "autosell"
USERINFO_AUTOSELL_FIELD.full_name = ".prootc.userinfo.autosell"
USERINFO_AUTOSELL_FIELD.number = 36
USERINFO_AUTOSELL_FIELD.index = 35
USERINFO_AUTOSELL_FIELD.label = 1
USERINFO_AUTOSELL_FIELD.has_default_value = false
USERINFO_AUTOSELL_FIELD.default_value = 0
USERINFO_AUTOSELL_FIELD.type = 5
USERINFO_AUTOSELL_FIELD.cpp_type = 1

USERINFO_MINBUY_FIELD.name = "minbuy"
USERINFO_MINBUY_FIELD.full_name = ".prootc.userinfo.minbuy"
USERINFO_MINBUY_FIELD.number = 37
USERINFO_MINBUY_FIELD.index = 36
USERINFO_MINBUY_FIELD.label = 1
USERINFO_MINBUY_FIELD.has_default_value = false
USERINFO_MINBUY_FIELD.default_value = ""
USERINFO_MINBUY_FIELD.type = 9
USERINFO_MINBUY_FIELD.cpp_type = 9

USERINFO_MAXBUY_FIELD.name = "maxbuy"
USERINFO_MAXBUY_FIELD.full_name = ".prootc.userinfo.maxbuy"
USERINFO_MAXBUY_FIELD.number = 38
USERINFO_MAXBUY_FIELD.index = 37
USERINFO_MAXBUY_FIELD.label = 1
USERINFO_MAXBUY_FIELD.has_default_value = false
USERINFO_MAXBUY_FIELD.default_value = ""
USERINFO_MAXBUY_FIELD.type = 9
USERINFO_MAXBUY_FIELD.cpp_type = 9

USERINFO_MINSELL_FIELD.name = "minsell"
USERINFO_MINSELL_FIELD.full_name = ".prootc.userinfo.minsell"
USERINFO_MINSELL_FIELD.number = 39
USERINFO_MINSELL_FIELD.index = 38
USERINFO_MINSELL_FIELD.label = 1
USERINFO_MINSELL_FIELD.has_default_value = false
USERINFO_MINSELL_FIELD.default_value = ""
USERINFO_MINSELL_FIELD.type = 9
USERINFO_MINSELL_FIELD.cpp_type = 9

USERINFO_MAXSELL_FIELD.name = "maxsell"
USERINFO_MAXSELL_FIELD.full_name = ".prootc.userinfo.maxsell"
USERINFO_MAXSELL_FIELD.number = 40
USERINFO_MAXSELL_FIELD.index = 39
USERINFO_MAXSELL_FIELD.label = 1
USERINFO_MAXSELL_FIELD.has_default_value = false
USERINFO_MAXSELL_FIELD.default_value = ""
USERINFO_MAXSELL_FIELD.type = 9
USERINFO_MAXSELL_FIELD.cpp_type = 9

USERINFO_ISLOCK_FIELD.name = "islock"
USERINFO_ISLOCK_FIELD.full_name = ".prootc.userinfo.islock"
USERINFO_ISLOCK_FIELD.number = 41
USERINFO_ISLOCK_FIELD.index = 40
USERINFO_ISLOCK_FIELD.label = 1
USERINFO_ISLOCK_FIELD.has_default_value = false
USERINFO_ISLOCK_FIELD.default_value = 0
USERINFO_ISLOCK_FIELD.type = 5
USERINFO_ISLOCK_FIELD.cpp_type = 1

USERINFO_ISHANGBUY_FIELD.name = "ishangbuy"
USERINFO_ISHANGBUY_FIELD.full_name = ".prootc.userinfo.ishangbuy"
USERINFO_ISHANGBUY_FIELD.number = 42
USERINFO_ISHANGBUY_FIELD.index = 41
USERINFO_ISHANGBUY_FIELD.label = 1
USERINFO_ISHANGBUY_FIELD.has_default_value = false
USERINFO_ISHANGBUY_FIELD.default_value = 0
USERINFO_ISHANGBUY_FIELD.type = 5
USERINFO_ISHANGBUY_FIELD.cpp_type = 1

USERINFO_ISHANGSELL_FIELD.name = "ishangsell"
USERINFO_ISHANGSELL_FIELD.full_name = ".prootc.userinfo.ishangsell"
USERINFO_ISHANGSELL_FIELD.number = 43
USERINFO_ISHANGSELL_FIELD.index = 42
USERINFO_ISHANGSELL_FIELD.label = 1
USERINFO_ISHANGSELL_FIELD.has_default_value = false
USERINFO_ISHANGSELL_FIELD.default_value = 0
USERINFO_ISHANGSELL_FIELD.type = 5
USERINFO_ISHANGSELL_FIELD.cpp_type = 1

USERINFO_COMMTYPE_FIELD.name = "commtype"
USERINFO_COMMTYPE_FIELD.full_name = ".prootc.userinfo.commtype"
USERINFO_COMMTYPE_FIELD.number = 44
USERINFO_COMMTYPE_FIELD.index = 43
USERINFO_COMMTYPE_FIELD.label = 1
USERINFO_COMMTYPE_FIELD.has_default_value = false
USERINFO_COMMTYPE_FIELD.default_value = 0
USERINFO_COMMTYPE_FIELD.type = 5
USERINFO_COMMTYPE_FIELD.cpp_type = 1

USERINFO_PROHIBITLOGIN_FIELD.name = "prohibitlogin"
USERINFO_PROHIBITLOGIN_FIELD.full_name = ".prootc.userinfo.prohibitlogin"
USERINFO_PROHIBITLOGIN_FIELD.number = 45
USERINFO_PROHIBITLOGIN_FIELD.index = 44
USERINFO_PROHIBITLOGIN_FIELD.label = 1
USERINFO_PROHIBITLOGIN_FIELD.has_default_value = false
USERINFO_PROHIBITLOGIN_FIELD.default_value = 0
USERINFO_PROHIBITLOGIN_FIELD.type = 5
USERINFO_PROHIBITLOGIN_FIELD.cpp_type = 1

USERINFO_ISACCEPTORDER_FIELD.name = "isacceptorder"
USERINFO_ISACCEPTORDER_FIELD.full_name = ".prootc.userinfo.isacceptorder"
USERINFO_ISACCEPTORDER_FIELD.number = 46
USERINFO_ISACCEPTORDER_FIELD.index = 45
USERINFO_ISACCEPTORDER_FIELD.label = 1
USERINFO_ISACCEPTORDER_FIELD.has_default_value = false
USERINFO_ISACCEPTORDER_FIELD.default_value = 0
USERINFO_ISACCEPTORDER_FIELD.type = 5
USERINFO_ISACCEPTORDER_FIELD.cpp_type = 1

USERINFO_DEALLASTTIME_FIELD.name = "deallasttime"
USERINFO_DEALLASTTIME_FIELD.full_name = ".prootc.userinfo.deallasttime"
USERINFO_DEALLASTTIME_FIELD.number = 47
USERINFO_DEALLASTTIME_FIELD.index = 46
USERINFO_DEALLASTTIME_FIELD.label = 1
USERINFO_DEALLASTTIME_FIELD.has_default_value = false
USERINFO_DEALLASTTIME_FIELD.default_value = ""
USERINFO_DEALLASTTIME_FIELD.type = 9
USERINFO_DEALLASTTIME_FIELD.cpp_type = 9

USERINFO_BANTIME_FIELD.name = "bantime"
USERINFO_BANTIME_FIELD.full_name = ".prootc.userinfo.bantime"
USERINFO_BANTIME_FIELD.number = 48
USERINFO_BANTIME_FIELD.index = 47
USERINFO_BANTIME_FIELD.label = 1
USERINFO_BANTIME_FIELD.has_default_value = false
USERINFO_BANTIME_FIELD.default_value = ""
USERINFO_BANTIME_FIELD.type = 9
USERINFO_BANTIME_FIELD.cpp_type = 9

USERINFO_ISINVITED_FIELD.name = "isinvited"
USERINFO_ISINVITED_FIELD.full_name = ".prootc.userinfo.isinvited"
USERINFO_ISINVITED_FIELD.number = 49
USERINFO_ISINVITED_FIELD.index = 48
USERINFO_ISINVITED_FIELD.label = 1
USERINFO_ISINVITED_FIELD.has_default_value = false
USERINFO_ISINVITED_FIELD.default_value = 0
USERINFO_ISINVITED_FIELD.type = 5
USERINFO_ISINVITED_FIELD.cpp_type = 1

USERINFO_ISOTC_FIELD.name = "isotc"
USERINFO_ISOTC_FIELD.full_name = ".prootc.userinfo.isotc"
USERINFO_ISOTC_FIELD.number = 50
USERINFO_ISOTC_FIELD.index = 49
USERINFO_ISOTC_FIELD.label = 1
USERINFO_ISOTC_FIELD.has_default_value = false
USERINFO_ISOTC_FIELD.default_value = 0
USERINFO_ISOTC_FIELD.type = 5
USERINFO_ISOTC_FIELD.cpp_type = 1

USERINFO_ISBEHALFPAY_FIELD.name = "isbehalfpay"
USERINFO_ISBEHALFPAY_FIELD.full_name = ".prootc.userinfo.isbehalfpay"
USERINFO_ISBEHALFPAY_FIELD.number = 51
USERINFO_ISBEHALFPAY_FIELD.index = 50
USERINFO_ISBEHALFPAY_FIELD.label = 1
USERINFO_ISBEHALFPAY_FIELD.has_default_value = false
USERINFO_ISBEHALFPAY_FIELD.default_value = 0
USERINFO_ISBEHALFPAY_FIELD.type = 5
USERINFO_ISBEHALFPAY_FIELD.cpp_type = 1

USERINFO_ERCFCAMOUNT_FIELD.name = "ercfcamount"
USERINFO_ERCFCAMOUNT_FIELD.full_name = ".prootc.userinfo.ercfcamount"
USERINFO_ERCFCAMOUNT_FIELD.number = 52
USERINFO_ERCFCAMOUNT_FIELD.index = 51
USERINFO_ERCFCAMOUNT_FIELD.label = 1
USERINFO_ERCFCAMOUNT_FIELD.has_default_value = false
USERINFO_ERCFCAMOUNT_FIELD.default_value = ""
USERINFO_ERCFCAMOUNT_FIELD.type = 9
USERINFO_ERCFCAMOUNT_FIELD.cpp_type = 9

USERINFO_ERCFCLOCKAMOUNT_FIELD.name = "ercfclockamount"
USERINFO_ERCFCLOCKAMOUNT_FIELD.full_name = ".prootc.userinfo.ercfclockamount"
USERINFO_ERCFCLOCKAMOUNT_FIELD.number = 53
USERINFO_ERCFCLOCKAMOUNT_FIELD.index = 52
USERINFO_ERCFCLOCKAMOUNT_FIELD.label = 1
USERINFO_ERCFCLOCKAMOUNT_FIELD.has_default_value = false
USERINFO_ERCFCLOCKAMOUNT_FIELD.default_value = ""
USERINFO_ERCFCLOCKAMOUNT_FIELD.type = 9
USERINFO_ERCFCLOCKAMOUNT_FIELD.cpp_type = 9

USERINFO_DEALCOINTYPE_FIELD.name = "dealcointype"
USERINFO_DEALCOINTYPE_FIELD.full_name = ".prootc.userinfo.dealcointype"
USERINFO_DEALCOINTYPE_FIELD.number = 54
USERINFO_DEALCOINTYPE_FIELD.index = 53
USERINFO_DEALCOINTYPE_FIELD.label = 1
USERINFO_DEALCOINTYPE_FIELD.has_default_value = false
USERINFO_DEALCOINTYPE_FIELD.default_value = 0
USERINFO_DEALCOINTYPE_FIELD.type = 5
USERINFO_DEALCOINTYPE_FIELD.cpp_type = 1

USERINFO_ENERGYVALUE_FIELD.name = "energyvalue"
USERINFO_ENERGYVALUE_FIELD.full_name = ".prootc.userinfo.energyvalue"
USERINFO_ENERGYVALUE_FIELD.number = 55
USERINFO_ENERGYVALUE_FIELD.index = 54
USERINFO_ENERGYVALUE_FIELD.label = 1
USERINFO_ENERGYVALUE_FIELD.has_default_value = false
USERINFO_ENERGYVALUE_FIELD.default_value = ""
USERINFO_ENERGYVALUE_FIELD.type = 9
USERINFO_ENERGYVALUE_FIELD.cpp_type = 9

USERINFO_WEIGHTSVALUE_FIELD.name = "weightsvalue"
USERINFO_WEIGHTSVALUE_FIELD.full_name = ".prootc.userinfo.weightsvalue"
USERINFO_WEIGHTSVALUE_FIELD.number = 56
USERINFO_WEIGHTSVALUE_FIELD.index = 55
USERINFO_WEIGHTSVALUE_FIELD.label = 1
USERINFO_WEIGHTSVALUE_FIELD.has_default_value = false
USERINFO_WEIGHTSVALUE_FIELD.default_value = ""
USERINFO_WEIGHTSVALUE_FIELD.type = 9
USERINFO_WEIGHTSVALUE_FIELD.cpp_type = 9

USERINFO_TEAMNAME_FIELD.name = "teamname"
USERINFO_TEAMNAME_FIELD.full_name = ".prootc.userinfo.teamname"
USERINFO_TEAMNAME_FIELD.number = 57
USERINFO_TEAMNAME_FIELD.index = 56
USERINFO_TEAMNAME_FIELD.label = 1
USERINFO_TEAMNAME_FIELD.has_default_value = false
USERINFO_TEAMNAME_FIELD.default_value = ""
USERINFO_TEAMNAME_FIELD.type = 9
USERINFO_TEAMNAME_FIELD.cpp_type = 9

USERINFO_PLATFORMID_FIELD.name = "platformid"
USERINFO_PLATFORMID_FIELD.full_name = ".prootc.userinfo.platformid"
USERINFO_PLATFORMID_FIELD.number = 58
USERINFO_PLATFORMID_FIELD.index = 57
USERINFO_PLATFORMID_FIELD.label = 1
USERINFO_PLATFORMID_FIELD.has_default_value = false
USERINFO_PLATFORMID_FIELD.default_value = 0
USERINFO_PLATFORMID_FIELD.type = 5
USERINFO_PLATFORMID_FIELD.cpp_type = 1

USERINFO_ISTEAMACCEPTORDER_FIELD.name = "isteamacceptorder"
USERINFO_ISTEAMACCEPTORDER_FIELD.full_name = ".prootc.userinfo.isteamacceptorder"
USERINFO_ISTEAMACCEPTORDER_FIELD.number = 59
USERINFO_ISTEAMACCEPTORDER_FIELD.index = 58
USERINFO_ISTEAMACCEPTORDER_FIELD.label = 1
USERINFO_ISTEAMACCEPTORDER_FIELD.has_default_value = false
USERINFO_ISTEAMACCEPTORDER_FIELD.default_value = 0
USERINFO_ISTEAMACCEPTORDER_FIELD.type = 5
USERINFO_ISTEAMACCEPTORDER_FIELD.cpp_type = 1

USERINFO_PREAUTHORIZATION_FIELD.name = "preauthorization"
USERINFO_PREAUTHORIZATION_FIELD.full_name = ".prootc.userinfo.preauthorization"
USERINFO_PREAUTHORIZATION_FIELD.number = 60
USERINFO_PREAUTHORIZATION_FIELD.index = 59
USERINFO_PREAUTHORIZATION_FIELD.label = 1
USERINFO_PREAUTHORIZATION_FIELD.has_default_value = false
USERINFO_PREAUTHORIZATION_FIELD.default_value = ""
USERINFO_PREAUTHORIZATION_FIELD.type = 9
USERINFO_PREAUTHORIZATION_FIELD.cpp_type = 9

USERINFO_COINPAYRATE_FIELD.name = "coinpayrate"
USERINFO_COINPAYRATE_FIELD.full_name = ".prootc.userinfo.coinpayrate"
USERINFO_COINPAYRATE_FIELD.number = 61
USERINFO_COINPAYRATE_FIELD.index = 60
USERINFO_COINPAYRATE_FIELD.label = 1
USERINFO_COINPAYRATE_FIELD.has_default_value = false
USERINFO_COINPAYRATE_FIELD.default_value = ""
USERINFO_COINPAYRATE_FIELD.type = 9
USERINFO_COINPAYRATE_FIELD.cpp_type = 9

USERINFO_COINPAYERC_FIELD.name = "coinpayerc"
USERINFO_COINPAYERC_FIELD.full_name = ".prootc.userinfo.coinpayerc"
USERINFO_COINPAYERC_FIELD.number = 62
USERINFO_COINPAYERC_FIELD.index = 61
USERINFO_COINPAYERC_FIELD.label = 1
USERINFO_COINPAYERC_FIELD.has_default_value = false
USERINFO_COINPAYERC_FIELD.default_value = 0
USERINFO_COINPAYERC_FIELD.type = 5
USERINFO_COINPAYERC_FIELD.cpp_type = 1

USERINFO_COINPAYTRC_FIELD.name = "coinpaytrc"
USERINFO_COINPAYTRC_FIELD.full_name = ".prootc.userinfo.coinpaytrc"
USERINFO_COINPAYTRC_FIELD.number = 63
USERINFO_COINPAYTRC_FIELD.index = 62
USERINFO_COINPAYTRC_FIELD.label = 1
USERINFO_COINPAYTRC_FIELD.has_default_value = false
USERINFO_COINPAYTRC_FIELD.default_value = 0
USERINFO_COINPAYTRC_FIELD.type = 5
USERINFO_COINPAYTRC_FIELD.cpp_type = 1

USERINFO_ALLOWSYSADDR_FIELD.name = "allowsysaddr"
USERINFO_ALLOWSYSADDR_FIELD.full_name = ".prootc.userinfo.allowsysaddr"
USERINFO_ALLOWSYSADDR_FIELD.number = 64
USERINFO_ALLOWSYSADDR_FIELD.index = 63
USERINFO_ALLOWSYSADDR_FIELD.label = 1
USERINFO_ALLOWSYSADDR_FIELD.has_default_value = false
USERINFO_ALLOWSYSADDR_FIELD.default_value = 0
USERINFO_ALLOWSYSADDR_FIELD.type = 5
USERINFO_ALLOWSYSADDR_FIELD.cpp_type = 1

USERINFO_SYSERCMIN_FIELD.name = "sysercmin"
USERINFO_SYSERCMIN_FIELD.full_name = ".prootc.userinfo.sysercmin"
USERINFO_SYSERCMIN_FIELD.number = 65
USERINFO_SYSERCMIN_FIELD.index = 64
USERINFO_SYSERCMIN_FIELD.label = 1
USERINFO_SYSERCMIN_FIELD.has_default_value = false
USERINFO_SYSERCMIN_FIELD.default_value = ""
USERINFO_SYSERCMIN_FIELD.type = 9
USERINFO_SYSERCMIN_FIELD.cpp_type = 9

USERINFO_COINPAYUSDTAMOUNT_FIELD.name = "coinpayusdtamount"
USERINFO_COINPAYUSDTAMOUNT_FIELD.full_name = ".prootc.userinfo.coinpayusdtamount"
USERINFO_COINPAYUSDTAMOUNT_FIELD.number = 66
USERINFO_COINPAYUSDTAMOUNT_FIELD.index = 65
USERINFO_COINPAYUSDTAMOUNT_FIELD.label = 1
USERINFO_COINPAYUSDTAMOUNT_FIELD.has_default_value = false
USERINFO_COINPAYUSDTAMOUNT_FIELD.default_value = ""
USERINFO_COINPAYUSDTAMOUNT_FIELD.type = 9
USERINFO_COINPAYUSDTAMOUNT_FIELD.cpp_type = 9

USERINFO_COINPAYUSDTLOCKAMOUNT_FIELD.name = "coinpayusdtlockamount"
USERINFO_COINPAYUSDTLOCKAMOUNT_FIELD.full_name = ".prootc.userinfo.coinpayusdtlockamount"
USERINFO_COINPAYUSDTLOCKAMOUNT_FIELD.number = 67
USERINFO_COINPAYUSDTLOCKAMOUNT_FIELD.index = 66
USERINFO_COINPAYUSDTLOCKAMOUNT_FIELD.label = 1
USERINFO_COINPAYUSDTLOCKAMOUNT_FIELD.has_default_value = false
USERINFO_COINPAYUSDTLOCKAMOUNT_FIELD.default_value = ""
USERINFO_COINPAYUSDTLOCKAMOUNT_FIELD.type = 9
USERINFO_COINPAYUSDTLOCKAMOUNT_FIELD.cpp_type = 9

USERINFO_ISCHECK_FIELD.name = "ischeck"
USERINFO_ISCHECK_FIELD.full_name = ".prootc.userinfo.ischeck"
USERINFO_ISCHECK_FIELD.number = 68
USERINFO_ISCHECK_FIELD.index = 67
USERINFO_ISCHECK_FIELD.label = 1
USERINFO_ISCHECK_FIELD.has_default_value = false
USERINFO_ISCHECK_FIELD.default_value = 0
USERINFO_ISCHECK_FIELD.type = 5
USERINFO_ISCHECK_FIELD.cpp_type = 1

USERINFO_EXTRACTCURRENCYRATEERC_FIELD.name = "extractcurrencyrateerc"
USERINFO_EXTRACTCURRENCYRATEERC_FIELD.full_name = ".prootc.userinfo.extractcurrencyrateerc"
USERINFO_EXTRACTCURRENCYRATEERC_FIELD.number = 69
USERINFO_EXTRACTCURRENCYRATEERC_FIELD.index = 68
USERINFO_EXTRACTCURRENCYRATEERC_FIELD.label = 1
USERINFO_EXTRACTCURRENCYRATEERC_FIELD.has_default_value = false
USERINFO_EXTRACTCURRENCYRATEERC_FIELD.default_value = ""
USERINFO_EXTRACTCURRENCYRATEERC_FIELD.type = 9
USERINFO_EXTRACTCURRENCYRATEERC_FIELD.cpp_type = 9

USERINFO_EXTRACTCURRENCYRATETRC_FIELD.name = "extractcurrencyratetrc"
USERINFO_EXTRACTCURRENCYRATETRC_FIELD.full_name = ".prootc.userinfo.extractcurrencyratetrc"
USERINFO_EXTRACTCURRENCYRATETRC_FIELD.number = 70
USERINFO_EXTRACTCURRENCYRATETRC_FIELD.index = 69
USERINFO_EXTRACTCURRENCYRATETRC_FIELD.label = 1
USERINFO_EXTRACTCURRENCYRATETRC_FIELD.has_default_value = false
USERINFO_EXTRACTCURRENCYRATETRC_FIELD.default_value = ""
USERINFO_EXTRACTCURRENCYRATETRC_FIELD.type = 9
USERINFO_EXTRACTCURRENCYRATETRC_FIELD.cpp_type = 9

USERINFO.name = "userinfo"
USERINFO.full_name = ".prootc.userinfo"
USERINFO.nested_types = {}
USERINFO.enum_types = {}
USERINFO.fields = {USERINFO_USERID_FIELD, USERINFO_CID_FIELD, USERINFO_ACCOUNT_FIELD, USERINFO_PASSWORD_FIELD, USERINFO_NICKNAME_FIELD, USERINFO_ETHADDRESS_FIELD, USERINFO_FACE1_FIELD, USERINFO_REGDATE_FIELD, USERINFO_SEX_FIELD, USERINFO_AGE_FIELD, USERINFO_EMAIL_FIELD, USERINFO_PHONENUM_FIELD, USERINFO_ETHAMOUNT_FIELD, USERINFO_ERCUSDTAMOUNT_FIELD, USERINFO_ERCUSDTLOCKAMOUNT_FIELD, USERINFO_CHANNEL_FIELD, USERINFO_INVITECODE_FIELD, USERINFO_BINDCODE_FIELD, USERINFO_IMEI_FIELD, USERINFO_DEVNAME_FIELD, USERINFO_MACNAME_FIELD, USERINFO_MOBILETYPE_FIELD, USERINFO_LASTTIME_FIELD, USERINFO_PENULTTIME_FIELD, USERINFO_ISBAN_FIELD, USERINFO_DESCRIPTION_FIELD, USERINFO_BLACKLIST_FIELD, USERINFO_IP_FIELD, USERINFO_PROVINCE_FIELD, USERINFO_CITY_FIELD, USERINFO_BINDTYPE_FIELD, USERINFO_ISFUNDPASSWORD_FIELD, USERINFO_PAYTYPELIST_FIELD, USERINFO_AGENT_FIELD, USERINFO_USERTYPE_FIELD, USERINFO_AUTOSELL_FIELD, USERINFO_MINBUY_FIELD, USERINFO_MAXBUY_FIELD, USERINFO_MINSELL_FIELD, USERINFO_MAXSELL_FIELD, USERINFO_ISLOCK_FIELD, USERINFO_ISHANGBUY_FIELD, USERINFO_ISHANGSELL_FIELD, USERINFO_COMMTYPE_FIELD, USERINFO_PROHIBITLOGIN_FIELD, USERINFO_ISACCEPTORDER_FIELD, USERINFO_DEALLASTTIME_FIELD, USERINFO_BANTIME_FIELD, USERINFO_ISINVITED_FIELD, USERINFO_ISOTC_FIELD, USERINFO_ISBEHALFPAY_FIELD, USERINFO_ERCFCAMOUNT_FIELD, USERINFO_ERCFCLOCKAMOUNT_FIELD, USERINFO_DEALCOINTYPE_FIELD, USERINFO_ENERGYVALUE_FIELD, USERINFO_WEIGHTSVALUE_FIELD, USERINFO_TEAMNAME_FIELD, USERINFO_PLATFORMID_FIELD, USERINFO_ISTEAMACCEPTORDER_FIELD, USERINFO_PREAUTHORIZATION_FIELD, USERINFO_COINPAYRATE_FIELD, USERINFO_COINPAYERC_FIELD, USERINFO_COINPAYTRC_FIELD, USERINFO_ALLOWSYSADDR_FIELD, USERINFO_SYSERCMIN_FIELD, USERINFO_COINPAYUSDTAMOUNT_FIELD, USERINFO_COINPAYUSDTLOCKAMOUNT_FIELD, USERINFO_ISCHECK_FIELD, USERINFO_EXTRACTCURRENCYRATEERC_FIELD, USERINFO_EXTRACTCURRENCYRATETRC_FIELD}
USERINFO.is_extendable = false
USERINFO.extensions = {}
COININFO_COINID_FIELD.name = "coinid"
COININFO_COINID_FIELD.full_name = ".prootc.coininfo.coinid"
COININFO_COINID_FIELD.number = 1
COININFO_COINID_FIELD.index = 0
COININFO_COINID_FIELD.label = 1
COININFO_COINID_FIELD.has_default_value = false
COININFO_COINID_FIELD.default_value = 0
COININFO_COINID_FIELD.type = 5
COININFO_COINID_FIELD.cpp_type = 1

COININFO_COINNAME_FIELD.name = "coinname"
COININFO_COINNAME_FIELD.full_name = ".prootc.coininfo.coinname"
COININFO_COINNAME_FIELD.number = 2
COININFO_COINNAME_FIELD.index = 1
COININFO_COINNAME_FIELD.label = 1
COININFO_COINNAME_FIELD.has_default_value = false
COININFO_COINNAME_FIELD.default_value = ""
COININFO_COINNAME_FIELD.type = 9
COININFO_COINNAME_FIELD.cpp_type = 9

COININFO_COINAMOUNT_FIELD.name = "coinamount"
COININFO_COINAMOUNT_FIELD.full_name = ".prootc.coininfo.coinamount"
COININFO_COINAMOUNT_FIELD.number = 3
COININFO_COINAMOUNT_FIELD.index = 2
COININFO_COINAMOUNT_FIELD.label = 1
COININFO_COINAMOUNT_FIELD.has_default_value = false
COININFO_COINAMOUNT_FIELD.default_value = ""
COININFO_COINAMOUNT_FIELD.type = 9
COININFO_COINAMOUNT_FIELD.cpp_type = 9

COININFO_COINLOCKAMOUNT_FIELD.name = "coinlockamount"
COININFO_COINLOCKAMOUNT_FIELD.full_name = ".prootc.coininfo.coinlockamount"
COININFO_COINLOCKAMOUNT_FIELD.number = 4
COININFO_COINLOCKAMOUNT_FIELD.index = 3
COININFO_COINLOCKAMOUNT_FIELD.label = 1
COININFO_COINLOCKAMOUNT_FIELD.has_default_value = false
COININFO_COINLOCKAMOUNT_FIELD.default_value = ""
COININFO_COINLOCKAMOUNT_FIELD.type = 9
COININFO_COINLOCKAMOUNT_FIELD.cpp_type = 9

COININFO_COINFREEAMOUNT_FIELD.name = "coinfreeamount"
COININFO_COINFREEAMOUNT_FIELD.full_name = ".prootc.coininfo.coinfreeamount"
COININFO_COINFREEAMOUNT_FIELD.number = 5
COININFO_COINFREEAMOUNT_FIELD.index = 4
COININFO_COINFREEAMOUNT_FIELD.label = 1
COININFO_COINFREEAMOUNT_FIELD.has_default_value = false
COININFO_COINFREEAMOUNT_FIELD.default_value = ""
COININFO_COINFREEAMOUNT_FIELD.type = 9
COININFO_COINFREEAMOUNT_FIELD.cpp_type = 9

COININFO_STATUS_FIELD.name = "status"
COININFO_STATUS_FIELD.full_name = ".prootc.coininfo.status"
COININFO_STATUS_FIELD.number = 6
COININFO_STATUS_FIELD.index = 5
COININFO_STATUS_FIELD.label = 1
COININFO_STATUS_FIELD.has_default_value = false
COININFO_STATUS_FIELD.default_value = 0
COININFO_STATUS_FIELD.type = 5
COININFO_STATUS_FIELD.cpp_type = 1

COININFO_BUYRATE_FIELD.name = "buyrate"
COININFO_BUYRATE_FIELD.full_name = ".prootc.coininfo.buyrate"
COININFO_BUYRATE_FIELD.number = 7
COININFO_BUYRATE_FIELD.index = 6
COININFO_BUYRATE_FIELD.label = 1
COININFO_BUYRATE_FIELD.has_default_value = false
COININFO_BUYRATE_FIELD.default_value = ""
COININFO_BUYRATE_FIELD.type = 9
COININFO_BUYRATE_FIELD.cpp_type = 9

COININFO_SELLRATE_FIELD.name = "sellrate"
COININFO_SELLRATE_FIELD.full_name = ".prootc.coininfo.sellrate"
COININFO_SELLRATE_FIELD.number = 8
COININFO_SELLRATE_FIELD.index = 7
COININFO_SELLRATE_FIELD.label = 1
COININFO_SELLRATE_FIELD.has_default_value = false
COININFO_SELLRATE_FIELD.default_value = ""
COININFO_SELLRATE_FIELD.type = 9
COININFO_SELLRATE_FIELD.cpp_type = 9

COININFO.name = "coininfo"
COININFO.full_name = ".prootc.coininfo"
COININFO.nested_types = {}
COININFO.enum_types = {}
COININFO.fields = {COININFO_COINID_FIELD, COININFO_COINNAME_FIELD, COININFO_COINAMOUNT_FIELD, COININFO_COINLOCKAMOUNT_FIELD, COININFO_COINFREEAMOUNT_FIELD, COININFO_STATUS_FIELD, COININFO_BUYRATE_FIELD, COININFO_SELLRATE_FIELD}
COININFO.is_extendable = false
COININFO.extensions = {}
COINADDR_ADDRTYPE_FIELD.name = "addrtype"
COINADDR_ADDRTYPE_FIELD.full_name = ".prootc.coinaddr.addrtype"
COINADDR_ADDRTYPE_FIELD.number = 1
COINADDR_ADDRTYPE_FIELD.index = 0
COINADDR_ADDRTYPE_FIELD.label = 1
COINADDR_ADDRTYPE_FIELD.has_default_value = false
COINADDR_ADDRTYPE_FIELD.default_value = 0
COINADDR_ADDRTYPE_FIELD.type = 5
COINADDR_ADDRTYPE_FIELD.cpp_type = 1

COINADDR_ADDRNAME_FIELD.name = "addrname"
COINADDR_ADDRNAME_FIELD.full_name = ".prootc.coinaddr.addrname"
COINADDR_ADDRNAME_FIELD.number = 2
COINADDR_ADDRNAME_FIELD.index = 1
COINADDR_ADDRNAME_FIELD.label = 1
COINADDR_ADDRNAME_FIELD.has_default_value = false
COINADDR_ADDRNAME_FIELD.default_value = ""
COINADDR_ADDRNAME_FIELD.type = 9
COINADDR_ADDRNAME_FIELD.cpp_type = 9

COINADDR_ADDRESS_FIELD.name = "address"
COINADDR_ADDRESS_FIELD.full_name = ".prootc.coinaddr.address"
COINADDR_ADDRESS_FIELD.number = 3
COINADDR_ADDRESS_FIELD.index = 2
COINADDR_ADDRESS_FIELD.label = 1
COINADDR_ADDRESS_FIELD.has_default_value = false
COINADDR_ADDRESS_FIELD.default_value = ""
COINADDR_ADDRESS_FIELD.type = 9
COINADDR_ADDRESS_FIELD.cpp_type = 9

COINADDR.name = "coinaddr"
COINADDR.full_name = ".prootc.coinaddr"
COINADDR.nested_types = {}
COINADDR.enum_types = {}
COINADDR.fields = {COINADDR_ADDRTYPE_FIELD, COINADDR_ADDRNAME_FIELD, COINADDR_ADDRESS_FIELD}
COINADDR.is_extendable = false
COINADDR.extensions = {}
PAYINFO_PAYTYPE_FIELD.name = "paytype"
PAYINFO_PAYTYPE_FIELD.full_name = ".prootc.payinfo.paytype"
PAYINFO_PAYTYPE_FIELD.number = 1
PAYINFO_PAYTYPE_FIELD.index = 0
PAYINFO_PAYTYPE_FIELD.label = 1
PAYINFO_PAYTYPE_FIELD.has_default_value = false
PAYINFO_PAYTYPE_FIELD.default_value = 0
PAYINFO_PAYTYPE_FIELD.type = 5
PAYINFO_PAYTYPE_FIELD.cpp_type = 1

PAYINFO_ACCOUNT_FIELD.name = "account"
PAYINFO_ACCOUNT_FIELD.full_name = ".prootc.payinfo.account"
PAYINFO_ACCOUNT_FIELD.number = 2
PAYINFO_ACCOUNT_FIELD.index = 1
PAYINFO_ACCOUNT_FIELD.label = 1
PAYINFO_ACCOUNT_FIELD.has_default_value = false
PAYINFO_ACCOUNT_FIELD.default_value = ""
PAYINFO_ACCOUNT_FIELD.type = 9
PAYINFO_ACCOUNT_FIELD.cpp_type = 9

PAYINFO_PAYEE_FIELD.name = "payee"
PAYINFO_PAYEE_FIELD.full_name = ".prootc.payinfo.payee"
PAYINFO_PAYEE_FIELD.number = 3
PAYINFO_PAYEE_FIELD.index = 2
PAYINFO_PAYEE_FIELD.label = 1
PAYINFO_PAYEE_FIELD.has_default_value = false
PAYINFO_PAYEE_FIELD.default_value = ""
PAYINFO_PAYEE_FIELD.type = 9
PAYINFO_PAYEE_FIELD.cpp_type = 9

PAYINFO_QRCODE_FIELD.name = "qrcode"
PAYINFO_QRCODE_FIELD.full_name = ".prootc.payinfo.qrcode"
PAYINFO_QRCODE_FIELD.number = 4
PAYINFO_QRCODE_FIELD.index = 3
PAYINFO_QRCODE_FIELD.label = 1
PAYINFO_QRCODE_FIELD.has_default_value = false
PAYINFO_QRCODE_FIELD.default_value = ""
PAYINFO_QRCODE_FIELD.type = 9
PAYINFO_QRCODE_FIELD.cpp_type = 9

PAYINFO_BANKNAME_FIELD.name = "bankname"
PAYINFO_BANKNAME_FIELD.full_name = ".prootc.payinfo.bankname"
PAYINFO_BANKNAME_FIELD.number = 5
PAYINFO_BANKNAME_FIELD.index = 4
PAYINFO_BANKNAME_FIELD.label = 1
PAYINFO_BANKNAME_FIELD.has_default_value = false
PAYINFO_BANKNAME_FIELD.default_value = ""
PAYINFO_BANKNAME_FIELD.type = 9
PAYINFO_BANKNAME_FIELD.cpp_type = 9

PAYINFO_BANKADDR_FIELD.name = "bankaddr"
PAYINFO_BANKADDR_FIELD.full_name = ".prootc.payinfo.bankaddr"
PAYINFO_BANKADDR_FIELD.number = 6
PAYINFO_BANKADDR_FIELD.index = 5
PAYINFO_BANKADDR_FIELD.label = 1
PAYINFO_BANKADDR_FIELD.has_default_value = false
PAYINFO_BANKADDR_FIELD.default_value = ""
PAYINFO_BANKADDR_FIELD.type = 9
PAYINFO_BANKADDR_FIELD.cpp_type = 9

PAYINFO_SINGLELIMIT_FIELD.name = "singlelimit"
PAYINFO_SINGLELIMIT_FIELD.full_name = ".prootc.payinfo.singlelimit"
PAYINFO_SINGLELIMIT_FIELD.number = 7
PAYINFO_SINGLELIMIT_FIELD.index = 6
PAYINFO_SINGLELIMIT_FIELD.label = 1
PAYINFO_SINGLELIMIT_FIELD.has_default_value = false
PAYINFO_SINGLELIMIT_FIELD.default_value = ""
PAYINFO_SINGLELIMIT_FIELD.type = 9
PAYINFO_SINGLELIMIT_FIELD.cpp_type = 9

PAYINFO_DAYLIMIT_FIELD.name = "daylimit"
PAYINFO_DAYLIMIT_FIELD.full_name = ".prootc.payinfo.daylimit"
PAYINFO_DAYLIMIT_FIELD.number = 8
PAYINFO_DAYLIMIT_FIELD.index = 7
PAYINFO_DAYLIMIT_FIELD.label = 1
PAYINFO_DAYLIMIT_FIELD.has_default_value = false
PAYINFO_DAYLIMIT_FIELD.default_value = ""
PAYINFO_DAYLIMIT_FIELD.type = 9
PAYINFO_DAYLIMIT_FIELD.cpp_type = 9

PAYINFO_ID_FIELD.name = "id"
PAYINFO_ID_FIELD.full_name = ".prootc.payinfo.id"
PAYINFO_ID_FIELD.number = 9
PAYINFO_ID_FIELD.index = 8
PAYINFO_ID_FIELD.label = 1
PAYINFO_ID_FIELD.has_default_value = false
PAYINFO_ID_FIELD.default_value = 0
PAYINFO_ID_FIELD.type = 5
PAYINFO_ID_FIELD.cpp_type = 1

PAYINFO_STATUS_FIELD.name = "status"
PAYINFO_STATUS_FIELD.full_name = ".prootc.payinfo.status"
PAYINFO_STATUS_FIELD.number = 10
PAYINFO_STATUS_FIELD.index = 9
PAYINFO_STATUS_FIELD.label = 1
PAYINFO_STATUS_FIELD.has_default_value = false
PAYINFO_STATUS_FIELD.default_value = 0
PAYINFO_STATUS_FIELD.type = 5
PAYINFO_STATUS_FIELD.cpp_type = 1

PAYINFO_USERID_FIELD.name = "userid"
PAYINFO_USERID_FIELD.full_name = ".prootc.payinfo.userid"
PAYINFO_USERID_FIELD.number = 11
PAYINFO_USERID_FIELD.index = 10
PAYINFO_USERID_FIELD.label = 1
PAYINFO_USERID_FIELD.has_default_value = false
PAYINFO_USERID_FIELD.default_value = 0
PAYINFO_USERID_FIELD.type = 5
PAYINFO_USERID_FIELD.cpp_type = 1

PAYINFO_TODAYMONEY_FIELD.name = "todaymoney"
PAYINFO_TODAYMONEY_FIELD.full_name = ".prootc.payinfo.todaymoney"
PAYINFO_TODAYMONEY_FIELD.number = 12
PAYINFO_TODAYMONEY_FIELD.index = 11
PAYINFO_TODAYMONEY_FIELD.label = 1
PAYINFO_TODAYMONEY_FIELD.has_default_value = false
PAYINFO_TODAYMONEY_FIELD.default_value = ""
PAYINFO_TODAYMONEY_FIELD.type = 9
PAYINFO_TODAYMONEY_FIELD.cpp_type = 9

PAYINFO_FOURTHPARTYID_FIELD.name = "fourthpartyid"
PAYINFO_FOURTHPARTYID_FIELD.full_name = ".prootc.payinfo.fourthpartyid"
PAYINFO_FOURTHPARTYID_FIELD.number = 13
PAYINFO_FOURTHPARTYID_FIELD.index = 12
PAYINFO_FOURTHPARTYID_FIELD.label = 1
PAYINFO_FOURTHPARTYID_FIELD.has_default_value = false
PAYINFO_FOURTHPARTYID_FIELD.default_value = 0
PAYINFO_FOURTHPARTYID_FIELD.type = 5
PAYINFO_FOURTHPARTYID_FIELD.cpp_type = 1

PAYINFO_DEALLASTTIME_FIELD.name = "deallasttime"
PAYINFO_DEALLASTTIME_FIELD.full_name = ".prootc.payinfo.deallasttime"
PAYINFO_DEALLASTTIME_FIELD.number = 14
PAYINFO_DEALLASTTIME_FIELD.index = 13
PAYINFO_DEALLASTTIME_FIELD.label = 1
PAYINFO_DEALLASTTIME_FIELD.has_default_value = false
PAYINFO_DEALLASTTIME_FIELD.default_value = ""
PAYINFO_DEALLASTTIME_FIELD.type = 9
PAYINFO_DEALLASTTIME_FIELD.cpp_type = 9

PAYINFO.name = "payinfo"
PAYINFO.full_name = ".prootc.payinfo"
PAYINFO.nested_types = {}
PAYINFO.enum_types = {}
PAYINFO.fields = {PAYINFO_PAYTYPE_FIELD, PAYINFO_ACCOUNT_FIELD, PAYINFO_PAYEE_FIELD, PAYINFO_QRCODE_FIELD, PAYINFO_BANKNAME_FIELD, PAYINFO_BANKADDR_FIELD, PAYINFO_SINGLELIMIT_FIELD, PAYINFO_DAYLIMIT_FIELD, PAYINFO_ID_FIELD, PAYINFO_STATUS_FIELD, PAYINFO_USERID_FIELD, PAYINFO_TODAYMONEY_FIELD, PAYINFO_FOURTHPARTYID_FIELD, PAYINFO_DEALLASTTIME_FIELD}
PAYINFO.is_extendable = false
PAYINFO.extensions = {}
SUBINFO_USERID_FIELD.name = "userid"
SUBINFO_USERID_FIELD.full_name = ".prootc.subinfo.userid"
SUBINFO_USERID_FIELD.number = 1
SUBINFO_USERID_FIELD.index = 0
SUBINFO_USERID_FIELD.label = 1
SUBINFO_USERID_FIELD.has_default_value = false
SUBINFO_USERID_FIELD.default_value = 0
SUBINFO_USERID_FIELD.type = 5
SUBINFO_USERID_FIELD.cpp_type = 1

SUBINFO_NICKNAME_FIELD.name = "nickname"
SUBINFO_NICKNAME_FIELD.full_name = ".prootc.subinfo.nickname"
SUBINFO_NICKNAME_FIELD.number = 2
SUBINFO_NICKNAME_FIELD.index = 1
SUBINFO_NICKNAME_FIELD.label = 1
SUBINFO_NICKNAME_FIELD.has_default_value = false
SUBINFO_NICKNAME_FIELD.default_value = ""
SUBINFO_NICKNAME_FIELD.type = 9
SUBINFO_NICKNAME_FIELD.cpp_type = 9

SUBINFO_TEAMPERFORMANCE_FIELD.name = "teamperformance"
SUBINFO_TEAMPERFORMANCE_FIELD.full_name = ".prootc.subinfo.teamperformance"
SUBINFO_TEAMPERFORMANCE_FIELD.number = 3
SUBINFO_TEAMPERFORMANCE_FIELD.index = 2
SUBINFO_TEAMPERFORMANCE_FIELD.label = 1
SUBINFO_TEAMPERFORMANCE_FIELD.has_default_value = false
SUBINFO_TEAMPERFORMANCE_FIELD.default_value = ""
SUBINFO_TEAMPERFORMANCE_FIELD.type = 9
SUBINFO_TEAMPERFORMANCE_FIELD.cpp_type = 9

SUBINFO_MYPERFORMANCE_FIELD.name = "myperformance"
SUBINFO_MYPERFORMANCE_FIELD.full_name = ".prootc.subinfo.myperformance"
SUBINFO_MYPERFORMANCE_FIELD.number = 4
SUBINFO_MYPERFORMANCE_FIELD.index = 3
SUBINFO_MYPERFORMANCE_FIELD.label = 1
SUBINFO_MYPERFORMANCE_FIELD.has_default_value = false
SUBINFO_MYPERFORMANCE_FIELD.default_value = ""
SUBINFO_MYPERFORMANCE_FIELD.type = 9
SUBINFO_MYPERFORMANCE_FIELD.cpp_type = 9

SUBINFO_ISACCEPTORDER_FIELD.name = "isacceptorder"
SUBINFO_ISACCEPTORDER_FIELD.full_name = ".prootc.subinfo.isacceptorder"
SUBINFO_ISACCEPTORDER_FIELD.number = 5
SUBINFO_ISACCEPTORDER_FIELD.index = 4
SUBINFO_ISACCEPTORDER_FIELD.label = 1
SUBINFO_ISACCEPTORDER_FIELD.has_default_value = false
SUBINFO_ISACCEPTORDER_FIELD.default_value = 0
SUBINFO_ISACCEPTORDER_FIELD.type = 5
SUBINFO_ISACCEPTORDER_FIELD.cpp_type = 1

SUBINFO_PROHIBITLOGIN_FIELD.name = "prohibitlogin"
SUBINFO_PROHIBITLOGIN_FIELD.full_name = ".prootc.subinfo.prohibitlogin"
SUBINFO_PROHIBITLOGIN_FIELD.number = 6
SUBINFO_PROHIBITLOGIN_FIELD.index = 5
SUBINFO_PROHIBITLOGIN_FIELD.label = 1
SUBINFO_PROHIBITLOGIN_FIELD.has_default_value = false
SUBINFO_PROHIBITLOGIN_FIELD.default_value = 0
SUBINFO_PROHIBITLOGIN_FIELD.type = 5
SUBINFO_PROHIBITLOGIN_FIELD.cpp_type = 1

SUBINFO_PAYTYPELIST_FIELD.name = "paytypelist"
SUBINFO_PAYTYPELIST_FIELD.full_name = ".prootc.subinfo.paytypelist"
SUBINFO_PAYTYPELIST_FIELD.number = 7
SUBINFO_PAYTYPELIST_FIELD.index = 6
SUBINFO_PAYTYPELIST_FIELD.label = 3
SUBINFO_PAYTYPELIST_FIELD.has_default_value = false
SUBINFO_PAYTYPELIST_FIELD.default_value = {}
SUBINFO_PAYTYPELIST_FIELD.type = 9
SUBINFO_PAYTYPELIST_FIELD.cpp_type = 9

SUBINFO_PAYRATELIST_FIELD.name = "payratelist"
SUBINFO_PAYRATELIST_FIELD.full_name = ".prootc.subinfo.payratelist"
SUBINFO_PAYRATELIST_FIELD.number = 8
SUBINFO_PAYRATELIST_FIELD.index = 7
SUBINFO_PAYRATELIST_FIELD.label = 3
SUBINFO_PAYRATELIST_FIELD.has_default_value = false
SUBINFO_PAYRATELIST_FIELD.default_value = {}
SUBINFO_PAYRATELIST_FIELD.type = 9
SUBINFO_PAYRATELIST_FIELD.cpp_type = 9

SUBINFO_BUYCOUNT_FIELD.name = "buycount"
SUBINFO_BUYCOUNT_FIELD.full_name = ".prootc.subinfo.buycount"
SUBINFO_BUYCOUNT_FIELD.number = 9
SUBINFO_BUYCOUNT_FIELD.index = 8
SUBINFO_BUYCOUNT_FIELD.label = 1
SUBINFO_BUYCOUNT_FIELD.has_default_value = false
SUBINFO_BUYCOUNT_FIELD.default_value = ""
SUBINFO_BUYCOUNT_FIELD.type = 9
SUBINFO_BUYCOUNT_FIELD.cpp_type = 9

SUBINFO_BUYRATELIST_FIELD.name = "buyratelist"
SUBINFO_BUYRATELIST_FIELD.full_name = ".prootc.subinfo.buyratelist"
SUBINFO_BUYRATELIST_FIELD.number = 10
SUBINFO_BUYRATELIST_FIELD.index = 9
SUBINFO_BUYRATELIST_FIELD.label = 3
SUBINFO_BUYRATELIST_FIELD.has_default_value = false
SUBINFO_BUYRATELIST_FIELD.default_value = {}
SUBINFO_BUYRATELIST_FIELD.type = 9
SUBINFO_BUYRATELIST_FIELD.cpp_type = 9

SUBINFO_BEHALFBUYRATELIST_FIELD.name = "behalfbuyratelist"
SUBINFO_BEHALFBUYRATELIST_FIELD.full_name = ".prootc.subinfo.behalfbuyratelist"
SUBINFO_BEHALFBUYRATELIST_FIELD.number = 11
SUBINFO_BEHALFBUYRATELIST_FIELD.index = 10
SUBINFO_BEHALFBUYRATELIST_FIELD.label = 3
SUBINFO_BEHALFBUYRATELIST_FIELD.has_default_value = false
SUBINFO_BEHALFBUYRATELIST_FIELD.default_value = {}
SUBINFO_BEHALFBUYRATELIST_FIELD.type = 9
SUBINFO_BEHALFBUYRATELIST_FIELD.cpp_type = 9

SUBINFO.name = "subinfo"
SUBINFO.full_name = ".prootc.subinfo"
SUBINFO.nested_types = {}
SUBINFO.enum_types = {}
SUBINFO.fields = {SUBINFO_USERID_FIELD, SUBINFO_NICKNAME_FIELD, SUBINFO_TEAMPERFORMANCE_FIELD, SUBINFO_MYPERFORMANCE_FIELD, SUBINFO_ISACCEPTORDER_FIELD, SUBINFO_PROHIBITLOGIN_FIELD, SUBINFO_PAYTYPELIST_FIELD, SUBINFO_PAYRATELIST_FIELD, SUBINFO_BUYCOUNT_FIELD, SUBINFO_BUYRATELIST_FIELD, SUBINFO_BEHALFBUYRATELIST_FIELD}
SUBINFO.is_extendable = false
SUBINFO.extensions = {}
TWALLETORDERINFO_ORDER_ID_FIELD.name = "order_id"
TWALLETORDERINFO_ORDER_ID_FIELD.full_name = ".prootc.twalletorderinfo.order_id"
TWALLETORDERINFO_ORDER_ID_FIELD.number = 1
TWALLETORDERINFO_ORDER_ID_FIELD.index = 0
TWALLETORDERINFO_ORDER_ID_FIELD.label = 1
TWALLETORDERINFO_ORDER_ID_FIELD.has_default_value = false
TWALLETORDERINFO_ORDER_ID_FIELD.default_value = ""
TWALLETORDERINFO_ORDER_ID_FIELD.type = 9
TWALLETORDERINFO_ORDER_ID_FIELD.cpp_type = 9

TWALLETORDERINFO_CHANNEL_FIELD.name = "channel"
TWALLETORDERINFO_CHANNEL_FIELD.full_name = ".prootc.twalletorderinfo.channel"
TWALLETORDERINFO_CHANNEL_FIELD.number = 2
TWALLETORDERINFO_CHANNEL_FIELD.index = 1
TWALLETORDERINFO_CHANNEL_FIELD.label = 1
TWALLETORDERINFO_CHANNEL_FIELD.has_default_value = false
TWALLETORDERINFO_CHANNEL_FIELD.default_value = ""
TWALLETORDERINFO_CHANNEL_FIELD.type = 9
TWALLETORDERINFO_CHANNEL_FIELD.cpp_type = 9

TWALLETORDERINFO_USERID_FIELD.name = "userid"
TWALLETORDERINFO_USERID_FIELD.full_name = ".prootc.twalletorderinfo.userid"
TWALLETORDERINFO_USERID_FIELD.number = 3
TWALLETORDERINFO_USERID_FIELD.index = 2
TWALLETORDERINFO_USERID_FIELD.label = 1
TWALLETORDERINFO_USERID_FIELD.has_default_value = false
TWALLETORDERINFO_USERID_FIELD.default_value = 0
TWALLETORDERINFO_USERID_FIELD.type = 5
TWALLETORDERINFO_USERID_FIELD.cpp_type = 1

TWALLETORDERINFO_NICKNAME_FIELD.name = "nickname"
TWALLETORDERINFO_NICKNAME_FIELD.full_name = ".prootc.twalletorderinfo.nickname"
TWALLETORDERINFO_NICKNAME_FIELD.number = 4
TWALLETORDERINFO_NICKNAME_FIELD.index = 3
TWALLETORDERINFO_NICKNAME_FIELD.label = 1
TWALLETORDERINFO_NICKNAME_FIELD.has_default_value = false
TWALLETORDERINFO_NICKNAME_FIELD.default_value = ""
TWALLETORDERINFO_NICKNAME_FIELD.type = 9
TWALLETORDERINFO_NICKNAME_FIELD.cpp_type = 9

TWALLETORDERINFO_CURRENCY_TYPE_FIELD.name = "currency_type"
TWALLETORDERINFO_CURRENCY_TYPE_FIELD.full_name = ".prootc.twalletorderinfo.currency_type"
TWALLETORDERINFO_CURRENCY_TYPE_FIELD.number = 5
TWALLETORDERINFO_CURRENCY_TYPE_FIELD.index = 4
TWALLETORDERINFO_CURRENCY_TYPE_FIELD.label = 1
TWALLETORDERINFO_CURRENCY_TYPE_FIELD.has_default_value = false
TWALLETORDERINFO_CURRENCY_TYPE_FIELD.default_value = ""
TWALLETORDERINFO_CURRENCY_TYPE_FIELD.type = 9
TWALLETORDERINFO_CURRENCY_TYPE_FIELD.cpp_type = 9

TWALLETORDERINFO_AMOUNT_FIELD.name = "amount"
TWALLETORDERINFO_AMOUNT_FIELD.full_name = ".prootc.twalletorderinfo.amount"
TWALLETORDERINFO_AMOUNT_FIELD.number = 6
TWALLETORDERINFO_AMOUNT_FIELD.index = 5
TWALLETORDERINFO_AMOUNT_FIELD.label = 1
TWALLETORDERINFO_AMOUNT_FIELD.has_default_value = false
TWALLETORDERINFO_AMOUNT_FIELD.default_value = ""
TWALLETORDERINFO_AMOUNT_FIELD.type = 9
TWALLETORDERINFO_AMOUNT_FIELD.cpp_type = 9

TWALLETORDERINFO_ACTUALAMOUNT_FIELD.name = "actualamount"
TWALLETORDERINFO_ACTUALAMOUNT_FIELD.full_name = ".prootc.twalletorderinfo.actualamount"
TWALLETORDERINFO_ACTUALAMOUNT_FIELD.number = 7
TWALLETORDERINFO_ACTUALAMOUNT_FIELD.index = 6
TWALLETORDERINFO_ACTUALAMOUNT_FIELD.label = 1
TWALLETORDERINFO_ACTUALAMOUNT_FIELD.has_default_value = false
TWALLETORDERINFO_ACTUALAMOUNT_FIELD.default_value = ""
TWALLETORDERINFO_ACTUALAMOUNT_FIELD.type = 9
TWALLETORDERINFO_ACTUALAMOUNT_FIELD.cpp_type = 9

TWALLETORDERINFO_TRUSTEESHIP_FIELD.name = "trusteeship"
TWALLETORDERINFO_TRUSTEESHIP_FIELD.full_name = ".prootc.twalletorderinfo.trusteeship"
TWALLETORDERINFO_TRUSTEESHIP_FIELD.number = 8
TWALLETORDERINFO_TRUSTEESHIP_FIELD.index = 7
TWALLETORDERINFO_TRUSTEESHIP_FIELD.label = 1
TWALLETORDERINFO_TRUSTEESHIP_FIELD.has_default_value = false
TWALLETORDERINFO_TRUSTEESHIP_FIELD.default_value = ""
TWALLETORDERINFO_TRUSTEESHIP_FIELD.type = 9
TWALLETORDERINFO_TRUSTEESHIP_FIELD.cpp_type = 9

TWALLETORDERINFO_USERTXID_FIELD.name = "usertxid"
TWALLETORDERINFO_USERTXID_FIELD.full_name = ".prootc.twalletorderinfo.usertxid"
TWALLETORDERINFO_USERTXID_FIELD.number = 9
TWALLETORDERINFO_USERTXID_FIELD.index = 8
TWALLETORDERINFO_USERTXID_FIELD.label = 1
TWALLETORDERINFO_USERTXID_FIELD.has_default_value = false
TWALLETORDERINFO_USERTXID_FIELD.default_value = ""
TWALLETORDERINFO_USERTXID_FIELD.type = 9
TWALLETORDERINFO_USERTXID_FIELD.cpp_type = 9

TWALLETORDERINFO_CREATE_FIELD.name = "create"
TWALLETORDERINFO_CREATE_FIELD.full_name = ".prootc.twalletorderinfo.create"
TWALLETORDERINFO_CREATE_FIELD.number = 10
TWALLETORDERINFO_CREATE_FIELD.index = 9
TWALLETORDERINFO_CREATE_FIELD.label = 1
TWALLETORDERINFO_CREATE_FIELD.has_default_value = false
TWALLETORDERINFO_CREATE_FIELD.default_value = ""
TWALLETORDERINFO_CREATE_FIELD.type = 9
TWALLETORDERINFO_CREATE_FIELD.cpp_type = 9

TWALLETORDERINFO_REMARKS_FIELD.name = "remarks"
TWALLETORDERINFO_REMARKS_FIELD.full_name = ".prootc.twalletorderinfo.remarks"
TWALLETORDERINFO_REMARKS_FIELD.number = 11
TWALLETORDERINFO_REMARKS_FIELD.index = 10
TWALLETORDERINFO_REMARKS_FIELD.label = 1
TWALLETORDERINFO_REMARKS_FIELD.has_default_value = false
TWALLETORDERINFO_REMARKS_FIELD.default_value = ""
TWALLETORDERINFO_REMARKS_FIELD.type = 9
TWALLETORDERINFO_REMARKS_FIELD.cpp_type = 9

TWALLETORDERINFO_STATUS_FIELD.name = "status"
TWALLETORDERINFO_STATUS_FIELD.full_name = ".prootc.twalletorderinfo.status"
TWALLETORDERINFO_STATUS_FIELD.number = 12
TWALLETORDERINFO_STATUS_FIELD.index = 11
TWALLETORDERINFO_STATUS_FIELD.label = 1
TWALLETORDERINFO_STATUS_FIELD.has_default_value = false
TWALLETORDERINFO_STATUS_FIELD.default_value = 0
TWALLETORDERINFO_STATUS_FIELD.type = 5
TWALLETORDERINFO_STATUS_FIELD.cpp_type = 1

TWALLETORDERINFO_COINID_FIELD.name = "coinid"
TWALLETORDERINFO_COINID_FIELD.full_name = ".prootc.twalletorderinfo.coinid"
TWALLETORDERINFO_COINID_FIELD.number = 13
TWALLETORDERINFO_COINID_FIELD.index = 12
TWALLETORDERINFO_COINID_FIELD.label = 1
TWALLETORDERINFO_COINID_FIELD.has_default_value = false
TWALLETORDERINFO_COINID_FIELD.default_value = ""
TWALLETORDERINFO_COINID_FIELD.type = 9
TWALLETORDERINFO_COINID_FIELD.cpp_type = 9

TWALLETORDERINFO_COINNAME_FIELD.name = "coinname"
TWALLETORDERINFO_COINNAME_FIELD.full_name = ".prootc.twalletorderinfo.coinname"
TWALLETORDERINFO_COINNAME_FIELD.number = 14
TWALLETORDERINFO_COINNAME_FIELD.index = 13
TWALLETORDERINFO_COINNAME_FIELD.label = 1
TWALLETORDERINFO_COINNAME_FIELD.has_default_value = false
TWALLETORDERINFO_COINNAME_FIELD.default_value = ""
TWALLETORDERINFO_COINNAME_FIELD.type = 9
TWALLETORDERINFO_COINNAME_FIELD.cpp_type = 9

TWALLETORDERINFO_ADDRTYPE_FIELD.name = "addrtype"
TWALLETORDERINFO_ADDRTYPE_FIELD.full_name = ".prootc.twalletorderinfo.addrtype"
TWALLETORDERINFO_ADDRTYPE_FIELD.number = 15
TWALLETORDERINFO_ADDRTYPE_FIELD.index = 14
TWALLETORDERINFO_ADDRTYPE_FIELD.label = 1
TWALLETORDERINFO_ADDRTYPE_FIELD.has_default_value = false
TWALLETORDERINFO_ADDRTYPE_FIELD.default_value = 0
TWALLETORDERINFO_ADDRTYPE_FIELD.type = 5
TWALLETORDERINFO_ADDRTYPE_FIELD.cpp_type = 1

TWALLETORDERINFO_ADDRNAME_FIELD.name = "addrname"
TWALLETORDERINFO_ADDRNAME_FIELD.full_name = ".prootc.twalletorderinfo.addrname"
TWALLETORDERINFO_ADDRNAME_FIELD.number = 16
TWALLETORDERINFO_ADDRNAME_FIELD.index = 15
TWALLETORDERINFO_ADDRNAME_FIELD.label = 1
TWALLETORDERINFO_ADDRNAME_FIELD.has_default_value = false
TWALLETORDERINFO_ADDRNAME_FIELD.default_value = ""
TWALLETORDERINFO_ADDRNAME_FIELD.type = 9
TWALLETORDERINFO_ADDRNAME_FIELD.cpp_type = 9

TWALLETORDERINFO.name = "twalletorderinfo"
TWALLETORDERINFO.full_name = ".prootc.twalletorderinfo"
TWALLETORDERINFO.nested_types = {}
TWALLETORDERINFO.enum_types = {}
TWALLETORDERINFO.fields = {TWALLETORDERINFO_ORDER_ID_FIELD, TWALLETORDERINFO_CHANNEL_FIELD, TWALLETORDERINFO_USERID_FIELD, TWALLETORDERINFO_NICKNAME_FIELD, TWALLETORDERINFO_CURRENCY_TYPE_FIELD, TWALLETORDERINFO_AMOUNT_FIELD, TWALLETORDERINFO_ACTUALAMOUNT_FIELD, TWALLETORDERINFO_TRUSTEESHIP_FIELD, TWALLETORDERINFO_USERTXID_FIELD, TWALLETORDERINFO_CREATE_FIELD, TWALLETORDERINFO_REMARKS_FIELD, TWALLETORDERINFO_STATUS_FIELD, TWALLETORDERINFO_COINID_FIELD, TWALLETORDERINFO_COINNAME_FIELD, TWALLETORDERINFO_ADDRTYPE_FIELD, TWALLETORDERINFO_ADDRNAME_FIELD}
TWALLETORDERINFO.is_extendable = false
TWALLETORDERINFO.extensions = {}

coinaddr = protobuf.Message(COINADDR)
coininfo = protobuf.Message(COININFO)
payinfo = protobuf.Message(PAYINFO)
subinfo = protobuf.Message(SUBINFO)
twalletorderinfo = protobuf.Message(TWALLETORDERINFO)
userinfo = protobuf.Message(USERINFO)

----------nimol modify---------
ST_HUMAN_PB_COINADDR = COINADDR
ST_HUMAN_PB_COININFO = COININFO
ST_HUMAN_PB_PAYINFO = PAYINFO
ST_HUMAN_PB_SUBINFO = SUBINFO
ST_HUMAN_PB_TWALLETORDERINFO = TWALLETORDERINFO
ST_HUMAN_PB_USERINFO = USERINFO
