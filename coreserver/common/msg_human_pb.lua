-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local st_human_pb = require("st_human_pb")
----------nimol modify---------
local ST_HUMAN_PB_COINADDR = st_human_pb.ST_HUMAN_PB_COINADDR
local ST_HUMAN_PB_COININFO = st_human_pb.ST_HUMAN_PB_COININFO
local ST_HUMAN_PB_PAYINFO = st_human_pb.ST_HUMAN_PB_PAYINFO
local ST_HUMAN_PB_SUBINFO = st_human_pb.ST_HUMAN_PB_SUBINFO
local ST_HUMAN_PB_USERINFO = st_human_pb.ST_HUMAN_PB_USERINFO
module('msg_human_pb')


local CGREGISTER = protobuf.Descriptor();
local CGREGISTER_NICKNAME_FIELD = protobuf.FieldDescriptor();
local CGREGISTER_PHONENUM_FIELD = protobuf.FieldDescriptor();
local CGREGISTER_PASSWORD_FIELD = protobuf.FieldDescriptor();
local CGREGISTER_AUTHCODE_FIELD = protobuf.FieldDescriptor();
local CGREGISTER_CHANNEL_FIELD = protobuf.FieldDescriptor();
local CGREGISTER_INVITECODE_FIELD = protobuf.FieldDescriptor();
local GCREGISTER = protobuf.Descriptor();
local GCREGISTER_RESULT_FIELD = protobuf.FieldDescriptor();
local GCREGISTER_MSG_FIELD = protobuf.FieldDescriptor();
local CGLOGIN = protobuf.Descriptor();
local CGLOGIN_PHONENUM_FIELD = protobuf.FieldDescriptor();
local CGLOGIN_PASSWORD_FIELD = protobuf.FieldDescriptor();
local CGLOGIN_CHANNEL_FIELD = protobuf.FieldDescriptor();
local CGLOGIN_BINDTYPE_FIELD = protobuf.FieldDescriptor();
local GCLOGIN = protobuf.Descriptor();
local GCLOGIN_RESULT_FIELD = protobuf.FieldDescriptor();
local GCLOGIN_MSG_FIELD = protobuf.FieldDescriptor();
local GCLOGIN_UINFO_FIELD = protobuf.FieldDescriptor();
local GCLOGIN_SYSTIME_FIELD = protobuf.FieldDescriptor();
local GCLOGIN_COINLIST_FIELD = protobuf.FieldDescriptor();
local GCLOGIN_ADDRLIST_FIELD = protobuf.FieldDescriptor();
local GCLOGIN_TRUSTEESHIPADDRESSLIST_FIELD = protobuf.FieldDescriptor();
local GCKITUSER = protobuf.Descriptor();
local GCKITUSER_RESULT_FIELD = protobuf.FieldDescriptor();
local GCKITUSER_MSG_FIELD = protobuf.FieldDescriptor();
local GCKITUSER_KITTYPE_FIELD = protobuf.FieldDescriptor();
local GCKITUSER_KITMSG_FIELD = protobuf.FieldDescriptor();
local CGFORGETLOGINPWD = protobuf.Descriptor();
local CGFORGETLOGINPWD_PHONENUM_FIELD = protobuf.FieldDescriptor();
local CGFORGETLOGINPWD_AUTHCODE_FIELD = protobuf.FieldDescriptor();
local CGFORGETLOGINPWD_NEWPWD_FIELD = protobuf.FieldDescriptor();
local CGFORGETLOGINPWD_CHANNEL_FIELD = protobuf.FieldDescriptor();
local GCFORGETLOGINPWD = protobuf.Descriptor();
local GCFORGETLOGINPWD_RESULT_FIELD = protobuf.FieldDescriptor();
local GCFORGETLOGINPWD_MSG_FIELD = protobuf.FieldDescriptor();
local CGUPDATELOGINPWD = protobuf.Descriptor();
local CGUPDATELOGINPWD_USERID_FIELD = protobuf.FieldDescriptor();
local CGUPDATELOGINPWD_OLDPWD_FIELD = protobuf.FieldDescriptor();
local CGUPDATELOGINPWD_AUTHCODE_FIELD = protobuf.FieldDescriptor();
local CGUPDATELOGINPWD_NEWPWD_FIELD = protobuf.FieldDescriptor();
local GCUPDATELOGINPWD = protobuf.Descriptor();
local GCUPDATELOGINPWD_RESULT_FIELD = protobuf.FieldDescriptor();
local GCUPDATELOGINPWD_MSG_FIELD = protobuf.FieldDescriptor();
local CGSETFUNDPWD = protobuf.Descriptor();
local CGSETFUNDPWD_USERID_FIELD = protobuf.FieldDescriptor();
local CGSETFUNDPWD_AUTHCODE_FIELD = protobuf.FieldDescriptor();
local CGSETFUNDPWD_PWD_FIELD = protobuf.FieldDescriptor();
local GCSETFUNDPWD = protobuf.Descriptor();
local GCSETFUNDPWD_RESULT_FIELD = protobuf.FieldDescriptor();
local GCSETFUNDPWD_MSG_FIELD = protobuf.FieldDescriptor();
local CGFORGETFUNDPWD = protobuf.Descriptor();
local CGFORGETFUNDPWD_USERID_FIELD = protobuf.FieldDescriptor();
local CGFORGETFUNDPWD_AUTHCODE_FIELD = protobuf.FieldDescriptor();
local CGFORGETFUNDPWD_OLDPWD_FIELD = protobuf.FieldDescriptor();
local CGFORGETFUNDPWD_NEWPWD_FIELD = protobuf.FieldDescriptor();
local GCFORGETFUNDPWD = protobuf.Descriptor();
local GCFORGETFUNDPWD_RESULT_FIELD = protobuf.FieldDescriptor();
local GCFORGETFUNDPWD_MSG_FIELD = protobuf.FieldDescriptor();
local CGUPDATENICKNAME = protobuf.Descriptor();
local CGUPDATENICKNAME_USERID_FIELD = protobuf.FieldDescriptor();
local CGUPDATENICKNAME_NICKNAME_FIELD = protobuf.FieldDescriptor();
local GCUPDATENICKNAME = protobuf.Descriptor();
local GCUPDATENICKNAME_RESULT_FIELD = protobuf.FieldDescriptor();
local GCUPDATENICKNAME_MSG_FIELD = protobuf.FieldDescriptor();
local CGADDPAY = protobuf.Descriptor();
local CGADDPAY_USERID_FIELD = protobuf.FieldDescriptor();
local CGADDPAY_ACCOUNT_FIELD = protobuf.FieldDescriptor();
local CGADDPAY_PAYEE_FIELD = protobuf.FieldDescriptor();
local CGADDPAY_QRCODE_FIELD = protobuf.FieldDescriptor();
local CGADDPAY_BANKNAME_FIELD = protobuf.FieldDescriptor();
local CGADDPAY_BANKADDR_FIELD = protobuf.FieldDescriptor();
local CGADDPAY_SINGLELIMIT_FIELD = protobuf.FieldDescriptor();
local CGADDPAY_DAYLIMIT_FIELD = protobuf.FieldDescriptor();
local CGADDPAY_PAYTYPE_FIELD = protobuf.FieldDescriptor();
local GCADDPAY = protobuf.Descriptor();
local GCADDPAY_RESULT_FIELD = protobuf.FieldDescriptor();
local GCADDPAY_MSG_FIELD = protobuf.FieldDescriptor();
local CGMODIFYPAY = protobuf.Descriptor();
local CGMODIFYPAY_USERID_FIELD = protobuf.FieldDescriptor();
local CGMODIFYPAY_ID_FIELD = protobuf.FieldDescriptor();
local CGMODIFYPAY_PAYTYPE_FIELD = protobuf.FieldDescriptor();
local CGMODIFYPAY_SINGLELIMIT_FIELD = protobuf.FieldDescriptor();
local CGMODIFYPAY_DAYLIMIT_FIELD = protobuf.FieldDescriptor();
local GCMODIFYPAY = protobuf.Descriptor();
local GCMODIFYPAY_RESULT_FIELD = protobuf.FieldDescriptor();
local GCMODIFYPAY_MSG_FIELD = protobuf.FieldDescriptor();
local CGDELETEPAY = protobuf.Descriptor();
local CGDELETEPAY_USERID_FIELD = protobuf.FieldDescriptor();
local CGDELETEPAY_ID_FIELD = protobuf.FieldDescriptor();
local CGDELETEPAY_PAYTYPE_FIELD = protobuf.FieldDescriptor();
local GCDELETEPAY = protobuf.Descriptor();
local GCDELETEPAY_RESULT_FIELD = protobuf.FieldDescriptor();
local GCDELETEPAY_MSG_FIELD = protobuf.FieldDescriptor();
local CGPAYSTATUS = protobuf.Descriptor();
local CGPAYSTATUS_USERID_FIELD = protobuf.FieldDescriptor();
local CGPAYSTATUS_ID_FIELD = protobuf.FieldDescriptor();
local CGPAYSTATUS_PAYTYPE_FIELD = protobuf.FieldDescriptor();
local GCPAYSTATUS = protobuf.Descriptor();
local GCPAYSTATUS_RESULT_FIELD = protobuf.FieldDescriptor();
local GCPAYSTATUS_MSG_FIELD = protobuf.FieldDescriptor();
local CGPAYLIST = protobuf.Descriptor();
local CGPAYLIST_USERID_FIELD = protobuf.FieldDescriptor();
local GCPAYLIST = protobuf.Descriptor();
local GCPAYLIST_RESULT_FIELD = protobuf.FieldDescriptor();
local GCPAYLIST_MSG_FIELD = protobuf.FieldDescriptor();
local GCPAYLIST_PAYLIST_FIELD = protobuf.FieldDescriptor();
local CGHEARTBEAT = protobuf.Descriptor();
local CGHEARTBEAT_USERID_FIELD = protobuf.FieldDescriptor();
local GCHEARTBEAT = protobuf.Descriptor();
local GCHEARTBEAT_RESULT_FIELD = protobuf.FieldDescriptor();
local GCHEARTBEAT_MSG_FIELD = protobuf.FieldDescriptor();
local CGRECONNECT = protobuf.Descriptor();
local CGRECONNECT_USERID_FIELD = protobuf.FieldDescriptor();
local GCRECONNECT = protobuf.Descriptor();
local GCRECONNECT_RESULT_FIELD = protobuf.FieldDescriptor();
local GCRECONNECT_MSG_FIELD = protobuf.FieldDescriptor();
local GCRECONNECT_UINFO_FIELD = protobuf.FieldDescriptor();
local GCRECONNECT_SYSTIME_FIELD = protobuf.FieldDescriptor();
local GCUPDATEUSERINFO = protobuf.Descriptor();
local GCUPDATEUSERINFO_RESULT_FIELD = protobuf.FieldDescriptor();
local GCUPDATEUSERINFO_MSG_FIELD = protobuf.FieldDescriptor();
local GCUPDATEUSERINFO_TYPELIST_FIELD = protobuf.FieldDescriptor();
local GCUPDATEUSERINFO_VALUELIST_FIELD = protobuf.FieldDescriptor();
local CGMYPERFORMANCE = protobuf.Descriptor();
local CGMYPERFORMANCE_USERID_FIELD = protobuf.FieldDescriptor();
local CGMYPERFORMANCE_PAGENUM_FIELD = protobuf.FieldDescriptor();
local CGMYPERFORMANCE_PAGESIZE_FIELD = protobuf.FieldDescriptor();
local CGMYPERFORMANCE_PAYTYPE_FIELD = protobuf.FieldDescriptor();
local CGMYPERFORMANCE_TIMETYPE_FIELD = protobuf.FieldDescriptor();
local CGMYPERFORMANCE_COINID_FIELD = protobuf.FieldDescriptor();
local GCMYPERFORMANCE = protobuf.Descriptor();
local GCMYPERFORMANCE_RESULT_FIELD = protobuf.FieldDescriptor();
local GCMYPERFORMANCE_MSG_FIELD = protobuf.FieldDescriptor();
local GCMYPERFORMANCE_USERID_FIELD = protobuf.FieldDescriptor();
local GCMYPERFORMANCE_PAGENUM_FIELD = protobuf.FieldDescriptor();
local GCMYPERFORMANCE_PAGESIZE_FIELD = protobuf.FieldDescriptor();
local GCMYPERFORMANCE_TODAYINCOME_FIELD = protobuf.FieldDescriptor();
local GCMYPERFORMANCE_YESTERDAYINCOME_FIELD = protobuf.FieldDescriptor();
local GCMYPERFORMANCE_PERFORMANCECOUNT_FIELD = protobuf.FieldDescriptor();
local GCMYPERFORMANCE_INCOMECOUNT_FIELD = protobuf.FieldDescriptor();
local GCMYPERFORMANCE_PAYTYPELIST_FIELD = protobuf.FieldDescriptor();
local GCMYPERFORMANCE_PAYRATELIST_FIELD = protobuf.FieldDescriptor();
local GCMYPERFORMANCE_INFOLIST_FIELD = protobuf.FieldDescriptor();
local GCMYPERFORMANCE_PAYTYPE_FIELD = protobuf.FieldDescriptor();
local GCMYPERFORMANCE_TIMETYPE_FIELD = protobuf.FieldDescriptor();
local GCMYPERFORMANCE_BUYRATELIST_FIELD = protobuf.FieldDescriptor();
local GCMYPERFORMANCE_BEHALFBUYRATELIST_FIELD = protobuf.FieldDescriptor();
local GCMYPERFORMANCE_COINID_FIELD = protobuf.FieldDescriptor();
local CGSETSUBINFO = protobuf.Descriptor();
local CGSETSUBINFO_USERID_FIELD = protobuf.FieldDescriptor();
local CGSETSUBINFO_TARGETUSERID_FIELD = protobuf.FieldDescriptor();
local CGSETSUBINFO_ISACCEPTORDER_FIELD = protobuf.FieldDescriptor();
local CGSETSUBINFO_PROHIBITLOGIN_FIELD = protobuf.FieldDescriptor();
local CGSETSUBINFO_PAYTYPELIST_FIELD = protobuf.FieldDescriptor();
local CGSETSUBINFO_PAYRATELIST_FIELD = protobuf.FieldDescriptor();
local CGSETSUBINFO_BUYRATELIST_FIELD = protobuf.FieldDescriptor();
local CGSETSUBINFO_BEHALFBUYRATELIST_FIELD = protobuf.FieldDescriptor();
local GCSETSUBINFO = protobuf.Descriptor();
local GCSETSUBINFO_RESULT_FIELD = protobuf.FieldDescriptor();
local GCSETSUBINFO_MSG_FIELD = protobuf.FieldDescriptor();
local GCSETSUBINFO_USERID_FIELD = protobuf.FieldDescriptor();
local GCSETSUBINFO_TARGETUSERID_FIELD = protobuf.FieldDescriptor();
local GCSETSUBINFO_ISACCEPTORDER_FIELD = protobuf.FieldDescriptor();
local GCSETSUBINFO_PROHIBITLOGIN_FIELD = protobuf.FieldDescriptor();
local GCSETSUBINFO_PAYTYPELIST_FIELD = protobuf.FieldDescriptor();
local GCSETSUBINFO_PAYRATELIST_FIELD = protobuf.FieldDescriptor();
local GCSETSUBINFO_BUYRATELIST_FIELD = protobuf.FieldDescriptor();
local GCSETSUBINFO_BEHALFBUYRATELIST_FIELD = protobuf.FieldDescriptor();
local CGCHECKFUNDPWD = protobuf.Descriptor();
local CGCHECKFUNDPWD_USERID_FIELD = protobuf.FieldDescriptor();
local CGCHECKFUNDPWD_FUNDPWD_FIELD = protobuf.FieldDescriptor();
local GCCHECKFUNDPWD = protobuf.Descriptor();
local GCCHECKFUNDPWD_RESULT_FIELD = protobuf.FieldDescriptor();
local GCCHECKFUNDPWD_MSG_FIELD = protobuf.FieldDescriptor();
local GCUPDATECOININFO = protobuf.Descriptor();
local GCUPDATECOININFO_RESULT_FIELD = protobuf.FieldDescriptor();
local GCUPDATECOININFO_MSG_FIELD = protobuf.FieldDescriptor();
local GCUPDATECOININFO_COINLIST_FIELD = protobuf.FieldDescriptor();
local CGGETUSERINFO = protobuf.Descriptor();
local CGGETUSERINFO_USERID_FIELD = protobuf.FieldDescriptor();
local GCGETUSERINFO = protobuf.Descriptor();
local GCGETUSERINFO_RESULT_FIELD = protobuf.FieldDescriptor();
local GCGETUSERINFO_MSG_FIELD = protobuf.FieldDescriptor();
local GCGETUSERINFO_UINFO_FIELD = protobuf.FieldDescriptor();
local GCGETUSERINFO_ADDRLIST_FIELD = protobuf.FieldDescriptor();

CGREGISTER_NICKNAME_FIELD.name = "nickname"
CGREGISTER_NICKNAME_FIELD.full_name = ".prootc.cgregister.nickname"
CGREGISTER_NICKNAME_FIELD.number = 1
CGREGISTER_NICKNAME_FIELD.index = 0
CGREGISTER_NICKNAME_FIELD.label = 1
CGREGISTER_NICKNAME_FIELD.has_default_value = false
CGREGISTER_NICKNAME_FIELD.default_value = ""
CGREGISTER_NICKNAME_FIELD.type = 9
CGREGISTER_NICKNAME_FIELD.cpp_type = 9

CGREGISTER_PHONENUM_FIELD.name = "phonenum"
CGREGISTER_PHONENUM_FIELD.full_name = ".prootc.cgregister.phonenum"
CGREGISTER_PHONENUM_FIELD.number = 2
CGREGISTER_PHONENUM_FIELD.index = 1
CGREGISTER_PHONENUM_FIELD.label = 1
CGREGISTER_PHONENUM_FIELD.has_default_value = false
CGREGISTER_PHONENUM_FIELD.default_value = ""
CGREGISTER_PHONENUM_FIELD.type = 9
CGREGISTER_PHONENUM_FIELD.cpp_type = 9

CGREGISTER_PASSWORD_FIELD.name = "password"
CGREGISTER_PASSWORD_FIELD.full_name = ".prootc.cgregister.password"
CGREGISTER_PASSWORD_FIELD.number = 3
CGREGISTER_PASSWORD_FIELD.index = 2
CGREGISTER_PASSWORD_FIELD.label = 1
CGREGISTER_PASSWORD_FIELD.has_default_value = false
CGREGISTER_PASSWORD_FIELD.default_value = ""
CGREGISTER_PASSWORD_FIELD.type = 9
CGREGISTER_PASSWORD_FIELD.cpp_type = 9

CGREGISTER_AUTHCODE_FIELD.name = "authcode"
CGREGISTER_AUTHCODE_FIELD.full_name = ".prootc.cgregister.authcode"
CGREGISTER_AUTHCODE_FIELD.number = 5
CGREGISTER_AUTHCODE_FIELD.index = 3
CGREGISTER_AUTHCODE_FIELD.label = 1
CGREGISTER_AUTHCODE_FIELD.has_default_value = false
CGREGISTER_AUTHCODE_FIELD.default_value = ""
CGREGISTER_AUTHCODE_FIELD.type = 9
CGREGISTER_AUTHCODE_FIELD.cpp_type = 9

CGREGISTER_CHANNEL_FIELD.name = "channel"
CGREGISTER_CHANNEL_FIELD.full_name = ".prootc.cgregister.channel"
CGREGISTER_CHANNEL_FIELD.number = 6
CGREGISTER_CHANNEL_FIELD.index = 4
CGREGISTER_CHANNEL_FIELD.label = 1
CGREGISTER_CHANNEL_FIELD.has_default_value = false
CGREGISTER_CHANNEL_FIELD.default_value = ""
CGREGISTER_CHANNEL_FIELD.type = 9
CGREGISTER_CHANNEL_FIELD.cpp_type = 9

CGREGISTER_INVITECODE_FIELD.name = "invitecode"
CGREGISTER_INVITECODE_FIELD.full_name = ".prootc.cgregister.invitecode"
CGREGISTER_INVITECODE_FIELD.number = 7
CGREGISTER_INVITECODE_FIELD.index = 5
CGREGISTER_INVITECODE_FIELD.label = 1
CGREGISTER_INVITECODE_FIELD.has_default_value = false
CGREGISTER_INVITECODE_FIELD.default_value = ""
CGREGISTER_INVITECODE_FIELD.type = 9
CGREGISTER_INVITECODE_FIELD.cpp_type = 9

CGREGISTER.name = "cgregister"
CGREGISTER.full_name = ".prootc.cgregister"
CGREGISTER.nested_types = {}
CGREGISTER.enum_types = {}
CGREGISTER.fields = {CGREGISTER_NICKNAME_FIELD, CGREGISTER_PHONENUM_FIELD, CGREGISTER_PASSWORD_FIELD, CGREGISTER_AUTHCODE_FIELD, CGREGISTER_CHANNEL_FIELD, CGREGISTER_INVITECODE_FIELD}
CGREGISTER.is_extendable = false
CGREGISTER.extensions = {}
GCREGISTER_RESULT_FIELD.name = "result"
GCREGISTER_RESULT_FIELD.full_name = ".prootc.gcregister.result"
GCREGISTER_RESULT_FIELD.number = 1
GCREGISTER_RESULT_FIELD.index = 0
GCREGISTER_RESULT_FIELD.label = 1
GCREGISTER_RESULT_FIELD.has_default_value = false
GCREGISTER_RESULT_FIELD.default_value = 0
GCREGISTER_RESULT_FIELD.type = 5
GCREGISTER_RESULT_FIELD.cpp_type = 1

GCREGISTER_MSG_FIELD.name = "msg"
GCREGISTER_MSG_FIELD.full_name = ".prootc.gcregister.msg"
GCREGISTER_MSG_FIELD.number = 2
GCREGISTER_MSG_FIELD.index = 1
GCREGISTER_MSG_FIELD.label = 1
GCREGISTER_MSG_FIELD.has_default_value = false
GCREGISTER_MSG_FIELD.default_value = ""
GCREGISTER_MSG_FIELD.type = 9
GCREGISTER_MSG_FIELD.cpp_type = 9

GCREGISTER.name = "gcregister"
GCREGISTER.full_name = ".prootc.gcregister"
GCREGISTER.nested_types = {}
GCREGISTER.enum_types = {}
GCREGISTER.fields = {GCREGISTER_RESULT_FIELD, GCREGISTER_MSG_FIELD}
GCREGISTER.is_extendable = false
GCREGISTER.extensions = {}
CGLOGIN_PHONENUM_FIELD.name = "phonenum"
CGLOGIN_PHONENUM_FIELD.full_name = ".prootc.cglogin.phonenum"
CGLOGIN_PHONENUM_FIELD.number = 1
CGLOGIN_PHONENUM_FIELD.index = 0
CGLOGIN_PHONENUM_FIELD.label = 1
CGLOGIN_PHONENUM_FIELD.has_default_value = false
CGLOGIN_PHONENUM_FIELD.default_value = ""
CGLOGIN_PHONENUM_FIELD.type = 9
CGLOGIN_PHONENUM_FIELD.cpp_type = 9

CGLOGIN_PASSWORD_FIELD.name = "password"
CGLOGIN_PASSWORD_FIELD.full_name = ".prootc.cglogin.password"
CGLOGIN_PASSWORD_FIELD.number = 2
CGLOGIN_PASSWORD_FIELD.index = 1
CGLOGIN_PASSWORD_FIELD.label = 1
CGLOGIN_PASSWORD_FIELD.has_default_value = false
CGLOGIN_PASSWORD_FIELD.default_value = ""
CGLOGIN_PASSWORD_FIELD.type = 9
CGLOGIN_PASSWORD_FIELD.cpp_type = 9

CGLOGIN_CHANNEL_FIELD.name = "channel"
CGLOGIN_CHANNEL_FIELD.full_name = ".prootc.cglogin.channel"
CGLOGIN_CHANNEL_FIELD.number = 4
CGLOGIN_CHANNEL_FIELD.index = 2
CGLOGIN_CHANNEL_FIELD.label = 1
CGLOGIN_CHANNEL_FIELD.has_default_value = false
CGLOGIN_CHANNEL_FIELD.default_value = ""
CGLOGIN_CHANNEL_FIELD.type = 9
CGLOGIN_CHANNEL_FIELD.cpp_type = 9

CGLOGIN_BINDTYPE_FIELD.name = "bindtype"
CGLOGIN_BINDTYPE_FIELD.full_name = ".prootc.cglogin.bindtype"
CGLOGIN_BINDTYPE_FIELD.number = 5
CGLOGIN_BINDTYPE_FIELD.index = 3
CGLOGIN_BINDTYPE_FIELD.label = 1
CGLOGIN_BINDTYPE_FIELD.has_default_value = false
CGLOGIN_BINDTYPE_FIELD.default_value = 0
CGLOGIN_BINDTYPE_FIELD.type = 5
CGLOGIN_BINDTYPE_FIELD.cpp_type = 1

CGLOGIN.name = "cglogin"
CGLOGIN.full_name = ".prootc.cglogin"
CGLOGIN.nested_types = {}
CGLOGIN.enum_types = {}
CGLOGIN.fields = {CGLOGIN_PHONENUM_FIELD, CGLOGIN_PASSWORD_FIELD, CGLOGIN_CHANNEL_FIELD, CGLOGIN_BINDTYPE_FIELD}
CGLOGIN.is_extendable = false
CGLOGIN.extensions = {}
GCLOGIN_RESULT_FIELD.name = "result"
GCLOGIN_RESULT_FIELD.full_name = ".prootc.gclogin.result"
GCLOGIN_RESULT_FIELD.number = 1
GCLOGIN_RESULT_FIELD.index = 0
GCLOGIN_RESULT_FIELD.label = 1
GCLOGIN_RESULT_FIELD.has_default_value = false
GCLOGIN_RESULT_FIELD.default_value = 0
GCLOGIN_RESULT_FIELD.type = 5
GCLOGIN_RESULT_FIELD.cpp_type = 1

GCLOGIN_MSG_FIELD.name = "msg"
GCLOGIN_MSG_FIELD.full_name = ".prootc.gclogin.msg"
GCLOGIN_MSG_FIELD.number = 2
GCLOGIN_MSG_FIELD.index = 1
GCLOGIN_MSG_FIELD.label = 1
GCLOGIN_MSG_FIELD.has_default_value = false
GCLOGIN_MSG_FIELD.default_value = ""
GCLOGIN_MSG_FIELD.type = 9
GCLOGIN_MSG_FIELD.cpp_type = 9

GCLOGIN_UINFO_FIELD.name = "uinfo"
GCLOGIN_UINFO_FIELD.full_name = ".prootc.gclogin.uinfo"
GCLOGIN_UINFO_FIELD.number = 3
GCLOGIN_UINFO_FIELD.index = 2
GCLOGIN_UINFO_FIELD.label = 1
GCLOGIN_UINFO_FIELD.has_default_value = false
GCLOGIN_UINFO_FIELD.default_value = nil
GCLOGIN_UINFO_FIELD.message_type = ST_HUMAN_PB_USERINFO
GCLOGIN_UINFO_FIELD.type = 11
GCLOGIN_UINFO_FIELD.cpp_type = 10

GCLOGIN_SYSTIME_FIELD.name = "systime"
GCLOGIN_SYSTIME_FIELD.full_name = ".prootc.gclogin.systime"
GCLOGIN_SYSTIME_FIELD.number = 4
GCLOGIN_SYSTIME_FIELD.index = 3
GCLOGIN_SYSTIME_FIELD.label = 1
GCLOGIN_SYSTIME_FIELD.has_default_value = false
GCLOGIN_SYSTIME_FIELD.default_value = 0
GCLOGIN_SYSTIME_FIELD.type = 5
GCLOGIN_SYSTIME_FIELD.cpp_type = 1

GCLOGIN_COINLIST_FIELD.name = "coinlist"
GCLOGIN_COINLIST_FIELD.full_name = ".prootc.gclogin.coinlist"
GCLOGIN_COINLIST_FIELD.number = 5
GCLOGIN_COINLIST_FIELD.index = 4
GCLOGIN_COINLIST_FIELD.label = 3
GCLOGIN_COINLIST_FIELD.has_default_value = false
GCLOGIN_COINLIST_FIELD.default_value = {}
GCLOGIN_COINLIST_FIELD.message_type = ST_HUMAN_PB_COININFO
GCLOGIN_COINLIST_FIELD.type = 11
GCLOGIN_COINLIST_FIELD.cpp_type = 10

GCLOGIN_ADDRLIST_FIELD.name = "addrlist"
GCLOGIN_ADDRLIST_FIELD.full_name = ".prootc.gclogin.addrlist"
GCLOGIN_ADDRLIST_FIELD.number = 6
GCLOGIN_ADDRLIST_FIELD.index = 5
GCLOGIN_ADDRLIST_FIELD.label = 3
GCLOGIN_ADDRLIST_FIELD.has_default_value = false
GCLOGIN_ADDRLIST_FIELD.default_value = {}
GCLOGIN_ADDRLIST_FIELD.message_type = ST_HUMAN_PB_COINADDR
GCLOGIN_ADDRLIST_FIELD.type = 11
GCLOGIN_ADDRLIST_FIELD.cpp_type = 10

GCLOGIN_TRUSTEESHIPADDRESSLIST_FIELD.name = "trusteeshipaddresslist"
GCLOGIN_TRUSTEESHIPADDRESSLIST_FIELD.full_name = ".prootc.gclogin.trusteeshipaddresslist"
GCLOGIN_TRUSTEESHIPADDRESSLIST_FIELD.number = 7
GCLOGIN_TRUSTEESHIPADDRESSLIST_FIELD.index = 6
GCLOGIN_TRUSTEESHIPADDRESSLIST_FIELD.label = 3
GCLOGIN_TRUSTEESHIPADDRESSLIST_FIELD.has_default_value = false
GCLOGIN_TRUSTEESHIPADDRESSLIST_FIELD.default_value = {}
GCLOGIN_TRUSTEESHIPADDRESSLIST_FIELD.message_type = ST_HUMAN_PB_COINADDR
GCLOGIN_TRUSTEESHIPADDRESSLIST_FIELD.type = 11
GCLOGIN_TRUSTEESHIPADDRESSLIST_FIELD.cpp_type = 10

GCLOGIN.name = "gclogin"
GCLOGIN.full_name = ".prootc.gclogin"
GCLOGIN.nested_types = {}
GCLOGIN.enum_types = {}
GCLOGIN.fields = {GCLOGIN_RESULT_FIELD, GCLOGIN_MSG_FIELD, GCLOGIN_UINFO_FIELD, GCLOGIN_SYSTIME_FIELD, GCLOGIN_COINLIST_FIELD, GCLOGIN_ADDRLIST_FIELD, GCLOGIN_TRUSTEESHIPADDRESSLIST_FIELD}
GCLOGIN.is_extendable = false
GCLOGIN.extensions = {}
GCKITUSER_RESULT_FIELD.name = "result"
GCKITUSER_RESULT_FIELD.full_name = ".prootc.gckituser.result"
GCKITUSER_RESULT_FIELD.number = 1
GCKITUSER_RESULT_FIELD.index = 0
GCKITUSER_RESULT_FIELD.label = 1
GCKITUSER_RESULT_FIELD.has_default_value = false
GCKITUSER_RESULT_FIELD.default_value = 0
GCKITUSER_RESULT_FIELD.type = 5
GCKITUSER_RESULT_FIELD.cpp_type = 1

GCKITUSER_MSG_FIELD.name = "msg"
GCKITUSER_MSG_FIELD.full_name = ".prootc.gckituser.msg"
GCKITUSER_MSG_FIELD.number = 2
GCKITUSER_MSG_FIELD.index = 1
GCKITUSER_MSG_FIELD.label = 1
GCKITUSER_MSG_FIELD.has_default_value = false
GCKITUSER_MSG_FIELD.default_value = ""
GCKITUSER_MSG_FIELD.type = 9
GCKITUSER_MSG_FIELD.cpp_type = 9

GCKITUSER_KITTYPE_FIELD.name = "kittype"
GCKITUSER_KITTYPE_FIELD.full_name = ".prootc.gckituser.kittype"
GCKITUSER_KITTYPE_FIELD.number = 3
GCKITUSER_KITTYPE_FIELD.index = 2
GCKITUSER_KITTYPE_FIELD.label = 1
GCKITUSER_KITTYPE_FIELD.has_default_value = false
GCKITUSER_KITTYPE_FIELD.default_value = 0
GCKITUSER_KITTYPE_FIELD.type = 5
GCKITUSER_KITTYPE_FIELD.cpp_type = 1

GCKITUSER_KITMSG_FIELD.name = "kitmsg"
GCKITUSER_KITMSG_FIELD.full_name = ".prootc.gckituser.kitmsg"
GCKITUSER_KITMSG_FIELD.number = 4
GCKITUSER_KITMSG_FIELD.index = 3
GCKITUSER_KITMSG_FIELD.label = 1
GCKITUSER_KITMSG_FIELD.has_default_value = false
GCKITUSER_KITMSG_FIELD.default_value = ""
GCKITUSER_KITMSG_FIELD.type = 9
GCKITUSER_KITMSG_FIELD.cpp_type = 9

GCKITUSER.name = "gckituser"
GCKITUSER.full_name = ".prootc.gckituser"
GCKITUSER.nested_types = {}
GCKITUSER.enum_types = {}
GCKITUSER.fields = {GCKITUSER_RESULT_FIELD, GCKITUSER_MSG_FIELD, GCKITUSER_KITTYPE_FIELD, GCKITUSER_KITMSG_FIELD}
GCKITUSER.is_extendable = false
GCKITUSER.extensions = {}
CGFORGETLOGINPWD_PHONENUM_FIELD.name = "phonenum"
CGFORGETLOGINPWD_PHONENUM_FIELD.full_name = ".prootc.cgforgetloginpwd.phonenum"
CGFORGETLOGINPWD_PHONENUM_FIELD.number = 1
CGFORGETLOGINPWD_PHONENUM_FIELD.index = 0
CGFORGETLOGINPWD_PHONENUM_FIELD.label = 1
CGFORGETLOGINPWD_PHONENUM_FIELD.has_default_value = false
CGFORGETLOGINPWD_PHONENUM_FIELD.default_value = ""
CGFORGETLOGINPWD_PHONENUM_FIELD.type = 9
CGFORGETLOGINPWD_PHONENUM_FIELD.cpp_type = 9

CGFORGETLOGINPWD_AUTHCODE_FIELD.name = "authcode"
CGFORGETLOGINPWD_AUTHCODE_FIELD.full_name = ".prootc.cgforgetloginpwd.authcode"
CGFORGETLOGINPWD_AUTHCODE_FIELD.number = 2
CGFORGETLOGINPWD_AUTHCODE_FIELD.index = 1
CGFORGETLOGINPWD_AUTHCODE_FIELD.label = 1
CGFORGETLOGINPWD_AUTHCODE_FIELD.has_default_value = false
CGFORGETLOGINPWD_AUTHCODE_FIELD.default_value = ""
CGFORGETLOGINPWD_AUTHCODE_FIELD.type = 9
CGFORGETLOGINPWD_AUTHCODE_FIELD.cpp_type = 9

CGFORGETLOGINPWD_NEWPWD_FIELD.name = "newpwd"
CGFORGETLOGINPWD_NEWPWD_FIELD.full_name = ".prootc.cgforgetloginpwd.newpwd"
CGFORGETLOGINPWD_NEWPWD_FIELD.number = 3
CGFORGETLOGINPWD_NEWPWD_FIELD.index = 2
CGFORGETLOGINPWD_NEWPWD_FIELD.label = 1
CGFORGETLOGINPWD_NEWPWD_FIELD.has_default_value = false
CGFORGETLOGINPWD_NEWPWD_FIELD.default_value = ""
CGFORGETLOGINPWD_NEWPWD_FIELD.type = 9
CGFORGETLOGINPWD_NEWPWD_FIELD.cpp_type = 9

CGFORGETLOGINPWD_CHANNEL_FIELD.name = "channel"
CGFORGETLOGINPWD_CHANNEL_FIELD.full_name = ".prootc.cgforgetloginpwd.channel"
CGFORGETLOGINPWD_CHANNEL_FIELD.number = 4
CGFORGETLOGINPWD_CHANNEL_FIELD.index = 3
CGFORGETLOGINPWD_CHANNEL_FIELD.label = 1
CGFORGETLOGINPWD_CHANNEL_FIELD.has_default_value = false
CGFORGETLOGINPWD_CHANNEL_FIELD.default_value = ""
CGFORGETLOGINPWD_CHANNEL_FIELD.type = 9
CGFORGETLOGINPWD_CHANNEL_FIELD.cpp_type = 9

CGFORGETLOGINPWD.name = "cgforgetloginpwd"
CGFORGETLOGINPWD.full_name = ".prootc.cgforgetloginpwd"
CGFORGETLOGINPWD.nested_types = {}
CGFORGETLOGINPWD.enum_types = {}
CGFORGETLOGINPWD.fields = {CGFORGETLOGINPWD_PHONENUM_FIELD, CGFORGETLOGINPWD_AUTHCODE_FIELD, CGFORGETLOGINPWD_NEWPWD_FIELD, CGFORGETLOGINPWD_CHANNEL_FIELD}
CGFORGETLOGINPWD.is_extendable = false
CGFORGETLOGINPWD.extensions = {}
GCFORGETLOGINPWD_RESULT_FIELD.name = "result"
GCFORGETLOGINPWD_RESULT_FIELD.full_name = ".prootc.gcforgetloginpwd.result"
GCFORGETLOGINPWD_RESULT_FIELD.number = 1
GCFORGETLOGINPWD_RESULT_FIELD.index = 0
GCFORGETLOGINPWD_RESULT_FIELD.label = 1
GCFORGETLOGINPWD_RESULT_FIELD.has_default_value = false
GCFORGETLOGINPWD_RESULT_FIELD.default_value = 0
GCFORGETLOGINPWD_RESULT_FIELD.type = 5
GCFORGETLOGINPWD_RESULT_FIELD.cpp_type = 1

GCFORGETLOGINPWD_MSG_FIELD.name = "msg"
GCFORGETLOGINPWD_MSG_FIELD.full_name = ".prootc.gcforgetloginpwd.msg"
GCFORGETLOGINPWD_MSG_FIELD.number = 2
GCFORGETLOGINPWD_MSG_FIELD.index = 1
GCFORGETLOGINPWD_MSG_FIELD.label = 1
GCFORGETLOGINPWD_MSG_FIELD.has_default_value = false
GCFORGETLOGINPWD_MSG_FIELD.default_value = ""
GCFORGETLOGINPWD_MSG_FIELD.type = 9
GCFORGETLOGINPWD_MSG_FIELD.cpp_type = 9

GCFORGETLOGINPWD.name = "gcforgetloginpwd"
GCFORGETLOGINPWD.full_name = ".prootc.gcforgetloginpwd"
GCFORGETLOGINPWD.nested_types = {}
GCFORGETLOGINPWD.enum_types = {}
GCFORGETLOGINPWD.fields = {GCFORGETLOGINPWD_RESULT_FIELD, GCFORGETLOGINPWD_MSG_FIELD}
GCFORGETLOGINPWD.is_extendable = false
GCFORGETLOGINPWD.extensions = {}
CGUPDATELOGINPWD_USERID_FIELD.name = "userid"
CGUPDATELOGINPWD_USERID_FIELD.full_name = ".prootc.cgupdateloginpwd.userid"
CGUPDATELOGINPWD_USERID_FIELD.number = 1
CGUPDATELOGINPWD_USERID_FIELD.index = 0
CGUPDATELOGINPWD_USERID_FIELD.label = 1
CGUPDATELOGINPWD_USERID_FIELD.has_default_value = false
CGUPDATELOGINPWD_USERID_FIELD.default_value = 0
CGUPDATELOGINPWD_USERID_FIELD.type = 5
CGUPDATELOGINPWD_USERID_FIELD.cpp_type = 1

CGUPDATELOGINPWD_OLDPWD_FIELD.name = "oldpwd"
CGUPDATELOGINPWD_OLDPWD_FIELD.full_name = ".prootc.cgupdateloginpwd.oldpwd"
CGUPDATELOGINPWD_OLDPWD_FIELD.number = 2
CGUPDATELOGINPWD_OLDPWD_FIELD.index = 1
CGUPDATELOGINPWD_OLDPWD_FIELD.label = 1
CGUPDATELOGINPWD_OLDPWD_FIELD.has_default_value = false
CGUPDATELOGINPWD_OLDPWD_FIELD.default_value = ""
CGUPDATELOGINPWD_OLDPWD_FIELD.type = 9
CGUPDATELOGINPWD_OLDPWD_FIELD.cpp_type = 9

CGUPDATELOGINPWD_AUTHCODE_FIELD.name = "authcode"
CGUPDATELOGINPWD_AUTHCODE_FIELD.full_name = ".prootc.cgupdateloginpwd.authcode"
CGUPDATELOGINPWD_AUTHCODE_FIELD.number = 3
CGUPDATELOGINPWD_AUTHCODE_FIELD.index = 2
CGUPDATELOGINPWD_AUTHCODE_FIELD.label = 1
CGUPDATELOGINPWD_AUTHCODE_FIELD.has_default_value = false
CGUPDATELOGINPWD_AUTHCODE_FIELD.default_value = ""
CGUPDATELOGINPWD_AUTHCODE_FIELD.type = 9
CGUPDATELOGINPWD_AUTHCODE_FIELD.cpp_type = 9

CGUPDATELOGINPWD_NEWPWD_FIELD.name = "newpwd"
CGUPDATELOGINPWD_NEWPWD_FIELD.full_name = ".prootc.cgupdateloginpwd.newpwd"
CGUPDATELOGINPWD_NEWPWD_FIELD.number = 4
CGUPDATELOGINPWD_NEWPWD_FIELD.index = 3
CGUPDATELOGINPWD_NEWPWD_FIELD.label = 1
CGUPDATELOGINPWD_NEWPWD_FIELD.has_default_value = false
CGUPDATELOGINPWD_NEWPWD_FIELD.default_value = ""
CGUPDATELOGINPWD_NEWPWD_FIELD.type = 9
CGUPDATELOGINPWD_NEWPWD_FIELD.cpp_type = 9

CGUPDATELOGINPWD.name = "cgupdateloginpwd"
CGUPDATELOGINPWD.full_name = ".prootc.cgupdateloginpwd"
CGUPDATELOGINPWD.nested_types = {}
CGUPDATELOGINPWD.enum_types = {}
CGUPDATELOGINPWD.fields = {CGUPDATELOGINPWD_USERID_FIELD, CGUPDATELOGINPWD_OLDPWD_FIELD, CGUPDATELOGINPWD_AUTHCODE_FIELD, CGUPDATELOGINPWD_NEWPWD_FIELD}
CGUPDATELOGINPWD.is_extendable = false
CGUPDATELOGINPWD.extensions = {}
GCUPDATELOGINPWD_RESULT_FIELD.name = "result"
GCUPDATELOGINPWD_RESULT_FIELD.full_name = ".prootc.gcupdateloginpwd.result"
GCUPDATELOGINPWD_RESULT_FIELD.number = 1
GCUPDATELOGINPWD_RESULT_FIELD.index = 0
GCUPDATELOGINPWD_RESULT_FIELD.label = 1
GCUPDATELOGINPWD_RESULT_FIELD.has_default_value = false
GCUPDATELOGINPWD_RESULT_FIELD.default_value = 0
GCUPDATELOGINPWD_RESULT_FIELD.type = 5
GCUPDATELOGINPWD_RESULT_FIELD.cpp_type = 1

GCUPDATELOGINPWD_MSG_FIELD.name = "msg"
GCUPDATELOGINPWD_MSG_FIELD.full_name = ".prootc.gcupdateloginpwd.msg"
GCUPDATELOGINPWD_MSG_FIELD.number = 2
GCUPDATELOGINPWD_MSG_FIELD.index = 1
GCUPDATELOGINPWD_MSG_FIELD.label = 1
GCUPDATELOGINPWD_MSG_FIELD.has_default_value = false
GCUPDATELOGINPWD_MSG_FIELD.default_value = ""
GCUPDATELOGINPWD_MSG_FIELD.type = 9
GCUPDATELOGINPWD_MSG_FIELD.cpp_type = 9

GCUPDATELOGINPWD.name = "gcupdateloginpwd"
GCUPDATELOGINPWD.full_name = ".prootc.gcupdateloginpwd"
GCUPDATELOGINPWD.nested_types = {}
GCUPDATELOGINPWD.enum_types = {}
GCUPDATELOGINPWD.fields = {GCUPDATELOGINPWD_RESULT_FIELD, GCUPDATELOGINPWD_MSG_FIELD}
GCUPDATELOGINPWD.is_extendable = false
GCUPDATELOGINPWD.extensions = {}
CGSETFUNDPWD_USERID_FIELD.name = "userid"
CGSETFUNDPWD_USERID_FIELD.full_name = ".prootc.cgsetfundpwd.userid"
CGSETFUNDPWD_USERID_FIELD.number = 1
CGSETFUNDPWD_USERID_FIELD.index = 0
CGSETFUNDPWD_USERID_FIELD.label = 1
CGSETFUNDPWD_USERID_FIELD.has_default_value = false
CGSETFUNDPWD_USERID_FIELD.default_value = 0
CGSETFUNDPWD_USERID_FIELD.type = 5
CGSETFUNDPWD_USERID_FIELD.cpp_type = 1

CGSETFUNDPWD_AUTHCODE_FIELD.name = "authcode"
CGSETFUNDPWD_AUTHCODE_FIELD.full_name = ".prootc.cgsetfundpwd.authcode"
CGSETFUNDPWD_AUTHCODE_FIELD.number = 2
CGSETFUNDPWD_AUTHCODE_FIELD.index = 1
CGSETFUNDPWD_AUTHCODE_FIELD.label = 1
CGSETFUNDPWD_AUTHCODE_FIELD.has_default_value = false
CGSETFUNDPWD_AUTHCODE_FIELD.default_value = ""
CGSETFUNDPWD_AUTHCODE_FIELD.type = 9
CGSETFUNDPWD_AUTHCODE_FIELD.cpp_type = 9

CGSETFUNDPWD_PWD_FIELD.name = "pwd"
CGSETFUNDPWD_PWD_FIELD.full_name = ".prootc.cgsetfundpwd.pwd"
CGSETFUNDPWD_PWD_FIELD.number = 3
CGSETFUNDPWD_PWD_FIELD.index = 2
CGSETFUNDPWD_PWD_FIELD.label = 1
CGSETFUNDPWD_PWD_FIELD.has_default_value = false
CGSETFUNDPWD_PWD_FIELD.default_value = ""
CGSETFUNDPWD_PWD_FIELD.type = 9
CGSETFUNDPWD_PWD_FIELD.cpp_type = 9

CGSETFUNDPWD.name = "cgsetfundpwd"
CGSETFUNDPWD.full_name = ".prootc.cgsetfundpwd"
CGSETFUNDPWD.nested_types = {}
CGSETFUNDPWD.enum_types = {}
CGSETFUNDPWD.fields = {CGSETFUNDPWD_USERID_FIELD, CGSETFUNDPWD_AUTHCODE_FIELD, CGSETFUNDPWD_PWD_FIELD}
CGSETFUNDPWD.is_extendable = false
CGSETFUNDPWD.extensions = {}
GCSETFUNDPWD_RESULT_FIELD.name = "result"
GCSETFUNDPWD_RESULT_FIELD.full_name = ".prootc.gcsetfundpwd.result"
GCSETFUNDPWD_RESULT_FIELD.number = 1
GCSETFUNDPWD_RESULT_FIELD.index = 0
GCSETFUNDPWD_RESULT_FIELD.label = 1
GCSETFUNDPWD_RESULT_FIELD.has_default_value = false
GCSETFUNDPWD_RESULT_FIELD.default_value = 0
GCSETFUNDPWD_RESULT_FIELD.type = 5
GCSETFUNDPWD_RESULT_FIELD.cpp_type = 1

GCSETFUNDPWD_MSG_FIELD.name = "msg"
GCSETFUNDPWD_MSG_FIELD.full_name = ".prootc.gcsetfundpwd.msg"
GCSETFUNDPWD_MSG_FIELD.number = 2
GCSETFUNDPWD_MSG_FIELD.index = 1
GCSETFUNDPWD_MSG_FIELD.label = 1
GCSETFUNDPWD_MSG_FIELD.has_default_value = false
GCSETFUNDPWD_MSG_FIELD.default_value = ""
GCSETFUNDPWD_MSG_FIELD.type = 9
GCSETFUNDPWD_MSG_FIELD.cpp_type = 9

GCSETFUNDPWD.name = "gcsetfundpwd"
GCSETFUNDPWD.full_name = ".prootc.gcsetfundpwd"
GCSETFUNDPWD.nested_types = {}
GCSETFUNDPWD.enum_types = {}
GCSETFUNDPWD.fields = {GCSETFUNDPWD_RESULT_FIELD, GCSETFUNDPWD_MSG_FIELD}
GCSETFUNDPWD.is_extendable = false
GCSETFUNDPWD.extensions = {}
CGFORGETFUNDPWD_USERID_FIELD.name = "userid"
CGFORGETFUNDPWD_USERID_FIELD.full_name = ".prootc.cgforgetfundpwd.userid"
CGFORGETFUNDPWD_USERID_FIELD.number = 1
CGFORGETFUNDPWD_USERID_FIELD.index = 0
CGFORGETFUNDPWD_USERID_FIELD.label = 1
CGFORGETFUNDPWD_USERID_FIELD.has_default_value = false
CGFORGETFUNDPWD_USERID_FIELD.default_value = 0
CGFORGETFUNDPWD_USERID_FIELD.type = 5
CGFORGETFUNDPWD_USERID_FIELD.cpp_type = 1

CGFORGETFUNDPWD_AUTHCODE_FIELD.name = "authcode"
CGFORGETFUNDPWD_AUTHCODE_FIELD.full_name = ".prootc.cgforgetfundpwd.authcode"
CGFORGETFUNDPWD_AUTHCODE_FIELD.number = 2
CGFORGETFUNDPWD_AUTHCODE_FIELD.index = 1
CGFORGETFUNDPWD_AUTHCODE_FIELD.label = 1
CGFORGETFUNDPWD_AUTHCODE_FIELD.has_default_value = false
CGFORGETFUNDPWD_AUTHCODE_FIELD.default_value = ""
CGFORGETFUNDPWD_AUTHCODE_FIELD.type = 9
CGFORGETFUNDPWD_AUTHCODE_FIELD.cpp_type = 9

CGFORGETFUNDPWD_OLDPWD_FIELD.name = "oldpwd"
CGFORGETFUNDPWD_OLDPWD_FIELD.full_name = ".prootc.cgforgetfundpwd.oldpwd"
CGFORGETFUNDPWD_OLDPWD_FIELD.number = 3
CGFORGETFUNDPWD_OLDPWD_FIELD.index = 2
CGFORGETFUNDPWD_OLDPWD_FIELD.label = 1
CGFORGETFUNDPWD_OLDPWD_FIELD.has_default_value = false
CGFORGETFUNDPWD_OLDPWD_FIELD.default_value = ""
CGFORGETFUNDPWD_OLDPWD_FIELD.type = 9
CGFORGETFUNDPWD_OLDPWD_FIELD.cpp_type = 9

CGFORGETFUNDPWD_NEWPWD_FIELD.name = "newpwd"
CGFORGETFUNDPWD_NEWPWD_FIELD.full_name = ".prootc.cgforgetfundpwd.newpwd"
CGFORGETFUNDPWD_NEWPWD_FIELD.number = 4
CGFORGETFUNDPWD_NEWPWD_FIELD.index = 3
CGFORGETFUNDPWD_NEWPWD_FIELD.label = 1
CGFORGETFUNDPWD_NEWPWD_FIELD.has_default_value = false
CGFORGETFUNDPWD_NEWPWD_FIELD.default_value = ""
CGFORGETFUNDPWD_NEWPWD_FIELD.type = 9
CGFORGETFUNDPWD_NEWPWD_FIELD.cpp_type = 9

CGFORGETFUNDPWD.name = "cgforgetfundpwd"
CGFORGETFUNDPWD.full_name = ".prootc.cgforgetfundpwd"
CGFORGETFUNDPWD.nested_types = {}
CGFORGETFUNDPWD.enum_types = {}
CGFORGETFUNDPWD.fields = {CGFORGETFUNDPWD_USERID_FIELD, CGFORGETFUNDPWD_AUTHCODE_FIELD, CGFORGETFUNDPWD_OLDPWD_FIELD, CGFORGETFUNDPWD_NEWPWD_FIELD}
CGFORGETFUNDPWD.is_extendable = false
CGFORGETFUNDPWD.extensions = {}
GCFORGETFUNDPWD_RESULT_FIELD.name = "result"
GCFORGETFUNDPWD_RESULT_FIELD.full_name = ".prootc.gcforgetfundpwd.result"
GCFORGETFUNDPWD_RESULT_FIELD.number = 1
GCFORGETFUNDPWD_RESULT_FIELD.index = 0
GCFORGETFUNDPWD_RESULT_FIELD.label = 1
GCFORGETFUNDPWD_RESULT_FIELD.has_default_value = false
GCFORGETFUNDPWD_RESULT_FIELD.default_value = 0
GCFORGETFUNDPWD_RESULT_FIELD.type = 5
GCFORGETFUNDPWD_RESULT_FIELD.cpp_type = 1

GCFORGETFUNDPWD_MSG_FIELD.name = "msg"
GCFORGETFUNDPWD_MSG_FIELD.full_name = ".prootc.gcforgetfundpwd.msg"
GCFORGETFUNDPWD_MSG_FIELD.number = 2
GCFORGETFUNDPWD_MSG_FIELD.index = 1
GCFORGETFUNDPWD_MSG_FIELD.label = 1
GCFORGETFUNDPWD_MSG_FIELD.has_default_value = false
GCFORGETFUNDPWD_MSG_FIELD.default_value = ""
GCFORGETFUNDPWD_MSG_FIELD.type = 9
GCFORGETFUNDPWD_MSG_FIELD.cpp_type = 9

GCFORGETFUNDPWD.name = "gcforgetfundpwd"
GCFORGETFUNDPWD.full_name = ".prootc.gcforgetfundpwd"
GCFORGETFUNDPWD.nested_types = {}
GCFORGETFUNDPWD.enum_types = {}
GCFORGETFUNDPWD.fields = {GCFORGETFUNDPWD_RESULT_FIELD, GCFORGETFUNDPWD_MSG_FIELD}
GCFORGETFUNDPWD.is_extendable = false
GCFORGETFUNDPWD.extensions = {}
CGUPDATENICKNAME_USERID_FIELD.name = "userid"
CGUPDATENICKNAME_USERID_FIELD.full_name = ".prootc.cgupdatenickname.userid"
CGUPDATENICKNAME_USERID_FIELD.number = 1
CGUPDATENICKNAME_USERID_FIELD.index = 0
CGUPDATENICKNAME_USERID_FIELD.label = 1
CGUPDATENICKNAME_USERID_FIELD.has_default_value = false
CGUPDATENICKNAME_USERID_FIELD.default_value = 0
CGUPDATENICKNAME_USERID_FIELD.type = 5
CGUPDATENICKNAME_USERID_FIELD.cpp_type = 1

CGUPDATENICKNAME_NICKNAME_FIELD.name = "nickname"
CGUPDATENICKNAME_NICKNAME_FIELD.full_name = ".prootc.cgupdatenickname.nickname"
CGUPDATENICKNAME_NICKNAME_FIELD.number = 2
CGUPDATENICKNAME_NICKNAME_FIELD.index = 1
CGUPDATENICKNAME_NICKNAME_FIELD.label = 1
CGUPDATENICKNAME_NICKNAME_FIELD.has_default_value = false
CGUPDATENICKNAME_NICKNAME_FIELD.default_value = ""
CGUPDATENICKNAME_NICKNAME_FIELD.type = 9
CGUPDATENICKNAME_NICKNAME_FIELD.cpp_type = 9

CGUPDATENICKNAME.name = "cgupdatenickname"
CGUPDATENICKNAME.full_name = ".prootc.cgupdatenickname"
CGUPDATENICKNAME.nested_types = {}
CGUPDATENICKNAME.enum_types = {}
CGUPDATENICKNAME.fields = {CGUPDATENICKNAME_USERID_FIELD, CGUPDATENICKNAME_NICKNAME_FIELD}
CGUPDATENICKNAME.is_extendable = false
CGUPDATENICKNAME.extensions = {}
GCUPDATENICKNAME_RESULT_FIELD.name = "result"
GCUPDATENICKNAME_RESULT_FIELD.full_name = ".prootc.gcupdatenickname.result"
GCUPDATENICKNAME_RESULT_FIELD.number = 1
GCUPDATENICKNAME_RESULT_FIELD.index = 0
GCUPDATENICKNAME_RESULT_FIELD.label = 1
GCUPDATENICKNAME_RESULT_FIELD.has_default_value = false
GCUPDATENICKNAME_RESULT_FIELD.default_value = 0
GCUPDATENICKNAME_RESULT_FIELD.type = 5
GCUPDATENICKNAME_RESULT_FIELD.cpp_type = 1

GCUPDATENICKNAME_MSG_FIELD.name = "msg"
GCUPDATENICKNAME_MSG_FIELD.full_name = ".prootc.gcupdatenickname.msg"
GCUPDATENICKNAME_MSG_FIELD.number = 2
GCUPDATENICKNAME_MSG_FIELD.index = 1
GCUPDATENICKNAME_MSG_FIELD.label = 1
GCUPDATENICKNAME_MSG_FIELD.has_default_value = false
GCUPDATENICKNAME_MSG_FIELD.default_value = ""
GCUPDATENICKNAME_MSG_FIELD.type = 9
GCUPDATENICKNAME_MSG_FIELD.cpp_type = 9

GCUPDATENICKNAME.name = "gcupdatenickname"
GCUPDATENICKNAME.full_name = ".prootc.gcupdatenickname"
GCUPDATENICKNAME.nested_types = {}
GCUPDATENICKNAME.enum_types = {}
GCUPDATENICKNAME.fields = {GCUPDATENICKNAME_RESULT_FIELD, GCUPDATENICKNAME_MSG_FIELD}
GCUPDATENICKNAME.is_extendable = false
GCUPDATENICKNAME.extensions = {}
CGADDPAY_USERID_FIELD.name = "userid"
CGADDPAY_USERID_FIELD.full_name = ".prootc.cgaddpay.userid"
CGADDPAY_USERID_FIELD.number = 1
CGADDPAY_USERID_FIELD.index = 0
CGADDPAY_USERID_FIELD.label = 1
CGADDPAY_USERID_FIELD.has_default_value = false
CGADDPAY_USERID_FIELD.default_value = 0
CGADDPAY_USERID_FIELD.type = 5
CGADDPAY_USERID_FIELD.cpp_type = 1

CGADDPAY_ACCOUNT_FIELD.name = "account"
CGADDPAY_ACCOUNT_FIELD.full_name = ".prootc.cgaddpay.account"
CGADDPAY_ACCOUNT_FIELD.number = 2
CGADDPAY_ACCOUNT_FIELD.index = 1
CGADDPAY_ACCOUNT_FIELD.label = 1
CGADDPAY_ACCOUNT_FIELD.has_default_value = false
CGADDPAY_ACCOUNT_FIELD.default_value = ""
CGADDPAY_ACCOUNT_FIELD.type = 9
CGADDPAY_ACCOUNT_FIELD.cpp_type = 9

CGADDPAY_PAYEE_FIELD.name = "payee"
CGADDPAY_PAYEE_FIELD.full_name = ".prootc.cgaddpay.payee"
CGADDPAY_PAYEE_FIELD.number = 3
CGADDPAY_PAYEE_FIELD.index = 2
CGADDPAY_PAYEE_FIELD.label = 1
CGADDPAY_PAYEE_FIELD.has_default_value = false
CGADDPAY_PAYEE_FIELD.default_value = ""
CGADDPAY_PAYEE_FIELD.type = 9
CGADDPAY_PAYEE_FIELD.cpp_type = 9

CGADDPAY_QRCODE_FIELD.name = "qrcode"
CGADDPAY_QRCODE_FIELD.full_name = ".prootc.cgaddpay.qrcode"
CGADDPAY_QRCODE_FIELD.number = 4
CGADDPAY_QRCODE_FIELD.index = 3
CGADDPAY_QRCODE_FIELD.label = 1
CGADDPAY_QRCODE_FIELD.has_default_value = false
CGADDPAY_QRCODE_FIELD.default_value = ""
CGADDPAY_QRCODE_FIELD.type = 9
CGADDPAY_QRCODE_FIELD.cpp_type = 9

CGADDPAY_BANKNAME_FIELD.name = "bankname"
CGADDPAY_BANKNAME_FIELD.full_name = ".prootc.cgaddpay.bankname"
CGADDPAY_BANKNAME_FIELD.number = 5
CGADDPAY_BANKNAME_FIELD.index = 4
CGADDPAY_BANKNAME_FIELD.label = 1
CGADDPAY_BANKNAME_FIELD.has_default_value = false
CGADDPAY_BANKNAME_FIELD.default_value = ""
CGADDPAY_BANKNAME_FIELD.type = 9
CGADDPAY_BANKNAME_FIELD.cpp_type = 9

CGADDPAY_BANKADDR_FIELD.name = "bankaddr"
CGADDPAY_BANKADDR_FIELD.full_name = ".prootc.cgaddpay.bankaddr"
CGADDPAY_BANKADDR_FIELD.number = 6
CGADDPAY_BANKADDR_FIELD.index = 5
CGADDPAY_BANKADDR_FIELD.label = 1
CGADDPAY_BANKADDR_FIELD.has_default_value = false
CGADDPAY_BANKADDR_FIELD.default_value = ""
CGADDPAY_BANKADDR_FIELD.type = 9
CGADDPAY_BANKADDR_FIELD.cpp_type = 9

CGADDPAY_SINGLELIMIT_FIELD.name = "singlelimit"
CGADDPAY_SINGLELIMIT_FIELD.full_name = ".prootc.cgaddpay.singlelimit"
CGADDPAY_SINGLELIMIT_FIELD.number = 7
CGADDPAY_SINGLELIMIT_FIELD.index = 6
CGADDPAY_SINGLELIMIT_FIELD.label = 1
CGADDPAY_SINGLELIMIT_FIELD.has_default_value = false
CGADDPAY_SINGLELIMIT_FIELD.default_value = ""
CGADDPAY_SINGLELIMIT_FIELD.type = 9
CGADDPAY_SINGLELIMIT_FIELD.cpp_type = 9

CGADDPAY_DAYLIMIT_FIELD.name = "daylimit"
CGADDPAY_DAYLIMIT_FIELD.full_name = ".prootc.cgaddpay.daylimit"
CGADDPAY_DAYLIMIT_FIELD.number = 8
CGADDPAY_DAYLIMIT_FIELD.index = 7
CGADDPAY_DAYLIMIT_FIELD.label = 1
CGADDPAY_DAYLIMIT_FIELD.has_default_value = false
CGADDPAY_DAYLIMIT_FIELD.default_value = ""
CGADDPAY_DAYLIMIT_FIELD.type = 9
CGADDPAY_DAYLIMIT_FIELD.cpp_type = 9

CGADDPAY_PAYTYPE_FIELD.name = "paytype"
CGADDPAY_PAYTYPE_FIELD.full_name = ".prootc.cgaddpay.paytype"
CGADDPAY_PAYTYPE_FIELD.number = 9
CGADDPAY_PAYTYPE_FIELD.index = 8
CGADDPAY_PAYTYPE_FIELD.label = 1
CGADDPAY_PAYTYPE_FIELD.has_default_value = false
CGADDPAY_PAYTYPE_FIELD.default_value = 0
CGADDPAY_PAYTYPE_FIELD.type = 5
CGADDPAY_PAYTYPE_FIELD.cpp_type = 1

CGADDPAY.name = "cgaddpay"
CGADDPAY.full_name = ".prootc.cgaddpay"
CGADDPAY.nested_types = {}
CGADDPAY.enum_types = {}
CGADDPAY.fields = {CGADDPAY_USERID_FIELD, CGADDPAY_ACCOUNT_FIELD, CGADDPAY_PAYEE_FIELD, CGADDPAY_QRCODE_FIELD, CGADDPAY_BANKNAME_FIELD, CGADDPAY_BANKADDR_FIELD, CGADDPAY_SINGLELIMIT_FIELD, CGADDPAY_DAYLIMIT_FIELD, CGADDPAY_PAYTYPE_FIELD}
CGADDPAY.is_extendable = false
CGADDPAY.extensions = {}
GCADDPAY_RESULT_FIELD.name = "result"
GCADDPAY_RESULT_FIELD.full_name = ".prootc.gcaddpay.result"
GCADDPAY_RESULT_FIELD.number = 1
GCADDPAY_RESULT_FIELD.index = 0
GCADDPAY_RESULT_FIELD.label = 1
GCADDPAY_RESULT_FIELD.has_default_value = false
GCADDPAY_RESULT_FIELD.default_value = 0
GCADDPAY_RESULT_FIELD.type = 5
GCADDPAY_RESULT_FIELD.cpp_type = 1

GCADDPAY_MSG_FIELD.name = "msg"
GCADDPAY_MSG_FIELD.full_name = ".prootc.gcaddpay.msg"
GCADDPAY_MSG_FIELD.number = 2
GCADDPAY_MSG_FIELD.index = 1
GCADDPAY_MSG_FIELD.label = 1
GCADDPAY_MSG_FIELD.has_default_value = false
GCADDPAY_MSG_FIELD.default_value = ""
GCADDPAY_MSG_FIELD.type = 9
GCADDPAY_MSG_FIELD.cpp_type = 9

GCADDPAY.name = "gcaddpay"
GCADDPAY.full_name = ".prootc.gcaddpay"
GCADDPAY.nested_types = {}
GCADDPAY.enum_types = {}
GCADDPAY.fields = {GCADDPAY_RESULT_FIELD, GCADDPAY_MSG_FIELD}
GCADDPAY.is_extendable = false
GCADDPAY.extensions = {}
CGMODIFYPAY_USERID_FIELD.name = "userid"
CGMODIFYPAY_USERID_FIELD.full_name = ".prootc.cgmodifypay.userid"
CGMODIFYPAY_USERID_FIELD.number = 1
CGMODIFYPAY_USERID_FIELD.index = 0
CGMODIFYPAY_USERID_FIELD.label = 1
CGMODIFYPAY_USERID_FIELD.has_default_value = false
CGMODIFYPAY_USERID_FIELD.default_value = 0
CGMODIFYPAY_USERID_FIELD.type = 5
CGMODIFYPAY_USERID_FIELD.cpp_type = 1

CGMODIFYPAY_ID_FIELD.name = "id"
CGMODIFYPAY_ID_FIELD.full_name = ".prootc.cgmodifypay.id"
CGMODIFYPAY_ID_FIELD.number = 2
CGMODIFYPAY_ID_FIELD.index = 1
CGMODIFYPAY_ID_FIELD.label = 1
CGMODIFYPAY_ID_FIELD.has_default_value = false
CGMODIFYPAY_ID_FIELD.default_value = 0
CGMODIFYPAY_ID_FIELD.type = 5
CGMODIFYPAY_ID_FIELD.cpp_type = 1

CGMODIFYPAY_PAYTYPE_FIELD.name = "paytype"
CGMODIFYPAY_PAYTYPE_FIELD.full_name = ".prootc.cgmodifypay.paytype"
CGMODIFYPAY_PAYTYPE_FIELD.number = 3
CGMODIFYPAY_PAYTYPE_FIELD.index = 2
CGMODIFYPAY_PAYTYPE_FIELD.label = 1
CGMODIFYPAY_PAYTYPE_FIELD.has_default_value = false
CGMODIFYPAY_PAYTYPE_FIELD.default_value = 0
CGMODIFYPAY_PAYTYPE_FIELD.type = 5
CGMODIFYPAY_PAYTYPE_FIELD.cpp_type = 1

CGMODIFYPAY_SINGLELIMIT_FIELD.name = "singlelimit"
CGMODIFYPAY_SINGLELIMIT_FIELD.full_name = ".prootc.cgmodifypay.singlelimit"
CGMODIFYPAY_SINGLELIMIT_FIELD.number = 4
CGMODIFYPAY_SINGLELIMIT_FIELD.index = 3
CGMODIFYPAY_SINGLELIMIT_FIELD.label = 1
CGMODIFYPAY_SINGLELIMIT_FIELD.has_default_value = false
CGMODIFYPAY_SINGLELIMIT_FIELD.default_value = ""
CGMODIFYPAY_SINGLELIMIT_FIELD.type = 9
CGMODIFYPAY_SINGLELIMIT_FIELD.cpp_type = 9

CGMODIFYPAY_DAYLIMIT_FIELD.name = "daylimit"
CGMODIFYPAY_DAYLIMIT_FIELD.full_name = ".prootc.cgmodifypay.daylimit"
CGMODIFYPAY_DAYLIMIT_FIELD.number = 5
CGMODIFYPAY_DAYLIMIT_FIELD.index = 4
CGMODIFYPAY_DAYLIMIT_FIELD.label = 1
CGMODIFYPAY_DAYLIMIT_FIELD.has_default_value = false
CGMODIFYPAY_DAYLIMIT_FIELD.default_value = ""
CGMODIFYPAY_DAYLIMIT_FIELD.type = 9
CGMODIFYPAY_DAYLIMIT_FIELD.cpp_type = 9

CGMODIFYPAY.name = "cgmodifypay"
CGMODIFYPAY.full_name = ".prootc.cgmodifypay"
CGMODIFYPAY.nested_types = {}
CGMODIFYPAY.enum_types = {}
CGMODIFYPAY.fields = {CGMODIFYPAY_USERID_FIELD, CGMODIFYPAY_ID_FIELD, CGMODIFYPAY_PAYTYPE_FIELD, CGMODIFYPAY_SINGLELIMIT_FIELD, CGMODIFYPAY_DAYLIMIT_FIELD}
CGMODIFYPAY.is_extendable = false
CGMODIFYPAY.extensions = {}
GCMODIFYPAY_RESULT_FIELD.name = "result"
GCMODIFYPAY_RESULT_FIELD.full_name = ".prootc.gcmodifypay.result"
GCMODIFYPAY_RESULT_FIELD.number = 1
GCMODIFYPAY_RESULT_FIELD.index = 0
GCMODIFYPAY_RESULT_FIELD.label = 1
GCMODIFYPAY_RESULT_FIELD.has_default_value = false
GCMODIFYPAY_RESULT_FIELD.default_value = 0
GCMODIFYPAY_RESULT_FIELD.type = 5
GCMODIFYPAY_RESULT_FIELD.cpp_type = 1

GCMODIFYPAY_MSG_FIELD.name = "msg"
GCMODIFYPAY_MSG_FIELD.full_name = ".prootc.gcmodifypay.msg"
GCMODIFYPAY_MSG_FIELD.number = 2
GCMODIFYPAY_MSG_FIELD.index = 1
GCMODIFYPAY_MSG_FIELD.label = 1
GCMODIFYPAY_MSG_FIELD.has_default_value = false
GCMODIFYPAY_MSG_FIELD.default_value = ""
GCMODIFYPAY_MSG_FIELD.type = 9
GCMODIFYPAY_MSG_FIELD.cpp_type = 9

GCMODIFYPAY.name = "gcmodifypay"
GCMODIFYPAY.full_name = ".prootc.gcmodifypay"
GCMODIFYPAY.nested_types = {}
GCMODIFYPAY.enum_types = {}
GCMODIFYPAY.fields = {GCMODIFYPAY_RESULT_FIELD, GCMODIFYPAY_MSG_FIELD}
GCMODIFYPAY.is_extendable = false
GCMODIFYPAY.extensions = {}
CGDELETEPAY_USERID_FIELD.name = "userid"
CGDELETEPAY_USERID_FIELD.full_name = ".prootc.cgdeletepay.userid"
CGDELETEPAY_USERID_FIELD.number = 1
CGDELETEPAY_USERID_FIELD.index = 0
CGDELETEPAY_USERID_FIELD.label = 1
CGDELETEPAY_USERID_FIELD.has_default_value = false
CGDELETEPAY_USERID_FIELD.default_value = 0
CGDELETEPAY_USERID_FIELD.type = 5
CGDELETEPAY_USERID_FIELD.cpp_type = 1

CGDELETEPAY_ID_FIELD.name = "id"
CGDELETEPAY_ID_FIELD.full_name = ".prootc.cgdeletepay.id"
CGDELETEPAY_ID_FIELD.number = 2
CGDELETEPAY_ID_FIELD.index = 1
CGDELETEPAY_ID_FIELD.label = 1
CGDELETEPAY_ID_FIELD.has_default_value = false
CGDELETEPAY_ID_FIELD.default_value = 0
CGDELETEPAY_ID_FIELD.type = 5
CGDELETEPAY_ID_FIELD.cpp_type = 1

CGDELETEPAY_PAYTYPE_FIELD.name = "paytype"
CGDELETEPAY_PAYTYPE_FIELD.full_name = ".prootc.cgdeletepay.paytype"
CGDELETEPAY_PAYTYPE_FIELD.number = 3
CGDELETEPAY_PAYTYPE_FIELD.index = 2
CGDELETEPAY_PAYTYPE_FIELD.label = 1
CGDELETEPAY_PAYTYPE_FIELD.has_default_value = false
CGDELETEPAY_PAYTYPE_FIELD.default_value = 0
CGDELETEPAY_PAYTYPE_FIELD.type = 5
CGDELETEPAY_PAYTYPE_FIELD.cpp_type = 1

CGDELETEPAY.name = "cgdeletepay"
CGDELETEPAY.full_name = ".prootc.cgdeletepay"
CGDELETEPAY.nested_types = {}
CGDELETEPAY.enum_types = {}
CGDELETEPAY.fields = {CGDELETEPAY_USERID_FIELD, CGDELETEPAY_ID_FIELD, CGDELETEPAY_PAYTYPE_FIELD}
CGDELETEPAY.is_extendable = false
CGDELETEPAY.extensions = {}
GCDELETEPAY_RESULT_FIELD.name = "result"
GCDELETEPAY_RESULT_FIELD.full_name = ".prootc.gcdeletepay.result"
GCDELETEPAY_RESULT_FIELD.number = 1
GCDELETEPAY_RESULT_FIELD.index = 0
GCDELETEPAY_RESULT_FIELD.label = 1
GCDELETEPAY_RESULT_FIELD.has_default_value = false
GCDELETEPAY_RESULT_FIELD.default_value = 0
GCDELETEPAY_RESULT_FIELD.type = 5
GCDELETEPAY_RESULT_FIELD.cpp_type = 1

GCDELETEPAY_MSG_FIELD.name = "msg"
GCDELETEPAY_MSG_FIELD.full_name = ".prootc.gcdeletepay.msg"
GCDELETEPAY_MSG_FIELD.number = 2
GCDELETEPAY_MSG_FIELD.index = 1
GCDELETEPAY_MSG_FIELD.label = 1
GCDELETEPAY_MSG_FIELD.has_default_value = false
GCDELETEPAY_MSG_FIELD.default_value = ""
GCDELETEPAY_MSG_FIELD.type = 9
GCDELETEPAY_MSG_FIELD.cpp_type = 9

GCDELETEPAY.name = "gcdeletepay"
GCDELETEPAY.full_name = ".prootc.gcdeletepay"
GCDELETEPAY.nested_types = {}
GCDELETEPAY.enum_types = {}
GCDELETEPAY.fields = {GCDELETEPAY_RESULT_FIELD, GCDELETEPAY_MSG_FIELD}
GCDELETEPAY.is_extendable = false
GCDELETEPAY.extensions = {}
CGPAYSTATUS_USERID_FIELD.name = "userid"
CGPAYSTATUS_USERID_FIELD.full_name = ".prootc.cgpaystatus.userid"
CGPAYSTATUS_USERID_FIELD.number = 1
CGPAYSTATUS_USERID_FIELD.index = 0
CGPAYSTATUS_USERID_FIELD.label = 1
CGPAYSTATUS_USERID_FIELD.has_default_value = false
CGPAYSTATUS_USERID_FIELD.default_value = 0
CGPAYSTATUS_USERID_FIELD.type = 5
CGPAYSTATUS_USERID_FIELD.cpp_type = 1

CGPAYSTATUS_ID_FIELD.name = "id"
CGPAYSTATUS_ID_FIELD.full_name = ".prootc.cgpaystatus.id"
CGPAYSTATUS_ID_FIELD.number = 2
CGPAYSTATUS_ID_FIELD.index = 1
CGPAYSTATUS_ID_FIELD.label = 1
CGPAYSTATUS_ID_FIELD.has_default_value = false
CGPAYSTATUS_ID_FIELD.default_value = 0
CGPAYSTATUS_ID_FIELD.type = 5
CGPAYSTATUS_ID_FIELD.cpp_type = 1

CGPAYSTATUS_PAYTYPE_FIELD.name = "paytype"
CGPAYSTATUS_PAYTYPE_FIELD.full_name = ".prootc.cgpaystatus.paytype"
CGPAYSTATUS_PAYTYPE_FIELD.number = 3
CGPAYSTATUS_PAYTYPE_FIELD.index = 2
CGPAYSTATUS_PAYTYPE_FIELD.label = 1
CGPAYSTATUS_PAYTYPE_FIELD.has_default_value = false
CGPAYSTATUS_PAYTYPE_FIELD.default_value = 0
CGPAYSTATUS_PAYTYPE_FIELD.type = 5
CGPAYSTATUS_PAYTYPE_FIELD.cpp_type = 1

CGPAYSTATUS.name = "cgpaystatus"
CGPAYSTATUS.full_name = ".prootc.cgpaystatus"
CGPAYSTATUS.nested_types = {}
CGPAYSTATUS.enum_types = {}
CGPAYSTATUS.fields = {CGPAYSTATUS_USERID_FIELD, CGPAYSTATUS_ID_FIELD, CGPAYSTATUS_PAYTYPE_FIELD}
CGPAYSTATUS.is_extendable = false
CGPAYSTATUS.extensions = {}
GCPAYSTATUS_RESULT_FIELD.name = "result"
GCPAYSTATUS_RESULT_FIELD.full_name = ".prootc.gcpaystatus.result"
GCPAYSTATUS_RESULT_FIELD.number = 1
GCPAYSTATUS_RESULT_FIELD.index = 0
GCPAYSTATUS_RESULT_FIELD.label = 1
GCPAYSTATUS_RESULT_FIELD.has_default_value = false
GCPAYSTATUS_RESULT_FIELD.default_value = 0
GCPAYSTATUS_RESULT_FIELD.type = 5
GCPAYSTATUS_RESULT_FIELD.cpp_type = 1

GCPAYSTATUS_MSG_FIELD.name = "msg"
GCPAYSTATUS_MSG_FIELD.full_name = ".prootc.gcpaystatus.msg"
GCPAYSTATUS_MSG_FIELD.number = 2
GCPAYSTATUS_MSG_FIELD.index = 1
GCPAYSTATUS_MSG_FIELD.label = 1
GCPAYSTATUS_MSG_FIELD.has_default_value = false
GCPAYSTATUS_MSG_FIELD.default_value = ""
GCPAYSTATUS_MSG_FIELD.type = 9
GCPAYSTATUS_MSG_FIELD.cpp_type = 9

GCPAYSTATUS.name = "gcpaystatus"
GCPAYSTATUS.full_name = ".prootc.gcpaystatus"
GCPAYSTATUS.nested_types = {}
GCPAYSTATUS.enum_types = {}
GCPAYSTATUS.fields = {GCPAYSTATUS_RESULT_FIELD, GCPAYSTATUS_MSG_FIELD}
GCPAYSTATUS.is_extendable = false
GCPAYSTATUS.extensions = {}
CGPAYLIST_USERID_FIELD.name = "userid"
CGPAYLIST_USERID_FIELD.full_name = ".prootc.cgpaylist.userid"
CGPAYLIST_USERID_FIELD.number = 1
CGPAYLIST_USERID_FIELD.index = 0
CGPAYLIST_USERID_FIELD.label = 1
CGPAYLIST_USERID_FIELD.has_default_value = false
CGPAYLIST_USERID_FIELD.default_value = 0
CGPAYLIST_USERID_FIELD.type = 5
CGPAYLIST_USERID_FIELD.cpp_type = 1

CGPAYLIST.name = "cgpaylist"
CGPAYLIST.full_name = ".prootc.cgpaylist"
CGPAYLIST.nested_types = {}
CGPAYLIST.enum_types = {}
CGPAYLIST.fields = {CGPAYLIST_USERID_FIELD}
CGPAYLIST.is_extendable = false
CGPAYLIST.extensions = {}
GCPAYLIST_RESULT_FIELD.name = "result"
GCPAYLIST_RESULT_FIELD.full_name = ".prootc.gcpaylist.result"
GCPAYLIST_RESULT_FIELD.number = 1
GCPAYLIST_RESULT_FIELD.index = 0
GCPAYLIST_RESULT_FIELD.label = 1
GCPAYLIST_RESULT_FIELD.has_default_value = false
GCPAYLIST_RESULT_FIELD.default_value = 0
GCPAYLIST_RESULT_FIELD.type = 5
GCPAYLIST_RESULT_FIELD.cpp_type = 1

GCPAYLIST_MSG_FIELD.name = "msg"
GCPAYLIST_MSG_FIELD.full_name = ".prootc.gcpaylist.msg"
GCPAYLIST_MSG_FIELD.number = 2
GCPAYLIST_MSG_FIELD.index = 1
GCPAYLIST_MSG_FIELD.label = 1
GCPAYLIST_MSG_FIELD.has_default_value = false
GCPAYLIST_MSG_FIELD.default_value = ""
GCPAYLIST_MSG_FIELD.type = 9
GCPAYLIST_MSG_FIELD.cpp_type = 9

GCPAYLIST_PAYLIST_FIELD.name = "paylist"
GCPAYLIST_PAYLIST_FIELD.full_name = ".prootc.gcpaylist.paylist"
GCPAYLIST_PAYLIST_FIELD.number = 3
GCPAYLIST_PAYLIST_FIELD.index = 2
GCPAYLIST_PAYLIST_FIELD.label = 3
GCPAYLIST_PAYLIST_FIELD.has_default_value = false
GCPAYLIST_PAYLIST_FIELD.default_value = {}
GCPAYLIST_PAYLIST_FIELD.message_type = ST_HUMAN_PB_PAYINFO
GCPAYLIST_PAYLIST_FIELD.type = 11
GCPAYLIST_PAYLIST_FIELD.cpp_type = 10

GCPAYLIST.name = "gcpaylist"
GCPAYLIST.full_name = ".prootc.gcpaylist"
GCPAYLIST.nested_types = {}
GCPAYLIST.enum_types = {}
GCPAYLIST.fields = {GCPAYLIST_RESULT_FIELD, GCPAYLIST_MSG_FIELD, GCPAYLIST_PAYLIST_FIELD}
GCPAYLIST.is_extendable = false
GCPAYLIST.extensions = {}
CGHEARTBEAT_USERID_FIELD.name = "userid"
CGHEARTBEAT_USERID_FIELD.full_name = ".prootc.cgheartbeat.userid"
CGHEARTBEAT_USERID_FIELD.number = 1
CGHEARTBEAT_USERID_FIELD.index = 0
CGHEARTBEAT_USERID_FIELD.label = 1
CGHEARTBEAT_USERID_FIELD.has_default_value = false
CGHEARTBEAT_USERID_FIELD.default_value = 0
CGHEARTBEAT_USERID_FIELD.type = 5
CGHEARTBEAT_USERID_FIELD.cpp_type = 1

CGHEARTBEAT.name = "cgheartbeat"
CGHEARTBEAT.full_name = ".prootc.cgheartbeat"
CGHEARTBEAT.nested_types = {}
CGHEARTBEAT.enum_types = {}
CGHEARTBEAT.fields = {CGHEARTBEAT_USERID_FIELD}
CGHEARTBEAT.is_extendable = false
CGHEARTBEAT.extensions = {}
GCHEARTBEAT_RESULT_FIELD.name = "result"
GCHEARTBEAT_RESULT_FIELD.full_name = ".prootc.gcheartbeat.result"
GCHEARTBEAT_RESULT_FIELD.number = 1
GCHEARTBEAT_RESULT_FIELD.index = 0
GCHEARTBEAT_RESULT_FIELD.label = 1
GCHEARTBEAT_RESULT_FIELD.has_default_value = false
GCHEARTBEAT_RESULT_FIELD.default_value = 0
GCHEARTBEAT_RESULT_FIELD.type = 5
GCHEARTBEAT_RESULT_FIELD.cpp_type = 1

GCHEARTBEAT_MSG_FIELD.name = "msg"
GCHEARTBEAT_MSG_FIELD.full_name = ".prootc.gcheartbeat.msg"
GCHEARTBEAT_MSG_FIELD.number = 2
GCHEARTBEAT_MSG_FIELD.index = 1
GCHEARTBEAT_MSG_FIELD.label = 1
GCHEARTBEAT_MSG_FIELD.has_default_value = false
GCHEARTBEAT_MSG_FIELD.default_value = ""
GCHEARTBEAT_MSG_FIELD.type = 9
GCHEARTBEAT_MSG_FIELD.cpp_type = 9

GCHEARTBEAT.name = "gcheartbeat"
GCHEARTBEAT.full_name = ".prootc.gcheartbeat"
GCHEARTBEAT.nested_types = {}
GCHEARTBEAT.enum_types = {}
GCHEARTBEAT.fields = {GCHEARTBEAT_RESULT_FIELD, GCHEARTBEAT_MSG_FIELD}
GCHEARTBEAT.is_extendable = false
GCHEARTBEAT.extensions = {}
CGRECONNECT_USERID_FIELD.name = "userid"
CGRECONNECT_USERID_FIELD.full_name = ".prootc.cgreconnect.userid"
CGRECONNECT_USERID_FIELD.number = 1
CGRECONNECT_USERID_FIELD.index = 0
CGRECONNECT_USERID_FIELD.label = 1
CGRECONNECT_USERID_FIELD.has_default_value = false
CGRECONNECT_USERID_FIELD.default_value = 0
CGRECONNECT_USERID_FIELD.type = 5
CGRECONNECT_USERID_FIELD.cpp_type = 1

CGRECONNECT.name = "cgreconnect"
CGRECONNECT.full_name = ".prootc.cgreconnect"
CGRECONNECT.nested_types = {}
CGRECONNECT.enum_types = {}
CGRECONNECT.fields = {CGRECONNECT_USERID_FIELD}
CGRECONNECT.is_extendable = false
CGRECONNECT.extensions = {}
GCRECONNECT_RESULT_FIELD.name = "result"
GCRECONNECT_RESULT_FIELD.full_name = ".prootc.gcreconnect.result"
GCRECONNECT_RESULT_FIELD.number = 1
GCRECONNECT_RESULT_FIELD.index = 0
GCRECONNECT_RESULT_FIELD.label = 1
GCRECONNECT_RESULT_FIELD.has_default_value = false
GCRECONNECT_RESULT_FIELD.default_value = 0
GCRECONNECT_RESULT_FIELD.type = 5
GCRECONNECT_RESULT_FIELD.cpp_type = 1

GCRECONNECT_MSG_FIELD.name = "msg"
GCRECONNECT_MSG_FIELD.full_name = ".prootc.gcreconnect.msg"
GCRECONNECT_MSG_FIELD.number = 2
GCRECONNECT_MSG_FIELD.index = 1
GCRECONNECT_MSG_FIELD.label = 1
GCRECONNECT_MSG_FIELD.has_default_value = false
GCRECONNECT_MSG_FIELD.default_value = ""
GCRECONNECT_MSG_FIELD.type = 9
GCRECONNECT_MSG_FIELD.cpp_type = 9

GCRECONNECT_UINFO_FIELD.name = "uinfo"
GCRECONNECT_UINFO_FIELD.full_name = ".prootc.gcreconnect.uinfo"
GCRECONNECT_UINFO_FIELD.number = 3
GCRECONNECT_UINFO_FIELD.index = 2
GCRECONNECT_UINFO_FIELD.label = 1
GCRECONNECT_UINFO_FIELD.has_default_value = false
GCRECONNECT_UINFO_FIELD.default_value = nil
GCRECONNECT_UINFO_FIELD.message_type = ST_HUMAN_PB_USERINFO
GCRECONNECT_UINFO_FIELD.type = 11
GCRECONNECT_UINFO_FIELD.cpp_type = 10

GCRECONNECT_SYSTIME_FIELD.name = "systime"
GCRECONNECT_SYSTIME_FIELD.full_name = ".prootc.gcreconnect.systime"
GCRECONNECT_SYSTIME_FIELD.number = 4
GCRECONNECT_SYSTIME_FIELD.index = 3
GCRECONNECT_SYSTIME_FIELD.label = 1
GCRECONNECT_SYSTIME_FIELD.has_default_value = false
GCRECONNECT_SYSTIME_FIELD.default_value = 0
GCRECONNECT_SYSTIME_FIELD.type = 5
GCRECONNECT_SYSTIME_FIELD.cpp_type = 1

GCRECONNECT.name = "gcreconnect"
GCRECONNECT.full_name = ".prootc.gcreconnect"
GCRECONNECT.nested_types = {}
GCRECONNECT.enum_types = {}
GCRECONNECT.fields = {GCRECONNECT_RESULT_FIELD, GCRECONNECT_MSG_FIELD, GCRECONNECT_UINFO_FIELD, GCRECONNECT_SYSTIME_FIELD}
GCRECONNECT.is_extendable = false
GCRECONNECT.extensions = {}
GCUPDATEUSERINFO_RESULT_FIELD.name = "result"
GCUPDATEUSERINFO_RESULT_FIELD.full_name = ".prootc.gcupdateuserinfo.result"
GCUPDATEUSERINFO_RESULT_FIELD.number = 1
GCUPDATEUSERINFO_RESULT_FIELD.index = 0
GCUPDATEUSERINFO_RESULT_FIELD.label = 1
GCUPDATEUSERINFO_RESULT_FIELD.has_default_value = false
GCUPDATEUSERINFO_RESULT_FIELD.default_value = 0
GCUPDATEUSERINFO_RESULT_FIELD.type = 5
GCUPDATEUSERINFO_RESULT_FIELD.cpp_type = 1

GCUPDATEUSERINFO_MSG_FIELD.name = "msg"
GCUPDATEUSERINFO_MSG_FIELD.full_name = ".prootc.gcupdateuserinfo.msg"
GCUPDATEUSERINFO_MSG_FIELD.number = 2
GCUPDATEUSERINFO_MSG_FIELD.index = 1
GCUPDATEUSERINFO_MSG_FIELD.label = 1
GCUPDATEUSERINFO_MSG_FIELD.has_default_value = false
GCUPDATEUSERINFO_MSG_FIELD.default_value = ""
GCUPDATEUSERINFO_MSG_FIELD.type = 9
GCUPDATEUSERINFO_MSG_FIELD.cpp_type = 9

GCUPDATEUSERINFO_TYPELIST_FIELD.name = "typelist"
GCUPDATEUSERINFO_TYPELIST_FIELD.full_name = ".prootc.gcupdateuserinfo.typelist"
GCUPDATEUSERINFO_TYPELIST_FIELD.number = 3
GCUPDATEUSERINFO_TYPELIST_FIELD.index = 2
GCUPDATEUSERINFO_TYPELIST_FIELD.label = 3
GCUPDATEUSERINFO_TYPELIST_FIELD.has_default_value = false
GCUPDATEUSERINFO_TYPELIST_FIELD.default_value = {}
GCUPDATEUSERINFO_TYPELIST_FIELD.type = 9
GCUPDATEUSERINFO_TYPELIST_FIELD.cpp_type = 9

GCUPDATEUSERINFO_VALUELIST_FIELD.name = "valuelist"
GCUPDATEUSERINFO_VALUELIST_FIELD.full_name = ".prootc.gcupdateuserinfo.valuelist"
GCUPDATEUSERINFO_VALUELIST_FIELD.number = 4
GCUPDATEUSERINFO_VALUELIST_FIELD.index = 3
GCUPDATEUSERINFO_VALUELIST_FIELD.label = 3
GCUPDATEUSERINFO_VALUELIST_FIELD.has_default_value = false
GCUPDATEUSERINFO_VALUELIST_FIELD.default_value = {}
GCUPDATEUSERINFO_VALUELIST_FIELD.type = 9
GCUPDATEUSERINFO_VALUELIST_FIELD.cpp_type = 9

GCUPDATEUSERINFO.name = "gcupdateuserinfo"
GCUPDATEUSERINFO.full_name = ".prootc.gcupdateuserinfo"
GCUPDATEUSERINFO.nested_types = {}
GCUPDATEUSERINFO.enum_types = {}
GCUPDATEUSERINFO.fields = {GCUPDATEUSERINFO_RESULT_FIELD, GCUPDATEUSERINFO_MSG_FIELD, GCUPDATEUSERINFO_TYPELIST_FIELD, GCUPDATEUSERINFO_VALUELIST_FIELD}
GCUPDATEUSERINFO.is_extendable = false
GCUPDATEUSERINFO.extensions = {}
CGMYPERFORMANCE_USERID_FIELD.name = "userid"
CGMYPERFORMANCE_USERID_FIELD.full_name = ".prootc.cgmyperformance.userid"
CGMYPERFORMANCE_USERID_FIELD.number = 1
CGMYPERFORMANCE_USERID_FIELD.index = 0
CGMYPERFORMANCE_USERID_FIELD.label = 1
CGMYPERFORMANCE_USERID_FIELD.has_default_value = false
CGMYPERFORMANCE_USERID_FIELD.default_value = 0
CGMYPERFORMANCE_USERID_FIELD.type = 5
CGMYPERFORMANCE_USERID_FIELD.cpp_type = 1

CGMYPERFORMANCE_PAGENUM_FIELD.name = "pagenum"
CGMYPERFORMANCE_PAGENUM_FIELD.full_name = ".prootc.cgmyperformance.pagenum"
CGMYPERFORMANCE_PAGENUM_FIELD.number = 2
CGMYPERFORMANCE_PAGENUM_FIELD.index = 1
CGMYPERFORMANCE_PAGENUM_FIELD.label = 1
CGMYPERFORMANCE_PAGENUM_FIELD.has_default_value = false
CGMYPERFORMANCE_PAGENUM_FIELD.default_value = 0
CGMYPERFORMANCE_PAGENUM_FIELD.type = 5
CGMYPERFORMANCE_PAGENUM_FIELD.cpp_type = 1

CGMYPERFORMANCE_PAGESIZE_FIELD.name = "pagesize"
CGMYPERFORMANCE_PAGESIZE_FIELD.full_name = ".prootc.cgmyperformance.pagesize"
CGMYPERFORMANCE_PAGESIZE_FIELD.number = 3
CGMYPERFORMANCE_PAGESIZE_FIELD.index = 2
CGMYPERFORMANCE_PAGESIZE_FIELD.label = 1
CGMYPERFORMANCE_PAGESIZE_FIELD.has_default_value = false
CGMYPERFORMANCE_PAGESIZE_FIELD.default_value = 0
CGMYPERFORMANCE_PAGESIZE_FIELD.type = 5
CGMYPERFORMANCE_PAGESIZE_FIELD.cpp_type = 1

CGMYPERFORMANCE_PAYTYPE_FIELD.name = "paytype"
CGMYPERFORMANCE_PAYTYPE_FIELD.full_name = ".prootc.cgmyperformance.paytype"
CGMYPERFORMANCE_PAYTYPE_FIELD.number = 4
CGMYPERFORMANCE_PAYTYPE_FIELD.index = 3
CGMYPERFORMANCE_PAYTYPE_FIELD.label = 1
CGMYPERFORMANCE_PAYTYPE_FIELD.has_default_value = false
CGMYPERFORMANCE_PAYTYPE_FIELD.default_value = 0
CGMYPERFORMANCE_PAYTYPE_FIELD.type = 5
CGMYPERFORMANCE_PAYTYPE_FIELD.cpp_type = 1

CGMYPERFORMANCE_TIMETYPE_FIELD.name = "timetype"
CGMYPERFORMANCE_TIMETYPE_FIELD.full_name = ".prootc.cgmyperformance.timetype"
CGMYPERFORMANCE_TIMETYPE_FIELD.number = 5
CGMYPERFORMANCE_TIMETYPE_FIELD.index = 4
CGMYPERFORMANCE_TIMETYPE_FIELD.label = 1
CGMYPERFORMANCE_TIMETYPE_FIELD.has_default_value = false
CGMYPERFORMANCE_TIMETYPE_FIELD.default_value = 0
CGMYPERFORMANCE_TIMETYPE_FIELD.type = 5
CGMYPERFORMANCE_TIMETYPE_FIELD.cpp_type = 1

CGMYPERFORMANCE_COINID_FIELD.name = "coinid"
CGMYPERFORMANCE_COINID_FIELD.full_name = ".prootc.cgmyperformance.coinid"
CGMYPERFORMANCE_COINID_FIELD.number = 6
CGMYPERFORMANCE_COINID_FIELD.index = 5
CGMYPERFORMANCE_COINID_FIELD.label = 1
CGMYPERFORMANCE_COINID_FIELD.has_default_value = false
CGMYPERFORMANCE_COINID_FIELD.default_value = 0
CGMYPERFORMANCE_COINID_FIELD.type = 5
CGMYPERFORMANCE_COINID_FIELD.cpp_type = 1

CGMYPERFORMANCE.name = "cgmyperformance"
CGMYPERFORMANCE.full_name = ".prootc.cgmyperformance"
CGMYPERFORMANCE.nested_types = {}
CGMYPERFORMANCE.enum_types = {}
CGMYPERFORMANCE.fields = {CGMYPERFORMANCE_USERID_FIELD, CGMYPERFORMANCE_PAGENUM_FIELD, CGMYPERFORMANCE_PAGESIZE_FIELD, CGMYPERFORMANCE_PAYTYPE_FIELD, CGMYPERFORMANCE_TIMETYPE_FIELD, CGMYPERFORMANCE_COINID_FIELD}
CGMYPERFORMANCE.is_extendable = false
CGMYPERFORMANCE.extensions = {}
GCMYPERFORMANCE_RESULT_FIELD.name = "result"
GCMYPERFORMANCE_RESULT_FIELD.full_name = ".prootc.gcmyperformance.result"
GCMYPERFORMANCE_RESULT_FIELD.number = 1
GCMYPERFORMANCE_RESULT_FIELD.index = 0
GCMYPERFORMANCE_RESULT_FIELD.label = 1
GCMYPERFORMANCE_RESULT_FIELD.has_default_value = false
GCMYPERFORMANCE_RESULT_FIELD.default_value = 0
GCMYPERFORMANCE_RESULT_FIELD.type = 5
GCMYPERFORMANCE_RESULT_FIELD.cpp_type = 1

GCMYPERFORMANCE_MSG_FIELD.name = "msg"
GCMYPERFORMANCE_MSG_FIELD.full_name = ".prootc.gcmyperformance.msg"
GCMYPERFORMANCE_MSG_FIELD.number = 2
GCMYPERFORMANCE_MSG_FIELD.index = 1
GCMYPERFORMANCE_MSG_FIELD.label = 1
GCMYPERFORMANCE_MSG_FIELD.has_default_value = false
GCMYPERFORMANCE_MSG_FIELD.default_value = ""
GCMYPERFORMANCE_MSG_FIELD.type = 9
GCMYPERFORMANCE_MSG_FIELD.cpp_type = 9

GCMYPERFORMANCE_USERID_FIELD.name = "userid"
GCMYPERFORMANCE_USERID_FIELD.full_name = ".prootc.gcmyperformance.userid"
GCMYPERFORMANCE_USERID_FIELD.number = 3
GCMYPERFORMANCE_USERID_FIELD.index = 2
GCMYPERFORMANCE_USERID_FIELD.label = 1
GCMYPERFORMANCE_USERID_FIELD.has_default_value = false
GCMYPERFORMANCE_USERID_FIELD.default_value = 0
GCMYPERFORMANCE_USERID_FIELD.type = 5
GCMYPERFORMANCE_USERID_FIELD.cpp_type = 1

GCMYPERFORMANCE_PAGENUM_FIELD.name = "pagenum"
GCMYPERFORMANCE_PAGENUM_FIELD.full_name = ".prootc.gcmyperformance.pagenum"
GCMYPERFORMANCE_PAGENUM_FIELD.number = 4
GCMYPERFORMANCE_PAGENUM_FIELD.index = 3
GCMYPERFORMANCE_PAGENUM_FIELD.label = 1
GCMYPERFORMANCE_PAGENUM_FIELD.has_default_value = false
GCMYPERFORMANCE_PAGENUM_FIELD.default_value = 0
GCMYPERFORMANCE_PAGENUM_FIELD.type = 5
GCMYPERFORMANCE_PAGENUM_FIELD.cpp_type = 1

GCMYPERFORMANCE_PAGESIZE_FIELD.name = "pagesize"
GCMYPERFORMANCE_PAGESIZE_FIELD.full_name = ".prootc.gcmyperformance.pagesize"
GCMYPERFORMANCE_PAGESIZE_FIELD.number = 5
GCMYPERFORMANCE_PAGESIZE_FIELD.index = 4
GCMYPERFORMANCE_PAGESIZE_FIELD.label = 1
GCMYPERFORMANCE_PAGESIZE_FIELD.has_default_value = false
GCMYPERFORMANCE_PAGESIZE_FIELD.default_value = 0
GCMYPERFORMANCE_PAGESIZE_FIELD.type = 5
GCMYPERFORMANCE_PAGESIZE_FIELD.cpp_type = 1

GCMYPERFORMANCE_TODAYINCOME_FIELD.name = "todayincome"
GCMYPERFORMANCE_TODAYINCOME_FIELD.full_name = ".prootc.gcmyperformance.todayincome"
GCMYPERFORMANCE_TODAYINCOME_FIELD.number = 6
GCMYPERFORMANCE_TODAYINCOME_FIELD.index = 5
GCMYPERFORMANCE_TODAYINCOME_FIELD.label = 1
GCMYPERFORMANCE_TODAYINCOME_FIELD.has_default_value = false
GCMYPERFORMANCE_TODAYINCOME_FIELD.default_value = ""
GCMYPERFORMANCE_TODAYINCOME_FIELD.type = 9
GCMYPERFORMANCE_TODAYINCOME_FIELD.cpp_type = 9

GCMYPERFORMANCE_YESTERDAYINCOME_FIELD.name = "yesterdayincome"
GCMYPERFORMANCE_YESTERDAYINCOME_FIELD.full_name = ".prootc.gcmyperformance.yesterdayincome"
GCMYPERFORMANCE_YESTERDAYINCOME_FIELD.number = 7
GCMYPERFORMANCE_YESTERDAYINCOME_FIELD.index = 6
GCMYPERFORMANCE_YESTERDAYINCOME_FIELD.label = 1
GCMYPERFORMANCE_YESTERDAYINCOME_FIELD.has_default_value = false
GCMYPERFORMANCE_YESTERDAYINCOME_FIELD.default_value = ""
GCMYPERFORMANCE_YESTERDAYINCOME_FIELD.type = 9
GCMYPERFORMANCE_YESTERDAYINCOME_FIELD.cpp_type = 9

GCMYPERFORMANCE_PERFORMANCECOUNT_FIELD.name = "performancecount"
GCMYPERFORMANCE_PERFORMANCECOUNT_FIELD.full_name = ".prootc.gcmyperformance.performancecount"
GCMYPERFORMANCE_PERFORMANCECOUNT_FIELD.number = 8
GCMYPERFORMANCE_PERFORMANCECOUNT_FIELD.index = 7
GCMYPERFORMANCE_PERFORMANCECOUNT_FIELD.label = 1
GCMYPERFORMANCE_PERFORMANCECOUNT_FIELD.has_default_value = false
GCMYPERFORMANCE_PERFORMANCECOUNT_FIELD.default_value = ""
GCMYPERFORMANCE_PERFORMANCECOUNT_FIELD.type = 9
GCMYPERFORMANCE_PERFORMANCECOUNT_FIELD.cpp_type = 9

GCMYPERFORMANCE_INCOMECOUNT_FIELD.name = "incomecount"
GCMYPERFORMANCE_INCOMECOUNT_FIELD.full_name = ".prootc.gcmyperformance.incomecount"
GCMYPERFORMANCE_INCOMECOUNT_FIELD.number = 9
GCMYPERFORMANCE_INCOMECOUNT_FIELD.index = 8
GCMYPERFORMANCE_INCOMECOUNT_FIELD.label = 1
GCMYPERFORMANCE_INCOMECOUNT_FIELD.has_default_value = false
GCMYPERFORMANCE_INCOMECOUNT_FIELD.default_value = ""
GCMYPERFORMANCE_INCOMECOUNT_FIELD.type = 9
GCMYPERFORMANCE_INCOMECOUNT_FIELD.cpp_type = 9

GCMYPERFORMANCE_PAYTYPELIST_FIELD.name = "paytypelist"
GCMYPERFORMANCE_PAYTYPELIST_FIELD.full_name = ".prootc.gcmyperformance.paytypelist"
GCMYPERFORMANCE_PAYTYPELIST_FIELD.number = 10
GCMYPERFORMANCE_PAYTYPELIST_FIELD.index = 9
GCMYPERFORMANCE_PAYTYPELIST_FIELD.label = 3
GCMYPERFORMANCE_PAYTYPELIST_FIELD.has_default_value = false
GCMYPERFORMANCE_PAYTYPELIST_FIELD.default_value = {}
GCMYPERFORMANCE_PAYTYPELIST_FIELD.type = 9
GCMYPERFORMANCE_PAYTYPELIST_FIELD.cpp_type = 9

GCMYPERFORMANCE_PAYRATELIST_FIELD.name = "payratelist"
GCMYPERFORMANCE_PAYRATELIST_FIELD.full_name = ".prootc.gcmyperformance.payratelist"
GCMYPERFORMANCE_PAYRATELIST_FIELD.number = 11
GCMYPERFORMANCE_PAYRATELIST_FIELD.index = 10
GCMYPERFORMANCE_PAYRATELIST_FIELD.label = 3
GCMYPERFORMANCE_PAYRATELIST_FIELD.has_default_value = false
GCMYPERFORMANCE_PAYRATELIST_FIELD.default_value = {}
GCMYPERFORMANCE_PAYRATELIST_FIELD.type = 9
GCMYPERFORMANCE_PAYRATELIST_FIELD.cpp_type = 9

GCMYPERFORMANCE_INFOLIST_FIELD.name = "infolist"
GCMYPERFORMANCE_INFOLIST_FIELD.full_name = ".prootc.gcmyperformance.infolist"
GCMYPERFORMANCE_INFOLIST_FIELD.number = 12
GCMYPERFORMANCE_INFOLIST_FIELD.index = 11
GCMYPERFORMANCE_INFOLIST_FIELD.label = 3
GCMYPERFORMANCE_INFOLIST_FIELD.has_default_value = false
GCMYPERFORMANCE_INFOLIST_FIELD.default_value = {}
GCMYPERFORMANCE_INFOLIST_FIELD.message_type = ST_HUMAN_PB_SUBINFO
GCMYPERFORMANCE_INFOLIST_FIELD.type = 11
GCMYPERFORMANCE_INFOLIST_FIELD.cpp_type = 10

GCMYPERFORMANCE_PAYTYPE_FIELD.name = "paytype"
GCMYPERFORMANCE_PAYTYPE_FIELD.full_name = ".prootc.gcmyperformance.paytype"
GCMYPERFORMANCE_PAYTYPE_FIELD.number = 13
GCMYPERFORMANCE_PAYTYPE_FIELD.index = 12
GCMYPERFORMANCE_PAYTYPE_FIELD.label = 1
GCMYPERFORMANCE_PAYTYPE_FIELD.has_default_value = false
GCMYPERFORMANCE_PAYTYPE_FIELD.default_value = 0
GCMYPERFORMANCE_PAYTYPE_FIELD.type = 5
GCMYPERFORMANCE_PAYTYPE_FIELD.cpp_type = 1

GCMYPERFORMANCE_TIMETYPE_FIELD.name = "timetype"
GCMYPERFORMANCE_TIMETYPE_FIELD.full_name = ".prootc.gcmyperformance.timetype"
GCMYPERFORMANCE_TIMETYPE_FIELD.number = 14
GCMYPERFORMANCE_TIMETYPE_FIELD.index = 13
GCMYPERFORMANCE_TIMETYPE_FIELD.label = 1
GCMYPERFORMANCE_TIMETYPE_FIELD.has_default_value = false
GCMYPERFORMANCE_TIMETYPE_FIELD.default_value = 0
GCMYPERFORMANCE_TIMETYPE_FIELD.type = 5
GCMYPERFORMANCE_TIMETYPE_FIELD.cpp_type = 1

GCMYPERFORMANCE_BUYRATELIST_FIELD.name = "buyratelist"
GCMYPERFORMANCE_BUYRATELIST_FIELD.full_name = ".prootc.gcmyperformance.buyratelist"
GCMYPERFORMANCE_BUYRATELIST_FIELD.number = 15
GCMYPERFORMANCE_BUYRATELIST_FIELD.index = 14
GCMYPERFORMANCE_BUYRATELIST_FIELD.label = 3
GCMYPERFORMANCE_BUYRATELIST_FIELD.has_default_value = false
GCMYPERFORMANCE_BUYRATELIST_FIELD.default_value = {}
GCMYPERFORMANCE_BUYRATELIST_FIELD.type = 9
GCMYPERFORMANCE_BUYRATELIST_FIELD.cpp_type = 9

GCMYPERFORMANCE_BEHALFBUYRATELIST_FIELD.name = "behalfbuyratelist"
GCMYPERFORMANCE_BEHALFBUYRATELIST_FIELD.full_name = ".prootc.gcmyperformance.behalfbuyratelist"
GCMYPERFORMANCE_BEHALFBUYRATELIST_FIELD.number = 16
GCMYPERFORMANCE_BEHALFBUYRATELIST_FIELD.index = 15
GCMYPERFORMANCE_BEHALFBUYRATELIST_FIELD.label = 3
GCMYPERFORMANCE_BEHALFBUYRATELIST_FIELD.has_default_value = false
GCMYPERFORMANCE_BEHALFBUYRATELIST_FIELD.default_value = {}
GCMYPERFORMANCE_BEHALFBUYRATELIST_FIELD.type = 9
GCMYPERFORMANCE_BEHALFBUYRATELIST_FIELD.cpp_type = 9

GCMYPERFORMANCE_COINID_FIELD.name = "coinid"
GCMYPERFORMANCE_COINID_FIELD.full_name = ".prootc.gcmyperformance.coinid"
GCMYPERFORMANCE_COINID_FIELD.number = 17
GCMYPERFORMANCE_COINID_FIELD.index = 16
GCMYPERFORMANCE_COINID_FIELD.label = 1
GCMYPERFORMANCE_COINID_FIELD.has_default_value = false
GCMYPERFORMANCE_COINID_FIELD.default_value = 0
GCMYPERFORMANCE_COINID_FIELD.type = 5
GCMYPERFORMANCE_COINID_FIELD.cpp_type = 1

GCMYPERFORMANCE.name = "gcmyperformance"
GCMYPERFORMANCE.full_name = ".prootc.gcmyperformance"
GCMYPERFORMANCE.nested_types = {}
GCMYPERFORMANCE.enum_types = {}
GCMYPERFORMANCE.fields = {GCMYPERFORMANCE_RESULT_FIELD, GCMYPERFORMANCE_MSG_FIELD, GCMYPERFORMANCE_USERID_FIELD, GCMYPERFORMANCE_PAGENUM_FIELD, GCMYPERFORMANCE_PAGESIZE_FIELD, GCMYPERFORMANCE_TODAYINCOME_FIELD, GCMYPERFORMANCE_YESTERDAYINCOME_FIELD, GCMYPERFORMANCE_PERFORMANCECOUNT_FIELD, GCMYPERFORMANCE_INCOMECOUNT_FIELD, GCMYPERFORMANCE_PAYTYPELIST_FIELD, GCMYPERFORMANCE_PAYRATELIST_FIELD, GCMYPERFORMANCE_INFOLIST_FIELD, GCMYPERFORMANCE_PAYTYPE_FIELD, GCMYPERFORMANCE_TIMETYPE_FIELD, GCMYPERFORMANCE_BUYRATELIST_FIELD, GCMYPERFORMANCE_BEHALFBUYRATELIST_FIELD, GCMYPERFORMANCE_COINID_FIELD}
GCMYPERFORMANCE.is_extendable = false
GCMYPERFORMANCE.extensions = {}
CGSETSUBINFO_USERID_FIELD.name = "userid"
CGSETSUBINFO_USERID_FIELD.full_name = ".prootc.cgsetsubinfo.userid"
CGSETSUBINFO_USERID_FIELD.number = 1
CGSETSUBINFO_USERID_FIELD.index = 0
CGSETSUBINFO_USERID_FIELD.label = 1
CGSETSUBINFO_USERID_FIELD.has_default_value = false
CGSETSUBINFO_USERID_FIELD.default_value = 0
CGSETSUBINFO_USERID_FIELD.type = 5
CGSETSUBINFO_USERID_FIELD.cpp_type = 1

CGSETSUBINFO_TARGETUSERID_FIELD.name = "targetuserid"
CGSETSUBINFO_TARGETUSERID_FIELD.full_name = ".prootc.cgsetsubinfo.targetuserid"
CGSETSUBINFO_TARGETUSERID_FIELD.number = 2
CGSETSUBINFO_TARGETUSERID_FIELD.index = 1
CGSETSUBINFO_TARGETUSERID_FIELD.label = 1
CGSETSUBINFO_TARGETUSERID_FIELD.has_default_value = false
CGSETSUBINFO_TARGETUSERID_FIELD.default_value = 0
CGSETSUBINFO_TARGETUSERID_FIELD.type = 5
CGSETSUBINFO_TARGETUSERID_FIELD.cpp_type = 1

CGSETSUBINFO_ISACCEPTORDER_FIELD.name = "isacceptorder"
CGSETSUBINFO_ISACCEPTORDER_FIELD.full_name = ".prootc.cgsetsubinfo.isacceptorder"
CGSETSUBINFO_ISACCEPTORDER_FIELD.number = 3
CGSETSUBINFO_ISACCEPTORDER_FIELD.index = 2
CGSETSUBINFO_ISACCEPTORDER_FIELD.label = 1
CGSETSUBINFO_ISACCEPTORDER_FIELD.has_default_value = false
CGSETSUBINFO_ISACCEPTORDER_FIELD.default_value = 0
CGSETSUBINFO_ISACCEPTORDER_FIELD.type = 5
CGSETSUBINFO_ISACCEPTORDER_FIELD.cpp_type = 1

CGSETSUBINFO_PROHIBITLOGIN_FIELD.name = "prohibitlogin"
CGSETSUBINFO_PROHIBITLOGIN_FIELD.full_name = ".prootc.cgsetsubinfo.prohibitlogin"
CGSETSUBINFO_PROHIBITLOGIN_FIELD.number = 4
CGSETSUBINFO_PROHIBITLOGIN_FIELD.index = 3
CGSETSUBINFO_PROHIBITLOGIN_FIELD.label = 1
CGSETSUBINFO_PROHIBITLOGIN_FIELD.has_default_value = false
CGSETSUBINFO_PROHIBITLOGIN_FIELD.default_value = 0
CGSETSUBINFO_PROHIBITLOGIN_FIELD.type = 5
CGSETSUBINFO_PROHIBITLOGIN_FIELD.cpp_type = 1

CGSETSUBINFO_PAYTYPELIST_FIELD.name = "paytypelist"
CGSETSUBINFO_PAYTYPELIST_FIELD.full_name = ".prootc.cgsetsubinfo.paytypelist"
CGSETSUBINFO_PAYTYPELIST_FIELD.number = 5
CGSETSUBINFO_PAYTYPELIST_FIELD.index = 4
CGSETSUBINFO_PAYTYPELIST_FIELD.label = 3
CGSETSUBINFO_PAYTYPELIST_FIELD.has_default_value = false
CGSETSUBINFO_PAYTYPELIST_FIELD.default_value = {}
CGSETSUBINFO_PAYTYPELIST_FIELD.type = 9
CGSETSUBINFO_PAYTYPELIST_FIELD.cpp_type = 9

CGSETSUBINFO_PAYRATELIST_FIELD.name = "payratelist"
CGSETSUBINFO_PAYRATELIST_FIELD.full_name = ".prootc.cgsetsubinfo.payratelist"
CGSETSUBINFO_PAYRATELIST_FIELD.number = 6
CGSETSUBINFO_PAYRATELIST_FIELD.index = 5
CGSETSUBINFO_PAYRATELIST_FIELD.label = 3
CGSETSUBINFO_PAYRATELIST_FIELD.has_default_value = false
CGSETSUBINFO_PAYRATELIST_FIELD.default_value = {}
CGSETSUBINFO_PAYRATELIST_FIELD.type = 9
CGSETSUBINFO_PAYRATELIST_FIELD.cpp_type = 9

CGSETSUBINFO_BUYRATELIST_FIELD.name = "buyratelist"
CGSETSUBINFO_BUYRATELIST_FIELD.full_name = ".prootc.cgsetsubinfo.buyratelist"
CGSETSUBINFO_BUYRATELIST_FIELD.number = 7
CGSETSUBINFO_BUYRATELIST_FIELD.index = 6
CGSETSUBINFO_BUYRATELIST_FIELD.label = 3
CGSETSUBINFO_BUYRATELIST_FIELD.has_default_value = false
CGSETSUBINFO_BUYRATELIST_FIELD.default_value = {}
CGSETSUBINFO_BUYRATELIST_FIELD.type = 9
CGSETSUBINFO_BUYRATELIST_FIELD.cpp_type = 9

CGSETSUBINFO_BEHALFBUYRATELIST_FIELD.name = "behalfbuyratelist"
CGSETSUBINFO_BEHALFBUYRATELIST_FIELD.full_name = ".prootc.cgsetsubinfo.behalfbuyratelist"
CGSETSUBINFO_BEHALFBUYRATELIST_FIELD.number = 8
CGSETSUBINFO_BEHALFBUYRATELIST_FIELD.index = 7
CGSETSUBINFO_BEHALFBUYRATELIST_FIELD.label = 3
CGSETSUBINFO_BEHALFBUYRATELIST_FIELD.has_default_value = false
CGSETSUBINFO_BEHALFBUYRATELIST_FIELD.default_value = {}
CGSETSUBINFO_BEHALFBUYRATELIST_FIELD.type = 9
CGSETSUBINFO_BEHALFBUYRATELIST_FIELD.cpp_type = 9

CGSETSUBINFO.name = "cgsetsubinfo"
CGSETSUBINFO.full_name = ".prootc.cgsetsubinfo"
CGSETSUBINFO.nested_types = {}
CGSETSUBINFO.enum_types = {}
CGSETSUBINFO.fields = {CGSETSUBINFO_USERID_FIELD, CGSETSUBINFO_TARGETUSERID_FIELD, CGSETSUBINFO_ISACCEPTORDER_FIELD, CGSETSUBINFO_PROHIBITLOGIN_FIELD, CGSETSUBINFO_PAYTYPELIST_FIELD, CGSETSUBINFO_PAYRATELIST_FIELD, CGSETSUBINFO_BUYRATELIST_FIELD, CGSETSUBINFO_BEHALFBUYRATELIST_FIELD}
CGSETSUBINFO.is_extendable = false
CGSETSUBINFO.extensions = {}
GCSETSUBINFO_RESULT_FIELD.name = "result"
GCSETSUBINFO_RESULT_FIELD.full_name = ".prootc.gcsetsubinfo.result"
GCSETSUBINFO_RESULT_FIELD.number = 1
GCSETSUBINFO_RESULT_FIELD.index = 0
GCSETSUBINFO_RESULT_FIELD.label = 1
GCSETSUBINFO_RESULT_FIELD.has_default_value = false
GCSETSUBINFO_RESULT_FIELD.default_value = 0
GCSETSUBINFO_RESULT_FIELD.type = 5
GCSETSUBINFO_RESULT_FIELD.cpp_type = 1

GCSETSUBINFO_MSG_FIELD.name = "msg"
GCSETSUBINFO_MSG_FIELD.full_name = ".prootc.gcsetsubinfo.msg"
GCSETSUBINFO_MSG_FIELD.number = 2
GCSETSUBINFO_MSG_FIELD.index = 1
GCSETSUBINFO_MSG_FIELD.label = 1
GCSETSUBINFO_MSG_FIELD.has_default_value = false
GCSETSUBINFO_MSG_FIELD.default_value = ""
GCSETSUBINFO_MSG_FIELD.type = 9
GCSETSUBINFO_MSG_FIELD.cpp_type = 9

GCSETSUBINFO_USERID_FIELD.name = "userid"
GCSETSUBINFO_USERID_FIELD.full_name = ".prootc.gcsetsubinfo.userid"
GCSETSUBINFO_USERID_FIELD.number = 3
GCSETSUBINFO_USERID_FIELD.index = 2
GCSETSUBINFO_USERID_FIELD.label = 1
GCSETSUBINFO_USERID_FIELD.has_default_value = false
GCSETSUBINFO_USERID_FIELD.default_value = 0
GCSETSUBINFO_USERID_FIELD.type = 5
GCSETSUBINFO_USERID_FIELD.cpp_type = 1

GCSETSUBINFO_TARGETUSERID_FIELD.name = "targetuserid"
GCSETSUBINFO_TARGETUSERID_FIELD.full_name = ".prootc.gcsetsubinfo.targetuserid"
GCSETSUBINFO_TARGETUSERID_FIELD.number = 4
GCSETSUBINFO_TARGETUSERID_FIELD.index = 3
GCSETSUBINFO_TARGETUSERID_FIELD.label = 1
GCSETSUBINFO_TARGETUSERID_FIELD.has_default_value = false
GCSETSUBINFO_TARGETUSERID_FIELD.default_value = 0
GCSETSUBINFO_TARGETUSERID_FIELD.type = 5
GCSETSUBINFO_TARGETUSERID_FIELD.cpp_type = 1

GCSETSUBINFO_ISACCEPTORDER_FIELD.name = "isacceptorder"
GCSETSUBINFO_ISACCEPTORDER_FIELD.full_name = ".prootc.gcsetsubinfo.isacceptorder"
GCSETSUBINFO_ISACCEPTORDER_FIELD.number = 5
GCSETSUBINFO_ISACCEPTORDER_FIELD.index = 4
GCSETSUBINFO_ISACCEPTORDER_FIELD.label = 1
GCSETSUBINFO_ISACCEPTORDER_FIELD.has_default_value = false
GCSETSUBINFO_ISACCEPTORDER_FIELD.default_value = 0
GCSETSUBINFO_ISACCEPTORDER_FIELD.type = 5
GCSETSUBINFO_ISACCEPTORDER_FIELD.cpp_type = 1

GCSETSUBINFO_PROHIBITLOGIN_FIELD.name = "prohibitlogin"
GCSETSUBINFO_PROHIBITLOGIN_FIELD.full_name = ".prootc.gcsetsubinfo.prohibitlogin"
GCSETSUBINFO_PROHIBITLOGIN_FIELD.number = 6
GCSETSUBINFO_PROHIBITLOGIN_FIELD.index = 5
GCSETSUBINFO_PROHIBITLOGIN_FIELD.label = 1
GCSETSUBINFO_PROHIBITLOGIN_FIELD.has_default_value = false
GCSETSUBINFO_PROHIBITLOGIN_FIELD.default_value = 0
GCSETSUBINFO_PROHIBITLOGIN_FIELD.type = 5
GCSETSUBINFO_PROHIBITLOGIN_FIELD.cpp_type = 1

GCSETSUBINFO_PAYTYPELIST_FIELD.name = "paytypelist"
GCSETSUBINFO_PAYTYPELIST_FIELD.full_name = ".prootc.gcsetsubinfo.paytypelist"
GCSETSUBINFO_PAYTYPELIST_FIELD.number = 7
GCSETSUBINFO_PAYTYPELIST_FIELD.index = 6
GCSETSUBINFO_PAYTYPELIST_FIELD.label = 3
GCSETSUBINFO_PAYTYPELIST_FIELD.has_default_value = false
GCSETSUBINFO_PAYTYPELIST_FIELD.default_value = {}
GCSETSUBINFO_PAYTYPELIST_FIELD.type = 9
GCSETSUBINFO_PAYTYPELIST_FIELD.cpp_type = 9

GCSETSUBINFO_PAYRATELIST_FIELD.name = "payratelist"
GCSETSUBINFO_PAYRATELIST_FIELD.full_name = ".prootc.gcsetsubinfo.payratelist"
GCSETSUBINFO_PAYRATELIST_FIELD.number = 8
GCSETSUBINFO_PAYRATELIST_FIELD.index = 7
GCSETSUBINFO_PAYRATELIST_FIELD.label = 3
GCSETSUBINFO_PAYRATELIST_FIELD.has_default_value = false
GCSETSUBINFO_PAYRATELIST_FIELD.default_value = {}
GCSETSUBINFO_PAYRATELIST_FIELD.type = 9
GCSETSUBINFO_PAYRATELIST_FIELD.cpp_type = 9

GCSETSUBINFO_BUYRATELIST_FIELD.name = "buyratelist"
GCSETSUBINFO_BUYRATELIST_FIELD.full_name = ".prootc.gcsetsubinfo.buyratelist"
GCSETSUBINFO_BUYRATELIST_FIELD.number = 9
GCSETSUBINFO_BUYRATELIST_FIELD.index = 8
GCSETSUBINFO_BUYRATELIST_FIELD.label = 3
GCSETSUBINFO_BUYRATELIST_FIELD.has_default_value = false
GCSETSUBINFO_BUYRATELIST_FIELD.default_value = {}
GCSETSUBINFO_BUYRATELIST_FIELD.type = 9
GCSETSUBINFO_BUYRATELIST_FIELD.cpp_type = 9

GCSETSUBINFO_BEHALFBUYRATELIST_FIELD.name = "behalfbuyratelist"
GCSETSUBINFO_BEHALFBUYRATELIST_FIELD.full_name = ".prootc.gcsetsubinfo.behalfbuyratelist"
GCSETSUBINFO_BEHALFBUYRATELIST_FIELD.number = 10
GCSETSUBINFO_BEHALFBUYRATELIST_FIELD.index = 9
GCSETSUBINFO_BEHALFBUYRATELIST_FIELD.label = 3
GCSETSUBINFO_BEHALFBUYRATELIST_FIELD.has_default_value = false
GCSETSUBINFO_BEHALFBUYRATELIST_FIELD.default_value = {}
GCSETSUBINFO_BEHALFBUYRATELIST_FIELD.type = 9
GCSETSUBINFO_BEHALFBUYRATELIST_FIELD.cpp_type = 9

GCSETSUBINFO.name = "gcsetsubinfo"
GCSETSUBINFO.full_name = ".prootc.gcsetsubinfo"
GCSETSUBINFO.nested_types = {}
GCSETSUBINFO.enum_types = {}
GCSETSUBINFO.fields = {GCSETSUBINFO_RESULT_FIELD, GCSETSUBINFO_MSG_FIELD, GCSETSUBINFO_USERID_FIELD, GCSETSUBINFO_TARGETUSERID_FIELD, GCSETSUBINFO_ISACCEPTORDER_FIELD, GCSETSUBINFO_PROHIBITLOGIN_FIELD, GCSETSUBINFO_PAYTYPELIST_FIELD, GCSETSUBINFO_PAYRATELIST_FIELD, GCSETSUBINFO_BUYRATELIST_FIELD, GCSETSUBINFO_BEHALFBUYRATELIST_FIELD}
GCSETSUBINFO.is_extendable = false
GCSETSUBINFO.extensions = {}
CGCHECKFUNDPWD_USERID_FIELD.name = "userid"
CGCHECKFUNDPWD_USERID_FIELD.full_name = ".prootc.cgcheckfundpwd.userid"
CGCHECKFUNDPWD_USERID_FIELD.number = 1
CGCHECKFUNDPWD_USERID_FIELD.index = 0
CGCHECKFUNDPWD_USERID_FIELD.label = 1
CGCHECKFUNDPWD_USERID_FIELD.has_default_value = false
CGCHECKFUNDPWD_USERID_FIELD.default_value = 0
CGCHECKFUNDPWD_USERID_FIELD.type = 5
CGCHECKFUNDPWD_USERID_FIELD.cpp_type = 1

CGCHECKFUNDPWD_FUNDPWD_FIELD.name = "fundpwd"
CGCHECKFUNDPWD_FUNDPWD_FIELD.full_name = ".prootc.cgcheckfundpwd.fundpwd"
CGCHECKFUNDPWD_FUNDPWD_FIELD.number = 2
CGCHECKFUNDPWD_FUNDPWD_FIELD.index = 1
CGCHECKFUNDPWD_FUNDPWD_FIELD.label = 1
CGCHECKFUNDPWD_FUNDPWD_FIELD.has_default_value = false
CGCHECKFUNDPWD_FUNDPWD_FIELD.default_value = ""
CGCHECKFUNDPWD_FUNDPWD_FIELD.type = 9
CGCHECKFUNDPWD_FUNDPWD_FIELD.cpp_type = 9

CGCHECKFUNDPWD.name = "cgcheckfundpwd"
CGCHECKFUNDPWD.full_name = ".prootc.cgcheckfundpwd"
CGCHECKFUNDPWD.nested_types = {}
CGCHECKFUNDPWD.enum_types = {}
CGCHECKFUNDPWD.fields = {CGCHECKFUNDPWD_USERID_FIELD, CGCHECKFUNDPWD_FUNDPWD_FIELD}
CGCHECKFUNDPWD.is_extendable = false
CGCHECKFUNDPWD.extensions = {}
GCCHECKFUNDPWD_RESULT_FIELD.name = "result"
GCCHECKFUNDPWD_RESULT_FIELD.full_name = ".prootc.gccheckfundpwd.result"
GCCHECKFUNDPWD_RESULT_FIELD.number = 1
GCCHECKFUNDPWD_RESULT_FIELD.index = 0
GCCHECKFUNDPWD_RESULT_FIELD.label = 1
GCCHECKFUNDPWD_RESULT_FIELD.has_default_value = false
GCCHECKFUNDPWD_RESULT_FIELD.default_value = 0
GCCHECKFUNDPWD_RESULT_FIELD.type = 5
GCCHECKFUNDPWD_RESULT_FIELD.cpp_type = 1

GCCHECKFUNDPWD_MSG_FIELD.name = "msg"
GCCHECKFUNDPWD_MSG_FIELD.full_name = ".prootc.gccheckfundpwd.msg"
GCCHECKFUNDPWD_MSG_FIELD.number = 2
GCCHECKFUNDPWD_MSG_FIELD.index = 1
GCCHECKFUNDPWD_MSG_FIELD.label = 1
GCCHECKFUNDPWD_MSG_FIELD.has_default_value = false
GCCHECKFUNDPWD_MSG_FIELD.default_value = ""
GCCHECKFUNDPWD_MSG_FIELD.type = 9
GCCHECKFUNDPWD_MSG_FIELD.cpp_type = 9

GCCHECKFUNDPWD.name = "gccheckfundpwd"
GCCHECKFUNDPWD.full_name = ".prootc.gccheckfundpwd"
GCCHECKFUNDPWD.nested_types = {}
GCCHECKFUNDPWD.enum_types = {}
GCCHECKFUNDPWD.fields = {GCCHECKFUNDPWD_RESULT_FIELD, GCCHECKFUNDPWD_MSG_FIELD}
GCCHECKFUNDPWD.is_extendable = false
GCCHECKFUNDPWD.extensions = {}
GCUPDATECOININFO_RESULT_FIELD.name = "result"
GCUPDATECOININFO_RESULT_FIELD.full_name = ".prootc.gcupdatecoininfo.result"
GCUPDATECOININFO_RESULT_FIELD.number = 1
GCUPDATECOININFO_RESULT_FIELD.index = 0
GCUPDATECOININFO_RESULT_FIELD.label = 1
GCUPDATECOININFO_RESULT_FIELD.has_default_value = false
GCUPDATECOININFO_RESULT_FIELD.default_value = 0
GCUPDATECOININFO_RESULT_FIELD.type = 5
GCUPDATECOININFO_RESULT_FIELD.cpp_type = 1

GCUPDATECOININFO_MSG_FIELD.name = "msg"
GCUPDATECOININFO_MSG_FIELD.full_name = ".prootc.gcupdatecoininfo.msg"
GCUPDATECOININFO_MSG_FIELD.number = 2
GCUPDATECOININFO_MSG_FIELD.index = 1
GCUPDATECOININFO_MSG_FIELD.label = 1
GCUPDATECOININFO_MSG_FIELD.has_default_value = false
GCUPDATECOININFO_MSG_FIELD.default_value = ""
GCUPDATECOININFO_MSG_FIELD.type = 9
GCUPDATECOININFO_MSG_FIELD.cpp_type = 9

GCUPDATECOININFO_COINLIST_FIELD.name = "coinlist"
GCUPDATECOININFO_COINLIST_FIELD.full_name = ".prootc.gcupdatecoininfo.coinlist"
GCUPDATECOININFO_COINLIST_FIELD.number = 3
GCUPDATECOININFO_COINLIST_FIELD.index = 2
GCUPDATECOININFO_COINLIST_FIELD.label = 3
GCUPDATECOININFO_COINLIST_FIELD.has_default_value = false
GCUPDATECOININFO_COINLIST_FIELD.default_value = {}
GCUPDATECOININFO_COINLIST_FIELD.message_type = ST_HUMAN_PB_COININFO
GCUPDATECOININFO_COINLIST_FIELD.type = 11
GCUPDATECOININFO_COINLIST_FIELD.cpp_type = 10

GCUPDATECOININFO.name = "gcupdatecoininfo"
GCUPDATECOININFO.full_name = ".prootc.gcupdatecoininfo"
GCUPDATECOININFO.nested_types = {}
GCUPDATECOININFO.enum_types = {}
GCUPDATECOININFO.fields = {GCUPDATECOININFO_RESULT_FIELD, GCUPDATECOININFO_MSG_FIELD, GCUPDATECOININFO_COINLIST_FIELD}
GCUPDATECOININFO.is_extendable = false
GCUPDATECOININFO.extensions = {}
CGGETUSERINFO_USERID_FIELD.name = "userid"
CGGETUSERINFO_USERID_FIELD.full_name = ".prootc.cggetuserinfo.userid"
CGGETUSERINFO_USERID_FIELD.number = 1
CGGETUSERINFO_USERID_FIELD.index = 0
CGGETUSERINFO_USERID_FIELD.label = 1
CGGETUSERINFO_USERID_FIELD.has_default_value = false
CGGETUSERINFO_USERID_FIELD.default_value = 0
CGGETUSERINFO_USERID_FIELD.type = 5
CGGETUSERINFO_USERID_FIELD.cpp_type = 1

CGGETUSERINFO.name = "cggetuserinfo"
CGGETUSERINFO.full_name = ".prootc.cggetuserinfo"
CGGETUSERINFO.nested_types = {}
CGGETUSERINFO.enum_types = {}
CGGETUSERINFO.fields = {CGGETUSERINFO_USERID_FIELD}
CGGETUSERINFO.is_extendable = false
CGGETUSERINFO.extensions = {}
GCGETUSERINFO_RESULT_FIELD.name = "result"
GCGETUSERINFO_RESULT_FIELD.full_name = ".prootc.gcgetuserinfo.result"
GCGETUSERINFO_RESULT_FIELD.number = 1
GCGETUSERINFO_RESULT_FIELD.index = 0
GCGETUSERINFO_RESULT_FIELD.label = 1
GCGETUSERINFO_RESULT_FIELD.has_default_value = false
GCGETUSERINFO_RESULT_FIELD.default_value = 0
GCGETUSERINFO_RESULT_FIELD.type = 5
GCGETUSERINFO_RESULT_FIELD.cpp_type = 1

GCGETUSERINFO_MSG_FIELD.name = "msg"
GCGETUSERINFO_MSG_FIELD.full_name = ".prootc.gcgetuserinfo.msg"
GCGETUSERINFO_MSG_FIELD.number = 2
GCGETUSERINFO_MSG_FIELD.index = 1
GCGETUSERINFO_MSG_FIELD.label = 1
GCGETUSERINFO_MSG_FIELD.has_default_value = false
GCGETUSERINFO_MSG_FIELD.default_value = ""
GCGETUSERINFO_MSG_FIELD.type = 9
GCGETUSERINFO_MSG_FIELD.cpp_type = 9

GCGETUSERINFO_UINFO_FIELD.name = "uinfo"
GCGETUSERINFO_UINFO_FIELD.full_name = ".prootc.gcgetuserinfo.uinfo"
GCGETUSERINFO_UINFO_FIELD.number = 3
GCGETUSERINFO_UINFO_FIELD.index = 2
GCGETUSERINFO_UINFO_FIELD.label = 1
GCGETUSERINFO_UINFO_FIELD.has_default_value = false
GCGETUSERINFO_UINFO_FIELD.default_value = nil
GCGETUSERINFO_UINFO_FIELD.message_type = ST_HUMAN_PB_USERINFO
GCGETUSERINFO_UINFO_FIELD.type = 11
GCGETUSERINFO_UINFO_FIELD.cpp_type = 10

GCGETUSERINFO_ADDRLIST_FIELD.name = "addrlist"
GCGETUSERINFO_ADDRLIST_FIELD.full_name = ".prootc.gcgetuserinfo.addrlist"
GCGETUSERINFO_ADDRLIST_FIELD.number = 4
GCGETUSERINFO_ADDRLIST_FIELD.index = 3
GCGETUSERINFO_ADDRLIST_FIELD.label = 3
GCGETUSERINFO_ADDRLIST_FIELD.has_default_value = false
GCGETUSERINFO_ADDRLIST_FIELD.default_value = {}
GCGETUSERINFO_ADDRLIST_FIELD.message_type = ST_HUMAN_PB_COINADDR
GCGETUSERINFO_ADDRLIST_FIELD.type = 11
GCGETUSERINFO_ADDRLIST_FIELD.cpp_type = 10

GCGETUSERINFO.name = "gcgetuserinfo"
GCGETUSERINFO.full_name = ".prootc.gcgetuserinfo"
GCGETUSERINFO.nested_types = {}
GCGETUSERINFO.enum_types = {}
GCGETUSERINFO.fields = {GCGETUSERINFO_RESULT_FIELD, GCGETUSERINFO_MSG_FIELD, GCGETUSERINFO_UINFO_FIELD, GCGETUSERINFO_ADDRLIST_FIELD}
GCGETUSERINFO.is_extendable = false
GCGETUSERINFO.extensions = {}

cgaddpay = protobuf.Message(CGADDPAY)
cgcheckfundpwd = protobuf.Message(CGCHECKFUNDPWD)
cgdeletepay = protobuf.Message(CGDELETEPAY)
cgforgetfundpwd = protobuf.Message(CGFORGETFUNDPWD)
cgforgetloginpwd = protobuf.Message(CGFORGETLOGINPWD)
cggetuserinfo = protobuf.Message(CGGETUSERINFO)
cgheartbeat = protobuf.Message(CGHEARTBEAT)
cglogin = protobuf.Message(CGLOGIN)
cgmodifypay = protobuf.Message(CGMODIFYPAY)
cgmyperformance = protobuf.Message(CGMYPERFORMANCE)
cgpaylist = protobuf.Message(CGPAYLIST)
cgpaystatus = protobuf.Message(CGPAYSTATUS)
cgreconnect = protobuf.Message(CGRECONNECT)
cgregister = protobuf.Message(CGREGISTER)
cgsetfundpwd = protobuf.Message(CGSETFUNDPWD)
cgsetsubinfo = protobuf.Message(CGSETSUBINFO)
cgupdateloginpwd = protobuf.Message(CGUPDATELOGINPWD)
cgupdatenickname = protobuf.Message(CGUPDATENICKNAME)
gcaddpay = protobuf.Message(GCADDPAY)
gccheckfundpwd = protobuf.Message(GCCHECKFUNDPWD)
gcdeletepay = protobuf.Message(GCDELETEPAY)
gcforgetfundpwd = protobuf.Message(GCFORGETFUNDPWD)
gcforgetloginpwd = protobuf.Message(GCFORGETLOGINPWD)
gcgetuserinfo = protobuf.Message(GCGETUSERINFO)
gcheartbeat = protobuf.Message(GCHEARTBEAT)
gckituser = protobuf.Message(GCKITUSER)
gclogin = protobuf.Message(GCLOGIN)
gcmodifypay = protobuf.Message(GCMODIFYPAY)
gcmyperformance = protobuf.Message(GCMYPERFORMANCE)
gcpaylist = protobuf.Message(GCPAYLIST)
gcpaystatus = protobuf.Message(GCPAYSTATUS)
gcreconnect = protobuf.Message(GCRECONNECT)
gcregister = protobuf.Message(GCREGISTER)
gcsetfundpwd = protobuf.Message(GCSETFUNDPWD)
gcsetsubinfo = protobuf.Message(GCSETSUBINFO)
gcupdatecoininfo = protobuf.Message(GCUPDATECOININFO)
gcupdateloginpwd = protobuf.Message(GCUPDATELOGINPWD)
gcupdatenickname = protobuf.Message(GCUPDATENICKNAME)
gcupdateuserinfo = protobuf.Message(GCUPDATEUSERINFO)

----------nimol modify---------
MSG_HUMAN_PB_CGADDPAY = CGADDPAY
MSG_HUMAN_PB_CGCHECKFUNDPWD = CGCHECKFUNDPWD
MSG_HUMAN_PB_CGDELETEPAY = CGDELETEPAY
MSG_HUMAN_PB_CGFORGETFUNDPWD = CGFORGETFUNDPWD
MSG_HUMAN_PB_CGFORGETLOGINPWD = CGFORGETLOGINPWD
MSG_HUMAN_PB_CGGETUSERINFO = CGGETUSERINFO
MSG_HUMAN_PB_CGHEARTBEAT = CGHEARTBEAT
MSG_HUMAN_PB_CGLOGIN = CGLOGIN
MSG_HUMAN_PB_CGMODIFYPAY = CGMODIFYPAY
MSG_HUMAN_PB_CGMYPERFORMANCE = CGMYPERFORMANCE
MSG_HUMAN_PB_CGPAYLIST = CGPAYLIST
MSG_HUMAN_PB_CGPAYSTATUS = CGPAYSTATUS
MSG_HUMAN_PB_CGRECONNECT = CGRECONNECT
MSG_HUMAN_PB_CGREGISTER = CGREGISTER
MSG_HUMAN_PB_CGSETFUNDPWD = CGSETFUNDPWD
MSG_HUMAN_PB_CGSETSUBINFO = CGSETSUBINFO
MSG_HUMAN_PB_CGUPDATELOGINPWD = CGUPDATELOGINPWD
MSG_HUMAN_PB_CGUPDATENICKNAME = CGUPDATENICKNAME
MSG_HUMAN_PB_GCADDPAY = GCADDPAY
MSG_HUMAN_PB_GCCHECKFUNDPWD = GCCHECKFUNDPWD
MSG_HUMAN_PB_GCDELETEPAY = GCDELETEPAY
MSG_HUMAN_PB_GCFORGETFUNDPWD = GCFORGETFUNDPWD
MSG_HUMAN_PB_GCFORGETLOGINPWD = GCFORGETLOGINPWD
MSG_HUMAN_PB_GCGETUSERINFO = GCGETUSERINFO
MSG_HUMAN_PB_GCHEARTBEAT = GCHEARTBEAT
MSG_HUMAN_PB_GCKITUSER = GCKITUSER
MSG_HUMAN_PB_GCLOGIN = GCLOGIN
MSG_HUMAN_PB_GCMODIFYPAY = GCMODIFYPAY
MSG_HUMAN_PB_GCMYPERFORMANCE = GCMYPERFORMANCE
MSG_HUMAN_PB_GCPAYLIST = GCPAYLIST
MSG_HUMAN_PB_GCPAYSTATUS = GCPAYSTATUS
MSG_HUMAN_PB_GCRECONNECT = GCRECONNECT
MSG_HUMAN_PB_GCREGISTER = GCREGISTER
MSG_HUMAN_PB_GCSETFUNDPWD = GCSETFUNDPWD
MSG_HUMAN_PB_GCSETSUBINFO = GCSETSUBINFO
MSG_HUMAN_PB_GCUPDATECOININFO = GCUPDATECOININFO
MSG_HUMAN_PB_GCUPDATELOGINPWD = GCUPDATELOGINPWD
MSG_HUMAN_PB_GCUPDATENICKNAME = GCUPDATENICKNAME
MSG_HUMAN_PB_GCUPDATEUSERINFO = GCUPDATEUSERINFO
