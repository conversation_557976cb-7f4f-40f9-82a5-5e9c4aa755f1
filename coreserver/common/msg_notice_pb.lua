-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local st_notice_pb = require("st_notice_pb")
----------nimol modify---------
local ST_NOTICE_PB_MESSAGEINFO = st_notice_pb.ST_NOTICE_PB_MESSAGEINFO
local ST_NOTICE_PB_NOTICEINFO = st_notice_pb.ST_NOTICE_PB_NOTICEINFO
module('msg_notice_pb')


local CGNOTICELIST = protobuf.Descriptor();
local CGNOTICELIST_USERID_FIELD = protobuf.FieldDescriptor();
local CGNOTICELIST_PAGENUM_FIELD = protobuf.FieldDescriptor();
local CGNOTICELIST_PAGESIZE_FIELD = protobuf.FieldDescriptor();
local GCNOTICELIST = protobuf.Descriptor();
local GCNOTICELIST_RESULT_FIELD = protobuf.FieldDescriptor();
local GCNOTICELIST_MSG_FIELD = protobuf.FieldDescriptor();
local GCNOTICELIST_PAGENUM_FIELD = protobuf.FieldDescriptor();
local GCNOTICELIST_PAGESIZE_FIELD = protobuf.FieldDescriptor();
local GCNOTICELIST_NOTICELIST_FIELD = protobuf.FieldDescriptor();
local CGNOTICEDETAIL = protobuf.Descriptor();
local CGNOTICEDETAIL_USERID_FIELD = protobuf.FieldDescriptor();
local CGNOTICEDETAIL_ID_FIELD = protobuf.FieldDescriptor();
local GCNOTICEDETAIL = protobuf.Descriptor();
local GCNOTICEDETAIL_RESULT_FIELD = protobuf.FieldDescriptor();
local GCNOTICEDETAIL_MSG_FIELD = protobuf.FieldDescriptor();
local CGNOTICEMESSAGELIST = protobuf.Descriptor();
local CGNOTICEMESSAGELIST_USERID_FIELD = protobuf.FieldDescriptor();
local CGNOTICEMESSAGELIST_PAGENUM_FIELD = protobuf.FieldDescriptor();
local CGNOTICEMESSAGELIST_PAGESIZE_FIELD = protobuf.FieldDescriptor();
local GCNOTICEMESSAGELIST = protobuf.Descriptor();
local GCNOTICEMESSAGELIST_RESULT_FIELD = protobuf.FieldDescriptor();
local GCNOTICEMESSAGELIST_MSG_FIELD = protobuf.FieldDescriptor();
local GCNOTICEMESSAGELIST_PAGENUM_FIELD = protobuf.FieldDescriptor();
local GCNOTICEMESSAGELIST_PAGESIZE_FIELD = protobuf.FieldDescriptor();
local GCNOTICEMESSAGELIST_MESSAGELIST_FIELD = protobuf.FieldDescriptor();
local CGNOTICEMESSAGEREAD = protobuf.Descriptor();
local CGNOTICEMESSAGEREAD_USERID_FIELD = protobuf.FieldDescriptor();
local CGNOTICEMESSAGEREAD_ID_FIELD = protobuf.FieldDescriptor();
local GCNOTICEMESSAGEREAD = protobuf.Descriptor();
local GCNOTICEMESSAGEREAD_RESULT_FIELD = protobuf.FieldDescriptor();
local GCNOTICEMESSAGEREAD_MSG_FIELD = protobuf.FieldDescriptor();
local CGNOTICEMESSAGEDELETE = protobuf.Descriptor();
local CGNOTICEMESSAGEDELETE_USERID_FIELD = protobuf.FieldDescriptor();
local CGNOTICEMESSAGEDELETE_ID_FIELD = protobuf.FieldDescriptor();
local GCNOTICEMESSAGEDELETE = protobuf.Descriptor();
local GCNOTICEMESSAGEDELETE_RESULT_FIELD = protobuf.FieldDescriptor();
local GCNOTICEMESSAGEDELETE_MSG_FIELD = protobuf.FieldDescriptor();

CGNOTICELIST_USERID_FIELD.name = "userid"
CGNOTICELIST_USERID_FIELD.full_name = ".prootc.cgnoticelist.userid"
CGNOTICELIST_USERID_FIELD.number = 1
CGNOTICELIST_USERID_FIELD.index = 0
CGNOTICELIST_USERID_FIELD.label = 1
CGNOTICELIST_USERID_FIELD.has_default_value = false
CGNOTICELIST_USERID_FIELD.default_value = 0
CGNOTICELIST_USERID_FIELD.type = 5
CGNOTICELIST_USERID_FIELD.cpp_type = 1

CGNOTICELIST_PAGENUM_FIELD.name = "pagenum"
CGNOTICELIST_PAGENUM_FIELD.full_name = ".prootc.cgnoticelist.pagenum"
CGNOTICELIST_PAGENUM_FIELD.number = 2
CGNOTICELIST_PAGENUM_FIELD.index = 1
CGNOTICELIST_PAGENUM_FIELD.label = 1
CGNOTICELIST_PAGENUM_FIELD.has_default_value = false
CGNOTICELIST_PAGENUM_FIELD.default_value = 0
CGNOTICELIST_PAGENUM_FIELD.type = 5
CGNOTICELIST_PAGENUM_FIELD.cpp_type = 1

CGNOTICELIST_PAGESIZE_FIELD.name = "pagesize"
CGNOTICELIST_PAGESIZE_FIELD.full_name = ".prootc.cgnoticelist.pagesize"
CGNOTICELIST_PAGESIZE_FIELD.number = 3
CGNOTICELIST_PAGESIZE_FIELD.index = 2
CGNOTICELIST_PAGESIZE_FIELD.label = 1
CGNOTICELIST_PAGESIZE_FIELD.has_default_value = false
CGNOTICELIST_PAGESIZE_FIELD.default_value = 0
CGNOTICELIST_PAGESIZE_FIELD.type = 5
CGNOTICELIST_PAGESIZE_FIELD.cpp_type = 1

CGNOTICELIST.name = "cgnoticelist"
CGNOTICELIST.full_name = ".prootc.cgnoticelist"
CGNOTICELIST.nested_types = {}
CGNOTICELIST.enum_types = {}
CGNOTICELIST.fields = {CGNOTICELIST_USERID_FIELD, CGNOTICELIST_PAGENUM_FIELD, CGNOTICELIST_PAGESIZE_FIELD}
CGNOTICELIST.is_extendable = false
CGNOTICELIST.extensions = {}
GCNOTICELIST_RESULT_FIELD.name = "result"
GCNOTICELIST_RESULT_FIELD.full_name = ".prootc.gcnoticelist.result"
GCNOTICELIST_RESULT_FIELD.number = 1
GCNOTICELIST_RESULT_FIELD.index = 0
GCNOTICELIST_RESULT_FIELD.label = 1
GCNOTICELIST_RESULT_FIELD.has_default_value = false
GCNOTICELIST_RESULT_FIELD.default_value = 0
GCNOTICELIST_RESULT_FIELD.type = 5
GCNOTICELIST_RESULT_FIELD.cpp_type = 1

GCNOTICELIST_MSG_FIELD.name = "msg"
GCNOTICELIST_MSG_FIELD.full_name = ".prootc.gcnoticelist.msg"
GCNOTICELIST_MSG_FIELD.number = 2
GCNOTICELIST_MSG_FIELD.index = 1
GCNOTICELIST_MSG_FIELD.label = 1
GCNOTICELIST_MSG_FIELD.has_default_value = false
GCNOTICELIST_MSG_FIELD.default_value = ""
GCNOTICELIST_MSG_FIELD.type = 9
GCNOTICELIST_MSG_FIELD.cpp_type = 9

GCNOTICELIST_PAGENUM_FIELD.name = "pagenum"
GCNOTICELIST_PAGENUM_FIELD.full_name = ".prootc.gcnoticelist.pagenum"
GCNOTICELIST_PAGENUM_FIELD.number = 3
GCNOTICELIST_PAGENUM_FIELD.index = 2
GCNOTICELIST_PAGENUM_FIELD.label = 1
GCNOTICELIST_PAGENUM_FIELD.has_default_value = false
GCNOTICELIST_PAGENUM_FIELD.default_value = 0
GCNOTICELIST_PAGENUM_FIELD.type = 5
GCNOTICELIST_PAGENUM_FIELD.cpp_type = 1

GCNOTICELIST_PAGESIZE_FIELD.name = "pagesize"
GCNOTICELIST_PAGESIZE_FIELD.full_name = ".prootc.gcnoticelist.pagesize"
GCNOTICELIST_PAGESIZE_FIELD.number = 4
GCNOTICELIST_PAGESIZE_FIELD.index = 3
GCNOTICELIST_PAGESIZE_FIELD.label = 1
GCNOTICELIST_PAGESIZE_FIELD.has_default_value = false
GCNOTICELIST_PAGESIZE_FIELD.default_value = 0
GCNOTICELIST_PAGESIZE_FIELD.type = 5
GCNOTICELIST_PAGESIZE_FIELD.cpp_type = 1

GCNOTICELIST_NOTICELIST_FIELD.name = "noticelist"
GCNOTICELIST_NOTICELIST_FIELD.full_name = ".prootc.gcnoticelist.noticelist"
GCNOTICELIST_NOTICELIST_FIELD.number = 5
GCNOTICELIST_NOTICELIST_FIELD.index = 4
GCNOTICELIST_NOTICELIST_FIELD.label = 3
GCNOTICELIST_NOTICELIST_FIELD.has_default_value = false
GCNOTICELIST_NOTICELIST_FIELD.default_value = {}
GCNOTICELIST_NOTICELIST_FIELD.message_type = ST_NOTICE_PB_NOTICEINFO
GCNOTICELIST_NOTICELIST_FIELD.type = 11
GCNOTICELIST_NOTICELIST_FIELD.cpp_type = 10

GCNOTICELIST.name = "gcnoticelist"
GCNOTICELIST.full_name = ".prootc.gcnoticelist"
GCNOTICELIST.nested_types = {}
GCNOTICELIST.enum_types = {}
GCNOTICELIST.fields = {GCNOTICELIST_RESULT_FIELD, GCNOTICELIST_MSG_FIELD, GCNOTICELIST_PAGENUM_FIELD, GCNOTICELIST_PAGESIZE_FIELD, GCNOTICELIST_NOTICELIST_FIELD}
GCNOTICELIST.is_extendable = false
GCNOTICELIST.extensions = {}
CGNOTICEDETAIL_USERID_FIELD.name = "userid"
CGNOTICEDETAIL_USERID_FIELD.full_name = ".prootc.cgnoticedetail.userid"
CGNOTICEDETAIL_USERID_FIELD.number = 1
CGNOTICEDETAIL_USERID_FIELD.index = 0
CGNOTICEDETAIL_USERID_FIELD.label = 1
CGNOTICEDETAIL_USERID_FIELD.has_default_value = false
CGNOTICEDETAIL_USERID_FIELD.default_value = 0
CGNOTICEDETAIL_USERID_FIELD.type = 5
CGNOTICEDETAIL_USERID_FIELD.cpp_type = 1

CGNOTICEDETAIL_ID_FIELD.name = "id"
CGNOTICEDETAIL_ID_FIELD.full_name = ".prootc.cgnoticedetail.id"
CGNOTICEDETAIL_ID_FIELD.number = 2
CGNOTICEDETAIL_ID_FIELD.index = 1
CGNOTICEDETAIL_ID_FIELD.label = 1
CGNOTICEDETAIL_ID_FIELD.has_default_value = false
CGNOTICEDETAIL_ID_FIELD.default_value = 0
CGNOTICEDETAIL_ID_FIELD.type = 5
CGNOTICEDETAIL_ID_FIELD.cpp_type = 1

CGNOTICEDETAIL.name = "cgnoticedetail"
CGNOTICEDETAIL.full_name = ".prootc.cgnoticedetail"
CGNOTICEDETAIL.nested_types = {}
CGNOTICEDETAIL.enum_types = {}
CGNOTICEDETAIL.fields = {CGNOTICEDETAIL_USERID_FIELD, CGNOTICEDETAIL_ID_FIELD}
CGNOTICEDETAIL.is_extendable = false
CGNOTICEDETAIL.extensions = {}
GCNOTICEDETAIL_RESULT_FIELD.name = "result"
GCNOTICEDETAIL_RESULT_FIELD.full_name = ".prootc.gcnoticedetail.result"
GCNOTICEDETAIL_RESULT_FIELD.number = 1
GCNOTICEDETAIL_RESULT_FIELD.index = 0
GCNOTICEDETAIL_RESULT_FIELD.label = 1
GCNOTICEDETAIL_RESULT_FIELD.has_default_value = false
GCNOTICEDETAIL_RESULT_FIELD.default_value = 0
GCNOTICEDETAIL_RESULT_FIELD.type = 5
GCNOTICEDETAIL_RESULT_FIELD.cpp_type = 1

GCNOTICEDETAIL_MSG_FIELD.name = "msg"
GCNOTICEDETAIL_MSG_FIELD.full_name = ".prootc.gcnoticedetail.msg"
GCNOTICEDETAIL_MSG_FIELD.number = 2
GCNOTICEDETAIL_MSG_FIELD.index = 1
GCNOTICEDETAIL_MSG_FIELD.label = 1
GCNOTICEDETAIL_MSG_FIELD.has_default_value = false
GCNOTICEDETAIL_MSG_FIELD.default_value = ""
GCNOTICEDETAIL_MSG_FIELD.type = 9
GCNOTICEDETAIL_MSG_FIELD.cpp_type = 9

GCNOTICEDETAIL.name = "gcnoticedetail"
GCNOTICEDETAIL.full_name = ".prootc.gcnoticedetail"
GCNOTICEDETAIL.nested_types = {}
GCNOTICEDETAIL.enum_types = {}
GCNOTICEDETAIL.fields = {GCNOTICEDETAIL_RESULT_FIELD, GCNOTICEDETAIL_MSG_FIELD}
GCNOTICEDETAIL.is_extendable = false
GCNOTICEDETAIL.extensions = {}
CGNOTICEMESSAGELIST_USERID_FIELD.name = "userid"
CGNOTICEMESSAGELIST_USERID_FIELD.full_name = ".prootc.cgnoticemessagelist.userid"
CGNOTICEMESSAGELIST_USERID_FIELD.number = 1
CGNOTICEMESSAGELIST_USERID_FIELD.index = 0
CGNOTICEMESSAGELIST_USERID_FIELD.label = 1
CGNOTICEMESSAGELIST_USERID_FIELD.has_default_value = false
CGNOTICEMESSAGELIST_USERID_FIELD.default_value = 0
CGNOTICEMESSAGELIST_USERID_FIELD.type = 5
CGNOTICEMESSAGELIST_USERID_FIELD.cpp_type = 1

CGNOTICEMESSAGELIST_PAGENUM_FIELD.name = "pagenum"
CGNOTICEMESSAGELIST_PAGENUM_FIELD.full_name = ".prootc.cgnoticemessagelist.pagenum"
CGNOTICEMESSAGELIST_PAGENUM_FIELD.number = 2
CGNOTICEMESSAGELIST_PAGENUM_FIELD.index = 1
CGNOTICEMESSAGELIST_PAGENUM_FIELD.label = 1
CGNOTICEMESSAGELIST_PAGENUM_FIELD.has_default_value = false
CGNOTICEMESSAGELIST_PAGENUM_FIELD.default_value = 0
CGNOTICEMESSAGELIST_PAGENUM_FIELD.type = 5
CGNOTICEMESSAGELIST_PAGENUM_FIELD.cpp_type = 1

CGNOTICEMESSAGELIST_PAGESIZE_FIELD.name = "pagesize"
CGNOTICEMESSAGELIST_PAGESIZE_FIELD.full_name = ".prootc.cgnoticemessagelist.pagesize"
CGNOTICEMESSAGELIST_PAGESIZE_FIELD.number = 3
CGNOTICEMESSAGELIST_PAGESIZE_FIELD.index = 2
CGNOTICEMESSAGELIST_PAGESIZE_FIELD.label = 1
CGNOTICEMESSAGELIST_PAGESIZE_FIELD.has_default_value = false
CGNOTICEMESSAGELIST_PAGESIZE_FIELD.default_value = 0
CGNOTICEMESSAGELIST_PAGESIZE_FIELD.type = 5
CGNOTICEMESSAGELIST_PAGESIZE_FIELD.cpp_type = 1

CGNOTICEMESSAGELIST.name = "cgnoticemessagelist"
CGNOTICEMESSAGELIST.full_name = ".prootc.cgnoticemessagelist"
CGNOTICEMESSAGELIST.nested_types = {}
CGNOTICEMESSAGELIST.enum_types = {}
CGNOTICEMESSAGELIST.fields = {CGNOTICEMESSAGELIST_USERID_FIELD, CGNOTICEMESSAGELIST_PAGENUM_FIELD, CGNOTICEMESSAGELIST_PAGESIZE_FIELD}
CGNOTICEMESSAGELIST.is_extendable = false
CGNOTICEMESSAGELIST.extensions = {}
GCNOTICEMESSAGELIST_RESULT_FIELD.name = "result"
GCNOTICEMESSAGELIST_RESULT_FIELD.full_name = ".prootc.gcnoticemessagelist.result"
GCNOTICEMESSAGELIST_RESULT_FIELD.number = 1
GCNOTICEMESSAGELIST_RESULT_FIELD.index = 0
GCNOTICEMESSAGELIST_RESULT_FIELD.label = 1
GCNOTICEMESSAGELIST_RESULT_FIELD.has_default_value = false
GCNOTICEMESSAGELIST_RESULT_FIELD.default_value = 0
GCNOTICEMESSAGELIST_RESULT_FIELD.type = 5
GCNOTICEMESSAGELIST_RESULT_FIELD.cpp_type = 1

GCNOTICEMESSAGELIST_MSG_FIELD.name = "msg"
GCNOTICEMESSAGELIST_MSG_FIELD.full_name = ".prootc.gcnoticemessagelist.msg"
GCNOTICEMESSAGELIST_MSG_FIELD.number = 2
GCNOTICEMESSAGELIST_MSG_FIELD.index = 1
GCNOTICEMESSAGELIST_MSG_FIELD.label = 1
GCNOTICEMESSAGELIST_MSG_FIELD.has_default_value = false
GCNOTICEMESSAGELIST_MSG_FIELD.default_value = ""
GCNOTICEMESSAGELIST_MSG_FIELD.type = 9
GCNOTICEMESSAGELIST_MSG_FIELD.cpp_type = 9

GCNOTICEMESSAGELIST_PAGENUM_FIELD.name = "pagenum"
GCNOTICEMESSAGELIST_PAGENUM_FIELD.full_name = ".prootc.gcnoticemessagelist.pagenum"
GCNOTICEMESSAGELIST_PAGENUM_FIELD.number = 3
GCNOTICEMESSAGELIST_PAGENUM_FIELD.index = 2
GCNOTICEMESSAGELIST_PAGENUM_FIELD.label = 1
GCNOTICEMESSAGELIST_PAGENUM_FIELD.has_default_value = false
GCNOTICEMESSAGELIST_PAGENUM_FIELD.default_value = 0
GCNOTICEMESSAGELIST_PAGENUM_FIELD.type = 5
GCNOTICEMESSAGELIST_PAGENUM_FIELD.cpp_type = 1

GCNOTICEMESSAGELIST_PAGESIZE_FIELD.name = "pagesize"
GCNOTICEMESSAGELIST_PAGESIZE_FIELD.full_name = ".prootc.gcnoticemessagelist.pagesize"
GCNOTICEMESSAGELIST_PAGESIZE_FIELD.number = 4
GCNOTICEMESSAGELIST_PAGESIZE_FIELD.index = 3
GCNOTICEMESSAGELIST_PAGESIZE_FIELD.label = 1
GCNOTICEMESSAGELIST_PAGESIZE_FIELD.has_default_value = false
GCNOTICEMESSAGELIST_PAGESIZE_FIELD.default_value = 0
GCNOTICEMESSAGELIST_PAGESIZE_FIELD.type = 5
GCNOTICEMESSAGELIST_PAGESIZE_FIELD.cpp_type = 1

GCNOTICEMESSAGELIST_MESSAGELIST_FIELD.name = "messagelist"
GCNOTICEMESSAGELIST_MESSAGELIST_FIELD.full_name = ".prootc.gcnoticemessagelist.messagelist"
GCNOTICEMESSAGELIST_MESSAGELIST_FIELD.number = 5
GCNOTICEMESSAGELIST_MESSAGELIST_FIELD.index = 4
GCNOTICEMESSAGELIST_MESSAGELIST_FIELD.label = 3
GCNOTICEMESSAGELIST_MESSAGELIST_FIELD.has_default_value = false
GCNOTICEMESSAGELIST_MESSAGELIST_FIELD.default_value = {}
GCNOTICEMESSAGELIST_MESSAGELIST_FIELD.message_type = ST_NOTICE_PB_MESSAGEINFO
GCNOTICEMESSAGELIST_MESSAGELIST_FIELD.type = 11
GCNOTICEMESSAGELIST_MESSAGELIST_FIELD.cpp_type = 10

GCNOTICEMESSAGELIST.name = "gcnoticemessagelist"
GCNOTICEMESSAGELIST.full_name = ".prootc.gcnoticemessagelist"
GCNOTICEMESSAGELIST.nested_types = {}
GCNOTICEMESSAGELIST.enum_types = {}
GCNOTICEMESSAGELIST.fields = {GCNOTICEMESSAGELIST_RESULT_FIELD, GCNOTICEMESSAGELIST_MSG_FIELD, GCNOTICEMESSAGELIST_PAGENUM_FIELD, GCNOTICEMESSAGELIST_PAGESIZE_FIELD, GCNOTICEMESSAGELIST_MESSAGELIST_FIELD}
GCNOTICEMESSAGELIST.is_extendable = false
GCNOTICEMESSAGELIST.extensions = {}
CGNOTICEMESSAGEREAD_USERID_FIELD.name = "userid"
CGNOTICEMESSAGEREAD_USERID_FIELD.full_name = ".prootc.cgnoticemessageread.userid"
CGNOTICEMESSAGEREAD_USERID_FIELD.number = 1
CGNOTICEMESSAGEREAD_USERID_FIELD.index = 0
CGNOTICEMESSAGEREAD_USERID_FIELD.label = 1
CGNOTICEMESSAGEREAD_USERID_FIELD.has_default_value = false
CGNOTICEMESSAGEREAD_USERID_FIELD.default_value = 0
CGNOTICEMESSAGEREAD_USERID_FIELD.type = 5
CGNOTICEMESSAGEREAD_USERID_FIELD.cpp_type = 1

CGNOTICEMESSAGEREAD_ID_FIELD.name = "id"
CGNOTICEMESSAGEREAD_ID_FIELD.full_name = ".prootc.cgnoticemessageread.id"
CGNOTICEMESSAGEREAD_ID_FIELD.number = 2
CGNOTICEMESSAGEREAD_ID_FIELD.index = 1
CGNOTICEMESSAGEREAD_ID_FIELD.label = 1
CGNOTICEMESSAGEREAD_ID_FIELD.has_default_value = false
CGNOTICEMESSAGEREAD_ID_FIELD.default_value = 0
CGNOTICEMESSAGEREAD_ID_FIELD.type = 5
CGNOTICEMESSAGEREAD_ID_FIELD.cpp_type = 1

CGNOTICEMESSAGEREAD.name = "cgnoticemessageread"
CGNOTICEMESSAGEREAD.full_name = ".prootc.cgnoticemessageread"
CGNOTICEMESSAGEREAD.nested_types = {}
CGNOTICEMESSAGEREAD.enum_types = {}
CGNOTICEMESSAGEREAD.fields = {CGNOTICEMESSAGEREAD_USERID_FIELD, CGNOTICEMESSAGEREAD_ID_FIELD}
CGNOTICEMESSAGEREAD.is_extendable = false
CGNOTICEMESSAGEREAD.extensions = {}
GCNOTICEMESSAGEREAD_RESULT_FIELD.name = "result"
GCNOTICEMESSAGEREAD_RESULT_FIELD.full_name = ".prootc.gcnoticemessageread.result"
GCNOTICEMESSAGEREAD_RESULT_FIELD.number = 1
GCNOTICEMESSAGEREAD_RESULT_FIELD.index = 0
GCNOTICEMESSAGEREAD_RESULT_FIELD.label = 1
GCNOTICEMESSAGEREAD_RESULT_FIELD.has_default_value = false
GCNOTICEMESSAGEREAD_RESULT_FIELD.default_value = 0
GCNOTICEMESSAGEREAD_RESULT_FIELD.type = 5
GCNOTICEMESSAGEREAD_RESULT_FIELD.cpp_type = 1

GCNOTICEMESSAGEREAD_MSG_FIELD.name = "msg"
GCNOTICEMESSAGEREAD_MSG_FIELD.full_name = ".prootc.gcnoticemessageread.msg"
GCNOTICEMESSAGEREAD_MSG_FIELD.number = 2
GCNOTICEMESSAGEREAD_MSG_FIELD.index = 1
GCNOTICEMESSAGEREAD_MSG_FIELD.label = 1
GCNOTICEMESSAGEREAD_MSG_FIELD.has_default_value = false
GCNOTICEMESSAGEREAD_MSG_FIELD.default_value = ""
GCNOTICEMESSAGEREAD_MSG_FIELD.type = 9
GCNOTICEMESSAGEREAD_MSG_FIELD.cpp_type = 9

GCNOTICEMESSAGEREAD.name = "gcnoticemessageread"
GCNOTICEMESSAGEREAD.full_name = ".prootc.gcnoticemessageread"
GCNOTICEMESSAGEREAD.nested_types = {}
GCNOTICEMESSAGEREAD.enum_types = {}
GCNOTICEMESSAGEREAD.fields = {GCNOTICEMESSAGEREAD_RESULT_FIELD, GCNOTICEMESSAGEREAD_MSG_FIELD}
GCNOTICEMESSAGEREAD.is_extendable = false
GCNOTICEMESSAGEREAD.extensions = {}
CGNOTICEMESSAGEDELETE_USERID_FIELD.name = "userid"
CGNOTICEMESSAGEDELETE_USERID_FIELD.full_name = ".prootc.cgnoticemessagedelete.userid"
CGNOTICEMESSAGEDELETE_USERID_FIELD.number = 1
CGNOTICEMESSAGEDELETE_USERID_FIELD.index = 0
CGNOTICEMESSAGEDELETE_USERID_FIELD.label = 1
CGNOTICEMESSAGEDELETE_USERID_FIELD.has_default_value = false
CGNOTICEMESSAGEDELETE_USERID_FIELD.default_value = 0
CGNOTICEMESSAGEDELETE_USERID_FIELD.type = 5
CGNOTICEMESSAGEDELETE_USERID_FIELD.cpp_type = 1

CGNOTICEMESSAGEDELETE_ID_FIELD.name = "id"
CGNOTICEMESSAGEDELETE_ID_FIELD.full_name = ".prootc.cgnoticemessagedelete.id"
CGNOTICEMESSAGEDELETE_ID_FIELD.number = 2
CGNOTICEMESSAGEDELETE_ID_FIELD.index = 1
CGNOTICEMESSAGEDELETE_ID_FIELD.label = 1
CGNOTICEMESSAGEDELETE_ID_FIELD.has_default_value = false
CGNOTICEMESSAGEDELETE_ID_FIELD.default_value = 0
CGNOTICEMESSAGEDELETE_ID_FIELD.type = 5
CGNOTICEMESSAGEDELETE_ID_FIELD.cpp_type = 1

CGNOTICEMESSAGEDELETE.name = "cgnoticemessagedelete"
CGNOTICEMESSAGEDELETE.full_name = ".prootc.cgnoticemessagedelete"
CGNOTICEMESSAGEDELETE.nested_types = {}
CGNOTICEMESSAGEDELETE.enum_types = {}
CGNOTICEMESSAGEDELETE.fields = {CGNOTICEMESSAGEDELETE_USERID_FIELD, CGNOTICEMESSAGEDELETE_ID_FIELD}
CGNOTICEMESSAGEDELETE.is_extendable = false
CGNOTICEMESSAGEDELETE.extensions = {}
GCNOTICEMESSAGEDELETE_RESULT_FIELD.name = "result"
GCNOTICEMESSAGEDELETE_RESULT_FIELD.full_name = ".prootc.gcnoticemessagedelete.result"
GCNOTICEMESSAGEDELETE_RESULT_FIELD.number = 1
GCNOTICEMESSAGEDELETE_RESULT_FIELD.index = 0
GCNOTICEMESSAGEDELETE_RESULT_FIELD.label = 1
GCNOTICEMESSAGEDELETE_RESULT_FIELD.has_default_value = false
GCNOTICEMESSAGEDELETE_RESULT_FIELD.default_value = 0
GCNOTICEMESSAGEDELETE_RESULT_FIELD.type = 5
GCNOTICEMESSAGEDELETE_RESULT_FIELD.cpp_type = 1

GCNOTICEMESSAGEDELETE_MSG_FIELD.name = "msg"
GCNOTICEMESSAGEDELETE_MSG_FIELD.full_name = ".prootc.gcnoticemessagedelete.msg"
GCNOTICEMESSAGEDELETE_MSG_FIELD.number = 2
GCNOTICEMESSAGEDELETE_MSG_FIELD.index = 1
GCNOTICEMESSAGEDELETE_MSG_FIELD.label = 1
GCNOTICEMESSAGEDELETE_MSG_FIELD.has_default_value = false
GCNOTICEMESSAGEDELETE_MSG_FIELD.default_value = ""
GCNOTICEMESSAGEDELETE_MSG_FIELD.type = 9
GCNOTICEMESSAGEDELETE_MSG_FIELD.cpp_type = 9

GCNOTICEMESSAGEDELETE.name = "gcnoticemessagedelete"
GCNOTICEMESSAGEDELETE.full_name = ".prootc.gcnoticemessagedelete"
GCNOTICEMESSAGEDELETE.nested_types = {}
GCNOTICEMESSAGEDELETE.enum_types = {}
GCNOTICEMESSAGEDELETE.fields = {GCNOTICEMESSAGEDELETE_RESULT_FIELD, GCNOTICEMESSAGEDELETE_MSG_FIELD}
GCNOTICEMESSAGEDELETE.is_extendable = false
GCNOTICEMESSAGEDELETE.extensions = {}

cgnoticedetail = protobuf.Message(CGNOTICEDETAIL)
cgnoticelist = protobuf.Message(CGNOTICELIST)
cgnoticemessagedelete = protobuf.Message(CGNOTICEMESSAGEDELETE)
cgnoticemessagelist = protobuf.Message(CGNOTICEMESSAGELIST)
cgnoticemessageread = protobuf.Message(CGNOTICEMESSAGEREAD)
gcnoticedetail = protobuf.Message(GCNOTICEDETAIL)
gcnoticelist = protobuf.Message(GCNOTICELIST)
gcnoticemessagedelete = protobuf.Message(GCNOTICEMESSAGEDELETE)
gcnoticemessagelist = protobuf.Message(GCNOTICEMESSAGELIST)
gcnoticemessageread = protobuf.Message(GCNOTICEMESSAGEREAD)

----------nimol modify---------
MSG_NOTICE_PB_CGNOTICEDETAIL = CGNOTICEDETAIL
MSG_NOTICE_PB_CGNOTICELIST = CGNOTICELIST
MSG_NOTICE_PB_CGNOTICEMESSAGEDELETE = CGNOTICEMESSAGEDELETE
MSG_NOTICE_PB_CGNOTICEMESSAGELIST = CGNOTICEMESSAGELIST
MSG_NOTICE_PB_CGNOTICEMESSAGEREAD = CGNOTICEMESSAGEREAD
MSG_NOTICE_PB_GCNOTICEDETAIL = GCNOTICEDETAIL
MSG_NOTICE_PB_GCNOTICELIST = GCNOTICELIST
MSG_NOTICE_PB_GCNOTICEMESSAGEDELETE = GCNOTICEMESSAGEDELETE
MSG_NOTICE_PB_GCNOTICEMESSAGELIST = GCNOTICEMESSAGELIST
MSG_NOTICE_PB_GCNOTICEMESSAGEREAD = GCNOTICEMESSAGEREAD
