g_marketDefine = {}


g_marketDefine.hang_buy = 0   --挂的是买单
g_marketDefine.hang_sell = 1  --挂的是卖单
g_marketDefine.hang_coin_pay = 2  --挂的是卖单

g_marketDefine.deal_buy = 0     --买单交易
g_marketDefine.deal_sell = 1    --卖单交易

g_marketDefine.amountType_digitalCurrency = 0     --数字货币
g_marketDefine.amountType_RMB = 1   			  --人民币

g_marketDefine.deal_status_wait = 1  --该订单开始，等待付款

g_marketDefine.deal_status_wait_timeout = 2  --等待超时，这个订单会失效，不能在使用。不过可以提起申诉。因为有可能玩家已经付了款，但是没有点击确认

g_marketDefine.deal_status_pay = 3  --订单已经付款，等待放行

g_marketDefine.deal_status_pass_timeout = 4  --这个是币商没有及时放行，然后币会冻结2个小时。两个小时内，币商都可以点击行，如果币商还是没有处理，那么币会自动划转过去

g_marketDefine.deal_status_timeout_frozen = 5  --因为币商没有点击放行，该交易被冻结。

g_marketDefine.deal_status_timeout_finish = 6  --该订单是超时完成的。

g_marketDefine.deal_status_finish = 7  --订单已经完成，这个完成是人工点击完成的

g_marketDefine.deal_status_cancel = 8  --订单被取消了。只有在状态1的情况下，才可能被取消

g_marketDefine.deal_status_appeal = 9  --申诉中，在冻结的状态下，可以进行申诉

g_marketDefine.deal_status_reply_appeal = 10  -- 回复申诉中

g_marketDefine.deal_status_appeal_finish = 11  --申诉完成

g_marketDefine.deal_status_failed = 12    --冻结的钱不足，交易失败了

g_marketDefine.deal_status_unreviewed = 13    --审核中
g_marketDefine.deal_status_matching = 14    	--匹配中
g_marketDefine.deal_status_audit_failure = 15    --审核不通过
g_marketDefine.deal_status_appeal_cancel = 16  --申诉取消


g_marketDefine.deal_from_common = 1    --普通订单
g_marketDefine.deal_from_trdpaid = 2    --三方订单
g_marketDefine.deal_from_shop_draw = 3    --商户提现


g_marketDefine.max_deal_num = 5     --每天最大的取消交易次数


g_marketDefine.chain_recd_unprocess = 0   --未处理
g_marketDefine.chain_recd_process = 1     --已经处理
g_marketDefine.chain_recd_fail = 2        --失败

g_marketDefine.pre_address_status_get = 0   --从结点服务器取回来
g_marketDefine.pre_address_status_use = 1   --登入端时间使用
g_marketDefine.pre_address_status_upload = 2   --向节点端上报对应的数据
g_marketDefine.pre_address_status_failed = 3   --上报失败
g_marketDefine.pre_address_status_finish = 4   --上报完成


g_marketDefine.max_pre_address_len = 5


g_marketDefine.coin_usdt_erc20 = {100, "ERC_USDT"}


g_marketDefine.deal_type = {}
g_marketDefine.deal_type['otc_buy'] = 101
g_marketDefine.deal_type['otc_sell'] = 102

g_marketDefine.deal_type['shop_customer_buy'] = 201    --商户用户买币,相当于用户充值
g_marketDefine.deal_type['shop_customer_sell'] = 202    --商户用户卖币,相当于用户提现

g_marketDefine.deal_type['shop_buy'] = 201    --商户买币，很少用到
g_marketDefine.deal_type['shop_sell'] = 202    --商户卖币,相当于商户下发
g_marketDefine.order_time_out = 600				--超时时间，秒		
g_marketDefine.cion_pay_order_time_out = 1800	--超时时间，秒		
g_marketDefine.order_time_rest = 1200			--超时时间，秒		


g_marketDefine.currency_type = {}
g_marketDefine.currency_type.USDT = 2003

g_marketDefine.addr_type_list = {101, 102}

g_marketDefine.addr_name_list = {}
g_marketDefine.addr_name_list[101] = "ERC-20"
g_marketDefine.addr_name_list[102] = "TRC-20"

g_marketDefine.block_chain_fee_multiple = 1000000000
