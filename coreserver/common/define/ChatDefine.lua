g_chatDefine = {}

g_chatDefine.chat_type = {}    --手机号登入

g_chatDefine.chat_type['sys_pay_info'] = 100  --默认的系统支付消息，在每个聊天之前创建
g_chatDefine.chat_type['sys_manager'] = 200   --系统发送的消息
g_chatDefine.chat_type['sys_center'] = 300   --客服发送的消息
g_chatDefine.chat_type['customer_to_merchant'] = 400   --客户发给币商
g_chatDefine.chat_type['merchant_to_customer'] = 500   --币商发给客户


g_chatDefine.time_out = {}
g_chatDefine.time_out['start_pay'] = 600