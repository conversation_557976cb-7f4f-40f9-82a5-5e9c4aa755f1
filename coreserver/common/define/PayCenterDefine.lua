g_payCenterDefine = {}
g_payCenterDefine.retuen_code = {}    --手机号登入

g_payCenterDefine.retuen_code['data_is_nill'] = {1001,"请传入正确参数"}
g_payCenterDefine.retuen_code['shop_merchant_is_null'] = {1002,"商户为空"}
g_payCenterDefine.retuen_code['shop_merchant_not_exist'] = {1003,"商户不存在"}
g_payCenterDefine.retuen_code['sign_error'] = {1004,"签名错误"}

g_payCenterDefine.pay_id_list = {} 
g_payCenterDefine.pay_id_list['zfb'] = 8101    --支付宝原生
g_payCenterDefine.pay_id_list['zfbsm'] = 8102   --支付宝扫码
g_payCenterDefine.pay_id_list['zfbh5'] = 8103   --支付宝H5

g_payCenterDefine.pay_id_list['qyzfbapp'] = 8111   --企业支付宝app
g_payCenterDefine.pay_id_list['qyzfbh5'] = 8112   --企业支付宝H5
g_payCenterDefine.pay_id_list['qyzfbhb'] = 8113   --企业支付宝红包


g_payCenterDefine.pay_id_list['yhk'] = 8201   --银行卡转卡
g_payCenterDefine.pay_id_list['ysf'] = 8202   --云闪付
g_payCenterDefine.pay_id_list['zfb2card'] = 8203   --支付宝转银行
g_payCenterDefine.pay_id_list['zfb2card'] = 8204   --微信转转银行

g_payCenterDefine.pay_id_list['wechat'] = 8301   --微信支付
g_payCenterDefine.pay_id_list['wechath5'] = 8302   --微信H5

g_payCenterDefine.pay_id_list['bitpay'] = 8401   --币支付的类型

