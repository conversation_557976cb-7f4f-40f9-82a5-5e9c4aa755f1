-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local st_human_pb = require("st_human_pb")
module('msg_human2_pb')


local CGTRANSFERENERGYVALUE = protobuf.Descriptor();
local CGTRANSFERENERGYVALUE_USERID_FIELD = protobuf.FieldDescriptor();
local CGTRANSFERENERGYVALUE_ENERGYVALUE_FIELD = protobuf.FieldDescriptor();
local CGTRANSFERENERGYVALUE_TOUSERID_FIELD = protobuf.FieldDescriptor();
local CGTRANSFERENERGYVALUE_FUNDPWD_FIELD = protobuf.FieldDescriptor();
local GCTRANSFERENERGYVALUE = protobuf.Descriptor();
local GCTRANSFERENERGYVALUE_RESULT_FIELD = protobuf.FieldDescriptor();
local GCTRANSFERENERGYVALUE_MSG_FIELD = protobuf.FieldDescriptor();

CGTRANSFERENERGYVALUE_USERID_FIELD.name = "userid"
CGTRANSFERENERGYVALUE_USERID_FIELD.full_name = ".prootc.cgtransferenergyvalue.userid"
CGTRANSFERENERGYVALUE_USERID_FIELD.number = 1
CGTRANSFERENERGYVALUE_USERID_FIELD.index = 0
CGTRANSFERENERGYVALUE_USERID_FIELD.label = 1
CGTRANSFERENERGYVALUE_USERID_FIELD.has_default_value = false
CGTRANSFERENERGYVALUE_USERID_FIELD.default_value = 0
CGTRANSFERENERGYVALUE_USERID_FIELD.type = 5
CGTRANSFERENERGYVALUE_USERID_FIELD.cpp_type = 1

CGTRANSFERENERGYVALUE_ENERGYVALUE_FIELD.name = "energyvalue"
CGTRANSFERENERGYVALUE_ENERGYVALUE_FIELD.full_name = ".prootc.cgtransferenergyvalue.energyvalue"
CGTRANSFERENERGYVALUE_ENERGYVALUE_FIELD.number = 2
CGTRANSFERENERGYVALUE_ENERGYVALUE_FIELD.index = 1
CGTRANSFERENERGYVALUE_ENERGYVALUE_FIELD.label = 1
CGTRANSFERENERGYVALUE_ENERGYVALUE_FIELD.has_default_value = false
CGTRANSFERENERGYVALUE_ENERGYVALUE_FIELD.default_value = ""
CGTRANSFERENERGYVALUE_ENERGYVALUE_FIELD.type = 9
CGTRANSFERENERGYVALUE_ENERGYVALUE_FIELD.cpp_type = 9

CGTRANSFERENERGYVALUE_TOUSERID_FIELD.name = "touserid"
CGTRANSFERENERGYVALUE_TOUSERID_FIELD.full_name = ".prootc.cgtransferenergyvalue.touserid"
CGTRANSFERENERGYVALUE_TOUSERID_FIELD.number = 3
CGTRANSFERENERGYVALUE_TOUSERID_FIELD.index = 2
CGTRANSFERENERGYVALUE_TOUSERID_FIELD.label = 1
CGTRANSFERENERGYVALUE_TOUSERID_FIELD.has_default_value = false
CGTRANSFERENERGYVALUE_TOUSERID_FIELD.default_value = 0
CGTRANSFERENERGYVALUE_TOUSERID_FIELD.type = 5
CGTRANSFERENERGYVALUE_TOUSERID_FIELD.cpp_type = 1

CGTRANSFERENERGYVALUE_FUNDPWD_FIELD.name = "fundpwd"
CGTRANSFERENERGYVALUE_FUNDPWD_FIELD.full_name = ".prootc.cgtransferenergyvalue.fundpwd"
CGTRANSFERENERGYVALUE_FUNDPWD_FIELD.number = 4
CGTRANSFERENERGYVALUE_FUNDPWD_FIELD.index = 3
CGTRANSFERENERGYVALUE_FUNDPWD_FIELD.label = 1
CGTRANSFERENERGYVALUE_FUNDPWD_FIELD.has_default_value = false
CGTRANSFERENERGYVALUE_FUNDPWD_FIELD.default_value = ""
CGTRANSFERENERGYVALUE_FUNDPWD_FIELD.type = 9
CGTRANSFERENERGYVALUE_FUNDPWD_FIELD.cpp_type = 9

CGTRANSFERENERGYVALUE.name = "cgtransferenergyvalue"
CGTRANSFERENERGYVALUE.full_name = ".prootc.cgtransferenergyvalue"
CGTRANSFERENERGYVALUE.nested_types = {}
CGTRANSFERENERGYVALUE.enum_types = {}
CGTRANSFERENERGYVALUE.fields = {CGTRANSFERENERGYVALUE_USERID_FIELD, CGTRANSFERENERGYVALUE_ENERGYVALUE_FIELD, CGTRANSFERENERGYVALUE_TOUSERID_FIELD, CGTRANSFERENERGYVALUE_FUNDPWD_FIELD}
CGTRANSFERENERGYVALUE.is_extendable = false
CGTRANSFERENERGYVALUE.extensions = {}
GCTRANSFERENERGYVALUE_RESULT_FIELD.name = "result"
GCTRANSFERENERGYVALUE_RESULT_FIELD.full_name = ".prootc.gctransferenergyvalue.result"
GCTRANSFERENERGYVALUE_RESULT_FIELD.number = 1
GCTRANSFERENERGYVALUE_RESULT_FIELD.index = 0
GCTRANSFERENERGYVALUE_RESULT_FIELD.label = 1
GCTRANSFERENERGYVALUE_RESULT_FIELD.has_default_value = false
GCTRANSFERENERGYVALUE_RESULT_FIELD.default_value = 0
GCTRANSFERENERGYVALUE_RESULT_FIELD.type = 5
GCTRANSFERENERGYVALUE_RESULT_FIELD.cpp_type = 1

GCTRANSFERENERGYVALUE_MSG_FIELD.name = "msg"
GCTRANSFERENERGYVALUE_MSG_FIELD.full_name = ".prootc.gctransferenergyvalue.msg"
GCTRANSFERENERGYVALUE_MSG_FIELD.number = 2
GCTRANSFERENERGYVALUE_MSG_FIELD.index = 1
GCTRANSFERENERGYVALUE_MSG_FIELD.label = 1
GCTRANSFERENERGYVALUE_MSG_FIELD.has_default_value = false
GCTRANSFERENERGYVALUE_MSG_FIELD.default_value = ""
GCTRANSFERENERGYVALUE_MSG_FIELD.type = 9
GCTRANSFERENERGYVALUE_MSG_FIELD.cpp_type = 9

GCTRANSFERENERGYVALUE.name = "gctransferenergyvalue"
GCTRANSFERENERGYVALUE.full_name = ".prootc.gctransferenergyvalue"
GCTRANSFERENERGYVALUE.nested_types = {}
GCTRANSFERENERGYVALUE.enum_types = {}
GCTRANSFERENERGYVALUE.fields = {GCTRANSFERENERGYVALUE_RESULT_FIELD, GCTRANSFERENERGYVALUE_MSG_FIELD}
GCTRANSFERENERGYVALUE.is_extendable = false
GCTRANSFERENERGYVALUE.extensions = {}

cgtransferenergyvalue = protobuf.Message(CGTRANSFERENERGYVALUE)
gctransferenergyvalue = protobuf.Message(GCTRANSFERENERGYVALUE)

----------nimol modify---------
MSG_HUMAN2_PB_CGTRANSFERENERGYVALUE = CGTRANSFERENERGYVALUE
MSG_HUMAN2_PB_GCTRANSFERENERGYVALUE = GCTRANSFERENERGYVALUE
