-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
module('st_agent_pb')


local AGENTINFO = protobuf.Descriptor();
local AGENTINFO_USERID_FIELD = protobuf.FieldDescriptor();
local AGENTINFO_STATE_FIELD = protobuf.FieldDescriptor();
local AGENTINFO_LEVEL_FIELD = protobuf.FieldDescriptor();
local AGENTINFO_SOLEAGENT_FIELD = protobuf.FieldDescriptor();
local AGENTINFO_AGENT_FIELD = protobuf.FieldDescriptor();
local AGENTINFO_GETRATE_FIELD = protobuf.FieldDescriptor();
local AGENTINFO_CHANNEL_FIELD = protobuf.FieldDescriptor();
local AGENTINFO_AGENTTYPE_FIELD = protobuf.FieldDescriptor();

AGENTINFO_USERID_FIELD.name = "userid"
AGENTINFO_USERID_FIELD.full_name = ".prootc.agentinfo.userid"
AGENTINFO_USERID_FIELD.number = 1
AGENTINFO_USERID_FIELD.index = 0
AGENTINFO_USERID_FIELD.label = 1
AGENTINFO_USERID_FIELD.has_default_value = false
AGENTINFO_USERID_FIELD.default_value = 0
AGENTINFO_USERID_FIELD.type = 5
AGENTINFO_USERID_FIELD.cpp_type = 1

AGENTINFO_STATE_FIELD.name = "state"
AGENTINFO_STATE_FIELD.full_name = ".prootc.agentinfo.state"
AGENTINFO_STATE_FIELD.number = 2
AGENTINFO_STATE_FIELD.index = 1
AGENTINFO_STATE_FIELD.label = 1
AGENTINFO_STATE_FIELD.has_default_value = false
AGENTINFO_STATE_FIELD.default_value = 0
AGENTINFO_STATE_FIELD.type = 5
AGENTINFO_STATE_FIELD.cpp_type = 1

AGENTINFO_LEVEL_FIELD.name = "level"
AGENTINFO_LEVEL_FIELD.full_name = ".prootc.agentinfo.level"
AGENTINFO_LEVEL_FIELD.number = 3
AGENTINFO_LEVEL_FIELD.index = 2
AGENTINFO_LEVEL_FIELD.label = 1
AGENTINFO_LEVEL_FIELD.has_default_value = false
AGENTINFO_LEVEL_FIELD.default_value = 0
AGENTINFO_LEVEL_FIELD.type = 5
AGENTINFO_LEVEL_FIELD.cpp_type = 1

AGENTINFO_SOLEAGENT_FIELD.name = "soleagent"
AGENTINFO_SOLEAGENT_FIELD.full_name = ".prootc.agentinfo.soleagent"
AGENTINFO_SOLEAGENT_FIELD.number = 4
AGENTINFO_SOLEAGENT_FIELD.index = 3
AGENTINFO_SOLEAGENT_FIELD.label = 1
AGENTINFO_SOLEAGENT_FIELD.has_default_value = false
AGENTINFO_SOLEAGENT_FIELD.default_value = 0
AGENTINFO_SOLEAGENT_FIELD.type = 5
AGENTINFO_SOLEAGENT_FIELD.cpp_type = 1

AGENTINFO_AGENT_FIELD.name = "agent"
AGENTINFO_AGENT_FIELD.full_name = ".prootc.agentinfo.agent"
AGENTINFO_AGENT_FIELD.number = 5
AGENTINFO_AGENT_FIELD.index = 4
AGENTINFO_AGENT_FIELD.label = 1
AGENTINFO_AGENT_FIELD.has_default_value = false
AGENTINFO_AGENT_FIELD.default_value = 0
AGENTINFO_AGENT_FIELD.type = 5
AGENTINFO_AGENT_FIELD.cpp_type = 1

AGENTINFO_GETRATE_FIELD.name = "getrate"
AGENTINFO_GETRATE_FIELD.full_name = ".prootc.agentinfo.getrate"
AGENTINFO_GETRATE_FIELD.number = 6
AGENTINFO_GETRATE_FIELD.index = 5
AGENTINFO_GETRATE_FIELD.label = 1
AGENTINFO_GETRATE_FIELD.has_default_value = false
AGENTINFO_GETRATE_FIELD.default_value = 0
AGENTINFO_GETRATE_FIELD.type = 5
AGENTINFO_GETRATE_FIELD.cpp_type = 1

AGENTINFO_CHANNEL_FIELD.name = "channel"
AGENTINFO_CHANNEL_FIELD.full_name = ".prootc.agentinfo.channel"
AGENTINFO_CHANNEL_FIELD.number = 7
AGENTINFO_CHANNEL_FIELD.index = 6
AGENTINFO_CHANNEL_FIELD.label = 1
AGENTINFO_CHANNEL_FIELD.has_default_value = false
AGENTINFO_CHANNEL_FIELD.default_value = 0
AGENTINFO_CHANNEL_FIELD.type = 5
AGENTINFO_CHANNEL_FIELD.cpp_type = 1

AGENTINFO_AGENTTYPE_FIELD.name = "agenttype"
AGENTINFO_AGENTTYPE_FIELD.full_name = ".prootc.agentinfo.agenttype"
AGENTINFO_AGENTTYPE_FIELD.number = 8
AGENTINFO_AGENTTYPE_FIELD.index = 7
AGENTINFO_AGENTTYPE_FIELD.label = 1
AGENTINFO_AGENTTYPE_FIELD.has_default_value = false
AGENTINFO_AGENTTYPE_FIELD.default_value = 0
AGENTINFO_AGENTTYPE_FIELD.type = 5
AGENTINFO_AGENTTYPE_FIELD.cpp_type = 1

AGENTINFO.name = "agentinfo"
AGENTINFO.full_name = ".prootc.agentinfo"
AGENTINFO.nested_types = {}
AGENTINFO.enum_types = {}
AGENTINFO.fields = {AGENTINFO_USERID_FIELD, AGENTINFO_STATE_FIELD, AGENTINFO_LEVEL_FIELD, AGENTINFO_SOLEAGENT_FIELD, AGENTINFO_AGENT_FIELD, AGENTINFO_GETRATE_FIELD, AGENTINFO_CHANNEL_FIELD, AGENTINFO_AGENTTYPE_FIELD}
AGENTINFO.is_extendable = false
AGENTINFO.extensions = {}

agentinfo = protobuf.Message(AGENTINFO)

----------nimol modify---------
ST_AGENT_PB_AGENTINFO = AGENTINFO
