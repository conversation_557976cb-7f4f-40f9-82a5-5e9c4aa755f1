package prootc;


message vendororderinfo   //绑定后属于我的代理的信息
{
	optional int32 userid = 1;
	optional int32 hangid = 2;    				//挂单ID
	optional int32 type = 3;       				//挂单类型：0是买单：1是卖单
	optional int32 pricetype = 4;    			//价格类型：0是普通价格，1是根据USDT市场价格来定
	optional string price = 5;         			//价格类型
	optional string amount = 6;        			//数量
	optional string minmoney = 7;      			//最小的金额
	optional string maxmoney = 8;      			//最大的金额
	optional int32 autoswitch = 9;    			//0是非自动挂单，1是自动挂单
	optional string message = 10;    			//挂单遗留的消息
	optional string maxamount = 11;   			//交易的总数量
	optional int32 enablestatus = 12;   		//状态：0，失效，1是可用
	optional string channel = 13;     	 		//渠道标识
	optional string nickname = 14;     			//昵称
	optional int32 unsoldordernum = 15;			//未成交订单数量
	optional int32 dealordernum = 16;			//成交订单数量
	optional string unsoldorderamount = 18;		//未成交订单币数
	optional string dealorderamount = 19;		//成交订单币数
	repeated int32 paylist = 20;				//支持的支付类型列表
	optional int32 cancelnum = 21;				//取消订单数量
	optional int32 usertype = 22;				//100=平台用户，200=币商，300=商户
	optional string payeeaccount = 23;			//收款人账号
	optional string payeename = 24;				//收款人账号
	optional string payeebank = 25;				//收款银行
	optional string payeebandaddr = 26;			//收款银行地址
	optional string deal_order_free = 27;		//已付的手续费
	optional string free = 28;					//应付总手续费
	repeated string feeRate = 29;				//手续费率
	optional string addfeerate = 30;			//追加手续费
	optional string createtime = 31;			//创建时间
	optional int32 channeldeal = 32;			//交易类型：0：公开，1:私有
	optional int32 dealmodel = 33;				//交易模式 0 可多次交易 1 一次交易完成
	optional string predictmoney = 34;			//预计到账
	optional string resttime = 35;				//一笔吃挂单取消后需要一个休息时间，才可以重新上架
	optional string dealmonery = 36;			//挂自动购买时 购买人民币数量
	optional string dealcount = 37;				//挂自动购买时， 已派的订单数量
	optional int32 withdrawtype = 38;			//提现类型 0 otc 1 代付
	optional int32 coinid = 39;					//币种ID
	optional string coinname = 40;				//币种名字
	optional int32 isexternal = 41;				//是否外部渠道 0-不是 1-是
	optional string unsoldordermoney = 42;		//未成交订单法币数量
	optional string dealordermoney = 43;		//成交订单法币数量
	optional int32 externalorderuserid = 44;	//外部订单派给拿给用户
	optional int32 isassigned = 46;				//是否已重交易所拿到订单
	optional int32 istake = 47;					//是否接单 0 接单 1 不接单
	optional int32 wallettype = 48;				//钱包类型 0 基础钱包 1 币付钱包
}


message customerorderinfo      //每产生一笔交易，在订单表中，就会有一个记录
{
	optional int32 dealid = 1;   //交易ID
	optional int32 type = 2;    //交易类型
	optional int32 vendororderid  = 3;    //币商挂单ID
	optional int32 customeruserid = 4;    //顾客的userid
	optional int32 vendoruserid = 5;     //币商ID
	optional string price = 6;   
	optional string amount = 7;
	optional string money = 8;  //成交金额
	optional int32 status = 9;   //状态
	optional string merchantorderid = 10;    //所关联的商户的定单ID
	optional string feerate = 11;    //费率
	optional string fee = 12;   //手续费
	repeated string proofurl = 13;   //付款证明
	optional string paytypelist = 14;   //支付方式,在钱包以及钱包的交易中，如果卖家提供了两个支付方式，那么就应该把两个支付方式都显示出来
	optional string payidlist = 15;    //对应支付表中配置的ID
	optional string createtime = 16;   //创建时间
	optional string paytime = 17;   //支付时间
	optional string passtime = 18;   //放行时间
	optional string channel = 19;    //渠道号
	optional string publicprice = 20;    //交易所中提供的价格类型
	optional int32 fromtype = 21;     //订单的来源类型，1：普通订单，2：三方订单，3：商户提币订单
	optional string customerusernickname = 22;    //顾客的昵称
	optional string vendorusernickname = 23;     //币商的昵称
	optional string notifyurl = 24;     //商户客户下的订单的时候，传入的通知回调
	optional string body = 25;     //商户客户的订单，给商户返回的参数
	optional string sellfeerate = 26;     //卖方费率，有需要，在商户卖币提现的时候，需要收取卖方费率
	optional string sellfee = 27;     	//费率总额
	optional string canceltime = 28;     //取消时间
	optional string buyfeerate = 29;     //买房费率
	optional string buyfee = 30;     	//费率总额
	optional string updatetime = 31;     	//更新时间
	optional int32 dealtype = 32;     	//100=币商交易，200=商户用户，300=商户自己
	optional string getamount = 33;     	//到账金额
	optional string income = 34;     	//收益
	optional int32 withdrawtype = 35;		//提现类型 0 otc 1 代付
	optional int32 coinid = 36;					//币种ID
	optional string coinname = 37;				//币种名字
	optional int32 isexternal = 38;				//是否外部渠道 0-不是 1-是
	optional string aftermoney = 39;			//法币到账数量
	optional string feemoney = 40;				//法币手续费
	optional string tradeid = 41;				//交易所订单ID
	optional int32 iswait = 43;					//等待释放的订单
	optional int32 chaintype = 44;				//链类型
	optional string chainname = 45;				//链名称
	optional string chainaddr = 46;				//链地址
	optional int32 owership = 47;				//0-系统地址 1-商户自备地址
	optional int32 wallettype = 48;				//钱包类型 0 基础钱包 1 币付钱包
}


message apppealinfo      //申诉的详情
{
	optional int32 appealid = 1;   			//申诉ID
	optional int32 fromuserid = 2;    		//申诉人userId
	optional string fromusernickname = 3;   //申诉人昵称
	optional int32 touserid  = 4;    		//被申诉人userId
	optional string tousernickname = 5;    	//被申诉人昵称
	optional int32 orderid = 6;    			//相关联的订单id
	optional string reason = 7;     		//申诉理由
	optional string description = 8;     	//详细描述
	optional string proofurl = 9;   		//相关凭证url
	optional string replyreason = 10;		//回复理由
	optional string replydescription = 11;	//回复详细描述
	optional string replyproofurl = 12;		//回复相关凭证
	optional string replytime = 13;			//回复时间
	optional string sysremark = 14;  		//客服备注
	optional int32 status = 15;   			//状态(0-新建,1-关闭,2-客服取消,3-客服放行)
	optional int32 needaudit = 16;    		//需要审核 (0-不需要, 1-需要) 默认0
	optional string createtime = 17;    	//创建时间
}

message currencyrecordinfo      //币币交易详情
{
	optional int32 id = 1;   				//id
	optional string txid = 2;    			//交易id
	optional int32 txtype = 3;   			//交易类型(1-转入/2-转出)
	optional string chainid  = 4;    		//所属公链
	optional string txdata = 5;    			//交易信息
	optional string fromaddr = 6;    		//发起地址
	optional string toaddr = 7;     		//目标地址
	optional string amount = 8;     		//数量
	optional string txfee = 9;   			//交易手续费
	optional int32 txstatus = 10;			//交易状态
	optional string txtime = 11;			//交易时间
	optional string blockhash = 12;			//区块hash
	optional int32 recdstatus = 13;			//记录状态 101-提币中 102-审核中 103-已驳回 104-出款中 105-成功
	optional string remark = 14;  			//备注
	optional string createtime = 15;   		//创建时间
	optional int32 specifictype = 16;   	//详细类型 1-充币 2-提币 3-转入 4-转账
	optional int32 coinid = 17;   			//币种ID
	optional string coinname = 18;   		//币种名字
	optional int32 addrtype = 19;   		//地址类型
	optional string addrname = 20;   		//地址名字
}


message incomeexpendituredetail
{
	optional int32 detailtype = 1;			//0 全部 1 购买 2出售 3充值 4 提现 5提币 6充币 7转入 8转出 9收益 10 手续费 11系统补款 12系统扣款 
	optional string amount = 2; 			//交易数量
	optional string afteramount = 3;		//余额
	optional string createtime = 4;  		//创建时间
}