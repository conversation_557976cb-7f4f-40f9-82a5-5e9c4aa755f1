package prootc;
import "st_human.proto";

//手机号注册
message cgregister 
{
	optional string nickname = 1;			 	//昵称
	optional string phonenum = 2;     			//电话号码
	optional string password = 3;				//密码
	optional string authcode = 5;        		//如果是验证码登录，需要填入验证
	optional string channel = 6;      			//渠道号
	optional string invitecode = 7;				//邀请码
}

message gcregister
{
	optional int32 result = 1;
	optional string msg = 2;				//当result 不等0时 这里赋值有提示语
}


//登陆
message cglogin
{
	optional string phonenum = 1;     			//电话号码
	optional string password = 2;				//密码
	optional string channel = 4;      			//渠道号
	optional int32 bindtype = 5;				//登陆类型: 1 手机登陆
}
message gclogin
{
	optional int32 result = 1;
	optional string msg = 2;					//当result 不等0时 这里赋值有提示语
	optional userinfo uinfo = 3;
	optional int32 systime = 4;   				//服务器时间戳，用于客户端校准
	repeated coininfo coinlist = 5;				//币数量		 		 
	repeated coinaddr addrlist = 6;				//链数组		 		 
	repeated coinaddr trusteeshipaddresslist = 7;//冷钱包地址		 		 
}

//踢出玩家
message gckituser
{
	optional int32 result = 1;
	optional string msg = 2;
	optional int32 kittype = 3;   				//1是账号在别处登录了。
	optional string kitmsg = 4;   				
	
}

//忘记登录密码
message cgforgetloginpwd
{
	optional string phonenum = 1;				//手机号
	optional string authcode = 2; 				//验证码	
	optional string newpwd = 3; 				//新密码
	optional string channel = 4;				//渠道号

}
message gcforgetloginpwd
{
	optional int32 result = 1;
	optional string msg = 2;					//当result 不等0时 这里赋值有提示语
}


//修改登录密码
message cgupdateloginpwd
{
	optional int32 userid = 1;					//玩家ID
	optional string oldpwd = 2;					//旧密码
	optional string authcode = 3; 				//验证码
	optional string newpwd = 4; 				//新密码
	
}
message gcupdateloginpwd
{
	optional int32 result = 1;
	optional string msg = 2;					//当result 不等0时 这里赋值有提示语
}


//设置资金密码
message cgsetfundpwd
{
	optional int32 userid = 1;					//玩家ID
	optional string authcode = 2; 				//验证码
	optional string pwd = 3; 					//密码
}
message gcsetfundpwd
{
	optional int32 result = 1;					
	optional string msg = 2; 				
}

//忘记资金密码
message cgforgetfundpwd
{
	optional int32 userid = 1;					//玩家ID
	optional string authcode = 2; 				//验证码
	optional string oldpwd = 3; 				//密码
	optional string newpwd = 4; 				//密码
}
message gcforgetfundpwd
{
	optional int32 result = 1;					
	optional string msg = 2; 	
}

//修改昵称
message cgupdatenickname
{
	optional int32 userid = 1;					//玩家ID
	optional string nickname = 2;				//昵称
}
message gcupdatenickname
{
	optional int32 result = 1;					
	optional string msg = 2; 	
}

//添加收款方式
message cgaddpay
{
	optional int32 userid = 1;					//玩家ID
	optional string account = 2;				//账号
	optional string payee = 3;					//收款人
	optional string qrcode = 4;					//收款码
	optional string bankname = 5;				//银行名字
	optional string bankaddr = 6;				//银行地址
	optional string singlelimit = 7;			//单笔限额
	optional string daylimit = 8;				//单日限额
	optional int32 paytype = 9;					//支付类型
}
message gcaddpay
{
	optional int32 result = 1;					
	optional string msg = 2; 
}

//修改收款方式限额
message cgmodifypay
{
	optional int32 userid = 1;					//玩家ID
	optional int32 id = 2;						//支付编号
	optional int32 paytype = 3;					//支付类型
	optional string singlelimit = 4;			//单笔限额
	optional string daylimit = 5;				//最大限额
}
message gcmodifypay
{
	optional int32 result = 1;					
	optional string msg = 2;
}

//删除收款方式
message cgdeletepay
{
	optional int32 userid = 1;					//玩家ID
	optional int32 id = 2;						//支付编号
	optional int32 paytype = 3;					//支付类型
}
message gcdeletepay
{
	optional int32 result = 1;					
	optional string msg = 2;
}

//失效/生效收款方式
message cgpaystatus
{
	optional int32 userid = 1;					//玩家ID
	optional int32 id = 2;						//支付编号
	optional int32 paytype = 3;					//支付类型
}
message gcpaystatus
{
	optional int32 result = 1;					
	optional string msg = 2;
}

//我的收款方式
message cgpaylist
{
	optional int32 userid = 1;					//玩家ID
}
message gcpaylist
{
	optional int32 result = 1;					
	optional string msg = 2;
	repeated payinfo paylist = 3;				//支付信息列表
}

//心跳包
message cgheartbeat
{
	optional int32 userid = 1;
}
message gcheartbeat
{
	optional int32 result = 1;
	optional string msg = 2;
}

//重连
message cgreconnect
{
	optional int32 userid = 1;
}
message gcreconnect
{
	optional int32 result = 1;
	optional string msg = 2;
	optional userinfo uinfo = 3;
	optional int32 systime = 4;   				//服务器时间戳，用于客户端校准
}

//更新用户信息
message gcupdateuserinfo
{
	optional int32 result = 1;
	optional string msg = 2;
	repeated string typelist = 3;
	repeated string valuelist = 4;
}


//我的业绩
message cgmyperformance
{
	optional int32 userid = 1;
	optional int32 pagenum = 2;
	optional int32 pagesize = 3;
	optional int32 paytype = 4;						//支付类型
	optional int32 timetype = 5;					//0 今天	1 昨天
	optional int32 coinid = 6;						//币种ID
}
message gcmyperformance
{
	optional int32 result = 1;
	optional string msg = 2;
	optional int32 userid = 3;
	optional int32 pagenum = 4;
	optional int32 pagesize = 5;
	optional string todayincome = 6;				//今日收益
	optional string yesterdayincome = 7;			//昨日收益
	optional string performancecount = 8;			//业绩总汇
	optional string incomecount = 9;				//收益总汇
	repeated string paytypelist = 10;				//支付方式列表
	repeated string payratelist = 11;				//出售费率
	repeated subinfo infolist = 12;				
	optional int32 paytype = 13;					////支付类型
	optional int32 timetype = 14;					//0 今天	1 昨天
	repeated string buyratelist = 15;				//购买费率
	repeated string behalfbuyratelist = 16;			//代付费率
	optional int32 coinid = 17;						//币种ID
}

//设置直属下级
message cgsetsubinfo
{
	optional int32 userid = 1;
	optional int32 targetuserid = 2;				
	optional int32 isacceptorder = 3;			//是否允许接单 0 允许1 禁止
	optional int32 prohibitlogin = 4;			//禁止登陆 0 允许登陆 1 禁止登陆
	repeated string paytypelist = 5;			//支付方式
	repeated string payratelist = 6;			//出售费率
	repeated string buyratelist = 7;			//购买费率
	repeated string behalfbuyratelist = 8;		//代付费率
	
}
message gcsetsubinfo
{
	optional int32 result = 1;
	optional string msg = 2;
	optional int32 userid = 3;
	optional int32 targetuserid = 4;				
	optional int32 isacceptorder = 5;			//是否允许接单 0 允许1 禁止
	optional int32 prohibitlogin = 6;			//禁止登陆 0 允许登陆 1 禁止登陆
	repeated string paytypelist = 7;			//支付方式
	repeated string payratelist = 8;			//出售费率
	repeated string buyratelist = 9;			//购买费率
	repeated string behalfbuyratelist = 10;		//代付费率
}

//检查资金密码
message cgcheckfundpwd
{
	optional int32 userid = 1;
	optional string fundpwd = 2;
}
message gccheckfundpwd
{
	optional int32 result = 1;
	optional string msg = 2;
}

//更新币信息
message gcupdatecoininfo
{
	optional int32 result = 1;
	optional string msg = 2;
	repeated coininfo coinlist = 3;
}

//查询用户信息
message cggetuserinfo
{
	optional int32 userid = 1;
}
message gcgetuserinfo
{
	optional int32 result = 1;
	optional string msg = 2;
	optional userinfo uinfo = 3;						//用户信息
	repeated coinaddr addrlist = 4;					//链数组	
}



