package prootc;
import "st_notice.proto";

//公告列表
message cgnoticelist 
{
	optional int32 userid = 1; 				//玩家ID
	optional int32 pagenum = 2; 			//页码
	optional int32 pagesize = 3; 			//一页多少条
}
message gcnoticelist 
{
	optional int32 result = 1;
	optional string msg = 2;				//当result 不等0时 这里赋值有提示语
	optional int32 pagenum = 3; 			//页码
	optional int32 pagesize = 4; 			//一页多少条
	repeated noticeinfo noticelist = 5;		//广播列表
	
}

//公告详情
message cgnoticedetail 
{
	optional int32 userid = 1; 				//玩家ID
	optional int32 id = 2; 					//公告ID
}
message gcnoticedetail 
{
	optional int32 result = 1;
	optional string msg = 2;				//当result 不等0时 这里赋值有提示语
}

//消息通知列表
message cgnoticemessagelist
{
	optional int32 userid = 1; 				//玩家ID
	optional int32 pagenum = 2; 			//页码
	optional int32 pagesize = 3; 			//一页多少条
}
message gcnoticemessagelist
{
	optional int32 result = 1;
	optional string msg = 2;				//当result 不等0时 这里赋值有提示语
	optional int32 pagenum = 3; 			//页码
	optional int32 pagesize = 4; 			//一页多少条
	repeated messageinfo messagelist = 5;		//广播列表
	
}

//阅读消息
message cgnoticemessageread
{
	optional int32 userid = 1; 				//玩家ID
	optional int32 id = 2; 					//消息ID
}
message gcnoticemessageread
{
	optional int32 result = 1;
	optional string msg = 2;				//当result 不等0时 这里赋值有提示语
}

//删除消息
message cgnoticemessagedelete
{
	optional int32 userid = 1; 				//玩家ID
	optional int32 id = 2; 					//消息ID
}
message gcnoticemessagedelete
{
	optional int32 result = 1;
	optional string msg = 2;				//当result 不等0时 这里赋值有提示语
}