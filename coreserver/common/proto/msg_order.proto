package prootc;
import "st_human.proto";
import "st_order.proto";

//挂单
message cghangorder   
{
	optional int32 userid = 1;
	optional int32 type = 2;          //挂单类型：0是买单：1是卖单
	optional int32 pricetype = 3;     //价格类型：0是普通价格，1是根据USDT市场价格来定
	optional string price = 4;         //价格类型
	optional string amount = 5;        //数量
	optional string minmoney = 6;      //最小的金额
	optional string maxmoney = 7;      //最大的金额
	optional int32 autoswitch = 8;    //0是非自动挂单，1是自动挂单
	optional string message = 9;       //挂单遗留的消息
	optional string fundpwd = 10;       //资金密码
	optional int32 coinid = 11;			//币种ID
}
message gchangorder   //注意，人物自己的信息和获得的物品通过其他的协议通知玩家,这里只返回购买的结果
{
	optional int32 result = 1;    //返回的通知
	optional string msg = 2;
	optional vendororderinfo voInfo = 3;
}

//修改挂单
message cgmodifyorder
{
	optional int32 userid = 1;
	optional int32 hangid = 2;    //订单ID
	optional int32 type = 3;       //挂单类型：0是买单：1是卖单
	optional int32 pricetype = 4;    //价格类型：0是普通价格，1是根据USDT市场价格来定
	optional string price = 5;         //价格类型
	optional string amount = 6;        //数量
	optional string minmoney = 7;      //最小的金额
	optional string maxmoney = 8;      //最大的金额
	optional int32 autoswitch = 9;    //0是非自动挂单，1是自动挂单
	optional string message = 10;    //挂单遗留的消息
	optional int32 enablestatus = 11;  //0:失效，1：可用

}
message gcmodifyorder
{
	optional int32 result = 1;    //返回的通知
	optional string msg = 2;
	optional vendororderinfo voInfo = 3;
}

//取消挂单
message cgdisableorder  
{
	optional int32 userid = 1;
	optional int32 hangid = 2;    //订单ID
}
message gcdisableorder
{
	optional int32 result = 1;    //返回的通知
	optional string msg = 2;
}

//查看自己的挂单
message cgorderlist {
	optional int32 userid = 1;
	optional int32 ordertype = 2;			//0 挂买  1 挂卖
	optional int32 pagenum = 3; 			//页码
	optional int32 pagesize = 4; 			//一页多少条
}
message gcorderlist {
	optional int32 result = 1;
	optional int32 msg = 2;
	repeated vendororderinfo volist = 3;
	repeated int32 dealnum = 4;				//正在交易中的数量
	optional int32 ordertype = 5;			//0 挂买  1 挂卖
	optional int32 pagenum = 6; 			//页码
	optional int32 pagesize = 7; 			//一页多少条
}

//快捷买卖币
message cgquickdeal   
{
	optional int32 userid = 1;
	optional int32 hangid = 2;  			//对应的挂单ID
	optional int32 dealtype = 3;  			//交易类型，0是买，1是卖
	optional string amount = 4;     		//交易数量
	optional string fundPwd = 5;   			//交易密码
	optional int32 moneytype = 6;  		 	//钱的类型 0-币 1-人民币
	optional int32 paytype = 7;				//支付方式
	optional int32 cionid = 8;				//币种ID
}
message gcquickdeal   
{
	optional int32 result = 1;
	optional string msg = 2;
	optional customerorderinfo coinfo = 3;  //返回一个对应的结构体
}

//交易，也就是买卖
message cgdeal   
{
	optional int32 userid = 1;
	optional int32 hangid = 2;  			//对应的挂单ID
	optional int32 dealtype = 3;  			//交易类型，0是买，1是卖
	optional string amount = 4;     		//交易数量
	optional string fundPwd = 5;   			//交易密码
	optional string price = 6;   			//价格
	optional int32 moneytype = 7;   		//钱的类型 0-币 1-人民币
	optional int32 paytype = 8;				//支付方式
}
message gcdeal
{
	optional int32 result = 1;   			//产生的一个订单的类型
	optional string msg = 2;  
	optional customerorderinfo coinfo = 3;   //返回一个对应的结构体
}

//确认支付
message cgpaid   
{
	optional int32 userid = 1;
	optional int32 dealid = 2;   //交易的ID
	optional string fundPwd = 3;
	repeated string proofurl = 4;  //上传的凭证的url
}
message gcpaid
{
	optional int32 result = 1;
	optional string msg = 2;
	optional customerorderinfo coinfo = 3;   //返回一个对应的结构体
}

//放行，如果是买单，那么是卖家放行，如果是卖单，那么是挂单的币商放行
message cgpass    
{
	optional int32 userid = 1;
	optional int32 dealid = 2;   //交易的ID
	optional string fundPwd = 3;
}
message gcpass
{
	optional int32 result = 1;
	optional string msg = 2;
	optional customerorderinfo coinfo = 3;   //返回一个对应的结构体
}

//取得剩余的取消次数
message cgcancelleftcount   
{
	optional int32 userid = 1;   //没人每天都会有固定的最大取消次数，这个次数仅对钱包app端用户有效
}   
message gccancelleftcount
{
	optional int32 result = 1;
	optional string msg = 2;
	optional int32 leftcount = 3;  //剩余次数
}

//取消交易订单
message cgcancel   
{
	optional int32 userid = 1;
	optional int32 dealid = 2;   //交易ID
}
message gccancel
{
	optional int32 result = 1;
	optional string msg = 2;
	optional customerorderinfo coinfo = 3;   //返回一个对应的结构体
}

//申诉
message cgappeal
{
	optional int32 userid = 1;
	optional int32 dealid = 2;
	optional string reason = 3;
	optional string description = 4; //详细原因
	optional string proofurl = 5;   //申诉凭证
}
message gcappeal
{
	optional int32 result = 1;
	optional string msg = 2;
	optional apppealinfo info = 3;   //返回一个对应的结构体	
	optional customerorderinfo coinfo = 4;   //返回一个对应的结构体	
}

//回复申诉
message cgappealreply
{
	optional int32 userid = 1;
	optional int32 dealid = 2;
	optional string reason = 3;
	optional string description = 4; //详细原因
	optional string proofurl = 5;   //申诉凭证	
}
message gcappealreply
{
	optional int32 result = 1;
	optional string msg = 2;
	optional customerorderinfo coinfo = 3;   //返回一个对应的结构体	
}

//申诉详情
message cgappealdetail
{
	optional int32 userid = 1;
	optional int32 dealid = 2;
}
message gcappealdetail
{
	optional int32 result = 1;
	optional string msg = 2;
	optional customerorderinfo coinfo = 3;   //返回一个对应的结构体		
}

//申诉列表
message cgappeallist   
{
	optional int32 userid = 1;
}
message gcappeallist
{
	
}

//顾客订单列表(查看币商的挂单)
message cgcustomerorderlist {
	optional int32 userid = 1;
	optional int32 ordertype = 2;     			//0 买单 1 卖单
	optional int32 pagenum = 3;     			//页数。每页显示10个。
	optional int32 pagesize = 4; 			//一页多少条
	optional int32 coinid = 5;					//币种ID
}
message gccustomerorderlist {
	optional int32 result = 1;
	optional string msg = 2;
	repeated vendororderinfo volist = 3;		//挂单信息
	optional int32 ordertype = 4;     			//0 买单 1 卖单
	optional int32 pagenum = 5;     			//页数。每页显示10个。
	optional int32 pagesize = 6; 				//一页多少条
	optional int32 coinid = 7;					//币种ID
}

//收支明细
message cgincomeexpendituredetail
{
	optional int32 userid = 1;		
	optional int32 detailtype = 2;				//0 全部 1 购买 2出售 3充值 4 提现 5提币 6充币 7转入 8转出 9收益 10 手续费 11系统补款 12系统扣款 
	optional int32 pagenum = 3;     			//页数。每页显示10个。
	optional int32 pagesize = 4; 				//一页多少条
	optional int32 coinid = 5;					//币种ID
}
message gcincomeexpendituredetail
{
	optional int32 result = 1;
	optional string msg = 2;
	optional int32 detailtype = 3;						//0 全部 1 购买 2出售 3充值 4 提现 5提币 6充币 7转入 8转出 9收益 10 手续费 11系统补款 12系统扣款
	optional int32 pagenum = 4;     					//页数。每页显示10个。
	optional int32 pagesize = 5; 						//一页多少条
	optional string parameter1 = 6;						//参数1 页面只有一个参数是就用这一个
	optional string parameter2 = 7;						//参数1 页面有二个参数是第二个就用这
	repeated incomeexpendituredetail detaillist = 8;	//参数1 页面有二个参数是第二个就用这
	optional int32 coinid = 9;							//币种ID
}

//激活订单
message cgorderactivation
{
	optional int32 userid = 1;		
	optional int32 dealid = 2;							//订单号
	optional string fundpwd = 3;						//资金密码
}
message gcorderactivation
{
	optional int32 result = 1;
	optional string msg = 2;
	optional customerorderinfo coinfo = 3;   			//返回一个对应的结构体
}