package prootc;


//创建一个websocket链接
message chatdata    //聊天的消息
{
	optional int32 timesec = 1;			 //int的型的时间戳
	optional int32 chattype = 2;     	 //聊天类型：g_chatDefine.chat_type  100：系统发送的支付消息，200：系统发送的消息；300：客服发送；400：客户发给币商，500：币商发给客户
	optional int32 msgtype = 3;          //消息类型：0:默认数字聊天，1：文字，2：图片，3：链接
	optional string msg  = 5;            //聊天的内容，该变量一般不展示，只有msgid不为为0的时候，才展示
	optional string pcorderid = 6;
	
	
}

