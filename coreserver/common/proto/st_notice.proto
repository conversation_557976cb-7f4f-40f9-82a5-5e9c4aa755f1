package prootc;

message noticeinfo
{
	optional int32 id = 1;						//公告ID
	optional string title = 2;					//标题
	optional string content = 3;				//内容
	optional int32 noticetype = 4;				//类型 暂无全是0
	optional int32 expirationtime = 5;			//公告过期时间
	optional string remark = 6;					//备注
}

message messageinfo
{
	optional int32 id = 1;						//消息通知id
	optional int32 messagetype = 2;				//类型 暂无全是0
	optional string title = 3;					//标题
	optional string content = 4;				//内容
	optional string remark = 5;					//备注
	optional int32 isread = 6;					//是否已读(0 未读 1已读)
}