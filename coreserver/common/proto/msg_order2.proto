package prootc;
import "st_human.proto";
import "st_order.proto";

//订单详情
message cgdetail   
{
	optional int32 userid = 1;
	optional int32 handid = 2;  
	
}
message gcdetail
{
	optional int32 result = 1;
	optional string msg = 2;
	optional customerorderinfo coinfo = 3;   //返回一个对应的结构体		
}

//收益详情 收入统计(币商)
message cgincomestat 
{
	optional int32 userid = 1;
	optional int32 starttime = 2;
	optional int32 endtime = 3;
	
}
message gcincomestat
{
	optional int32 result = 1;
	optional string msg = 2;	
	optional string total = 3;						//合计
	optional string buy = 4;						//购入
	optional string topup = 5;						//充入
	optional string transferinto = 6;				//转入
	optional string reward = 7;						//奖励
	
}

//支出统计(币商)
message cgexpensestat
{
	optional int32 userid = 1;
	optional int32 starttime = 2;
	optional int32 endtime = 3;
}
message gcexpensestat
{
	optional int32 result = 1;
	optional string msg = 2;	
	optional string total = 3;						//合计
	optional string sell = 4;						//出售
	optional string withdraw = 5;					//提币
	optional string rollout = 6;					//转出
	optional string fee = 7;						//手续费
	
}

 //今天的现金流（今日售出/买入）
message cgtodayflow  
{
	optional int32 userid = 1;
	optional int32 coinid = 2;
}
message gctodayflow   
{
	optional int32 result = 1;
	optional string msg = 2;
	optional string todaySell = 3;					//进入卖出
	optional string todaybuy = 4;					//进入买人
	optional int32 averagetime = 5;					//平均确定时长
	optional string successrate = 6;				//交易成功率
	optional string exchangerate = 7;				//汇率
	optional int32 coinid = 8;
}

//交易记录
message cgdealrecdlist   
{
	optional int32 userid = 1;  
	optional int32 type = 2;									//类型 0-买 1-卖 2-全部
	optional int32 status = 3;									//订单状态 0-全部 1-未付款 3-已付款 7-已完成, 8-已取消, 9-申诉中
	optional int32 paytype = 4;									//交易类型  8101-支付宝,8201-微信,8001-银行卡
	optional int32 pagenum = 5;									//页码
	optional int32 pagesize = 6;								//笔数
	
}
message gedealrecdlist
{
	optional int32 result = 1;
	optional string msg = 2;
	optional int32 type = 3;									//类型 0-买 1-卖 2-全部
	optional int32 status = 4;									//订单状态 0-全部 1-未付款 3-已付款 7-已完成, 8-已取消, 9-申诉中
	optional int32 paytype = 5;									//交易类型 8101-支付宝,8201-微信,8001-银行卡
	optional int32 pagenum = 6;									//页码
	optional int32 pagesize = 7;								//笔数
	repeated customerorderinfo infolist = 8;					//信息列表
	optional int32 ordercount = 9;								//法币订单数
	optional int32 finishordercount = 10;						//成功法币订单数
	
	
}

//待处理的订单
message cgworkinglist 
{
	optional int32 userid = 1;
}
message gcworkinglist
{
	optional int32 result = 1;
	optional string msg = 2;
	repeated customerorderinfo infolist = 3;					//信息列表
}


//提币/转账
message cgwithdraw
{
	optional int32 userid = 1;
	optional int32 ordertype = 2;								//订单类型， 1-提币 2-转账
	optional string toaddress = 3;								//提币地址
	optional int32 currencytype = 4;							//货币类型  2003
	optional string currencyamount = 5;							//货币数量
	optional string subject = 6;								//暂时不知道是什么
	optional string version = 7;								//暂时不知道是什么
	optional string notifyurl = 8;								//暂时不知道是什么
	optional string body = 9;									//暂时不知道是什么
	optional string outtradeno = 10;							//暂时不知道是什么
	optional string fundpwd = 11;								//资金密码
	optional int32 addrtype = 12;								//地址类型
	optional int32 touserid = 13;								//目标用户
}
message gcwithdraw
{
	optional int32 result = 1;
	optional string msg = 2;							
}

//币币交易记录
message cgwithdrawrecord
{
	optional int32 userid = 1;
	optional int32 ordertype = 2;								//操作类型， 0-全部 1-充币 2-提币 3-转入 4-转账
	optional int32 pagenum = 3; 								//页码
	optional int32 pagesize = 4; 								//一页多少条
}
message gcwithdrawrecord
{
	optional int32 result = 1;
	optional string msg = 2;	
	optional int32 userid = 3;
	optional int32 ordertype = 4;								//操作类型， 0-全部 1-充币 2-提币 3-转入 4-转账
	optional int32 pagenum = 5; 								//页码
	optional int32 pagesize = 6; 								//一页多少条
	repeated currencyrecordinfo recordinfo = 7; 				//币币交易详情列表
	
}

//查询汇率
message cgexchangerate
{
	optional int32 userid = 1;
}
message gcexchangerate
{
	optional int32 result = 1;
	optional string msg = 2;	
	optional string buyrate = 3;									//买汇率
	optional string sellrate = 4;									//卖汇率			
}


//查询是否站内地址
message cgcheckaddr
{
	optional string adds = 1;
}
message gccheckaddr
{
	optional int32 result = 1;
	optional string msg = 2;	
	optional string adds = 3;		
	optional int32 ret = 4;											// 0 站内地址 1 站外地址		
}


//托管钱包充值
message cgtwalletrecharge
{
	optional int32 userid = 1;
	optional string amount = 2;										//数量
	optional int32 addrtype = 3;									//地址类型
	optional int32 coinid = 4;										//币的类型
}
message gctwalletrecharge
{
	optional int32 result = 1;
	optional string msg = 2;	
	optional int32 userid = 3;
	optional string amount = 4;										//数量
	optional twalletorderinfo info = 5;								//订单信息	
}

//提交TXID
message cgsubmittxid
{
	optional int32 userid = 1;
	optional string orderid = 2;								//钱包地址
	optional string txid = 3;										
}
message gcsubmittxid
{
	optional int32 result = 1;
	optional string msg = 2;	
	optional twalletorderinfo info = 3;								//订单信息	
}

//托管钱包充值记录
message cgtwalletrecord
{
	optional int32 userid = 1;
	optional int32 pagenum = 2;
	optional int32 pagesize = 3;
	optional int32 ordertype = 4;									//订单类型 0 全部
									
}
message gctwalletrecord
{
	optional int32 result = 1;
	optional string msg = 2;
	optional int32 pagenum = 3;
	optional int32 pagesize = 4;
	optional int32 ordertype = 5;									//订单类型 0 全部	
	repeated twalletorderinfo infolist = 6;							//订单信息列表
}


//提币转账手续
message cggetwithdrawalfee
{
	optional int32 userid = 1;
	optional int32 coinid = 2;										//币种ID
	optional int32 chaintype = 3;									//链类型,区块类型 101-erc 102-trc
}
message gcgetwithdrawalfee
{
	optional int32 result = 1;
	optional string msg = 2;
	optional string withdrawalfee = 3;								//提币手续费
	optional string minwithdrawalcount = 4;							//最小提现
	optional string maxwithdrawalcount = 5;							//最大提现
	optional string transferfee = 6;								//转账手续费
	optional string mintransfercount = 7;							//最小转账
	optional string maxtransfercount = 8;							//最大转账
	optional int32 coinid = 9;										//币种ID
	optional int32 chaintype = 10;									//链类型,区块类型 101-erc 102-trc
}

//修改支付凭证
message cgmodifyproofurl
{
	optional int32 userid = 1;		
	optional int32 dealid = 2;		
	repeated string proofurl = 3;		
}
message gcmodifyproofurl
{
	optional int32 result = 1;
	optional string msg = 2;
	optional int32 dealid = 3;						
	optional customerorderinfo coinfo = 4;  		 //返回一个对应的结构体
}

//通知客户端新增挂单
message gcupdatevendororder
{
	optional int32 result = 1;
	optional string msg = 2;
	repeated vendororderinfo volist = 3;
}

//搜索交易订单
message cgsearchdealrecdlist   
{
	optional int32 userid = 1;  
	optional int32 searchtype = 2;								//搜索类型 1-流水号 2-收款人 3-收款账号 4-商户订单号
	optional string keyword = 3;								//关键字
	optional int32 pagenum = 4;									//页码
	optional int32 pagesize = 5;								//笔数
	
}
message gcsearchdealrecdlist
{
	optional int32 result = 1;
	optional string msg = 2;						
	optional int32 searchtype = 3;								//搜索类型 1-流水号 2-收款人 3-收款账号 4-商户订单号
	optional string keyword = 4;								//关键字
	optional int32 pagenum = 5;									//页码
	optional int32 pagesize = 6;								//笔数
	repeated customerorderinfo infolist = 7;					//信息列表
}







