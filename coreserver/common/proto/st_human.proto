package prootc;

message userinfo
{
	optional int32 userid = 1;   				//用户标识ID
	optional string cid = 2;                 	//用作判断手机标识的唯一字符串
	optional string account = 3;				//账号
	optional string password = 4;				//密码
	optional string nickname = 5;				//昵称
	optional string ethaddress = 6;   			//eth地址
	optional string face1 = 7;					//头像
	optional string regdate = 8;				//注册时间
	optional int32 sex = 9;						//性别
	optional int32 age = 10;					//年龄
	optional string email = 11;					//邮箱
	optional string phonenum = 12;				//电话
	optional string ethamount = 13;			//eth的数量
	optional string ercusdtamount = 14;		//erc20的usdt的数量
	optional string ercusdtlockamount = 15;	//erc20的usdt的锁定数量
	optional string channel = 16;				//渠道号
	optional string invitecode = 17;			//自己的邀请码
	optional string bindcode = 18;				//绑定的上级邀请码
	optional string imei = 19;					//手机IMEI码
	optional string devname = 20;             	//设备号
	optional string macname = 21;             	//物理地址
	optional int32 mobiletype = 22;           	//运营商
	optional int32 lasttime = 23;             	//上次登录时间
	optional int32 penulttime = 24;           	//倒数第二次登录
	optional int32 isban = 25;                	//是否禁号，列入黑名单
	optional string description = 26;       	//个性签名
	optional int32 blacklist = 27;				
	optional string ip = 28;             		//IP地址
	optional string province = 29;       		//省份
	optional string city = 30;           		//城市
	optional int32 bindtype = 31;           	//注册类型 1 手机注册
	optional int32 isfundpassword = 32;         //是否有设定资金密码
	repeated int32 paytypelist = 33;			//支付通道列表
	optional int32 agent = 34;					//直属上级
	optional int32 usertype = 35;				//用户类型 100=平台账号，用于资金归集；200=币商代理；201=码商；202=币商；300=商户代理，301=商户
	optional int32 autosell = 36;				//自动售卖 0-否  大于0 自动售卖的订单
	optional string minbuy = 37;				//最小购买
	optional string maxbuy = 38;				//最大购买
	optional string minsell = 39;				//最小出售
	optional string maxsell = 40;				//最大出售
	optional int32 islock = 41;					//是否锁定 0解锁 1锁定
	optional int32 ishangbuy = 42;				//是否允许挂买单，1=允许 2不允许
	optional int32 ishangsell = 43;				//是否允许挂卖单，1=允许 2不允许
	optional int32 commtype = 44;				//收益类型：101=按级差进行收益，后期添加其他
	optional int32 prohibitlogin = 45;			//是否禁止登陆
	optional int32 isacceptorder = 46;			//是否允许接单 0 允许1 禁止
	optional string deallasttime = 47;			//上次交易时间
	optional string bantime = 48;				//封禁时间
	optional int32 isinvited = 49;				//是否允许开设代理 0允许 1禁止
	optional int32 isotc = 50;					//是否开启OTC交易 0 开启 1 不开
	optional int32 isbehalfpay = 51;			//是否开启代付交易 0 开启 1 不开
	optional string ercfcamount = 52;			//法定货币数量
	optional string ercfclockamount = 53;		//法定货币冻结数量
	optional int32 dealcointype = 54;			//成交货币类型 0 数字货币 1 法币
	optional string energyvalue = 55;			//能量值
	optional string weightsvalue = 56;			//权重值
	optional string teamname = 57;				//团队名称
	optional int32 platformid = 58;				//平台编号
	optional int32 isteamacceptorder = 59;		//是否禁止团队接单 0 否 1 是
	optional string preauthorization = 60;		//提现预售额度
	optional string coinpayrate = 61;			//币支付费率
	optional int32 coinpayerc = 62;				//是否开启erc充币 0-关闭 1-开启
	optional int32 coinpaytrc = 63;				//是否开启trc充币 0-关闭 1-开启
	optional int32 allowsysaddr = 64;			//是否允许使用系统地址 0-否 1-是
	optional string sysercmin = 65;				//使用系统erc地址最小值
	optional string coinpayusdtamount = 66;		//币付钱包
	optional string coinpayusdtlockamount = 67;	//币付冻结钱包
	optional int32 ischeck = 68;				//重复订单检查 0 检查 1 不检查
	optional string extractcurrencyrateerc = 69;//erc的提币手续费
	optional string extractcurrencyratetrc = 70;//trc的提币手续费
}


message coininfo
{
	optional int32 coinid	= 1;					//币种ID
	optional string coinname = 2;					//币种昵称
	optional string coinamount = 3;					//币总数量	
	optional string coinlockamount = 4;				//冻结币总数量	
	optional string coinfreeamount = 5;				//可用币的数量
	optional int32 status = 6;						//币状态 0-可用 1-不可用
	optional string buyrate = 7;					//购买汇率
	optional string sellrate = 8;					//购买汇率
}

message coinaddr
{
	optional int32 addrtype	= 1;					//链类型
	optional string addrname = 2;					//链名称
	optional string address = 3;					//链地址
}

message payinfo
{
	optional int32 paytype = 1;					//支付类型
	optional string account = 2;				//账号
	optional string payee = 3;					//收款人
	optional string qrcode = 4;					//收款码
	optional string bankname = 5;				//开户行
	optional string bankaddr = 6;				//开户地址
	optional string singlelimit = 7;			//单笔限额
	optional string daylimit = 8;				//单日限额
	optional int32 id = 9;						//编号
	optional int32 status = 10;					//0 失效 1 生效
	optional int32 userid = 11;					//所属玩家
	optional string todaymoney = 12;			//今日收款		
	optional int32 fourthpartyid = 13;			//四方ID
	optional string deallasttime = 14;			//最后一次交易的时间
}


message subinfo
{
	optional int32 userid = 1;					//用户ID
	optional string nickname = 2;				//用户名字
	optional string teamperformance = 3;		//出售量
	optional string myperformance = 4;			//给上级贡献的收益
	optional int32 isacceptorder = 5;			//是否允许接单 0 允许1 禁止
	optional int32 prohibitlogin = 6;			//禁止登陆 0 允许登陆 1 禁止登陆
	repeated string paytypelist = 7;			//支付方式
	repeated string payratelist = 8;			//出售费率
	optional string buycount = 9;				//购买量
	repeated string buyratelist = 10;			//购买费率
	repeated string behalfbuyratelist = 11;		//代付费率
}


message twalletorderinfo
{
	optional string order_id = 1;					//订单号
	optional string channel = 2;					//渠道号
	optional int32 userid = 3;						//用户id
	optional string nickname = 4;					//用户昵称
	optional string currency_type = 5;				//币种
	optional string amount = 6;						//币数量
	optional string actualamount = 7;				//实际数量
	optional string trusteeship = 8;				//冷钱包地址
	optional string usertxid = 9;					//用户上传TXID
	optional string create = 10;					//创建时间
	optional string remarks = 11;					//备注
	optional int32 status = 12;						//0-未到账 1-已到账
	optional string coinid = 13;					//币种ID
	optional string coinname = 14;					//币种名字
	optional int32 addrtype = 15;					//地址类型
	optional string addrname = 16;					//地址名字
}






