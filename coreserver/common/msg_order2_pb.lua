-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local st_human_pb = require("st_human_pb")
local st_order_pb = require("st_order_pb")
----------nimol modify---------
local ST_HUMAN_PB_TWALLETORDERINFO = st_human_pb.ST_HUMAN_PB_TWALLETORDERINFO
local ST_ORDER_PB_CURRENCYRECORDINFO = st_order_pb.ST_ORDER_PB_CURRENCYRECORDINFO
local ST_ORDER_PB_CUSTOMERORDERINFO = st_order_pb.ST_ORDER_PB_CUSTOMERORDERINFO
local ST_ORDER_PB_VENDORORDERINFO = st_order_pb.ST_ORDER_PB_VENDORORDERINFO
module('msg_order2_pb')


local CGDETAIL = protobuf.Descriptor();
local CGDETAIL_USERID_FIELD = protobuf.FieldDescriptor();
local CGDETAIL_HANDID_FIELD = protobuf.FieldDescriptor();
local GCDETAIL = protobuf.Descriptor();
local GCDETAIL_RESULT_FIELD = protobuf.FieldDescriptor();
local GCDETAIL_MSG_FIELD = protobuf.FieldDescriptor();
local GCDETAIL_COINFO_FIELD = protobuf.FieldDescriptor();
local CGINCOMESTAT = protobuf.Descriptor();
local CGINCOMESTAT_USERID_FIELD = protobuf.FieldDescriptor();
local CGINCOMESTAT_STARTTIME_FIELD = protobuf.FieldDescriptor();
local CGINCOMESTAT_ENDTIME_FIELD = protobuf.FieldDescriptor();
local GCINCOMESTAT = protobuf.Descriptor();
local GCINCOMESTAT_RESULT_FIELD = protobuf.FieldDescriptor();
local GCINCOMESTAT_MSG_FIELD = protobuf.FieldDescriptor();
local GCINCOMESTAT_TOTAL_FIELD = protobuf.FieldDescriptor();
local GCINCOMESTAT_BUY_FIELD = protobuf.FieldDescriptor();
local GCINCOMESTAT_TOPUP_FIELD = protobuf.FieldDescriptor();
local GCINCOMESTAT_TRANSFERINTO_FIELD = protobuf.FieldDescriptor();
local GCINCOMESTAT_REWARD_FIELD = protobuf.FieldDescriptor();
local CGEXPENSESTAT = protobuf.Descriptor();
local CGEXPENSESTAT_USERID_FIELD = protobuf.FieldDescriptor();
local CGEXPENSESTAT_STARTTIME_FIELD = protobuf.FieldDescriptor();
local CGEXPENSESTAT_ENDTIME_FIELD = protobuf.FieldDescriptor();
local GCEXPENSESTAT = protobuf.Descriptor();
local GCEXPENSESTAT_RESULT_FIELD = protobuf.FieldDescriptor();
local GCEXPENSESTAT_MSG_FIELD = protobuf.FieldDescriptor();
local GCEXPENSESTAT_TOTAL_FIELD = protobuf.FieldDescriptor();
local GCEXPENSESTAT_SELL_FIELD = protobuf.FieldDescriptor();
local GCEXPENSESTAT_WITHDRAW_FIELD = protobuf.FieldDescriptor();
local GCEXPENSESTAT_ROLLOUT_FIELD = protobuf.FieldDescriptor();
local GCEXPENSESTAT_FEE_FIELD = protobuf.FieldDescriptor();
local CGTODAYFLOW = protobuf.Descriptor();
local CGTODAYFLOW_USERID_FIELD = protobuf.FieldDescriptor();
local CGTODAYFLOW_COINID_FIELD = protobuf.FieldDescriptor();
local GCTODAYFLOW = protobuf.Descriptor();
local GCTODAYFLOW_RESULT_FIELD = protobuf.FieldDescriptor();
local GCTODAYFLOW_MSG_FIELD = protobuf.FieldDescriptor();
local GCTODAYFLOW_TODAYSELL_FIELD = protobuf.FieldDescriptor();
local GCTODAYFLOW_TODAYBUY_FIELD = protobuf.FieldDescriptor();
local GCTODAYFLOW_AVERAGETIME_FIELD = protobuf.FieldDescriptor();
local GCTODAYFLOW_SUCCESSRATE_FIELD = protobuf.FieldDescriptor();
local GCTODAYFLOW_EXCHANGERATE_FIELD = protobuf.FieldDescriptor();
local GCTODAYFLOW_COINID_FIELD = protobuf.FieldDescriptor();
local CGDEALRECDLIST = protobuf.Descriptor();
local CGDEALRECDLIST_USERID_FIELD = protobuf.FieldDescriptor();
local CGDEALRECDLIST_TYPE_FIELD = protobuf.FieldDescriptor();
local CGDEALRECDLIST_STATUS_FIELD = protobuf.FieldDescriptor();
local CGDEALRECDLIST_PAYTYPE_FIELD = protobuf.FieldDescriptor();
local CGDEALRECDLIST_PAGENUM_FIELD = protobuf.FieldDescriptor();
local CGDEALRECDLIST_PAGESIZE_FIELD = protobuf.FieldDescriptor();
local GEDEALRECDLIST = protobuf.Descriptor();
local GEDEALRECDLIST_RESULT_FIELD = protobuf.FieldDescriptor();
local GEDEALRECDLIST_MSG_FIELD = protobuf.FieldDescriptor();
local GEDEALRECDLIST_TYPE_FIELD = protobuf.FieldDescriptor();
local GEDEALRECDLIST_STATUS_FIELD = protobuf.FieldDescriptor();
local GEDEALRECDLIST_PAYTYPE_FIELD = protobuf.FieldDescriptor();
local GEDEALRECDLIST_PAGENUM_FIELD = protobuf.FieldDescriptor();
local GEDEALRECDLIST_PAGESIZE_FIELD = protobuf.FieldDescriptor();
local GEDEALRECDLIST_INFOLIST_FIELD = protobuf.FieldDescriptor();
local GEDEALRECDLIST_ORDERCOUNT_FIELD = protobuf.FieldDescriptor();
local GEDEALRECDLIST_FINISHORDERCOUNT_FIELD = protobuf.FieldDescriptor();
local CGWORKINGLIST = protobuf.Descriptor();
local CGWORKINGLIST_USERID_FIELD = protobuf.FieldDescriptor();
local GCWORKINGLIST = protobuf.Descriptor();
local GCWORKINGLIST_RESULT_FIELD = protobuf.FieldDescriptor();
local GCWORKINGLIST_MSG_FIELD = protobuf.FieldDescriptor();
local GCWORKINGLIST_INFOLIST_FIELD = protobuf.FieldDescriptor();
local CGWITHDRAW = protobuf.Descriptor();
local CGWITHDRAW_USERID_FIELD = protobuf.FieldDescriptor();
local CGWITHDRAW_ORDERTYPE_FIELD = protobuf.FieldDescriptor();
local CGWITHDRAW_TOADDRESS_FIELD = protobuf.FieldDescriptor();
local CGWITHDRAW_CURRENCYTYPE_FIELD = protobuf.FieldDescriptor();
local CGWITHDRAW_CURRENCYAMOUNT_FIELD = protobuf.FieldDescriptor();
local CGWITHDRAW_SUBJECT_FIELD = protobuf.FieldDescriptor();
local CGWITHDRAW_VERSION_FIELD = protobuf.FieldDescriptor();
local CGWITHDRAW_NOTIFYURL_FIELD = protobuf.FieldDescriptor();
local CGWITHDRAW_BODY_FIELD = protobuf.FieldDescriptor();
local CGWITHDRAW_OUTTRADENO_FIELD = protobuf.FieldDescriptor();
local CGWITHDRAW_FUNDPWD_FIELD = protobuf.FieldDescriptor();
local CGWITHDRAW_ADDRTYPE_FIELD = protobuf.FieldDescriptor();
local CGWITHDRAW_TOUSERID_FIELD = protobuf.FieldDescriptor();
local GCWITHDRAW = protobuf.Descriptor();
local GCWITHDRAW_RESULT_FIELD = protobuf.FieldDescriptor();
local GCWITHDRAW_MSG_FIELD = protobuf.FieldDescriptor();
local CGWITHDRAWRECORD = protobuf.Descriptor();
local CGWITHDRAWRECORD_USERID_FIELD = protobuf.FieldDescriptor();
local CGWITHDRAWRECORD_ORDERTYPE_FIELD = protobuf.FieldDescriptor();
local CGWITHDRAWRECORD_PAGENUM_FIELD = protobuf.FieldDescriptor();
local CGWITHDRAWRECORD_PAGESIZE_FIELD = protobuf.FieldDescriptor();
local GCWITHDRAWRECORD = protobuf.Descriptor();
local GCWITHDRAWRECORD_RESULT_FIELD = protobuf.FieldDescriptor();
local GCWITHDRAWRECORD_MSG_FIELD = protobuf.FieldDescriptor();
local GCWITHDRAWRECORD_USERID_FIELD = protobuf.FieldDescriptor();
local GCWITHDRAWRECORD_ORDERTYPE_FIELD = protobuf.FieldDescriptor();
local GCWITHDRAWRECORD_PAGENUM_FIELD = protobuf.FieldDescriptor();
local GCWITHDRAWRECORD_PAGESIZE_FIELD = protobuf.FieldDescriptor();
local GCWITHDRAWRECORD_RECORDINFO_FIELD = protobuf.FieldDescriptor();
local CGEXCHANGERATE = protobuf.Descriptor();
local CGEXCHANGERATE_USERID_FIELD = protobuf.FieldDescriptor();
local GCEXCHANGERATE = protobuf.Descriptor();
local GCEXCHANGERATE_RESULT_FIELD = protobuf.FieldDescriptor();
local GCEXCHANGERATE_MSG_FIELD = protobuf.FieldDescriptor();
local GCEXCHANGERATE_BUYRATE_FIELD = protobuf.FieldDescriptor();
local GCEXCHANGERATE_SELLRATE_FIELD = protobuf.FieldDescriptor();
local CGCHECKADDR = protobuf.Descriptor();
local CGCHECKADDR_ADDS_FIELD = protobuf.FieldDescriptor();
local GCCHECKADDR = protobuf.Descriptor();
local GCCHECKADDR_RESULT_FIELD = protobuf.FieldDescriptor();
local GCCHECKADDR_MSG_FIELD = protobuf.FieldDescriptor();
local GCCHECKADDR_ADDS_FIELD = protobuf.FieldDescriptor();
local GCCHECKADDR_RET_FIELD = protobuf.FieldDescriptor();
local CGTWALLETRECHARGE = protobuf.Descriptor();
local CGTWALLETRECHARGE_USERID_FIELD = protobuf.FieldDescriptor();
local CGTWALLETRECHARGE_AMOUNT_FIELD = protobuf.FieldDescriptor();
local CGTWALLETRECHARGE_ADDRTYPE_FIELD = protobuf.FieldDescriptor();
local CGTWALLETRECHARGE_COINID_FIELD = protobuf.FieldDescriptor();
local GCTWALLETRECHARGE = protobuf.Descriptor();
local GCTWALLETRECHARGE_RESULT_FIELD = protobuf.FieldDescriptor();
local GCTWALLETRECHARGE_MSG_FIELD = protobuf.FieldDescriptor();
local GCTWALLETRECHARGE_USERID_FIELD = protobuf.FieldDescriptor();
local GCTWALLETRECHARGE_AMOUNT_FIELD = protobuf.FieldDescriptor();
local GCTWALLETRECHARGE_INFO_FIELD = protobuf.FieldDescriptor();
local CGSUBMITTXID = protobuf.Descriptor();
local CGSUBMITTXID_USERID_FIELD = protobuf.FieldDescriptor();
local CGSUBMITTXID_ORDERID_FIELD = protobuf.FieldDescriptor();
local CGSUBMITTXID_TXID_FIELD = protobuf.FieldDescriptor();
local GCSUBMITTXID = protobuf.Descriptor();
local GCSUBMITTXID_RESULT_FIELD = protobuf.FieldDescriptor();
local GCSUBMITTXID_MSG_FIELD = protobuf.FieldDescriptor();
local GCSUBMITTXID_INFO_FIELD = protobuf.FieldDescriptor();
local CGTWALLETRECORD = protobuf.Descriptor();
local CGTWALLETRECORD_USERID_FIELD = protobuf.FieldDescriptor();
local CGTWALLETRECORD_PAGENUM_FIELD = protobuf.FieldDescriptor();
local CGTWALLETRECORD_PAGESIZE_FIELD = protobuf.FieldDescriptor();
local CGTWALLETRECORD_ORDERTYPE_FIELD = protobuf.FieldDescriptor();
local GCTWALLETRECORD = protobuf.Descriptor();
local GCTWALLETRECORD_RESULT_FIELD = protobuf.FieldDescriptor();
local GCTWALLETRECORD_MSG_FIELD = protobuf.FieldDescriptor();
local GCTWALLETRECORD_PAGENUM_FIELD = protobuf.FieldDescriptor();
local GCTWALLETRECORD_PAGESIZE_FIELD = protobuf.FieldDescriptor();
local GCTWALLETRECORD_ORDERTYPE_FIELD = protobuf.FieldDescriptor();
local GCTWALLETRECORD_INFOLIST_FIELD = protobuf.FieldDescriptor();
local CGGETWITHDRAWALFEE = protobuf.Descriptor();
local CGGETWITHDRAWALFEE_USERID_FIELD = protobuf.FieldDescriptor();
local CGGETWITHDRAWALFEE_COINID_FIELD = protobuf.FieldDescriptor();
local CGGETWITHDRAWALFEE_CHAINTYPE_FIELD = protobuf.FieldDescriptor();
local GCGETWITHDRAWALFEE = protobuf.Descriptor();
local GCGETWITHDRAWALFEE_RESULT_FIELD = protobuf.FieldDescriptor();
local GCGETWITHDRAWALFEE_MSG_FIELD = protobuf.FieldDescriptor();
local GCGETWITHDRAWALFEE_WITHDRAWALFEE_FIELD = protobuf.FieldDescriptor();
local GCGETWITHDRAWALFEE_MINWITHDRAWALCOUNT_FIELD = protobuf.FieldDescriptor();
local GCGETWITHDRAWALFEE_MAXWITHDRAWALCOUNT_FIELD = protobuf.FieldDescriptor();
local GCGETWITHDRAWALFEE_TRANSFERFEE_FIELD = protobuf.FieldDescriptor();
local GCGETWITHDRAWALFEE_MINTRANSFERCOUNT_FIELD = protobuf.FieldDescriptor();
local GCGETWITHDRAWALFEE_MAXTRANSFERCOUNT_FIELD = protobuf.FieldDescriptor();
local GCGETWITHDRAWALFEE_COINID_FIELD = protobuf.FieldDescriptor();
local GCGETWITHDRAWALFEE_CHAINTYPE_FIELD = protobuf.FieldDescriptor();
local CGMODIFYPROOFURL = protobuf.Descriptor();
local CGMODIFYPROOFURL_USERID_FIELD = protobuf.FieldDescriptor();
local CGMODIFYPROOFURL_DEALID_FIELD = protobuf.FieldDescriptor();
local CGMODIFYPROOFURL_PROOFURL_FIELD = protobuf.FieldDescriptor();
local GCMODIFYPROOFURL = protobuf.Descriptor();
local GCMODIFYPROOFURL_RESULT_FIELD = protobuf.FieldDescriptor();
local GCMODIFYPROOFURL_MSG_FIELD = protobuf.FieldDescriptor();
local GCMODIFYPROOFURL_DEALID_FIELD = protobuf.FieldDescriptor();
local GCMODIFYPROOFURL_COINFO_FIELD = protobuf.FieldDescriptor();
local GCUPDATEVENDORORDER = protobuf.Descriptor();
local GCUPDATEVENDORORDER_RESULT_FIELD = protobuf.FieldDescriptor();
local GCUPDATEVENDORORDER_MSG_FIELD = protobuf.FieldDescriptor();
local GCUPDATEVENDORORDER_VOLIST_FIELD = protobuf.FieldDescriptor();
local CGSEARCHDEALRECDLIST = protobuf.Descriptor();
local CGSEARCHDEALRECDLIST_USERID_FIELD = protobuf.FieldDescriptor();
local CGSEARCHDEALRECDLIST_SEARCHTYPE_FIELD = protobuf.FieldDescriptor();
local CGSEARCHDEALRECDLIST_KEYWORD_FIELD = protobuf.FieldDescriptor();
local CGSEARCHDEALRECDLIST_PAGENUM_FIELD = protobuf.FieldDescriptor();
local CGSEARCHDEALRECDLIST_PAGESIZE_FIELD = protobuf.FieldDescriptor();
local GCSEARCHDEALRECDLIST = protobuf.Descriptor();
local GCSEARCHDEALRECDLIST_RESULT_FIELD = protobuf.FieldDescriptor();
local GCSEARCHDEALRECDLIST_MSG_FIELD = protobuf.FieldDescriptor();
local GCSEARCHDEALRECDLIST_SEARCHTYPE_FIELD = protobuf.FieldDescriptor();
local GCSEARCHDEALRECDLIST_KEYWORD_FIELD = protobuf.FieldDescriptor();
local GCSEARCHDEALRECDLIST_PAGENUM_FIELD = protobuf.FieldDescriptor();
local GCSEARCHDEALRECDLIST_PAGESIZE_FIELD = protobuf.FieldDescriptor();
local GCSEARCHDEALRECDLIST_INFOLIST_FIELD = protobuf.FieldDescriptor();

CGDETAIL_USERID_FIELD.name = "userid"
CGDETAIL_USERID_FIELD.full_name = ".prootc.cgdetail.userid"
CGDETAIL_USERID_FIELD.number = 1
CGDETAIL_USERID_FIELD.index = 0
CGDETAIL_USERID_FIELD.label = 1
CGDETAIL_USERID_FIELD.has_default_value = false
CGDETAIL_USERID_FIELD.default_value = 0
CGDETAIL_USERID_FIELD.type = 5
CGDETAIL_USERID_FIELD.cpp_type = 1

CGDETAIL_HANDID_FIELD.name = "handid"
CGDETAIL_HANDID_FIELD.full_name = ".prootc.cgdetail.handid"
CGDETAIL_HANDID_FIELD.number = 2
CGDETAIL_HANDID_FIELD.index = 1
CGDETAIL_HANDID_FIELD.label = 1
CGDETAIL_HANDID_FIELD.has_default_value = false
CGDETAIL_HANDID_FIELD.default_value = 0
CGDETAIL_HANDID_FIELD.type = 5
CGDETAIL_HANDID_FIELD.cpp_type = 1

CGDETAIL.name = "cgdetail"
CGDETAIL.full_name = ".prootc.cgdetail"
CGDETAIL.nested_types = {}
CGDETAIL.enum_types = {}
CGDETAIL.fields = {CGDETAIL_USERID_FIELD, CGDETAIL_HANDID_FIELD}
CGDETAIL.is_extendable = false
CGDETAIL.extensions = {}
GCDETAIL_RESULT_FIELD.name = "result"
GCDETAIL_RESULT_FIELD.full_name = ".prootc.gcdetail.result"
GCDETAIL_RESULT_FIELD.number = 1
GCDETAIL_RESULT_FIELD.index = 0
GCDETAIL_RESULT_FIELD.label = 1
GCDETAIL_RESULT_FIELD.has_default_value = false
GCDETAIL_RESULT_FIELD.default_value = 0
GCDETAIL_RESULT_FIELD.type = 5
GCDETAIL_RESULT_FIELD.cpp_type = 1

GCDETAIL_MSG_FIELD.name = "msg"
GCDETAIL_MSG_FIELD.full_name = ".prootc.gcdetail.msg"
GCDETAIL_MSG_FIELD.number = 2
GCDETAIL_MSG_FIELD.index = 1
GCDETAIL_MSG_FIELD.label = 1
GCDETAIL_MSG_FIELD.has_default_value = false
GCDETAIL_MSG_FIELD.default_value = ""
GCDETAIL_MSG_FIELD.type = 9
GCDETAIL_MSG_FIELD.cpp_type = 9

GCDETAIL_COINFO_FIELD.name = "coinfo"
GCDETAIL_COINFO_FIELD.full_name = ".prootc.gcdetail.coinfo"
GCDETAIL_COINFO_FIELD.number = 3
GCDETAIL_COINFO_FIELD.index = 2
GCDETAIL_COINFO_FIELD.label = 1
GCDETAIL_COINFO_FIELD.has_default_value = false
GCDETAIL_COINFO_FIELD.default_value = nil
GCDETAIL_COINFO_FIELD.message_type = ST_ORDER_PB_CUSTOMERORDERINFO
GCDETAIL_COINFO_FIELD.type = 11
GCDETAIL_COINFO_FIELD.cpp_type = 10

GCDETAIL.name = "gcdetail"
GCDETAIL.full_name = ".prootc.gcdetail"
GCDETAIL.nested_types = {}
GCDETAIL.enum_types = {}
GCDETAIL.fields = {GCDETAIL_RESULT_FIELD, GCDETAIL_MSG_FIELD, GCDETAIL_COINFO_FIELD}
GCDETAIL.is_extendable = false
GCDETAIL.extensions = {}
CGINCOMESTAT_USERID_FIELD.name = "userid"
CGINCOMESTAT_USERID_FIELD.full_name = ".prootc.cgincomestat.userid"
CGINCOMESTAT_USERID_FIELD.number = 1
CGINCOMESTAT_USERID_FIELD.index = 0
CGINCOMESTAT_USERID_FIELD.label = 1
CGINCOMESTAT_USERID_FIELD.has_default_value = false
CGINCOMESTAT_USERID_FIELD.default_value = 0
CGINCOMESTAT_USERID_FIELD.type = 5
CGINCOMESTAT_USERID_FIELD.cpp_type = 1

CGINCOMESTAT_STARTTIME_FIELD.name = "starttime"
CGINCOMESTAT_STARTTIME_FIELD.full_name = ".prootc.cgincomestat.starttime"
CGINCOMESTAT_STARTTIME_FIELD.number = 2
CGINCOMESTAT_STARTTIME_FIELD.index = 1
CGINCOMESTAT_STARTTIME_FIELD.label = 1
CGINCOMESTAT_STARTTIME_FIELD.has_default_value = false
CGINCOMESTAT_STARTTIME_FIELD.default_value = 0
CGINCOMESTAT_STARTTIME_FIELD.type = 5
CGINCOMESTAT_STARTTIME_FIELD.cpp_type = 1

CGINCOMESTAT_ENDTIME_FIELD.name = "endtime"
CGINCOMESTAT_ENDTIME_FIELD.full_name = ".prootc.cgincomestat.endtime"
CGINCOMESTAT_ENDTIME_FIELD.number = 3
CGINCOMESTAT_ENDTIME_FIELD.index = 2
CGINCOMESTAT_ENDTIME_FIELD.label = 1
CGINCOMESTAT_ENDTIME_FIELD.has_default_value = false
CGINCOMESTAT_ENDTIME_FIELD.default_value = 0
CGINCOMESTAT_ENDTIME_FIELD.type = 5
CGINCOMESTAT_ENDTIME_FIELD.cpp_type = 1

CGINCOMESTAT.name = "cgincomestat"
CGINCOMESTAT.full_name = ".prootc.cgincomestat"
CGINCOMESTAT.nested_types = {}
CGINCOMESTAT.enum_types = {}
CGINCOMESTAT.fields = {CGINCOMESTAT_USERID_FIELD, CGINCOMESTAT_STARTTIME_FIELD, CGINCOMESTAT_ENDTIME_FIELD}
CGINCOMESTAT.is_extendable = false
CGINCOMESTAT.extensions = {}
GCINCOMESTAT_RESULT_FIELD.name = "result"
GCINCOMESTAT_RESULT_FIELD.full_name = ".prootc.gcincomestat.result"
GCINCOMESTAT_RESULT_FIELD.number = 1
GCINCOMESTAT_RESULT_FIELD.index = 0
GCINCOMESTAT_RESULT_FIELD.label = 1
GCINCOMESTAT_RESULT_FIELD.has_default_value = false
GCINCOMESTAT_RESULT_FIELD.default_value = 0
GCINCOMESTAT_RESULT_FIELD.type = 5
GCINCOMESTAT_RESULT_FIELD.cpp_type = 1

GCINCOMESTAT_MSG_FIELD.name = "msg"
GCINCOMESTAT_MSG_FIELD.full_name = ".prootc.gcincomestat.msg"
GCINCOMESTAT_MSG_FIELD.number = 2
GCINCOMESTAT_MSG_FIELD.index = 1
GCINCOMESTAT_MSG_FIELD.label = 1
GCINCOMESTAT_MSG_FIELD.has_default_value = false
GCINCOMESTAT_MSG_FIELD.default_value = ""
GCINCOMESTAT_MSG_FIELD.type = 9
GCINCOMESTAT_MSG_FIELD.cpp_type = 9

GCINCOMESTAT_TOTAL_FIELD.name = "total"
GCINCOMESTAT_TOTAL_FIELD.full_name = ".prootc.gcincomestat.total"
GCINCOMESTAT_TOTAL_FIELD.number = 3
GCINCOMESTAT_TOTAL_FIELD.index = 2
GCINCOMESTAT_TOTAL_FIELD.label = 1
GCINCOMESTAT_TOTAL_FIELD.has_default_value = false
GCINCOMESTAT_TOTAL_FIELD.default_value = ""
GCINCOMESTAT_TOTAL_FIELD.type = 9
GCINCOMESTAT_TOTAL_FIELD.cpp_type = 9

GCINCOMESTAT_BUY_FIELD.name = "buy"
GCINCOMESTAT_BUY_FIELD.full_name = ".prootc.gcincomestat.buy"
GCINCOMESTAT_BUY_FIELD.number = 4
GCINCOMESTAT_BUY_FIELD.index = 3
GCINCOMESTAT_BUY_FIELD.label = 1
GCINCOMESTAT_BUY_FIELD.has_default_value = false
GCINCOMESTAT_BUY_FIELD.default_value = ""
GCINCOMESTAT_BUY_FIELD.type = 9
GCINCOMESTAT_BUY_FIELD.cpp_type = 9

GCINCOMESTAT_TOPUP_FIELD.name = "topup"
GCINCOMESTAT_TOPUP_FIELD.full_name = ".prootc.gcincomestat.topup"
GCINCOMESTAT_TOPUP_FIELD.number = 5
GCINCOMESTAT_TOPUP_FIELD.index = 4
GCINCOMESTAT_TOPUP_FIELD.label = 1
GCINCOMESTAT_TOPUP_FIELD.has_default_value = false
GCINCOMESTAT_TOPUP_FIELD.default_value = ""
GCINCOMESTAT_TOPUP_FIELD.type = 9
GCINCOMESTAT_TOPUP_FIELD.cpp_type = 9

GCINCOMESTAT_TRANSFERINTO_FIELD.name = "transferinto"
GCINCOMESTAT_TRANSFERINTO_FIELD.full_name = ".prootc.gcincomestat.transferinto"
GCINCOMESTAT_TRANSFERINTO_FIELD.number = 6
GCINCOMESTAT_TRANSFERINTO_FIELD.index = 5
GCINCOMESTAT_TRANSFERINTO_FIELD.label = 1
GCINCOMESTAT_TRANSFERINTO_FIELD.has_default_value = false
GCINCOMESTAT_TRANSFERINTO_FIELD.default_value = ""
GCINCOMESTAT_TRANSFERINTO_FIELD.type = 9
GCINCOMESTAT_TRANSFERINTO_FIELD.cpp_type = 9

GCINCOMESTAT_REWARD_FIELD.name = "reward"
GCINCOMESTAT_REWARD_FIELD.full_name = ".prootc.gcincomestat.reward"
GCINCOMESTAT_REWARD_FIELD.number = 7
GCINCOMESTAT_REWARD_FIELD.index = 6
GCINCOMESTAT_REWARD_FIELD.label = 1
GCINCOMESTAT_REWARD_FIELD.has_default_value = false
GCINCOMESTAT_REWARD_FIELD.default_value = ""
GCINCOMESTAT_REWARD_FIELD.type = 9
GCINCOMESTAT_REWARD_FIELD.cpp_type = 9

GCINCOMESTAT.name = "gcincomestat"
GCINCOMESTAT.full_name = ".prootc.gcincomestat"
GCINCOMESTAT.nested_types = {}
GCINCOMESTAT.enum_types = {}
GCINCOMESTAT.fields = {GCINCOMESTAT_RESULT_FIELD, GCINCOMESTAT_MSG_FIELD, GCINCOMESTAT_TOTAL_FIELD, GCINCOMESTAT_BUY_FIELD, GCINCOMESTAT_TOPUP_FIELD, GCINCOMESTAT_TRANSFERINTO_FIELD, GCINCOMESTAT_REWARD_FIELD}
GCINCOMESTAT.is_extendable = false
GCINCOMESTAT.extensions = {}
CGEXPENSESTAT_USERID_FIELD.name = "userid"
CGEXPENSESTAT_USERID_FIELD.full_name = ".prootc.cgexpensestat.userid"
CGEXPENSESTAT_USERID_FIELD.number = 1
CGEXPENSESTAT_USERID_FIELD.index = 0
CGEXPENSESTAT_USERID_FIELD.label = 1
CGEXPENSESTAT_USERID_FIELD.has_default_value = false
CGEXPENSESTAT_USERID_FIELD.default_value = 0
CGEXPENSESTAT_USERID_FIELD.type = 5
CGEXPENSESTAT_USERID_FIELD.cpp_type = 1

CGEXPENSESTAT_STARTTIME_FIELD.name = "starttime"
CGEXPENSESTAT_STARTTIME_FIELD.full_name = ".prootc.cgexpensestat.starttime"
CGEXPENSESTAT_STARTTIME_FIELD.number = 2
CGEXPENSESTAT_STARTTIME_FIELD.index = 1
CGEXPENSESTAT_STARTTIME_FIELD.label = 1
CGEXPENSESTAT_STARTTIME_FIELD.has_default_value = false
CGEXPENSESTAT_STARTTIME_FIELD.default_value = 0
CGEXPENSESTAT_STARTTIME_FIELD.type = 5
CGEXPENSESTAT_STARTTIME_FIELD.cpp_type = 1

CGEXPENSESTAT_ENDTIME_FIELD.name = "endtime"
CGEXPENSESTAT_ENDTIME_FIELD.full_name = ".prootc.cgexpensestat.endtime"
CGEXPENSESTAT_ENDTIME_FIELD.number = 3
CGEXPENSESTAT_ENDTIME_FIELD.index = 2
CGEXPENSESTAT_ENDTIME_FIELD.label = 1
CGEXPENSESTAT_ENDTIME_FIELD.has_default_value = false
CGEXPENSESTAT_ENDTIME_FIELD.default_value = 0
CGEXPENSESTAT_ENDTIME_FIELD.type = 5
CGEXPENSESTAT_ENDTIME_FIELD.cpp_type = 1

CGEXPENSESTAT.name = "cgexpensestat"
CGEXPENSESTAT.full_name = ".prootc.cgexpensestat"
CGEXPENSESTAT.nested_types = {}
CGEXPENSESTAT.enum_types = {}
CGEXPENSESTAT.fields = {CGEXPENSESTAT_USERID_FIELD, CGEXPENSESTAT_STARTTIME_FIELD, CGEXPENSESTAT_ENDTIME_FIELD}
CGEXPENSESTAT.is_extendable = false
CGEXPENSESTAT.extensions = {}
GCEXPENSESTAT_RESULT_FIELD.name = "result"
GCEXPENSESTAT_RESULT_FIELD.full_name = ".prootc.gcexpensestat.result"
GCEXPENSESTAT_RESULT_FIELD.number = 1
GCEXPENSESTAT_RESULT_FIELD.index = 0
GCEXPENSESTAT_RESULT_FIELD.label = 1
GCEXPENSESTAT_RESULT_FIELD.has_default_value = false
GCEXPENSESTAT_RESULT_FIELD.default_value = 0
GCEXPENSESTAT_RESULT_FIELD.type = 5
GCEXPENSESTAT_RESULT_FIELD.cpp_type = 1

GCEXPENSESTAT_MSG_FIELD.name = "msg"
GCEXPENSESTAT_MSG_FIELD.full_name = ".prootc.gcexpensestat.msg"
GCEXPENSESTAT_MSG_FIELD.number = 2
GCEXPENSESTAT_MSG_FIELD.index = 1
GCEXPENSESTAT_MSG_FIELD.label = 1
GCEXPENSESTAT_MSG_FIELD.has_default_value = false
GCEXPENSESTAT_MSG_FIELD.default_value = ""
GCEXPENSESTAT_MSG_FIELD.type = 9
GCEXPENSESTAT_MSG_FIELD.cpp_type = 9

GCEXPENSESTAT_TOTAL_FIELD.name = "total"
GCEXPENSESTAT_TOTAL_FIELD.full_name = ".prootc.gcexpensestat.total"
GCEXPENSESTAT_TOTAL_FIELD.number = 3
GCEXPENSESTAT_TOTAL_FIELD.index = 2
GCEXPENSESTAT_TOTAL_FIELD.label = 1
GCEXPENSESTAT_TOTAL_FIELD.has_default_value = false
GCEXPENSESTAT_TOTAL_FIELD.default_value = ""
GCEXPENSESTAT_TOTAL_FIELD.type = 9
GCEXPENSESTAT_TOTAL_FIELD.cpp_type = 9

GCEXPENSESTAT_SELL_FIELD.name = "sell"
GCEXPENSESTAT_SELL_FIELD.full_name = ".prootc.gcexpensestat.sell"
GCEXPENSESTAT_SELL_FIELD.number = 4
GCEXPENSESTAT_SELL_FIELD.index = 3
GCEXPENSESTAT_SELL_FIELD.label = 1
GCEXPENSESTAT_SELL_FIELD.has_default_value = false
GCEXPENSESTAT_SELL_FIELD.default_value = ""
GCEXPENSESTAT_SELL_FIELD.type = 9
GCEXPENSESTAT_SELL_FIELD.cpp_type = 9

GCEXPENSESTAT_WITHDRAW_FIELD.name = "withdraw"
GCEXPENSESTAT_WITHDRAW_FIELD.full_name = ".prootc.gcexpensestat.withdraw"
GCEXPENSESTAT_WITHDRAW_FIELD.number = 5
GCEXPENSESTAT_WITHDRAW_FIELD.index = 4
GCEXPENSESTAT_WITHDRAW_FIELD.label = 1
GCEXPENSESTAT_WITHDRAW_FIELD.has_default_value = false
GCEXPENSESTAT_WITHDRAW_FIELD.default_value = ""
GCEXPENSESTAT_WITHDRAW_FIELD.type = 9
GCEXPENSESTAT_WITHDRAW_FIELD.cpp_type = 9

GCEXPENSESTAT_ROLLOUT_FIELD.name = "rollout"
GCEXPENSESTAT_ROLLOUT_FIELD.full_name = ".prootc.gcexpensestat.rollout"
GCEXPENSESTAT_ROLLOUT_FIELD.number = 6
GCEXPENSESTAT_ROLLOUT_FIELD.index = 5
GCEXPENSESTAT_ROLLOUT_FIELD.label = 1
GCEXPENSESTAT_ROLLOUT_FIELD.has_default_value = false
GCEXPENSESTAT_ROLLOUT_FIELD.default_value = ""
GCEXPENSESTAT_ROLLOUT_FIELD.type = 9
GCEXPENSESTAT_ROLLOUT_FIELD.cpp_type = 9

GCEXPENSESTAT_FEE_FIELD.name = "fee"
GCEXPENSESTAT_FEE_FIELD.full_name = ".prootc.gcexpensestat.fee"
GCEXPENSESTAT_FEE_FIELD.number = 7
GCEXPENSESTAT_FEE_FIELD.index = 6
GCEXPENSESTAT_FEE_FIELD.label = 1
GCEXPENSESTAT_FEE_FIELD.has_default_value = false
GCEXPENSESTAT_FEE_FIELD.default_value = ""
GCEXPENSESTAT_FEE_FIELD.type = 9
GCEXPENSESTAT_FEE_FIELD.cpp_type = 9

GCEXPENSESTAT.name = "gcexpensestat"
GCEXPENSESTAT.full_name = ".prootc.gcexpensestat"
GCEXPENSESTAT.nested_types = {}
GCEXPENSESTAT.enum_types = {}
GCEXPENSESTAT.fields = {GCEXPENSESTAT_RESULT_FIELD, GCEXPENSESTAT_MSG_FIELD, GCEXPENSESTAT_TOTAL_FIELD, GCEXPENSESTAT_SELL_FIELD, GCEXPENSESTAT_WITHDRAW_FIELD, GCEXPENSESTAT_ROLLOUT_FIELD, GCEXPENSESTAT_FEE_FIELD}
GCEXPENSESTAT.is_extendable = false
GCEXPENSESTAT.extensions = {}
CGTODAYFLOW_USERID_FIELD.name = "userid"
CGTODAYFLOW_USERID_FIELD.full_name = ".prootc.cgtodayflow.userid"
CGTODAYFLOW_USERID_FIELD.number = 1
CGTODAYFLOW_USERID_FIELD.index = 0
CGTODAYFLOW_USERID_FIELD.label = 1
CGTODAYFLOW_USERID_FIELD.has_default_value = false
CGTODAYFLOW_USERID_FIELD.default_value = 0
CGTODAYFLOW_USERID_FIELD.type = 5
CGTODAYFLOW_USERID_FIELD.cpp_type = 1

CGTODAYFLOW_COINID_FIELD.name = "coinid"
CGTODAYFLOW_COINID_FIELD.full_name = ".prootc.cgtodayflow.coinid"
CGTODAYFLOW_COINID_FIELD.number = 2
CGTODAYFLOW_COINID_FIELD.index = 1
CGTODAYFLOW_COINID_FIELD.label = 1
CGTODAYFLOW_COINID_FIELD.has_default_value = false
CGTODAYFLOW_COINID_FIELD.default_value = 0
CGTODAYFLOW_COINID_FIELD.type = 5
CGTODAYFLOW_COINID_FIELD.cpp_type = 1

CGTODAYFLOW.name = "cgtodayflow"
CGTODAYFLOW.full_name = ".prootc.cgtodayflow"
CGTODAYFLOW.nested_types = {}
CGTODAYFLOW.enum_types = {}
CGTODAYFLOW.fields = {CGTODAYFLOW_USERID_FIELD, CGTODAYFLOW_COINID_FIELD}
CGTODAYFLOW.is_extendable = false
CGTODAYFLOW.extensions = {}
GCTODAYFLOW_RESULT_FIELD.name = "result"
GCTODAYFLOW_RESULT_FIELD.full_name = ".prootc.gctodayflow.result"
GCTODAYFLOW_RESULT_FIELD.number = 1
GCTODAYFLOW_RESULT_FIELD.index = 0
GCTODAYFLOW_RESULT_FIELD.label = 1
GCTODAYFLOW_RESULT_FIELD.has_default_value = false
GCTODAYFLOW_RESULT_FIELD.default_value = 0
GCTODAYFLOW_RESULT_FIELD.type = 5
GCTODAYFLOW_RESULT_FIELD.cpp_type = 1

GCTODAYFLOW_MSG_FIELD.name = "msg"
GCTODAYFLOW_MSG_FIELD.full_name = ".prootc.gctodayflow.msg"
GCTODAYFLOW_MSG_FIELD.number = 2
GCTODAYFLOW_MSG_FIELD.index = 1
GCTODAYFLOW_MSG_FIELD.label = 1
GCTODAYFLOW_MSG_FIELD.has_default_value = false
GCTODAYFLOW_MSG_FIELD.default_value = ""
GCTODAYFLOW_MSG_FIELD.type = 9
GCTODAYFLOW_MSG_FIELD.cpp_type = 9

GCTODAYFLOW_TODAYSELL_FIELD.name = "todaySell"
GCTODAYFLOW_TODAYSELL_FIELD.full_name = ".prootc.gctodayflow.todaySell"
GCTODAYFLOW_TODAYSELL_FIELD.number = 3
GCTODAYFLOW_TODAYSELL_FIELD.index = 2
GCTODAYFLOW_TODAYSELL_FIELD.label = 1
GCTODAYFLOW_TODAYSELL_FIELD.has_default_value = false
GCTODAYFLOW_TODAYSELL_FIELD.default_value = ""
GCTODAYFLOW_TODAYSELL_FIELD.type = 9
GCTODAYFLOW_TODAYSELL_FIELD.cpp_type = 9

GCTODAYFLOW_TODAYBUY_FIELD.name = "todaybuy"
GCTODAYFLOW_TODAYBUY_FIELD.full_name = ".prootc.gctodayflow.todaybuy"
GCTODAYFLOW_TODAYBUY_FIELD.number = 4
GCTODAYFLOW_TODAYBUY_FIELD.index = 3
GCTODAYFLOW_TODAYBUY_FIELD.label = 1
GCTODAYFLOW_TODAYBUY_FIELD.has_default_value = false
GCTODAYFLOW_TODAYBUY_FIELD.default_value = ""
GCTODAYFLOW_TODAYBUY_FIELD.type = 9
GCTODAYFLOW_TODAYBUY_FIELD.cpp_type = 9

GCTODAYFLOW_AVERAGETIME_FIELD.name = "averagetime"
GCTODAYFLOW_AVERAGETIME_FIELD.full_name = ".prootc.gctodayflow.averagetime"
GCTODAYFLOW_AVERAGETIME_FIELD.number = 5
GCTODAYFLOW_AVERAGETIME_FIELD.index = 4
GCTODAYFLOW_AVERAGETIME_FIELD.label = 1
GCTODAYFLOW_AVERAGETIME_FIELD.has_default_value = false
GCTODAYFLOW_AVERAGETIME_FIELD.default_value = 0
GCTODAYFLOW_AVERAGETIME_FIELD.type = 5
GCTODAYFLOW_AVERAGETIME_FIELD.cpp_type = 1

GCTODAYFLOW_SUCCESSRATE_FIELD.name = "successrate"
GCTODAYFLOW_SUCCESSRATE_FIELD.full_name = ".prootc.gctodayflow.successrate"
GCTODAYFLOW_SUCCESSRATE_FIELD.number = 6
GCTODAYFLOW_SUCCESSRATE_FIELD.index = 5
GCTODAYFLOW_SUCCESSRATE_FIELD.label = 1
GCTODAYFLOW_SUCCESSRATE_FIELD.has_default_value = false
GCTODAYFLOW_SUCCESSRATE_FIELD.default_value = ""
GCTODAYFLOW_SUCCESSRATE_FIELD.type = 9
GCTODAYFLOW_SUCCESSRATE_FIELD.cpp_type = 9

GCTODAYFLOW_EXCHANGERATE_FIELD.name = "exchangerate"
GCTODAYFLOW_EXCHANGERATE_FIELD.full_name = ".prootc.gctodayflow.exchangerate"
GCTODAYFLOW_EXCHANGERATE_FIELD.number = 7
GCTODAYFLOW_EXCHANGERATE_FIELD.index = 6
GCTODAYFLOW_EXCHANGERATE_FIELD.label = 1
GCTODAYFLOW_EXCHANGERATE_FIELD.has_default_value = false
GCTODAYFLOW_EXCHANGERATE_FIELD.default_value = ""
GCTODAYFLOW_EXCHANGERATE_FIELD.type = 9
GCTODAYFLOW_EXCHANGERATE_FIELD.cpp_type = 9

GCTODAYFLOW_COINID_FIELD.name = "coinid"
GCTODAYFLOW_COINID_FIELD.full_name = ".prootc.gctodayflow.coinid"
GCTODAYFLOW_COINID_FIELD.number = 8
GCTODAYFLOW_COINID_FIELD.index = 7
GCTODAYFLOW_COINID_FIELD.label = 1
GCTODAYFLOW_COINID_FIELD.has_default_value = false
GCTODAYFLOW_COINID_FIELD.default_value = 0
GCTODAYFLOW_COINID_FIELD.type = 5
GCTODAYFLOW_COINID_FIELD.cpp_type = 1

GCTODAYFLOW.name = "gctodayflow"
GCTODAYFLOW.full_name = ".prootc.gctodayflow"
GCTODAYFLOW.nested_types = {}
GCTODAYFLOW.enum_types = {}
GCTODAYFLOW.fields = {GCTODAYFLOW_RESULT_FIELD, GCTODAYFLOW_MSG_FIELD, GCTODAYFLOW_TODAYSELL_FIELD, GCTODAYFLOW_TODAYBUY_FIELD, GCTODAYFLOW_AVERAGETIME_FIELD, GCTODAYFLOW_SUCCESSRATE_FIELD, GCTODAYFLOW_EXCHANGERATE_FIELD, GCTODAYFLOW_COINID_FIELD}
GCTODAYFLOW.is_extendable = false
GCTODAYFLOW.extensions = {}
CGDEALRECDLIST_USERID_FIELD.name = "userid"
CGDEALRECDLIST_USERID_FIELD.full_name = ".prootc.cgdealrecdlist.userid"
CGDEALRECDLIST_USERID_FIELD.number = 1
CGDEALRECDLIST_USERID_FIELD.index = 0
CGDEALRECDLIST_USERID_FIELD.label = 1
CGDEALRECDLIST_USERID_FIELD.has_default_value = false
CGDEALRECDLIST_USERID_FIELD.default_value = 0
CGDEALRECDLIST_USERID_FIELD.type = 5
CGDEALRECDLIST_USERID_FIELD.cpp_type = 1

CGDEALRECDLIST_TYPE_FIELD.name = "type"
CGDEALRECDLIST_TYPE_FIELD.full_name = ".prootc.cgdealrecdlist.type"
CGDEALRECDLIST_TYPE_FIELD.number = 2
CGDEALRECDLIST_TYPE_FIELD.index = 1
CGDEALRECDLIST_TYPE_FIELD.label = 1
CGDEALRECDLIST_TYPE_FIELD.has_default_value = false
CGDEALRECDLIST_TYPE_FIELD.default_value = 0
CGDEALRECDLIST_TYPE_FIELD.type = 5
CGDEALRECDLIST_TYPE_FIELD.cpp_type = 1

CGDEALRECDLIST_STATUS_FIELD.name = "status"
CGDEALRECDLIST_STATUS_FIELD.full_name = ".prootc.cgdealrecdlist.status"
CGDEALRECDLIST_STATUS_FIELD.number = 3
CGDEALRECDLIST_STATUS_FIELD.index = 2
CGDEALRECDLIST_STATUS_FIELD.label = 1
CGDEALRECDLIST_STATUS_FIELD.has_default_value = false
CGDEALRECDLIST_STATUS_FIELD.default_value = 0
CGDEALRECDLIST_STATUS_FIELD.type = 5
CGDEALRECDLIST_STATUS_FIELD.cpp_type = 1

CGDEALRECDLIST_PAYTYPE_FIELD.name = "paytype"
CGDEALRECDLIST_PAYTYPE_FIELD.full_name = ".prootc.cgdealrecdlist.paytype"
CGDEALRECDLIST_PAYTYPE_FIELD.number = 4
CGDEALRECDLIST_PAYTYPE_FIELD.index = 3
CGDEALRECDLIST_PAYTYPE_FIELD.label = 1
CGDEALRECDLIST_PAYTYPE_FIELD.has_default_value = false
CGDEALRECDLIST_PAYTYPE_FIELD.default_value = 0
CGDEALRECDLIST_PAYTYPE_FIELD.type = 5
CGDEALRECDLIST_PAYTYPE_FIELD.cpp_type = 1

CGDEALRECDLIST_PAGENUM_FIELD.name = "pagenum"
CGDEALRECDLIST_PAGENUM_FIELD.full_name = ".prootc.cgdealrecdlist.pagenum"
CGDEALRECDLIST_PAGENUM_FIELD.number = 5
CGDEALRECDLIST_PAGENUM_FIELD.index = 4
CGDEALRECDLIST_PAGENUM_FIELD.label = 1
CGDEALRECDLIST_PAGENUM_FIELD.has_default_value = false
CGDEALRECDLIST_PAGENUM_FIELD.default_value = 0
CGDEALRECDLIST_PAGENUM_FIELD.type = 5
CGDEALRECDLIST_PAGENUM_FIELD.cpp_type = 1

CGDEALRECDLIST_PAGESIZE_FIELD.name = "pagesize"
CGDEALRECDLIST_PAGESIZE_FIELD.full_name = ".prootc.cgdealrecdlist.pagesize"
CGDEALRECDLIST_PAGESIZE_FIELD.number = 6
CGDEALRECDLIST_PAGESIZE_FIELD.index = 5
CGDEALRECDLIST_PAGESIZE_FIELD.label = 1
CGDEALRECDLIST_PAGESIZE_FIELD.has_default_value = false
CGDEALRECDLIST_PAGESIZE_FIELD.default_value = 0
CGDEALRECDLIST_PAGESIZE_FIELD.type = 5
CGDEALRECDLIST_PAGESIZE_FIELD.cpp_type = 1

CGDEALRECDLIST.name = "cgdealrecdlist"
CGDEALRECDLIST.full_name = ".prootc.cgdealrecdlist"
CGDEALRECDLIST.nested_types = {}
CGDEALRECDLIST.enum_types = {}
CGDEALRECDLIST.fields = {CGDEALRECDLIST_USERID_FIELD, CGDEALRECDLIST_TYPE_FIELD, CGDEALRECDLIST_STATUS_FIELD, CGDEALRECDLIST_PAYTYPE_FIELD, CGDEALRECDLIST_PAGENUM_FIELD, CGDEALRECDLIST_PAGESIZE_FIELD}
CGDEALRECDLIST.is_extendable = false
CGDEALRECDLIST.extensions = {}
GEDEALRECDLIST_RESULT_FIELD.name = "result"
GEDEALRECDLIST_RESULT_FIELD.full_name = ".prootc.gedealrecdlist.result"
GEDEALRECDLIST_RESULT_FIELD.number = 1
GEDEALRECDLIST_RESULT_FIELD.index = 0
GEDEALRECDLIST_RESULT_FIELD.label = 1
GEDEALRECDLIST_RESULT_FIELD.has_default_value = false
GEDEALRECDLIST_RESULT_FIELD.default_value = 0
GEDEALRECDLIST_RESULT_FIELD.type = 5
GEDEALRECDLIST_RESULT_FIELD.cpp_type = 1

GEDEALRECDLIST_MSG_FIELD.name = "msg"
GEDEALRECDLIST_MSG_FIELD.full_name = ".prootc.gedealrecdlist.msg"
GEDEALRECDLIST_MSG_FIELD.number = 2
GEDEALRECDLIST_MSG_FIELD.index = 1
GEDEALRECDLIST_MSG_FIELD.label = 1
GEDEALRECDLIST_MSG_FIELD.has_default_value = false
GEDEALRECDLIST_MSG_FIELD.default_value = ""
GEDEALRECDLIST_MSG_FIELD.type = 9
GEDEALRECDLIST_MSG_FIELD.cpp_type = 9

GEDEALRECDLIST_TYPE_FIELD.name = "type"
GEDEALRECDLIST_TYPE_FIELD.full_name = ".prootc.gedealrecdlist.type"
GEDEALRECDLIST_TYPE_FIELD.number = 3
GEDEALRECDLIST_TYPE_FIELD.index = 2
GEDEALRECDLIST_TYPE_FIELD.label = 1
GEDEALRECDLIST_TYPE_FIELD.has_default_value = false
GEDEALRECDLIST_TYPE_FIELD.default_value = 0
GEDEALRECDLIST_TYPE_FIELD.type = 5
GEDEALRECDLIST_TYPE_FIELD.cpp_type = 1

GEDEALRECDLIST_STATUS_FIELD.name = "status"
GEDEALRECDLIST_STATUS_FIELD.full_name = ".prootc.gedealrecdlist.status"
GEDEALRECDLIST_STATUS_FIELD.number = 4
GEDEALRECDLIST_STATUS_FIELD.index = 3
GEDEALRECDLIST_STATUS_FIELD.label = 1
GEDEALRECDLIST_STATUS_FIELD.has_default_value = false
GEDEALRECDLIST_STATUS_FIELD.default_value = 0
GEDEALRECDLIST_STATUS_FIELD.type = 5
GEDEALRECDLIST_STATUS_FIELD.cpp_type = 1

GEDEALRECDLIST_PAYTYPE_FIELD.name = "paytype"
GEDEALRECDLIST_PAYTYPE_FIELD.full_name = ".prootc.gedealrecdlist.paytype"
GEDEALRECDLIST_PAYTYPE_FIELD.number = 5
GEDEALRECDLIST_PAYTYPE_FIELD.index = 4
GEDEALRECDLIST_PAYTYPE_FIELD.label = 1
GEDEALRECDLIST_PAYTYPE_FIELD.has_default_value = false
GEDEALRECDLIST_PAYTYPE_FIELD.default_value = 0
GEDEALRECDLIST_PAYTYPE_FIELD.type = 5
GEDEALRECDLIST_PAYTYPE_FIELD.cpp_type = 1

GEDEALRECDLIST_PAGENUM_FIELD.name = "pagenum"
GEDEALRECDLIST_PAGENUM_FIELD.full_name = ".prootc.gedealrecdlist.pagenum"
GEDEALRECDLIST_PAGENUM_FIELD.number = 6
GEDEALRECDLIST_PAGENUM_FIELD.index = 5
GEDEALRECDLIST_PAGENUM_FIELD.label = 1
GEDEALRECDLIST_PAGENUM_FIELD.has_default_value = false
GEDEALRECDLIST_PAGENUM_FIELD.default_value = 0
GEDEALRECDLIST_PAGENUM_FIELD.type = 5
GEDEALRECDLIST_PAGENUM_FIELD.cpp_type = 1

GEDEALRECDLIST_PAGESIZE_FIELD.name = "pagesize"
GEDEALRECDLIST_PAGESIZE_FIELD.full_name = ".prootc.gedealrecdlist.pagesize"
GEDEALRECDLIST_PAGESIZE_FIELD.number = 7
GEDEALRECDLIST_PAGESIZE_FIELD.index = 6
GEDEALRECDLIST_PAGESIZE_FIELD.label = 1
GEDEALRECDLIST_PAGESIZE_FIELD.has_default_value = false
GEDEALRECDLIST_PAGESIZE_FIELD.default_value = 0
GEDEALRECDLIST_PAGESIZE_FIELD.type = 5
GEDEALRECDLIST_PAGESIZE_FIELD.cpp_type = 1

GEDEALRECDLIST_INFOLIST_FIELD.name = "infolist"
GEDEALRECDLIST_INFOLIST_FIELD.full_name = ".prootc.gedealrecdlist.infolist"
GEDEALRECDLIST_INFOLIST_FIELD.number = 8
GEDEALRECDLIST_INFOLIST_FIELD.index = 7
GEDEALRECDLIST_INFOLIST_FIELD.label = 3
GEDEALRECDLIST_INFOLIST_FIELD.has_default_value = false
GEDEALRECDLIST_INFOLIST_FIELD.default_value = {}
GEDEALRECDLIST_INFOLIST_FIELD.message_type = ST_ORDER_PB_CUSTOMERORDERINFO
GEDEALRECDLIST_INFOLIST_FIELD.type = 11
GEDEALRECDLIST_INFOLIST_FIELD.cpp_type = 10

GEDEALRECDLIST_ORDERCOUNT_FIELD.name = "ordercount"
GEDEALRECDLIST_ORDERCOUNT_FIELD.full_name = ".prootc.gedealrecdlist.ordercount"
GEDEALRECDLIST_ORDERCOUNT_FIELD.number = 9
GEDEALRECDLIST_ORDERCOUNT_FIELD.index = 8
GEDEALRECDLIST_ORDERCOUNT_FIELD.label = 1
GEDEALRECDLIST_ORDERCOUNT_FIELD.has_default_value = false
GEDEALRECDLIST_ORDERCOUNT_FIELD.default_value = 0
GEDEALRECDLIST_ORDERCOUNT_FIELD.type = 5
GEDEALRECDLIST_ORDERCOUNT_FIELD.cpp_type = 1

GEDEALRECDLIST_FINISHORDERCOUNT_FIELD.name = "finishordercount"
GEDEALRECDLIST_FINISHORDERCOUNT_FIELD.full_name = ".prootc.gedealrecdlist.finishordercount"
GEDEALRECDLIST_FINISHORDERCOUNT_FIELD.number = 10
GEDEALRECDLIST_FINISHORDERCOUNT_FIELD.index = 9
GEDEALRECDLIST_FINISHORDERCOUNT_FIELD.label = 1
GEDEALRECDLIST_FINISHORDERCOUNT_FIELD.has_default_value = false
GEDEALRECDLIST_FINISHORDERCOUNT_FIELD.default_value = 0
GEDEALRECDLIST_FINISHORDERCOUNT_FIELD.type = 5
GEDEALRECDLIST_FINISHORDERCOUNT_FIELD.cpp_type = 1

GEDEALRECDLIST.name = "gedealrecdlist"
GEDEALRECDLIST.full_name = ".prootc.gedealrecdlist"
GEDEALRECDLIST.nested_types = {}
GEDEALRECDLIST.enum_types = {}
GEDEALRECDLIST.fields = {GEDEALRECDLIST_RESULT_FIELD, GEDEALRECDLIST_MSG_FIELD, GEDEALRECDLIST_TYPE_FIELD, GEDEALRECDLIST_STATUS_FIELD, GEDEALRECDLIST_PAYTYPE_FIELD, GEDEALRECDLIST_PAGENUM_FIELD, GEDEALRECDLIST_PAGESIZE_FIELD, GEDEALRECDLIST_INFOLIST_FIELD, GEDEALRECDLIST_ORDERCOUNT_FIELD, GEDEALRECDLIST_FINISHORDERCOUNT_FIELD}
GEDEALRECDLIST.is_extendable = false
GEDEALRECDLIST.extensions = {}
CGWORKINGLIST_USERID_FIELD.name = "userid"
CGWORKINGLIST_USERID_FIELD.full_name = ".prootc.cgworkinglist.userid"
CGWORKINGLIST_USERID_FIELD.number = 1
CGWORKINGLIST_USERID_FIELD.index = 0
CGWORKINGLIST_USERID_FIELD.label = 1
CGWORKINGLIST_USERID_FIELD.has_default_value = false
CGWORKINGLIST_USERID_FIELD.default_value = 0
CGWORKINGLIST_USERID_FIELD.type = 5
CGWORKINGLIST_USERID_FIELD.cpp_type = 1

CGWORKINGLIST.name = "cgworkinglist"
CGWORKINGLIST.full_name = ".prootc.cgworkinglist"
CGWORKINGLIST.nested_types = {}
CGWORKINGLIST.enum_types = {}
CGWORKINGLIST.fields = {CGWORKINGLIST_USERID_FIELD}
CGWORKINGLIST.is_extendable = false
CGWORKINGLIST.extensions = {}
GCWORKINGLIST_RESULT_FIELD.name = "result"
GCWORKINGLIST_RESULT_FIELD.full_name = ".prootc.gcworkinglist.result"
GCWORKINGLIST_RESULT_FIELD.number = 1
GCWORKINGLIST_RESULT_FIELD.index = 0
GCWORKINGLIST_RESULT_FIELD.label = 1
GCWORKINGLIST_RESULT_FIELD.has_default_value = false
GCWORKINGLIST_RESULT_FIELD.default_value = 0
GCWORKINGLIST_RESULT_FIELD.type = 5
GCWORKINGLIST_RESULT_FIELD.cpp_type = 1

GCWORKINGLIST_MSG_FIELD.name = "msg"
GCWORKINGLIST_MSG_FIELD.full_name = ".prootc.gcworkinglist.msg"
GCWORKINGLIST_MSG_FIELD.number = 2
GCWORKINGLIST_MSG_FIELD.index = 1
GCWORKINGLIST_MSG_FIELD.label = 1
GCWORKINGLIST_MSG_FIELD.has_default_value = false
GCWORKINGLIST_MSG_FIELD.default_value = ""
GCWORKINGLIST_MSG_FIELD.type = 9
GCWORKINGLIST_MSG_FIELD.cpp_type = 9

GCWORKINGLIST_INFOLIST_FIELD.name = "infolist"
GCWORKINGLIST_INFOLIST_FIELD.full_name = ".prootc.gcworkinglist.infolist"
GCWORKINGLIST_INFOLIST_FIELD.number = 3
GCWORKINGLIST_INFOLIST_FIELD.index = 2
GCWORKINGLIST_INFOLIST_FIELD.label = 3
GCWORKINGLIST_INFOLIST_FIELD.has_default_value = false
GCWORKINGLIST_INFOLIST_FIELD.default_value = {}
GCWORKINGLIST_INFOLIST_FIELD.message_type = ST_ORDER_PB_CUSTOMERORDERINFO
GCWORKINGLIST_INFOLIST_FIELD.type = 11
GCWORKINGLIST_INFOLIST_FIELD.cpp_type = 10

GCWORKINGLIST.name = "gcworkinglist"
GCWORKINGLIST.full_name = ".prootc.gcworkinglist"
GCWORKINGLIST.nested_types = {}
GCWORKINGLIST.enum_types = {}
GCWORKINGLIST.fields = {GCWORKINGLIST_RESULT_FIELD, GCWORKINGLIST_MSG_FIELD, GCWORKINGLIST_INFOLIST_FIELD}
GCWORKINGLIST.is_extendable = false
GCWORKINGLIST.extensions = {}
CGWITHDRAW_USERID_FIELD.name = "userid"
CGWITHDRAW_USERID_FIELD.full_name = ".prootc.cgwithdraw.userid"
CGWITHDRAW_USERID_FIELD.number = 1
CGWITHDRAW_USERID_FIELD.index = 0
CGWITHDRAW_USERID_FIELD.label = 1
CGWITHDRAW_USERID_FIELD.has_default_value = false
CGWITHDRAW_USERID_FIELD.default_value = 0
CGWITHDRAW_USERID_FIELD.type = 5
CGWITHDRAW_USERID_FIELD.cpp_type = 1

CGWITHDRAW_ORDERTYPE_FIELD.name = "ordertype"
CGWITHDRAW_ORDERTYPE_FIELD.full_name = ".prootc.cgwithdraw.ordertype"
CGWITHDRAW_ORDERTYPE_FIELD.number = 2
CGWITHDRAW_ORDERTYPE_FIELD.index = 1
CGWITHDRAW_ORDERTYPE_FIELD.label = 1
CGWITHDRAW_ORDERTYPE_FIELD.has_default_value = false
CGWITHDRAW_ORDERTYPE_FIELD.default_value = 0
CGWITHDRAW_ORDERTYPE_FIELD.type = 5
CGWITHDRAW_ORDERTYPE_FIELD.cpp_type = 1

CGWITHDRAW_TOADDRESS_FIELD.name = "toaddress"
CGWITHDRAW_TOADDRESS_FIELD.full_name = ".prootc.cgwithdraw.toaddress"
CGWITHDRAW_TOADDRESS_FIELD.number = 3
CGWITHDRAW_TOADDRESS_FIELD.index = 2
CGWITHDRAW_TOADDRESS_FIELD.label = 1
CGWITHDRAW_TOADDRESS_FIELD.has_default_value = false
CGWITHDRAW_TOADDRESS_FIELD.default_value = ""
CGWITHDRAW_TOADDRESS_FIELD.type = 9
CGWITHDRAW_TOADDRESS_FIELD.cpp_type = 9

CGWITHDRAW_CURRENCYTYPE_FIELD.name = "currencytype"
CGWITHDRAW_CURRENCYTYPE_FIELD.full_name = ".prootc.cgwithdraw.currencytype"
CGWITHDRAW_CURRENCYTYPE_FIELD.number = 4
CGWITHDRAW_CURRENCYTYPE_FIELD.index = 3
CGWITHDRAW_CURRENCYTYPE_FIELD.label = 1
CGWITHDRAW_CURRENCYTYPE_FIELD.has_default_value = false
CGWITHDRAW_CURRENCYTYPE_FIELD.default_value = 0
CGWITHDRAW_CURRENCYTYPE_FIELD.type = 5
CGWITHDRAW_CURRENCYTYPE_FIELD.cpp_type = 1

CGWITHDRAW_CURRENCYAMOUNT_FIELD.name = "currencyamount"
CGWITHDRAW_CURRENCYAMOUNT_FIELD.full_name = ".prootc.cgwithdraw.currencyamount"
CGWITHDRAW_CURRENCYAMOUNT_FIELD.number = 5
CGWITHDRAW_CURRENCYAMOUNT_FIELD.index = 4
CGWITHDRAW_CURRENCYAMOUNT_FIELD.label = 1
CGWITHDRAW_CURRENCYAMOUNT_FIELD.has_default_value = false
CGWITHDRAW_CURRENCYAMOUNT_FIELD.default_value = ""
CGWITHDRAW_CURRENCYAMOUNT_FIELD.type = 9
CGWITHDRAW_CURRENCYAMOUNT_FIELD.cpp_type = 9

CGWITHDRAW_SUBJECT_FIELD.name = "subject"
CGWITHDRAW_SUBJECT_FIELD.full_name = ".prootc.cgwithdraw.subject"
CGWITHDRAW_SUBJECT_FIELD.number = 6
CGWITHDRAW_SUBJECT_FIELD.index = 5
CGWITHDRAW_SUBJECT_FIELD.label = 1
CGWITHDRAW_SUBJECT_FIELD.has_default_value = false
CGWITHDRAW_SUBJECT_FIELD.default_value = ""
CGWITHDRAW_SUBJECT_FIELD.type = 9
CGWITHDRAW_SUBJECT_FIELD.cpp_type = 9

CGWITHDRAW_VERSION_FIELD.name = "version"
CGWITHDRAW_VERSION_FIELD.full_name = ".prootc.cgwithdraw.version"
CGWITHDRAW_VERSION_FIELD.number = 7
CGWITHDRAW_VERSION_FIELD.index = 6
CGWITHDRAW_VERSION_FIELD.label = 1
CGWITHDRAW_VERSION_FIELD.has_default_value = false
CGWITHDRAW_VERSION_FIELD.default_value = ""
CGWITHDRAW_VERSION_FIELD.type = 9
CGWITHDRAW_VERSION_FIELD.cpp_type = 9

CGWITHDRAW_NOTIFYURL_FIELD.name = "notifyurl"
CGWITHDRAW_NOTIFYURL_FIELD.full_name = ".prootc.cgwithdraw.notifyurl"
CGWITHDRAW_NOTIFYURL_FIELD.number = 8
CGWITHDRAW_NOTIFYURL_FIELD.index = 7
CGWITHDRAW_NOTIFYURL_FIELD.label = 1
CGWITHDRAW_NOTIFYURL_FIELD.has_default_value = false
CGWITHDRAW_NOTIFYURL_FIELD.default_value = ""
CGWITHDRAW_NOTIFYURL_FIELD.type = 9
CGWITHDRAW_NOTIFYURL_FIELD.cpp_type = 9

CGWITHDRAW_BODY_FIELD.name = "body"
CGWITHDRAW_BODY_FIELD.full_name = ".prootc.cgwithdraw.body"
CGWITHDRAW_BODY_FIELD.number = 9
CGWITHDRAW_BODY_FIELD.index = 8
CGWITHDRAW_BODY_FIELD.label = 1
CGWITHDRAW_BODY_FIELD.has_default_value = false
CGWITHDRAW_BODY_FIELD.default_value = ""
CGWITHDRAW_BODY_FIELD.type = 9
CGWITHDRAW_BODY_FIELD.cpp_type = 9

CGWITHDRAW_OUTTRADENO_FIELD.name = "outtradeno"
CGWITHDRAW_OUTTRADENO_FIELD.full_name = ".prootc.cgwithdraw.outtradeno"
CGWITHDRAW_OUTTRADENO_FIELD.number = 10
CGWITHDRAW_OUTTRADENO_FIELD.index = 9
CGWITHDRAW_OUTTRADENO_FIELD.label = 1
CGWITHDRAW_OUTTRADENO_FIELD.has_default_value = false
CGWITHDRAW_OUTTRADENO_FIELD.default_value = ""
CGWITHDRAW_OUTTRADENO_FIELD.type = 9
CGWITHDRAW_OUTTRADENO_FIELD.cpp_type = 9

CGWITHDRAW_FUNDPWD_FIELD.name = "fundpwd"
CGWITHDRAW_FUNDPWD_FIELD.full_name = ".prootc.cgwithdraw.fundpwd"
CGWITHDRAW_FUNDPWD_FIELD.number = 11
CGWITHDRAW_FUNDPWD_FIELD.index = 10
CGWITHDRAW_FUNDPWD_FIELD.label = 1
CGWITHDRAW_FUNDPWD_FIELD.has_default_value = false
CGWITHDRAW_FUNDPWD_FIELD.default_value = ""
CGWITHDRAW_FUNDPWD_FIELD.type = 9
CGWITHDRAW_FUNDPWD_FIELD.cpp_type = 9

CGWITHDRAW_ADDRTYPE_FIELD.name = "addrtype"
CGWITHDRAW_ADDRTYPE_FIELD.full_name = ".prootc.cgwithdraw.addrtype"
CGWITHDRAW_ADDRTYPE_FIELD.number = 12
CGWITHDRAW_ADDRTYPE_FIELD.index = 11
CGWITHDRAW_ADDRTYPE_FIELD.label = 1
CGWITHDRAW_ADDRTYPE_FIELD.has_default_value = false
CGWITHDRAW_ADDRTYPE_FIELD.default_value = 0
CGWITHDRAW_ADDRTYPE_FIELD.type = 5
CGWITHDRAW_ADDRTYPE_FIELD.cpp_type = 1

CGWITHDRAW_TOUSERID_FIELD.name = "touserid"
CGWITHDRAW_TOUSERID_FIELD.full_name = ".prootc.cgwithdraw.touserid"
CGWITHDRAW_TOUSERID_FIELD.number = 13
CGWITHDRAW_TOUSERID_FIELD.index = 12
CGWITHDRAW_TOUSERID_FIELD.label = 1
CGWITHDRAW_TOUSERID_FIELD.has_default_value = false
CGWITHDRAW_TOUSERID_FIELD.default_value = 0
CGWITHDRAW_TOUSERID_FIELD.type = 5
CGWITHDRAW_TOUSERID_FIELD.cpp_type = 1

CGWITHDRAW.name = "cgwithdraw"
CGWITHDRAW.full_name = ".prootc.cgwithdraw"
CGWITHDRAW.nested_types = {}
CGWITHDRAW.enum_types = {}
CGWITHDRAW.fields = {CGWITHDRAW_USERID_FIELD, CGWITHDRAW_ORDERTYPE_FIELD, CGWITHDRAW_TOADDRESS_FIELD, CGWITHDRAW_CURRENCYTYPE_FIELD, CGWITHDRAW_CURRENCYAMOUNT_FIELD, CGWITHDRAW_SUBJECT_FIELD, CGWITHDRAW_VERSION_FIELD, CGWITHDRAW_NOTIFYURL_FIELD, CGWITHDRAW_BODY_FIELD, CGWITHDRAW_OUTTRADENO_FIELD, CGWITHDRAW_FUNDPWD_FIELD, CGWITHDRAW_ADDRTYPE_FIELD, CGWITHDRAW_TOUSERID_FIELD}
CGWITHDRAW.is_extendable = false
CGWITHDRAW.extensions = {}
GCWITHDRAW_RESULT_FIELD.name = "result"
GCWITHDRAW_RESULT_FIELD.full_name = ".prootc.gcwithdraw.result"
GCWITHDRAW_RESULT_FIELD.number = 1
GCWITHDRAW_RESULT_FIELD.index = 0
GCWITHDRAW_RESULT_FIELD.label = 1
GCWITHDRAW_RESULT_FIELD.has_default_value = false
GCWITHDRAW_RESULT_FIELD.default_value = 0
GCWITHDRAW_RESULT_FIELD.type = 5
GCWITHDRAW_RESULT_FIELD.cpp_type = 1

GCWITHDRAW_MSG_FIELD.name = "msg"
GCWITHDRAW_MSG_FIELD.full_name = ".prootc.gcwithdraw.msg"
GCWITHDRAW_MSG_FIELD.number = 2
GCWITHDRAW_MSG_FIELD.index = 1
GCWITHDRAW_MSG_FIELD.label = 1
GCWITHDRAW_MSG_FIELD.has_default_value = false
GCWITHDRAW_MSG_FIELD.default_value = ""
GCWITHDRAW_MSG_FIELD.type = 9
GCWITHDRAW_MSG_FIELD.cpp_type = 9

GCWITHDRAW.name = "gcwithdraw"
GCWITHDRAW.full_name = ".prootc.gcwithdraw"
GCWITHDRAW.nested_types = {}
GCWITHDRAW.enum_types = {}
GCWITHDRAW.fields = {GCWITHDRAW_RESULT_FIELD, GCWITHDRAW_MSG_FIELD}
GCWITHDRAW.is_extendable = false
GCWITHDRAW.extensions = {}
CGWITHDRAWRECORD_USERID_FIELD.name = "userid"
CGWITHDRAWRECORD_USERID_FIELD.full_name = ".prootc.cgwithdrawrecord.userid"
CGWITHDRAWRECORD_USERID_FIELD.number = 1
CGWITHDRAWRECORD_USERID_FIELD.index = 0
CGWITHDRAWRECORD_USERID_FIELD.label = 1
CGWITHDRAWRECORD_USERID_FIELD.has_default_value = false
CGWITHDRAWRECORD_USERID_FIELD.default_value = 0
CGWITHDRAWRECORD_USERID_FIELD.type = 5
CGWITHDRAWRECORD_USERID_FIELD.cpp_type = 1

CGWITHDRAWRECORD_ORDERTYPE_FIELD.name = "ordertype"
CGWITHDRAWRECORD_ORDERTYPE_FIELD.full_name = ".prootc.cgwithdrawrecord.ordertype"
CGWITHDRAWRECORD_ORDERTYPE_FIELD.number = 2
CGWITHDRAWRECORD_ORDERTYPE_FIELD.index = 1
CGWITHDRAWRECORD_ORDERTYPE_FIELD.label = 1
CGWITHDRAWRECORD_ORDERTYPE_FIELD.has_default_value = false
CGWITHDRAWRECORD_ORDERTYPE_FIELD.default_value = 0
CGWITHDRAWRECORD_ORDERTYPE_FIELD.type = 5
CGWITHDRAWRECORD_ORDERTYPE_FIELD.cpp_type = 1

CGWITHDRAWRECORD_PAGENUM_FIELD.name = "pagenum"
CGWITHDRAWRECORD_PAGENUM_FIELD.full_name = ".prootc.cgwithdrawrecord.pagenum"
CGWITHDRAWRECORD_PAGENUM_FIELD.number = 3
CGWITHDRAWRECORD_PAGENUM_FIELD.index = 2
CGWITHDRAWRECORD_PAGENUM_FIELD.label = 1
CGWITHDRAWRECORD_PAGENUM_FIELD.has_default_value = false
CGWITHDRAWRECORD_PAGENUM_FIELD.default_value = 0
CGWITHDRAWRECORD_PAGENUM_FIELD.type = 5
CGWITHDRAWRECORD_PAGENUM_FIELD.cpp_type = 1

CGWITHDRAWRECORD_PAGESIZE_FIELD.name = "pagesize"
CGWITHDRAWRECORD_PAGESIZE_FIELD.full_name = ".prootc.cgwithdrawrecord.pagesize"
CGWITHDRAWRECORD_PAGESIZE_FIELD.number = 4
CGWITHDRAWRECORD_PAGESIZE_FIELD.index = 3
CGWITHDRAWRECORD_PAGESIZE_FIELD.label = 1
CGWITHDRAWRECORD_PAGESIZE_FIELD.has_default_value = false
CGWITHDRAWRECORD_PAGESIZE_FIELD.default_value = 0
CGWITHDRAWRECORD_PAGESIZE_FIELD.type = 5
CGWITHDRAWRECORD_PAGESIZE_FIELD.cpp_type = 1

CGWITHDRAWRECORD.name = "cgwithdrawrecord"
CGWITHDRAWRECORD.full_name = ".prootc.cgwithdrawrecord"
CGWITHDRAWRECORD.nested_types = {}
CGWITHDRAWRECORD.enum_types = {}
CGWITHDRAWRECORD.fields = {CGWITHDRAWRECORD_USERID_FIELD, CGWITHDRAWRECORD_ORDERTYPE_FIELD, CGWITHDRAWRECORD_PAGENUM_FIELD, CGWITHDRAWRECORD_PAGESIZE_FIELD}
CGWITHDRAWRECORD.is_extendable = false
CGWITHDRAWRECORD.extensions = {}
GCWITHDRAWRECORD_RESULT_FIELD.name = "result"
GCWITHDRAWRECORD_RESULT_FIELD.full_name = ".prootc.gcwithdrawrecord.result"
GCWITHDRAWRECORD_RESULT_FIELD.number = 1
GCWITHDRAWRECORD_RESULT_FIELD.index = 0
GCWITHDRAWRECORD_RESULT_FIELD.label = 1
GCWITHDRAWRECORD_RESULT_FIELD.has_default_value = false
GCWITHDRAWRECORD_RESULT_FIELD.default_value = 0
GCWITHDRAWRECORD_RESULT_FIELD.type = 5
GCWITHDRAWRECORD_RESULT_FIELD.cpp_type = 1

GCWITHDRAWRECORD_MSG_FIELD.name = "msg"
GCWITHDRAWRECORD_MSG_FIELD.full_name = ".prootc.gcwithdrawrecord.msg"
GCWITHDRAWRECORD_MSG_FIELD.number = 2
GCWITHDRAWRECORD_MSG_FIELD.index = 1
GCWITHDRAWRECORD_MSG_FIELD.label = 1
GCWITHDRAWRECORD_MSG_FIELD.has_default_value = false
GCWITHDRAWRECORD_MSG_FIELD.default_value = ""
GCWITHDRAWRECORD_MSG_FIELD.type = 9
GCWITHDRAWRECORD_MSG_FIELD.cpp_type = 9

GCWITHDRAWRECORD_USERID_FIELD.name = "userid"
GCWITHDRAWRECORD_USERID_FIELD.full_name = ".prootc.gcwithdrawrecord.userid"
GCWITHDRAWRECORD_USERID_FIELD.number = 3
GCWITHDRAWRECORD_USERID_FIELD.index = 2
GCWITHDRAWRECORD_USERID_FIELD.label = 1
GCWITHDRAWRECORD_USERID_FIELD.has_default_value = false
GCWITHDRAWRECORD_USERID_FIELD.default_value = 0
GCWITHDRAWRECORD_USERID_FIELD.type = 5
GCWITHDRAWRECORD_USERID_FIELD.cpp_type = 1

GCWITHDRAWRECORD_ORDERTYPE_FIELD.name = "ordertype"
GCWITHDRAWRECORD_ORDERTYPE_FIELD.full_name = ".prootc.gcwithdrawrecord.ordertype"
GCWITHDRAWRECORD_ORDERTYPE_FIELD.number = 4
GCWITHDRAWRECORD_ORDERTYPE_FIELD.index = 3
GCWITHDRAWRECORD_ORDERTYPE_FIELD.label = 1
GCWITHDRAWRECORD_ORDERTYPE_FIELD.has_default_value = false
GCWITHDRAWRECORD_ORDERTYPE_FIELD.default_value = 0
GCWITHDRAWRECORD_ORDERTYPE_FIELD.type = 5
GCWITHDRAWRECORD_ORDERTYPE_FIELD.cpp_type = 1

GCWITHDRAWRECORD_PAGENUM_FIELD.name = "pagenum"
GCWITHDRAWRECORD_PAGENUM_FIELD.full_name = ".prootc.gcwithdrawrecord.pagenum"
GCWITHDRAWRECORD_PAGENUM_FIELD.number = 5
GCWITHDRAWRECORD_PAGENUM_FIELD.index = 4
GCWITHDRAWRECORD_PAGENUM_FIELD.label = 1
GCWITHDRAWRECORD_PAGENUM_FIELD.has_default_value = false
GCWITHDRAWRECORD_PAGENUM_FIELD.default_value = 0
GCWITHDRAWRECORD_PAGENUM_FIELD.type = 5
GCWITHDRAWRECORD_PAGENUM_FIELD.cpp_type = 1

GCWITHDRAWRECORD_PAGESIZE_FIELD.name = "pagesize"
GCWITHDRAWRECORD_PAGESIZE_FIELD.full_name = ".prootc.gcwithdrawrecord.pagesize"
GCWITHDRAWRECORD_PAGESIZE_FIELD.number = 6
GCWITHDRAWRECORD_PAGESIZE_FIELD.index = 5
GCWITHDRAWRECORD_PAGESIZE_FIELD.label = 1
GCWITHDRAWRECORD_PAGESIZE_FIELD.has_default_value = false
GCWITHDRAWRECORD_PAGESIZE_FIELD.default_value = 0
GCWITHDRAWRECORD_PAGESIZE_FIELD.type = 5
GCWITHDRAWRECORD_PAGESIZE_FIELD.cpp_type = 1

GCWITHDRAWRECORD_RECORDINFO_FIELD.name = "recordinfo"
GCWITHDRAWRECORD_RECORDINFO_FIELD.full_name = ".prootc.gcwithdrawrecord.recordinfo"
GCWITHDRAWRECORD_RECORDINFO_FIELD.number = 7
GCWITHDRAWRECORD_RECORDINFO_FIELD.index = 6
GCWITHDRAWRECORD_RECORDINFO_FIELD.label = 3
GCWITHDRAWRECORD_RECORDINFO_FIELD.has_default_value = false
GCWITHDRAWRECORD_RECORDINFO_FIELD.default_value = {}
GCWITHDRAWRECORD_RECORDINFO_FIELD.message_type = ST_ORDER_PB_CURRENCYRECORDINFO
GCWITHDRAWRECORD_RECORDINFO_FIELD.type = 11
GCWITHDRAWRECORD_RECORDINFO_FIELD.cpp_type = 10

GCWITHDRAWRECORD.name = "gcwithdrawrecord"
GCWITHDRAWRECORD.full_name = ".prootc.gcwithdrawrecord"
GCWITHDRAWRECORD.nested_types = {}
GCWITHDRAWRECORD.enum_types = {}
GCWITHDRAWRECORD.fields = {GCWITHDRAWRECORD_RESULT_FIELD, GCWITHDRAWRECORD_MSG_FIELD, GCWITHDRAWRECORD_USERID_FIELD, GCWITHDRAWRECORD_ORDERTYPE_FIELD, GCWITHDRAWRECORD_PAGENUM_FIELD, GCWITHDRAWRECORD_PAGESIZE_FIELD, GCWITHDRAWRECORD_RECORDINFO_FIELD}
GCWITHDRAWRECORD.is_extendable = false
GCWITHDRAWRECORD.extensions = {}
CGEXCHANGERATE_USERID_FIELD.name = "userid"
CGEXCHANGERATE_USERID_FIELD.full_name = ".prootc.cgexchangerate.userid"
CGEXCHANGERATE_USERID_FIELD.number = 1
CGEXCHANGERATE_USERID_FIELD.index = 0
CGEXCHANGERATE_USERID_FIELD.label = 1
CGEXCHANGERATE_USERID_FIELD.has_default_value = false
CGEXCHANGERATE_USERID_FIELD.default_value = 0
CGEXCHANGERATE_USERID_FIELD.type = 5
CGEXCHANGERATE_USERID_FIELD.cpp_type = 1

CGEXCHANGERATE.name = "cgexchangerate"
CGEXCHANGERATE.full_name = ".prootc.cgexchangerate"
CGEXCHANGERATE.nested_types = {}
CGEXCHANGERATE.enum_types = {}
CGEXCHANGERATE.fields = {CGEXCHANGERATE_USERID_FIELD}
CGEXCHANGERATE.is_extendable = false
CGEXCHANGERATE.extensions = {}
GCEXCHANGERATE_RESULT_FIELD.name = "result"
GCEXCHANGERATE_RESULT_FIELD.full_name = ".prootc.gcexchangerate.result"
GCEXCHANGERATE_RESULT_FIELD.number = 1
GCEXCHANGERATE_RESULT_FIELD.index = 0
GCEXCHANGERATE_RESULT_FIELD.label = 1
GCEXCHANGERATE_RESULT_FIELD.has_default_value = false
GCEXCHANGERATE_RESULT_FIELD.default_value = 0
GCEXCHANGERATE_RESULT_FIELD.type = 5
GCEXCHANGERATE_RESULT_FIELD.cpp_type = 1

GCEXCHANGERATE_MSG_FIELD.name = "msg"
GCEXCHANGERATE_MSG_FIELD.full_name = ".prootc.gcexchangerate.msg"
GCEXCHANGERATE_MSG_FIELD.number = 2
GCEXCHANGERATE_MSG_FIELD.index = 1
GCEXCHANGERATE_MSG_FIELD.label = 1
GCEXCHANGERATE_MSG_FIELD.has_default_value = false
GCEXCHANGERATE_MSG_FIELD.default_value = ""
GCEXCHANGERATE_MSG_FIELD.type = 9
GCEXCHANGERATE_MSG_FIELD.cpp_type = 9

GCEXCHANGERATE_BUYRATE_FIELD.name = "buyrate"
GCEXCHANGERATE_BUYRATE_FIELD.full_name = ".prootc.gcexchangerate.buyrate"
GCEXCHANGERATE_BUYRATE_FIELD.number = 3
GCEXCHANGERATE_BUYRATE_FIELD.index = 2
GCEXCHANGERATE_BUYRATE_FIELD.label = 1
GCEXCHANGERATE_BUYRATE_FIELD.has_default_value = false
GCEXCHANGERATE_BUYRATE_FIELD.default_value = ""
GCEXCHANGERATE_BUYRATE_FIELD.type = 9
GCEXCHANGERATE_BUYRATE_FIELD.cpp_type = 9

GCEXCHANGERATE_SELLRATE_FIELD.name = "sellrate"
GCEXCHANGERATE_SELLRATE_FIELD.full_name = ".prootc.gcexchangerate.sellrate"
GCEXCHANGERATE_SELLRATE_FIELD.number = 4
GCEXCHANGERATE_SELLRATE_FIELD.index = 3
GCEXCHANGERATE_SELLRATE_FIELD.label = 1
GCEXCHANGERATE_SELLRATE_FIELD.has_default_value = false
GCEXCHANGERATE_SELLRATE_FIELD.default_value = ""
GCEXCHANGERATE_SELLRATE_FIELD.type = 9
GCEXCHANGERATE_SELLRATE_FIELD.cpp_type = 9

GCEXCHANGERATE.name = "gcexchangerate"
GCEXCHANGERATE.full_name = ".prootc.gcexchangerate"
GCEXCHANGERATE.nested_types = {}
GCEXCHANGERATE.enum_types = {}
GCEXCHANGERATE.fields = {GCEXCHANGERATE_RESULT_FIELD, GCEXCHANGERATE_MSG_FIELD, GCEXCHANGERATE_BUYRATE_FIELD, GCEXCHANGERATE_SELLRATE_FIELD}
GCEXCHANGERATE.is_extendable = false
GCEXCHANGERATE.extensions = {}
CGCHECKADDR_ADDS_FIELD.name = "adds"
CGCHECKADDR_ADDS_FIELD.full_name = ".prootc.cgcheckaddr.adds"
CGCHECKADDR_ADDS_FIELD.number = 1
CGCHECKADDR_ADDS_FIELD.index = 0
CGCHECKADDR_ADDS_FIELD.label = 1
CGCHECKADDR_ADDS_FIELD.has_default_value = false
CGCHECKADDR_ADDS_FIELD.default_value = ""
CGCHECKADDR_ADDS_FIELD.type = 9
CGCHECKADDR_ADDS_FIELD.cpp_type = 9

CGCHECKADDR.name = "cgcheckaddr"
CGCHECKADDR.full_name = ".prootc.cgcheckaddr"
CGCHECKADDR.nested_types = {}
CGCHECKADDR.enum_types = {}
CGCHECKADDR.fields = {CGCHECKADDR_ADDS_FIELD}
CGCHECKADDR.is_extendable = false
CGCHECKADDR.extensions = {}
GCCHECKADDR_RESULT_FIELD.name = "result"
GCCHECKADDR_RESULT_FIELD.full_name = ".prootc.gccheckaddr.result"
GCCHECKADDR_RESULT_FIELD.number = 1
GCCHECKADDR_RESULT_FIELD.index = 0
GCCHECKADDR_RESULT_FIELD.label = 1
GCCHECKADDR_RESULT_FIELD.has_default_value = false
GCCHECKADDR_RESULT_FIELD.default_value = 0
GCCHECKADDR_RESULT_FIELD.type = 5
GCCHECKADDR_RESULT_FIELD.cpp_type = 1

GCCHECKADDR_MSG_FIELD.name = "msg"
GCCHECKADDR_MSG_FIELD.full_name = ".prootc.gccheckaddr.msg"
GCCHECKADDR_MSG_FIELD.number = 2
GCCHECKADDR_MSG_FIELD.index = 1
GCCHECKADDR_MSG_FIELD.label = 1
GCCHECKADDR_MSG_FIELD.has_default_value = false
GCCHECKADDR_MSG_FIELD.default_value = ""
GCCHECKADDR_MSG_FIELD.type = 9
GCCHECKADDR_MSG_FIELD.cpp_type = 9

GCCHECKADDR_ADDS_FIELD.name = "adds"
GCCHECKADDR_ADDS_FIELD.full_name = ".prootc.gccheckaddr.adds"
GCCHECKADDR_ADDS_FIELD.number = 3
GCCHECKADDR_ADDS_FIELD.index = 2
GCCHECKADDR_ADDS_FIELD.label = 1
GCCHECKADDR_ADDS_FIELD.has_default_value = false
GCCHECKADDR_ADDS_FIELD.default_value = ""
GCCHECKADDR_ADDS_FIELD.type = 9
GCCHECKADDR_ADDS_FIELD.cpp_type = 9

GCCHECKADDR_RET_FIELD.name = "ret"
GCCHECKADDR_RET_FIELD.full_name = ".prootc.gccheckaddr.ret"
GCCHECKADDR_RET_FIELD.number = 4
GCCHECKADDR_RET_FIELD.index = 3
GCCHECKADDR_RET_FIELD.label = 1
GCCHECKADDR_RET_FIELD.has_default_value = false
GCCHECKADDR_RET_FIELD.default_value = 0
GCCHECKADDR_RET_FIELD.type = 5
GCCHECKADDR_RET_FIELD.cpp_type = 1

GCCHECKADDR.name = "gccheckaddr"
GCCHECKADDR.full_name = ".prootc.gccheckaddr"
GCCHECKADDR.nested_types = {}
GCCHECKADDR.enum_types = {}
GCCHECKADDR.fields = {GCCHECKADDR_RESULT_FIELD, GCCHECKADDR_MSG_FIELD, GCCHECKADDR_ADDS_FIELD, GCCHECKADDR_RET_FIELD}
GCCHECKADDR.is_extendable = false
GCCHECKADDR.extensions = {}
CGTWALLETRECHARGE_USERID_FIELD.name = "userid"
CGTWALLETRECHARGE_USERID_FIELD.full_name = ".prootc.cgtwalletrecharge.userid"
CGTWALLETRECHARGE_USERID_FIELD.number = 1
CGTWALLETRECHARGE_USERID_FIELD.index = 0
CGTWALLETRECHARGE_USERID_FIELD.label = 1
CGTWALLETRECHARGE_USERID_FIELD.has_default_value = false
CGTWALLETRECHARGE_USERID_FIELD.default_value = 0
CGTWALLETRECHARGE_USERID_FIELD.type = 5
CGTWALLETRECHARGE_USERID_FIELD.cpp_type = 1

CGTWALLETRECHARGE_AMOUNT_FIELD.name = "amount"
CGTWALLETRECHARGE_AMOUNT_FIELD.full_name = ".prootc.cgtwalletrecharge.amount"
CGTWALLETRECHARGE_AMOUNT_FIELD.number = 2
CGTWALLETRECHARGE_AMOUNT_FIELD.index = 1
CGTWALLETRECHARGE_AMOUNT_FIELD.label = 1
CGTWALLETRECHARGE_AMOUNT_FIELD.has_default_value = false
CGTWALLETRECHARGE_AMOUNT_FIELD.default_value = ""
CGTWALLETRECHARGE_AMOUNT_FIELD.type = 9
CGTWALLETRECHARGE_AMOUNT_FIELD.cpp_type = 9

CGTWALLETRECHARGE_ADDRTYPE_FIELD.name = "addrtype"
CGTWALLETRECHARGE_ADDRTYPE_FIELD.full_name = ".prootc.cgtwalletrecharge.addrtype"
CGTWALLETRECHARGE_ADDRTYPE_FIELD.number = 3
CGTWALLETRECHARGE_ADDRTYPE_FIELD.index = 2
CGTWALLETRECHARGE_ADDRTYPE_FIELD.label = 1
CGTWALLETRECHARGE_ADDRTYPE_FIELD.has_default_value = false
CGTWALLETRECHARGE_ADDRTYPE_FIELD.default_value = 0
CGTWALLETRECHARGE_ADDRTYPE_FIELD.type = 5
CGTWALLETRECHARGE_ADDRTYPE_FIELD.cpp_type = 1

CGTWALLETRECHARGE_COINID_FIELD.name = "coinid"
CGTWALLETRECHARGE_COINID_FIELD.full_name = ".prootc.cgtwalletrecharge.coinid"
CGTWALLETRECHARGE_COINID_FIELD.number = 4
CGTWALLETRECHARGE_COINID_FIELD.index = 3
CGTWALLETRECHARGE_COINID_FIELD.label = 1
CGTWALLETRECHARGE_COINID_FIELD.has_default_value = false
CGTWALLETRECHARGE_COINID_FIELD.default_value = 0
CGTWALLETRECHARGE_COINID_FIELD.type = 5
CGTWALLETRECHARGE_COINID_FIELD.cpp_type = 1

CGTWALLETRECHARGE.name = "cgtwalletrecharge"
CGTWALLETRECHARGE.full_name = ".prootc.cgtwalletrecharge"
CGTWALLETRECHARGE.nested_types = {}
CGTWALLETRECHARGE.enum_types = {}
CGTWALLETRECHARGE.fields = {CGTWALLETRECHARGE_USERID_FIELD, CGTWALLETRECHARGE_AMOUNT_FIELD, CGTWALLETRECHARGE_ADDRTYPE_FIELD, CGTWALLETRECHARGE_COINID_FIELD}
CGTWALLETRECHARGE.is_extendable = false
CGTWALLETRECHARGE.extensions = {}
GCTWALLETRECHARGE_RESULT_FIELD.name = "result"
GCTWALLETRECHARGE_RESULT_FIELD.full_name = ".prootc.gctwalletrecharge.result"
GCTWALLETRECHARGE_RESULT_FIELD.number = 1
GCTWALLETRECHARGE_RESULT_FIELD.index = 0
GCTWALLETRECHARGE_RESULT_FIELD.label = 1
GCTWALLETRECHARGE_RESULT_FIELD.has_default_value = false
GCTWALLETRECHARGE_RESULT_FIELD.default_value = 0
GCTWALLETRECHARGE_RESULT_FIELD.type = 5
GCTWALLETRECHARGE_RESULT_FIELD.cpp_type = 1

GCTWALLETRECHARGE_MSG_FIELD.name = "msg"
GCTWALLETRECHARGE_MSG_FIELD.full_name = ".prootc.gctwalletrecharge.msg"
GCTWALLETRECHARGE_MSG_FIELD.number = 2
GCTWALLETRECHARGE_MSG_FIELD.index = 1
GCTWALLETRECHARGE_MSG_FIELD.label = 1
GCTWALLETRECHARGE_MSG_FIELD.has_default_value = false
GCTWALLETRECHARGE_MSG_FIELD.default_value = ""
GCTWALLETRECHARGE_MSG_FIELD.type = 9
GCTWALLETRECHARGE_MSG_FIELD.cpp_type = 9

GCTWALLETRECHARGE_USERID_FIELD.name = "userid"
GCTWALLETRECHARGE_USERID_FIELD.full_name = ".prootc.gctwalletrecharge.userid"
GCTWALLETRECHARGE_USERID_FIELD.number = 3
GCTWALLETRECHARGE_USERID_FIELD.index = 2
GCTWALLETRECHARGE_USERID_FIELD.label = 1
GCTWALLETRECHARGE_USERID_FIELD.has_default_value = false
GCTWALLETRECHARGE_USERID_FIELD.default_value = 0
GCTWALLETRECHARGE_USERID_FIELD.type = 5
GCTWALLETRECHARGE_USERID_FIELD.cpp_type = 1

GCTWALLETRECHARGE_AMOUNT_FIELD.name = "amount"
GCTWALLETRECHARGE_AMOUNT_FIELD.full_name = ".prootc.gctwalletrecharge.amount"
GCTWALLETRECHARGE_AMOUNT_FIELD.number = 4
GCTWALLETRECHARGE_AMOUNT_FIELD.index = 3
GCTWALLETRECHARGE_AMOUNT_FIELD.label = 1
GCTWALLETRECHARGE_AMOUNT_FIELD.has_default_value = false
GCTWALLETRECHARGE_AMOUNT_FIELD.default_value = ""
GCTWALLETRECHARGE_AMOUNT_FIELD.type = 9
GCTWALLETRECHARGE_AMOUNT_FIELD.cpp_type = 9

GCTWALLETRECHARGE_INFO_FIELD.name = "info"
GCTWALLETRECHARGE_INFO_FIELD.full_name = ".prootc.gctwalletrecharge.info"
GCTWALLETRECHARGE_INFO_FIELD.number = 5
GCTWALLETRECHARGE_INFO_FIELD.index = 4
GCTWALLETRECHARGE_INFO_FIELD.label = 1
GCTWALLETRECHARGE_INFO_FIELD.has_default_value = false
GCTWALLETRECHARGE_INFO_FIELD.default_value = nil
GCTWALLETRECHARGE_INFO_FIELD.message_type = ST_HUMAN_PB_TWALLETORDERINFO
GCTWALLETRECHARGE_INFO_FIELD.type = 11
GCTWALLETRECHARGE_INFO_FIELD.cpp_type = 10

GCTWALLETRECHARGE.name = "gctwalletrecharge"
GCTWALLETRECHARGE.full_name = ".prootc.gctwalletrecharge"
GCTWALLETRECHARGE.nested_types = {}
GCTWALLETRECHARGE.enum_types = {}
GCTWALLETRECHARGE.fields = {GCTWALLETRECHARGE_RESULT_FIELD, GCTWALLETRECHARGE_MSG_FIELD, GCTWALLETRECHARGE_USERID_FIELD, GCTWALLETRECHARGE_AMOUNT_FIELD, GCTWALLETRECHARGE_INFO_FIELD}
GCTWALLETRECHARGE.is_extendable = false
GCTWALLETRECHARGE.extensions = {}
CGSUBMITTXID_USERID_FIELD.name = "userid"
CGSUBMITTXID_USERID_FIELD.full_name = ".prootc.cgsubmittxid.userid"
CGSUBMITTXID_USERID_FIELD.number = 1
CGSUBMITTXID_USERID_FIELD.index = 0
CGSUBMITTXID_USERID_FIELD.label = 1
CGSUBMITTXID_USERID_FIELD.has_default_value = false
CGSUBMITTXID_USERID_FIELD.default_value = 0
CGSUBMITTXID_USERID_FIELD.type = 5
CGSUBMITTXID_USERID_FIELD.cpp_type = 1

CGSUBMITTXID_ORDERID_FIELD.name = "orderid"
CGSUBMITTXID_ORDERID_FIELD.full_name = ".prootc.cgsubmittxid.orderid"
CGSUBMITTXID_ORDERID_FIELD.number = 2
CGSUBMITTXID_ORDERID_FIELD.index = 1
CGSUBMITTXID_ORDERID_FIELD.label = 1
CGSUBMITTXID_ORDERID_FIELD.has_default_value = false
CGSUBMITTXID_ORDERID_FIELD.default_value = ""
CGSUBMITTXID_ORDERID_FIELD.type = 9
CGSUBMITTXID_ORDERID_FIELD.cpp_type = 9

CGSUBMITTXID_TXID_FIELD.name = "txid"
CGSUBMITTXID_TXID_FIELD.full_name = ".prootc.cgsubmittxid.txid"
CGSUBMITTXID_TXID_FIELD.number = 3
CGSUBMITTXID_TXID_FIELD.index = 2
CGSUBMITTXID_TXID_FIELD.label = 1
CGSUBMITTXID_TXID_FIELD.has_default_value = false
CGSUBMITTXID_TXID_FIELD.default_value = ""
CGSUBMITTXID_TXID_FIELD.type = 9
CGSUBMITTXID_TXID_FIELD.cpp_type = 9

CGSUBMITTXID.name = "cgsubmittxid"
CGSUBMITTXID.full_name = ".prootc.cgsubmittxid"
CGSUBMITTXID.nested_types = {}
CGSUBMITTXID.enum_types = {}
CGSUBMITTXID.fields = {CGSUBMITTXID_USERID_FIELD, CGSUBMITTXID_ORDERID_FIELD, CGSUBMITTXID_TXID_FIELD}
CGSUBMITTXID.is_extendable = false
CGSUBMITTXID.extensions = {}
GCSUBMITTXID_RESULT_FIELD.name = "result"
GCSUBMITTXID_RESULT_FIELD.full_name = ".prootc.gcsubmittxid.result"
GCSUBMITTXID_RESULT_FIELD.number = 1
GCSUBMITTXID_RESULT_FIELD.index = 0
GCSUBMITTXID_RESULT_FIELD.label = 1
GCSUBMITTXID_RESULT_FIELD.has_default_value = false
GCSUBMITTXID_RESULT_FIELD.default_value = 0
GCSUBMITTXID_RESULT_FIELD.type = 5
GCSUBMITTXID_RESULT_FIELD.cpp_type = 1

GCSUBMITTXID_MSG_FIELD.name = "msg"
GCSUBMITTXID_MSG_FIELD.full_name = ".prootc.gcsubmittxid.msg"
GCSUBMITTXID_MSG_FIELD.number = 2
GCSUBMITTXID_MSG_FIELD.index = 1
GCSUBMITTXID_MSG_FIELD.label = 1
GCSUBMITTXID_MSG_FIELD.has_default_value = false
GCSUBMITTXID_MSG_FIELD.default_value = ""
GCSUBMITTXID_MSG_FIELD.type = 9
GCSUBMITTXID_MSG_FIELD.cpp_type = 9

GCSUBMITTXID_INFO_FIELD.name = "info"
GCSUBMITTXID_INFO_FIELD.full_name = ".prootc.gcsubmittxid.info"
GCSUBMITTXID_INFO_FIELD.number = 3
GCSUBMITTXID_INFO_FIELD.index = 2
GCSUBMITTXID_INFO_FIELD.label = 1
GCSUBMITTXID_INFO_FIELD.has_default_value = false
GCSUBMITTXID_INFO_FIELD.default_value = nil
GCSUBMITTXID_INFO_FIELD.message_type = ST_HUMAN_PB_TWALLETORDERINFO
GCSUBMITTXID_INFO_FIELD.type = 11
GCSUBMITTXID_INFO_FIELD.cpp_type = 10

GCSUBMITTXID.name = "gcsubmittxid"
GCSUBMITTXID.full_name = ".prootc.gcsubmittxid"
GCSUBMITTXID.nested_types = {}
GCSUBMITTXID.enum_types = {}
GCSUBMITTXID.fields = {GCSUBMITTXID_RESULT_FIELD, GCSUBMITTXID_MSG_FIELD, GCSUBMITTXID_INFO_FIELD}
GCSUBMITTXID.is_extendable = false
GCSUBMITTXID.extensions = {}
CGTWALLETRECORD_USERID_FIELD.name = "userid"
CGTWALLETRECORD_USERID_FIELD.full_name = ".prootc.cgtwalletrecord.userid"
CGTWALLETRECORD_USERID_FIELD.number = 1
CGTWALLETRECORD_USERID_FIELD.index = 0
CGTWALLETRECORD_USERID_FIELD.label = 1
CGTWALLETRECORD_USERID_FIELD.has_default_value = false
CGTWALLETRECORD_USERID_FIELD.default_value = 0
CGTWALLETRECORD_USERID_FIELD.type = 5
CGTWALLETRECORD_USERID_FIELD.cpp_type = 1

CGTWALLETRECORD_PAGENUM_FIELD.name = "pagenum"
CGTWALLETRECORD_PAGENUM_FIELD.full_name = ".prootc.cgtwalletrecord.pagenum"
CGTWALLETRECORD_PAGENUM_FIELD.number = 2
CGTWALLETRECORD_PAGENUM_FIELD.index = 1
CGTWALLETRECORD_PAGENUM_FIELD.label = 1
CGTWALLETRECORD_PAGENUM_FIELD.has_default_value = false
CGTWALLETRECORD_PAGENUM_FIELD.default_value = 0
CGTWALLETRECORD_PAGENUM_FIELD.type = 5
CGTWALLETRECORD_PAGENUM_FIELD.cpp_type = 1

CGTWALLETRECORD_PAGESIZE_FIELD.name = "pagesize"
CGTWALLETRECORD_PAGESIZE_FIELD.full_name = ".prootc.cgtwalletrecord.pagesize"
CGTWALLETRECORD_PAGESIZE_FIELD.number = 3
CGTWALLETRECORD_PAGESIZE_FIELD.index = 2
CGTWALLETRECORD_PAGESIZE_FIELD.label = 1
CGTWALLETRECORD_PAGESIZE_FIELD.has_default_value = false
CGTWALLETRECORD_PAGESIZE_FIELD.default_value = 0
CGTWALLETRECORD_PAGESIZE_FIELD.type = 5
CGTWALLETRECORD_PAGESIZE_FIELD.cpp_type = 1

CGTWALLETRECORD_ORDERTYPE_FIELD.name = "ordertype"
CGTWALLETRECORD_ORDERTYPE_FIELD.full_name = ".prootc.cgtwalletrecord.ordertype"
CGTWALLETRECORD_ORDERTYPE_FIELD.number = 4
CGTWALLETRECORD_ORDERTYPE_FIELD.index = 3
CGTWALLETRECORD_ORDERTYPE_FIELD.label = 1
CGTWALLETRECORD_ORDERTYPE_FIELD.has_default_value = false
CGTWALLETRECORD_ORDERTYPE_FIELD.default_value = 0
CGTWALLETRECORD_ORDERTYPE_FIELD.type = 5
CGTWALLETRECORD_ORDERTYPE_FIELD.cpp_type = 1

CGTWALLETRECORD.name = "cgtwalletrecord"
CGTWALLETRECORD.full_name = ".prootc.cgtwalletrecord"
CGTWALLETRECORD.nested_types = {}
CGTWALLETRECORD.enum_types = {}
CGTWALLETRECORD.fields = {CGTWALLETRECORD_USERID_FIELD, CGTWALLETRECORD_PAGENUM_FIELD, CGTWALLETRECORD_PAGESIZE_FIELD, CGTWALLETRECORD_ORDERTYPE_FIELD}
CGTWALLETRECORD.is_extendable = false
CGTWALLETRECORD.extensions = {}
GCTWALLETRECORD_RESULT_FIELD.name = "result"
GCTWALLETRECORD_RESULT_FIELD.full_name = ".prootc.gctwalletrecord.result"
GCTWALLETRECORD_RESULT_FIELD.number = 1
GCTWALLETRECORD_RESULT_FIELD.index = 0
GCTWALLETRECORD_RESULT_FIELD.label = 1
GCTWALLETRECORD_RESULT_FIELD.has_default_value = false
GCTWALLETRECORD_RESULT_FIELD.default_value = 0
GCTWALLETRECORD_RESULT_FIELD.type = 5
GCTWALLETRECORD_RESULT_FIELD.cpp_type = 1

GCTWALLETRECORD_MSG_FIELD.name = "msg"
GCTWALLETRECORD_MSG_FIELD.full_name = ".prootc.gctwalletrecord.msg"
GCTWALLETRECORD_MSG_FIELD.number = 2
GCTWALLETRECORD_MSG_FIELD.index = 1
GCTWALLETRECORD_MSG_FIELD.label = 1
GCTWALLETRECORD_MSG_FIELD.has_default_value = false
GCTWALLETRECORD_MSG_FIELD.default_value = ""
GCTWALLETRECORD_MSG_FIELD.type = 9
GCTWALLETRECORD_MSG_FIELD.cpp_type = 9

GCTWALLETRECORD_PAGENUM_FIELD.name = "pagenum"
GCTWALLETRECORD_PAGENUM_FIELD.full_name = ".prootc.gctwalletrecord.pagenum"
GCTWALLETRECORD_PAGENUM_FIELD.number = 3
GCTWALLETRECORD_PAGENUM_FIELD.index = 2
GCTWALLETRECORD_PAGENUM_FIELD.label = 1
GCTWALLETRECORD_PAGENUM_FIELD.has_default_value = false
GCTWALLETRECORD_PAGENUM_FIELD.default_value = 0
GCTWALLETRECORD_PAGENUM_FIELD.type = 5
GCTWALLETRECORD_PAGENUM_FIELD.cpp_type = 1

GCTWALLETRECORD_PAGESIZE_FIELD.name = "pagesize"
GCTWALLETRECORD_PAGESIZE_FIELD.full_name = ".prootc.gctwalletrecord.pagesize"
GCTWALLETRECORD_PAGESIZE_FIELD.number = 4
GCTWALLETRECORD_PAGESIZE_FIELD.index = 3
GCTWALLETRECORD_PAGESIZE_FIELD.label = 1
GCTWALLETRECORD_PAGESIZE_FIELD.has_default_value = false
GCTWALLETRECORD_PAGESIZE_FIELD.default_value = 0
GCTWALLETRECORD_PAGESIZE_FIELD.type = 5
GCTWALLETRECORD_PAGESIZE_FIELD.cpp_type = 1

GCTWALLETRECORD_ORDERTYPE_FIELD.name = "ordertype"
GCTWALLETRECORD_ORDERTYPE_FIELD.full_name = ".prootc.gctwalletrecord.ordertype"
GCTWALLETRECORD_ORDERTYPE_FIELD.number = 5
GCTWALLETRECORD_ORDERTYPE_FIELD.index = 4
GCTWALLETRECORD_ORDERTYPE_FIELD.label = 1
GCTWALLETRECORD_ORDERTYPE_FIELD.has_default_value = false
GCTWALLETRECORD_ORDERTYPE_FIELD.default_value = 0
GCTWALLETRECORD_ORDERTYPE_FIELD.type = 5
GCTWALLETRECORD_ORDERTYPE_FIELD.cpp_type = 1

GCTWALLETRECORD_INFOLIST_FIELD.name = "infolist"
GCTWALLETRECORD_INFOLIST_FIELD.full_name = ".prootc.gctwalletrecord.infolist"
GCTWALLETRECORD_INFOLIST_FIELD.number = 6
GCTWALLETRECORD_INFOLIST_FIELD.index = 5
GCTWALLETRECORD_INFOLIST_FIELD.label = 3
GCTWALLETRECORD_INFOLIST_FIELD.has_default_value = false
GCTWALLETRECORD_INFOLIST_FIELD.default_value = {}
GCTWALLETRECORD_INFOLIST_FIELD.message_type = ST_HUMAN_PB_TWALLETORDERINFO
GCTWALLETRECORD_INFOLIST_FIELD.type = 11
GCTWALLETRECORD_INFOLIST_FIELD.cpp_type = 10

GCTWALLETRECORD.name = "gctwalletrecord"
GCTWALLETRECORD.full_name = ".prootc.gctwalletrecord"
GCTWALLETRECORD.nested_types = {}
GCTWALLETRECORD.enum_types = {}
GCTWALLETRECORD.fields = {GCTWALLETRECORD_RESULT_FIELD, GCTWALLETRECORD_MSG_FIELD, GCTWALLETRECORD_PAGENUM_FIELD, GCTWALLETRECORD_PAGESIZE_FIELD, GCTWALLETRECORD_ORDERTYPE_FIELD, GCTWALLETRECORD_INFOLIST_FIELD}
GCTWALLETRECORD.is_extendable = false
GCTWALLETRECORD.extensions = {}
CGGETWITHDRAWALFEE_USERID_FIELD.name = "userid"
CGGETWITHDRAWALFEE_USERID_FIELD.full_name = ".prootc.cggetwithdrawalfee.userid"
CGGETWITHDRAWALFEE_USERID_FIELD.number = 1
CGGETWITHDRAWALFEE_USERID_FIELD.index = 0
CGGETWITHDRAWALFEE_USERID_FIELD.label = 1
CGGETWITHDRAWALFEE_USERID_FIELD.has_default_value = false
CGGETWITHDRAWALFEE_USERID_FIELD.default_value = 0
CGGETWITHDRAWALFEE_USERID_FIELD.type = 5
CGGETWITHDRAWALFEE_USERID_FIELD.cpp_type = 1

CGGETWITHDRAWALFEE_COINID_FIELD.name = "coinid"
CGGETWITHDRAWALFEE_COINID_FIELD.full_name = ".prootc.cggetwithdrawalfee.coinid"
CGGETWITHDRAWALFEE_COINID_FIELD.number = 2
CGGETWITHDRAWALFEE_COINID_FIELD.index = 1
CGGETWITHDRAWALFEE_COINID_FIELD.label = 1
CGGETWITHDRAWALFEE_COINID_FIELD.has_default_value = false
CGGETWITHDRAWALFEE_COINID_FIELD.default_value = 0
CGGETWITHDRAWALFEE_COINID_FIELD.type = 5
CGGETWITHDRAWALFEE_COINID_FIELD.cpp_type = 1

CGGETWITHDRAWALFEE_CHAINTYPE_FIELD.name = "chaintype"
CGGETWITHDRAWALFEE_CHAINTYPE_FIELD.full_name = ".prootc.cggetwithdrawalfee.chaintype"
CGGETWITHDRAWALFEE_CHAINTYPE_FIELD.number = 3
CGGETWITHDRAWALFEE_CHAINTYPE_FIELD.index = 2
CGGETWITHDRAWALFEE_CHAINTYPE_FIELD.label = 1
CGGETWITHDRAWALFEE_CHAINTYPE_FIELD.has_default_value = false
CGGETWITHDRAWALFEE_CHAINTYPE_FIELD.default_value = 0
CGGETWITHDRAWALFEE_CHAINTYPE_FIELD.type = 5
CGGETWITHDRAWALFEE_CHAINTYPE_FIELD.cpp_type = 1

CGGETWITHDRAWALFEE.name = "cggetwithdrawalfee"
CGGETWITHDRAWALFEE.full_name = ".prootc.cggetwithdrawalfee"
CGGETWITHDRAWALFEE.nested_types = {}
CGGETWITHDRAWALFEE.enum_types = {}
CGGETWITHDRAWALFEE.fields = {CGGETWITHDRAWALFEE_USERID_FIELD, CGGETWITHDRAWALFEE_COINID_FIELD, CGGETWITHDRAWALFEE_CHAINTYPE_FIELD}
CGGETWITHDRAWALFEE.is_extendable = false
CGGETWITHDRAWALFEE.extensions = {}
GCGETWITHDRAWALFEE_RESULT_FIELD.name = "result"
GCGETWITHDRAWALFEE_RESULT_FIELD.full_name = ".prootc.gcgetwithdrawalfee.result"
GCGETWITHDRAWALFEE_RESULT_FIELD.number = 1
GCGETWITHDRAWALFEE_RESULT_FIELD.index = 0
GCGETWITHDRAWALFEE_RESULT_FIELD.label = 1
GCGETWITHDRAWALFEE_RESULT_FIELD.has_default_value = false
GCGETWITHDRAWALFEE_RESULT_FIELD.default_value = 0
GCGETWITHDRAWALFEE_RESULT_FIELD.type = 5
GCGETWITHDRAWALFEE_RESULT_FIELD.cpp_type = 1

GCGETWITHDRAWALFEE_MSG_FIELD.name = "msg"
GCGETWITHDRAWALFEE_MSG_FIELD.full_name = ".prootc.gcgetwithdrawalfee.msg"
GCGETWITHDRAWALFEE_MSG_FIELD.number = 2
GCGETWITHDRAWALFEE_MSG_FIELD.index = 1
GCGETWITHDRAWALFEE_MSG_FIELD.label = 1
GCGETWITHDRAWALFEE_MSG_FIELD.has_default_value = false
GCGETWITHDRAWALFEE_MSG_FIELD.default_value = ""
GCGETWITHDRAWALFEE_MSG_FIELD.type = 9
GCGETWITHDRAWALFEE_MSG_FIELD.cpp_type = 9

GCGETWITHDRAWALFEE_WITHDRAWALFEE_FIELD.name = "withdrawalfee"
GCGETWITHDRAWALFEE_WITHDRAWALFEE_FIELD.full_name = ".prootc.gcgetwithdrawalfee.withdrawalfee"
GCGETWITHDRAWALFEE_WITHDRAWALFEE_FIELD.number = 3
GCGETWITHDRAWALFEE_WITHDRAWALFEE_FIELD.index = 2
GCGETWITHDRAWALFEE_WITHDRAWALFEE_FIELD.label = 1
GCGETWITHDRAWALFEE_WITHDRAWALFEE_FIELD.has_default_value = false
GCGETWITHDRAWALFEE_WITHDRAWALFEE_FIELD.default_value = ""
GCGETWITHDRAWALFEE_WITHDRAWALFEE_FIELD.type = 9
GCGETWITHDRAWALFEE_WITHDRAWALFEE_FIELD.cpp_type = 9

GCGETWITHDRAWALFEE_MINWITHDRAWALCOUNT_FIELD.name = "minwithdrawalcount"
GCGETWITHDRAWALFEE_MINWITHDRAWALCOUNT_FIELD.full_name = ".prootc.gcgetwithdrawalfee.minwithdrawalcount"
GCGETWITHDRAWALFEE_MINWITHDRAWALCOUNT_FIELD.number = 4
GCGETWITHDRAWALFEE_MINWITHDRAWALCOUNT_FIELD.index = 3
GCGETWITHDRAWALFEE_MINWITHDRAWALCOUNT_FIELD.label = 1
GCGETWITHDRAWALFEE_MINWITHDRAWALCOUNT_FIELD.has_default_value = false
GCGETWITHDRAWALFEE_MINWITHDRAWALCOUNT_FIELD.default_value = ""
GCGETWITHDRAWALFEE_MINWITHDRAWALCOUNT_FIELD.type = 9
GCGETWITHDRAWALFEE_MINWITHDRAWALCOUNT_FIELD.cpp_type = 9

GCGETWITHDRAWALFEE_MAXWITHDRAWALCOUNT_FIELD.name = "maxwithdrawalcount"
GCGETWITHDRAWALFEE_MAXWITHDRAWALCOUNT_FIELD.full_name = ".prootc.gcgetwithdrawalfee.maxwithdrawalcount"
GCGETWITHDRAWALFEE_MAXWITHDRAWALCOUNT_FIELD.number = 5
GCGETWITHDRAWALFEE_MAXWITHDRAWALCOUNT_FIELD.index = 4
GCGETWITHDRAWALFEE_MAXWITHDRAWALCOUNT_FIELD.label = 1
GCGETWITHDRAWALFEE_MAXWITHDRAWALCOUNT_FIELD.has_default_value = false
GCGETWITHDRAWALFEE_MAXWITHDRAWALCOUNT_FIELD.default_value = ""
GCGETWITHDRAWALFEE_MAXWITHDRAWALCOUNT_FIELD.type = 9
GCGETWITHDRAWALFEE_MAXWITHDRAWALCOUNT_FIELD.cpp_type = 9

GCGETWITHDRAWALFEE_TRANSFERFEE_FIELD.name = "transferfee"
GCGETWITHDRAWALFEE_TRANSFERFEE_FIELD.full_name = ".prootc.gcgetwithdrawalfee.transferfee"
GCGETWITHDRAWALFEE_TRANSFERFEE_FIELD.number = 6
GCGETWITHDRAWALFEE_TRANSFERFEE_FIELD.index = 5
GCGETWITHDRAWALFEE_TRANSFERFEE_FIELD.label = 1
GCGETWITHDRAWALFEE_TRANSFERFEE_FIELD.has_default_value = false
GCGETWITHDRAWALFEE_TRANSFERFEE_FIELD.default_value = ""
GCGETWITHDRAWALFEE_TRANSFERFEE_FIELD.type = 9
GCGETWITHDRAWALFEE_TRANSFERFEE_FIELD.cpp_type = 9

GCGETWITHDRAWALFEE_MINTRANSFERCOUNT_FIELD.name = "mintransfercount"
GCGETWITHDRAWALFEE_MINTRANSFERCOUNT_FIELD.full_name = ".prootc.gcgetwithdrawalfee.mintransfercount"
GCGETWITHDRAWALFEE_MINTRANSFERCOUNT_FIELD.number = 7
GCGETWITHDRAWALFEE_MINTRANSFERCOUNT_FIELD.index = 6
GCGETWITHDRAWALFEE_MINTRANSFERCOUNT_FIELD.label = 1
GCGETWITHDRAWALFEE_MINTRANSFERCOUNT_FIELD.has_default_value = false
GCGETWITHDRAWALFEE_MINTRANSFERCOUNT_FIELD.default_value = ""
GCGETWITHDRAWALFEE_MINTRANSFERCOUNT_FIELD.type = 9
GCGETWITHDRAWALFEE_MINTRANSFERCOUNT_FIELD.cpp_type = 9

GCGETWITHDRAWALFEE_MAXTRANSFERCOUNT_FIELD.name = "maxtransfercount"
GCGETWITHDRAWALFEE_MAXTRANSFERCOUNT_FIELD.full_name = ".prootc.gcgetwithdrawalfee.maxtransfercount"
GCGETWITHDRAWALFEE_MAXTRANSFERCOUNT_FIELD.number = 8
GCGETWITHDRAWALFEE_MAXTRANSFERCOUNT_FIELD.index = 7
GCGETWITHDRAWALFEE_MAXTRANSFERCOUNT_FIELD.label = 1
GCGETWITHDRAWALFEE_MAXTRANSFERCOUNT_FIELD.has_default_value = false
GCGETWITHDRAWALFEE_MAXTRANSFERCOUNT_FIELD.default_value = ""
GCGETWITHDRAWALFEE_MAXTRANSFERCOUNT_FIELD.type = 9
GCGETWITHDRAWALFEE_MAXTRANSFERCOUNT_FIELD.cpp_type = 9

GCGETWITHDRAWALFEE_COINID_FIELD.name = "coinid"
GCGETWITHDRAWALFEE_COINID_FIELD.full_name = ".prootc.gcgetwithdrawalfee.coinid"
GCGETWITHDRAWALFEE_COINID_FIELD.number = 9
GCGETWITHDRAWALFEE_COINID_FIELD.index = 8
GCGETWITHDRAWALFEE_COINID_FIELD.label = 1
GCGETWITHDRAWALFEE_COINID_FIELD.has_default_value = false
GCGETWITHDRAWALFEE_COINID_FIELD.default_value = 0
GCGETWITHDRAWALFEE_COINID_FIELD.type = 5
GCGETWITHDRAWALFEE_COINID_FIELD.cpp_type = 1

GCGETWITHDRAWALFEE_CHAINTYPE_FIELD.name = "chaintype"
GCGETWITHDRAWALFEE_CHAINTYPE_FIELD.full_name = ".prootc.gcgetwithdrawalfee.chaintype"
GCGETWITHDRAWALFEE_CHAINTYPE_FIELD.number = 10
GCGETWITHDRAWALFEE_CHAINTYPE_FIELD.index = 9
GCGETWITHDRAWALFEE_CHAINTYPE_FIELD.label = 1
GCGETWITHDRAWALFEE_CHAINTYPE_FIELD.has_default_value = false
GCGETWITHDRAWALFEE_CHAINTYPE_FIELD.default_value = 0
GCGETWITHDRAWALFEE_CHAINTYPE_FIELD.type = 5
GCGETWITHDRAWALFEE_CHAINTYPE_FIELD.cpp_type = 1

GCGETWITHDRAWALFEE.name = "gcgetwithdrawalfee"
GCGETWITHDRAWALFEE.full_name = ".prootc.gcgetwithdrawalfee"
GCGETWITHDRAWALFEE.nested_types = {}
GCGETWITHDRAWALFEE.enum_types = {}
GCGETWITHDRAWALFEE.fields = {GCGETWITHDRAWALFEE_RESULT_FIELD, GCGETWITHDRAWALFEE_MSG_FIELD, GCGETWITHDRAWALFEE_WITHDRAWALFEE_FIELD, GCGETWITHDRAWALFEE_MINWITHDRAWALCOUNT_FIELD, GCGETWITHDRAWALFEE_MAXWITHDRAWALCOUNT_FIELD, GCGETWITHDRAWALFEE_TRANSFERFEE_FIELD, GCGETWITHDRAWALFEE_MINTRANSFERCOUNT_FIELD, GCGETWITHDRAWALFEE_MAXTRANSFERCOUNT_FIELD, GCGETWITHDRAWALFEE_COINID_FIELD, GCGETWITHDRAWALFEE_CHAINTYPE_FIELD}
GCGETWITHDRAWALFEE.is_extendable = false
GCGETWITHDRAWALFEE.extensions = {}
CGMODIFYPROOFURL_USERID_FIELD.name = "userid"
CGMODIFYPROOFURL_USERID_FIELD.full_name = ".prootc.cgmodifyproofurl.userid"
CGMODIFYPROOFURL_USERID_FIELD.number = 1
CGMODIFYPROOFURL_USERID_FIELD.index = 0
CGMODIFYPROOFURL_USERID_FIELD.label = 1
CGMODIFYPROOFURL_USERID_FIELD.has_default_value = false
CGMODIFYPROOFURL_USERID_FIELD.default_value = 0
CGMODIFYPROOFURL_USERID_FIELD.type = 5
CGMODIFYPROOFURL_USERID_FIELD.cpp_type = 1

CGMODIFYPROOFURL_DEALID_FIELD.name = "dealid"
CGMODIFYPROOFURL_DEALID_FIELD.full_name = ".prootc.cgmodifyproofurl.dealid"
CGMODIFYPROOFURL_DEALID_FIELD.number = 2
CGMODIFYPROOFURL_DEALID_FIELD.index = 1
CGMODIFYPROOFURL_DEALID_FIELD.label = 1
CGMODIFYPROOFURL_DEALID_FIELD.has_default_value = false
CGMODIFYPROOFURL_DEALID_FIELD.default_value = 0
CGMODIFYPROOFURL_DEALID_FIELD.type = 5
CGMODIFYPROOFURL_DEALID_FIELD.cpp_type = 1

CGMODIFYPROOFURL_PROOFURL_FIELD.name = "proofurl"
CGMODIFYPROOFURL_PROOFURL_FIELD.full_name = ".prootc.cgmodifyproofurl.proofurl"
CGMODIFYPROOFURL_PROOFURL_FIELD.number = 3
CGMODIFYPROOFURL_PROOFURL_FIELD.index = 2
CGMODIFYPROOFURL_PROOFURL_FIELD.label = 3
CGMODIFYPROOFURL_PROOFURL_FIELD.has_default_value = false
CGMODIFYPROOFURL_PROOFURL_FIELD.default_value = {}
CGMODIFYPROOFURL_PROOFURL_FIELD.type = 9
CGMODIFYPROOFURL_PROOFURL_FIELD.cpp_type = 9

CGMODIFYPROOFURL.name = "cgmodifyproofurl"
CGMODIFYPROOFURL.full_name = ".prootc.cgmodifyproofurl"
CGMODIFYPROOFURL.nested_types = {}
CGMODIFYPROOFURL.enum_types = {}
CGMODIFYPROOFURL.fields = {CGMODIFYPROOFURL_USERID_FIELD, CGMODIFYPROOFURL_DEALID_FIELD, CGMODIFYPROOFURL_PROOFURL_FIELD}
CGMODIFYPROOFURL.is_extendable = false
CGMODIFYPROOFURL.extensions = {}
GCMODIFYPROOFURL_RESULT_FIELD.name = "result"
GCMODIFYPROOFURL_RESULT_FIELD.full_name = ".prootc.gcmodifyproofurl.result"
GCMODIFYPROOFURL_RESULT_FIELD.number = 1
GCMODIFYPROOFURL_RESULT_FIELD.index = 0
GCMODIFYPROOFURL_RESULT_FIELD.label = 1
GCMODIFYPROOFURL_RESULT_FIELD.has_default_value = false
GCMODIFYPROOFURL_RESULT_FIELD.default_value = 0
GCMODIFYPROOFURL_RESULT_FIELD.type = 5
GCMODIFYPROOFURL_RESULT_FIELD.cpp_type = 1

GCMODIFYPROOFURL_MSG_FIELD.name = "msg"
GCMODIFYPROOFURL_MSG_FIELD.full_name = ".prootc.gcmodifyproofurl.msg"
GCMODIFYPROOFURL_MSG_FIELD.number = 2
GCMODIFYPROOFURL_MSG_FIELD.index = 1
GCMODIFYPROOFURL_MSG_FIELD.label = 1
GCMODIFYPROOFURL_MSG_FIELD.has_default_value = false
GCMODIFYPROOFURL_MSG_FIELD.default_value = ""
GCMODIFYPROOFURL_MSG_FIELD.type = 9
GCMODIFYPROOFURL_MSG_FIELD.cpp_type = 9

GCMODIFYPROOFURL_DEALID_FIELD.name = "dealid"
GCMODIFYPROOFURL_DEALID_FIELD.full_name = ".prootc.gcmodifyproofurl.dealid"
GCMODIFYPROOFURL_DEALID_FIELD.number = 3
GCMODIFYPROOFURL_DEALID_FIELD.index = 2
GCMODIFYPROOFURL_DEALID_FIELD.label = 1
GCMODIFYPROOFURL_DEALID_FIELD.has_default_value = false
GCMODIFYPROOFURL_DEALID_FIELD.default_value = 0
GCMODIFYPROOFURL_DEALID_FIELD.type = 5
GCMODIFYPROOFURL_DEALID_FIELD.cpp_type = 1

GCMODIFYPROOFURL_COINFO_FIELD.name = "coinfo"
GCMODIFYPROOFURL_COINFO_FIELD.full_name = ".prootc.gcmodifyproofurl.coinfo"
GCMODIFYPROOFURL_COINFO_FIELD.number = 4
GCMODIFYPROOFURL_COINFO_FIELD.index = 3
GCMODIFYPROOFURL_COINFO_FIELD.label = 1
GCMODIFYPROOFURL_COINFO_FIELD.has_default_value = false
GCMODIFYPROOFURL_COINFO_FIELD.default_value = nil
GCMODIFYPROOFURL_COINFO_FIELD.message_type = ST_ORDER_PB_CUSTOMERORDERINFO
GCMODIFYPROOFURL_COINFO_FIELD.type = 11
GCMODIFYPROOFURL_COINFO_FIELD.cpp_type = 10

GCMODIFYPROOFURL.name = "gcmodifyproofurl"
GCMODIFYPROOFURL.full_name = ".prootc.gcmodifyproofurl"
GCMODIFYPROOFURL.nested_types = {}
GCMODIFYPROOFURL.enum_types = {}
GCMODIFYPROOFURL.fields = {GCMODIFYPROOFURL_RESULT_FIELD, GCMODIFYPROOFURL_MSG_FIELD, GCMODIFYPROOFURL_DEALID_FIELD, GCMODIFYPROOFURL_COINFO_FIELD}
GCMODIFYPROOFURL.is_extendable = false
GCMODIFYPROOFURL.extensions = {}
GCUPDATEVENDORORDER_RESULT_FIELD.name = "result"
GCUPDATEVENDORORDER_RESULT_FIELD.full_name = ".prootc.gcupdatevendororder.result"
GCUPDATEVENDORORDER_RESULT_FIELD.number = 1
GCUPDATEVENDORORDER_RESULT_FIELD.index = 0
GCUPDATEVENDORORDER_RESULT_FIELD.label = 1
GCUPDATEVENDORORDER_RESULT_FIELD.has_default_value = false
GCUPDATEVENDORORDER_RESULT_FIELD.default_value = 0
GCUPDATEVENDORORDER_RESULT_FIELD.type = 5
GCUPDATEVENDORORDER_RESULT_FIELD.cpp_type = 1

GCUPDATEVENDORORDER_MSG_FIELD.name = "msg"
GCUPDATEVENDORORDER_MSG_FIELD.full_name = ".prootc.gcupdatevendororder.msg"
GCUPDATEVENDORORDER_MSG_FIELD.number = 2
GCUPDATEVENDORORDER_MSG_FIELD.index = 1
GCUPDATEVENDORORDER_MSG_FIELD.label = 1
GCUPDATEVENDORORDER_MSG_FIELD.has_default_value = false
GCUPDATEVENDORORDER_MSG_FIELD.default_value = ""
GCUPDATEVENDORORDER_MSG_FIELD.type = 9
GCUPDATEVENDORORDER_MSG_FIELD.cpp_type = 9

GCUPDATEVENDORORDER_VOLIST_FIELD.name = "volist"
GCUPDATEVENDORORDER_VOLIST_FIELD.full_name = ".prootc.gcupdatevendororder.volist"
GCUPDATEVENDORORDER_VOLIST_FIELD.number = 3
GCUPDATEVENDORORDER_VOLIST_FIELD.index = 2
GCUPDATEVENDORORDER_VOLIST_FIELD.label = 3
GCUPDATEVENDORORDER_VOLIST_FIELD.has_default_value = false
GCUPDATEVENDORORDER_VOLIST_FIELD.default_value = {}
GCUPDATEVENDORORDER_VOLIST_FIELD.message_type = ST_ORDER_PB_VENDORORDERINFO
GCUPDATEVENDORORDER_VOLIST_FIELD.type = 11
GCUPDATEVENDORORDER_VOLIST_FIELD.cpp_type = 10

GCUPDATEVENDORORDER.name = "gcupdatevendororder"
GCUPDATEVENDORORDER.full_name = ".prootc.gcupdatevendororder"
GCUPDATEVENDORORDER.nested_types = {}
GCUPDATEVENDORORDER.enum_types = {}
GCUPDATEVENDORORDER.fields = {GCUPDATEVENDORORDER_RESULT_FIELD, GCUPDATEVENDORORDER_MSG_FIELD, GCUPDATEVENDORORDER_VOLIST_FIELD}
GCUPDATEVENDORORDER.is_extendable = false
GCUPDATEVENDORORDER.extensions = {}
CGSEARCHDEALRECDLIST_USERID_FIELD.name = "userid"
CGSEARCHDEALRECDLIST_USERID_FIELD.full_name = ".prootc.cgsearchdealrecdlist.userid"
CGSEARCHDEALRECDLIST_USERID_FIELD.number = 1
CGSEARCHDEALRECDLIST_USERID_FIELD.index = 0
CGSEARCHDEALRECDLIST_USERID_FIELD.label = 1
CGSEARCHDEALRECDLIST_USERID_FIELD.has_default_value = false
CGSEARCHDEALRECDLIST_USERID_FIELD.default_value = 0
CGSEARCHDEALRECDLIST_USERID_FIELD.type = 5
CGSEARCHDEALRECDLIST_USERID_FIELD.cpp_type = 1

CGSEARCHDEALRECDLIST_SEARCHTYPE_FIELD.name = "searchtype"
CGSEARCHDEALRECDLIST_SEARCHTYPE_FIELD.full_name = ".prootc.cgsearchdealrecdlist.searchtype"
CGSEARCHDEALRECDLIST_SEARCHTYPE_FIELD.number = 2
CGSEARCHDEALRECDLIST_SEARCHTYPE_FIELD.index = 1
CGSEARCHDEALRECDLIST_SEARCHTYPE_FIELD.label = 1
CGSEARCHDEALRECDLIST_SEARCHTYPE_FIELD.has_default_value = false
CGSEARCHDEALRECDLIST_SEARCHTYPE_FIELD.default_value = 0
CGSEARCHDEALRECDLIST_SEARCHTYPE_FIELD.type = 5
CGSEARCHDEALRECDLIST_SEARCHTYPE_FIELD.cpp_type = 1

CGSEARCHDEALRECDLIST_KEYWORD_FIELD.name = "keyword"
CGSEARCHDEALRECDLIST_KEYWORD_FIELD.full_name = ".prootc.cgsearchdealrecdlist.keyword"
CGSEARCHDEALRECDLIST_KEYWORD_FIELD.number = 3
CGSEARCHDEALRECDLIST_KEYWORD_FIELD.index = 2
CGSEARCHDEALRECDLIST_KEYWORD_FIELD.label = 1
CGSEARCHDEALRECDLIST_KEYWORD_FIELD.has_default_value = false
CGSEARCHDEALRECDLIST_KEYWORD_FIELD.default_value = ""
CGSEARCHDEALRECDLIST_KEYWORD_FIELD.type = 9
CGSEARCHDEALRECDLIST_KEYWORD_FIELD.cpp_type = 9

CGSEARCHDEALRECDLIST_PAGENUM_FIELD.name = "pagenum"
CGSEARCHDEALRECDLIST_PAGENUM_FIELD.full_name = ".prootc.cgsearchdealrecdlist.pagenum"
CGSEARCHDEALRECDLIST_PAGENUM_FIELD.number = 4
CGSEARCHDEALRECDLIST_PAGENUM_FIELD.index = 3
CGSEARCHDEALRECDLIST_PAGENUM_FIELD.label = 1
CGSEARCHDEALRECDLIST_PAGENUM_FIELD.has_default_value = false
CGSEARCHDEALRECDLIST_PAGENUM_FIELD.default_value = 0
CGSEARCHDEALRECDLIST_PAGENUM_FIELD.type = 5
CGSEARCHDEALRECDLIST_PAGENUM_FIELD.cpp_type = 1

CGSEARCHDEALRECDLIST_PAGESIZE_FIELD.name = "pagesize"
CGSEARCHDEALRECDLIST_PAGESIZE_FIELD.full_name = ".prootc.cgsearchdealrecdlist.pagesize"
CGSEARCHDEALRECDLIST_PAGESIZE_FIELD.number = 5
CGSEARCHDEALRECDLIST_PAGESIZE_FIELD.index = 4
CGSEARCHDEALRECDLIST_PAGESIZE_FIELD.label = 1
CGSEARCHDEALRECDLIST_PAGESIZE_FIELD.has_default_value = false
CGSEARCHDEALRECDLIST_PAGESIZE_FIELD.default_value = 0
CGSEARCHDEALRECDLIST_PAGESIZE_FIELD.type = 5
CGSEARCHDEALRECDLIST_PAGESIZE_FIELD.cpp_type = 1

CGSEARCHDEALRECDLIST.name = "cgsearchdealrecdlist"
CGSEARCHDEALRECDLIST.full_name = ".prootc.cgsearchdealrecdlist"
CGSEARCHDEALRECDLIST.nested_types = {}
CGSEARCHDEALRECDLIST.enum_types = {}
CGSEARCHDEALRECDLIST.fields = {CGSEARCHDEALRECDLIST_USERID_FIELD, CGSEARCHDEALRECDLIST_SEARCHTYPE_FIELD, CGSEARCHDEALRECDLIST_KEYWORD_FIELD, CGSEARCHDEALRECDLIST_PAGENUM_FIELD, CGSEARCHDEALRECDLIST_PAGESIZE_FIELD}
CGSEARCHDEALRECDLIST.is_extendable = false
CGSEARCHDEALRECDLIST.extensions = {}
GCSEARCHDEALRECDLIST_RESULT_FIELD.name = "result"
GCSEARCHDEALRECDLIST_RESULT_FIELD.full_name = ".prootc.gcsearchdealrecdlist.result"
GCSEARCHDEALRECDLIST_RESULT_FIELD.number = 1
GCSEARCHDEALRECDLIST_RESULT_FIELD.index = 0
GCSEARCHDEALRECDLIST_RESULT_FIELD.label = 1
GCSEARCHDEALRECDLIST_RESULT_FIELD.has_default_value = false
GCSEARCHDEALRECDLIST_RESULT_FIELD.default_value = 0
GCSEARCHDEALRECDLIST_RESULT_FIELD.type = 5
GCSEARCHDEALRECDLIST_RESULT_FIELD.cpp_type = 1

GCSEARCHDEALRECDLIST_MSG_FIELD.name = "msg"
GCSEARCHDEALRECDLIST_MSG_FIELD.full_name = ".prootc.gcsearchdealrecdlist.msg"
GCSEARCHDEALRECDLIST_MSG_FIELD.number = 2
GCSEARCHDEALRECDLIST_MSG_FIELD.index = 1
GCSEARCHDEALRECDLIST_MSG_FIELD.label = 1
GCSEARCHDEALRECDLIST_MSG_FIELD.has_default_value = false
GCSEARCHDEALRECDLIST_MSG_FIELD.default_value = ""
GCSEARCHDEALRECDLIST_MSG_FIELD.type = 9
GCSEARCHDEALRECDLIST_MSG_FIELD.cpp_type = 9

GCSEARCHDEALRECDLIST_SEARCHTYPE_FIELD.name = "searchtype"
GCSEARCHDEALRECDLIST_SEARCHTYPE_FIELD.full_name = ".prootc.gcsearchdealrecdlist.searchtype"
GCSEARCHDEALRECDLIST_SEARCHTYPE_FIELD.number = 3
GCSEARCHDEALRECDLIST_SEARCHTYPE_FIELD.index = 2
GCSEARCHDEALRECDLIST_SEARCHTYPE_FIELD.label = 1
GCSEARCHDEALRECDLIST_SEARCHTYPE_FIELD.has_default_value = false
GCSEARCHDEALRECDLIST_SEARCHTYPE_FIELD.default_value = 0
GCSEARCHDEALRECDLIST_SEARCHTYPE_FIELD.type = 5
GCSEARCHDEALRECDLIST_SEARCHTYPE_FIELD.cpp_type = 1

GCSEARCHDEALRECDLIST_KEYWORD_FIELD.name = "keyword"
GCSEARCHDEALRECDLIST_KEYWORD_FIELD.full_name = ".prootc.gcsearchdealrecdlist.keyword"
GCSEARCHDEALRECDLIST_KEYWORD_FIELD.number = 4
GCSEARCHDEALRECDLIST_KEYWORD_FIELD.index = 3
GCSEARCHDEALRECDLIST_KEYWORD_FIELD.label = 1
GCSEARCHDEALRECDLIST_KEYWORD_FIELD.has_default_value = false
GCSEARCHDEALRECDLIST_KEYWORD_FIELD.default_value = ""
GCSEARCHDEALRECDLIST_KEYWORD_FIELD.type = 9
GCSEARCHDEALRECDLIST_KEYWORD_FIELD.cpp_type = 9

GCSEARCHDEALRECDLIST_PAGENUM_FIELD.name = "pagenum"
GCSEARCHDEALRECDLIST_PAGENUM_FIELD.full_name = ".prootc.gcsearchdealrecdlist.pagenum"
GCSEARCHDEALRECDLIST_PAGENUM_FIELD.number = 5
GCSEARCHDEALRECDLIST_PAGENUM_FIELD.index = 4
GCSEARCHDEALRECDLIST_PAGENUM_FIELD.label = 1
GCSEARCHDEALRECDLIST_PAGENUM_FIELD.has_default_value = false
GCSEARCHDEALRECDLIST_PAGENUM_FIELD.default_value = 0
GCSEARCHDEALRECDLIST_PAGENUM_FIELD.type = 5
GCSEARCHDEALRECDLIST_PAGENUM_FIELD.cpp_type = 1

GCSEARCHDEALRECDLIST_PAGESIZE_FIELD.name = "pagesize"
GCSEARCHDEALRECDLIST_PAGESIZE_FIELD.full_name = ".prootc.gcsearchdealrecdlist.pagesize"
GCSEARCHDEALRECDLIST_PAGESIZE_FIELD.number = 6
GCSEARCHDEALRECDLIST_PAGESIZE_FIELD.index = 5
GCSEARCHDEALRECDLIST_PAGESIZE_FIELD.label = 1
GCSEARCHDEALRECDLIST_PAGESIZE_FIELD.has_default_value = false
GCSEARCHDEALRECDLIST_PAGESIZE_FIELD.default_value = 0
GCSEARCHDEALRECDLIST_PAGESIZE_FIELD.type = 5
GCSEARCHDEALRECDLIST_PAGESIZE_FIELD.cpp_type = 1

GCSEARCHDEALRECDLIST_INFOLIST_FIELD.name = "infolist"
GCSEARCHDEALRECDLIST_INFOLIST_FIELD.full_name = ".prootc.gcsearchdealrecdlist.infolist"
GCSEARCHDEALRECDLIST_INFOLIST_FIELD.number = 7
GCSEARCHDEALRECDLIST_INFOLIST_FIELD.index = 6
GCSEARCHDEALRECDLIST_INFOLIST_FIELD.label = 3
GCSEARCHDEALRECDLIST_INFOLIST_FIELD.has_default_value = false
GCSEARCHDEALRECDLIST_INFOLIST_FIELD.default_value = {}
GCSEARCHDEALRECDLIST_INFOLIST_FIELD.message_type = ST_ORDER_PB_CUSTOMERORDERINFO
GCSEARCHDEALRECDLIST_INFOLIST_FIELD.type = 11
GCSEARCHDEALRECDLIST_INFOLIST_FIELD.cpp_type = 10

GCSEARCHDEALRECDLIST.name = "gcsearchdealrecdlist"
GCSEARCHDEALRECDLIST.full_name = ".prootc.gcsearchdealrecdlist"
GCSEARCHDEALRECDLIST.nested_types = {}
GCSEARCHDEALRECDLIST.enum_types = {}
GCSEARCHDEALRECDLIST.fields = {GCSEARCHDEALRECDLIST_RESULT_FIELD, GCSEARCHDEALRECDLIST_MSG_FIELD, GCSEARCHDEALRECDLIST_SEARCHTYPE_FIELD, GCSEARCHDEALRECDLIST_KEYWORD_FIELD, GCSEARCHDEALRECDLIST_PAGENUM_FIELD, GCSEARCHDEALRECDLIST_PAGESIZE_FIELD, GCSEARCHDEALRECDLIST_INFOLIST_FIELD}
GCSEARCHDEALRECDLIST.is_extendable = false
GCSEARCHDEALRECDLIST.extensions = {}

cgcheckaddr = protobuf.Message(CGCHECKADDR)
cgdealrecdlist = protobuf.Message(CGDEALRECDLIST)
cgdetail = protobuf.Message(CGDETAIL)
cgexchangerate = protobuf.Message(CGEXCHANGERATE)
cgexpensestat = protobuf.Message(CGEXPENSESTAT)
cggetwithdrawalfee = protobuf.Message(CGGETWITHDRAWALFEE)
cgincomestat = protobuf.Message(CGINCOMESTAT)
cgmodifyproofurl = protobuf.Message(CGMODIFYPROOFURL)
cgsearchdealrecdlist = protobuf.Message(CGSEARCHDEALRECDLIST)
cgsubmittxid = protobuf.Message(CGSUBMITTXID)
cgtodayflow = protobuf.Message(CGTODAYFLOW)
cgtwalletrecharge = protobuf.Message(CGTWALLETRECHARGE)
cgtwalletrecord = protobuf.Message(CGTWALLETRECORD)
cgwithdraw = protobuf.Message(CGWITHDRAW)
cgwithdrawrecord = protobuf.Message(CGWITHDRAWRECORD)
cgworkinglist = protobuf.Message(CGWORKINGLIST)
gccheckaddr = protobuf.Message(GCCHECKADDR)
gcdetail = protobuf.Message(GCDETAIL)
gcexchangerate = protobuf.Message(GCEXCHANGERATE)
gcexpensestat = protobuf.Message(GCEXPENSESTAT)
gcgetwithdrawalfee = protobuf.Message(GCGETWITHDRAWALFEE)
gcincomestat = protobuf.Message(GCINCOMESTAT)
gcmodifyproofurl = protobuf.Message(GCMODIFYPROOFURL)
gcsearchdealrecdlist = protobuf.Message(GCSEARCHDEALRECDLIST)
gcsubmittxid = protobuf.Message(GCSUBMITTXID)
gctodayflow = protobuf.Message(GCTODAYFLOW)
gctwalletrecharge = protobuf.Message(GCTWALLETRECHARGE)
gctwalletrecord = protobuf.Message(GCTWALLETRECORD)
gcupdatevendororder = protobuf.Message(GCUPDATEVENDORORDER)
gcwithdraw = protobuf.Message(GCWITHDRAW)
gcwithdrawrecord = protobuf.Message(GCWITHDRAWRECORD)
gcworkinglist = protobuf.Message(GCWORKINGLIST)
gedealrecdlist = protobuf.Message(GEDEALRECDLIST)

----------nimol modify---------
MSG_ORDER2_PB_CGCHECKADDR = CGCHECKADDR
MSG_ORDER2_PB_CGDEALRECDLIST = CGDEALRECDLIST
MSG_ORDER2_PB_CGDETAIL = CGDETAIL
MSG_ORDER2_PB_CGEXCHANGERATE = CGEXCHANGERATE
MSG_ORDER2_PB_CGEXPENSESTAT = CGEXPENSESTAT
MSG_ORDER2_PB_CGGETWITHDRAWALFEE = CGGETWITHDRAWALFEE
MSG_ORDER2_PB_CGINCOMESTAT = CGINCOMESTAT
MSG_ORDER2_PB_CGMODIFYPROOFURL = CGMODIFYPROOFURL
MSG_ORDER2_PB_CGSEARCHDEALRECDLIST = CGSEARCHDEALRECDLIST
MSG_ORDER2_PB_CGSUBMITTXID = CGSUBMITTXID
MSG_ORDER2_PB_CGTODAYFLOW = CGTODAYFLOW
MSG_ORDER2_PB_CGTWALLETRECHARGE = CGTWALLETRECHARGE
MSG_ORDER2_PB_CGTWALLETRECORD = CGTWALLETRECORD
MSG_ORDER2_PB_CGWITHDRAW = CGWITHDRAW
MSG_ORDER2_PB_CGWITHDRAWRECORD = CGWITHDRAWRECORD
MSG_ORDER2_PB_CGWORKINGLIST = CGWORKINGLIST
MSG_ORDER2_PB_GCCHECKADDR = GCCHECKADDR
MSG_ORDER2_PB_GCDETAIL = GCDETAIL
MSG_ORDER2_PB_GCEXCHANGERATE = GCEXCHANGERATE
MSG_ORDER2_PB_GCEXPENSESTAT = GCEXPENSESTAT
MSG_ORDER2_PB_GCGETWITHDRAWALFEE = GCGETWITHDRAWALFEE
MSG_ORDER2_PB_GCINCOMESTAT = GCINCOMESTAT
MSG_ORDER2_PB_GCMODIFYPROOFURL = GCMODIFYPROOFURL
MSG_ORDER2_PB_GCSEARCHDEALRECDLIST = GCSEARCHDEALRECDLIST
MSG_ORDER2_PB_GCSUBMITTXID = GCSUBMITTXID
MSG_ORDER2_PB_GCTODAYFLOW = GCTODAYFLOW
MSG_ORDER2_PB_GCTWALLETRECHARGE = GCTWALLETRECHARGE
MSG_ORDER2_PB_GCTWALLETRECORD = GCTWALLETRECORD
MSG_ORDER2_PB_GCUPDATEVENDORORDER = GCUPDATEVENDORORDER
MSG_ORDER2_PB_GCWITHDRAW = GCWITHDRAW
MSG_ORDER2_PB_GCWITHDRAWRECORD = GCWITHDRAWRECORD
MSG_ORDER2_PB_GCWORKINGLIST = GCWORKINGLIST
MSG_ORDER2_PB_GEDEALRECDLIST = GEDEALRECDLIST
