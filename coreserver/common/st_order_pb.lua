-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
module('st_order_pb')


local VENDORORDERINFO = protobuf.Descriptor();
local VENDORORDERINFO_USERID_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_HANGID_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_TYPE_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_PRICETYPE_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_PRICE_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_AMOUNT_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_MINMONEY_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_MAXMONEY_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_AUTOSWITCH_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_MESSAGE_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_MAXAMOUNT_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_ENABLESTATUS_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_CHANNEL_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_NICKNAME_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_UNSOLDORDERNUM_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_DEALORDERNUM_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_UNSOLDORDERAMOUNT_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_DEALORDERAMOUNT_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_PAYLIST_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_CANCELNUM_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_USERTYPE_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_PAYEEACCOUNT_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_PAYEENAME_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_PAYEEBANK_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_PAYEEBANDADDR_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_DEAL_ORDER_FREE_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_FREE_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_FEERATE_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_ADDFEERATE_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_CREATETIME_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_CHANNELDEAL_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_DEALMODEL_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_PREDICTMONEY_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_RESTTIME_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_DEALMONERY_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_DEALCOUNT_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_WITHDRAWTYPE_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_COINID_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_COINNAME_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_ISEXTERNAL_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_UNSOLDORDERMONEY_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_DEALORDERMONEY_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_EXTERNALORDERUSERID_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_ISASSIGNED_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_ISTAKE_FIELD = protobuf.FieldDescriptor();
local VENDORORDERINFO_WALLETTYPE_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO = protobuf.Descriptor();
local CUSTOMERORDERINFO_DEALID_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_TYPE_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_VENDORORDERID_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_CUSTOMERUSERID_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_VENDORUSERID_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_PRICE_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_AMOUNT_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_MONEY_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_STATUS_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_MERCHANTORDERID_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_FEERATE_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_FEE_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_PROOFURL_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_PAYTYPELIST_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_PAYIDLIST_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_CREATETIME_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_PAYTIME_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_PASSTIME_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_CHANNEL_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_PUBLICPRICE_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_FROMTYPE_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_CUSTOMERUSERNICKNAME_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_VENDORUSERNICKNAME_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_NOTIFYURL_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_BODY_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_SELLFEERATE_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_SELLFEE_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_CANCELTIME_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_BUYFEERATE_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_BUYFEE_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_UPDATETIME_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_DEALTYPE_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_GETAMOUNT_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_INCOME_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_WITHDRAWTYPE_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_COINID_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_COINNAME_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_ISEXTERNAL_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_AFTERMONEY_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_FEEMONEY_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_TRADEID_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_ISWAIT_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_CHAINTYPE_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_CHAINNAME_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_CHAINADDR_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_OWERSHIP_FIELD = protobuf.FieldDescriptor();
local CUSTOMERORDERINFO_WALLETTYPE_FIELD = protobuf.FieldDescriptor();
local APPPEALINFO = protobuf.Descriptor();
local APPPEALINFO_APPEALID_FIELD = protobuf.FieldDescriptor();
local APPPEALINFO_FROMUSERID_FIELD = protobuf.FieldDescriptor();
local APPPEALINFO_FROMUSERNICKNAME_FIELD = protobuf.FieldDescriptor();
local APPPEALINFO_TOUSERID_FIELD = protobuf.FieldDescriptor();
local APPPEALINFO_TOUSERNICKNAME_FIELD = protobuf.FieldDescriptor();
local APPPEALINFO_ORDERID_FIELD = protobuf.FieldDescriptor();
local APPPEALINFO_REASON_FIELD = protobuf.FieldDescriptor();
local APPPEALINFO_DESCRIPTION_FIELD = protobuf.FieldDescriptor();
local APPPEALINFO_PROOFURL_FIELD = protobuf.FieldDescriptor();
local APPPEALINFO_REPLYREASON_FIELD = protobuf.FieldDescriptor();
local APPPEALINFO_REPLYDESCRIPTION_FIELD = protobuf.FieldDescriptor();
local APPPEALINFO_REPLYPROOFURL_FIELD = protobuf.FieldDescriptor();
local APPPEALINFO_REPLYTIME_FIELD = protobuf.FieldDescriptor();
local APPPEALINFO_SYSREMARK_FIELD = protobuf.FieldDescriptor();
local APPPEALINFO_STATUS_FIELD = protobuf.FieldDescriptor();
local APPPEALINFO_NEEDAUDIT_FIELD = protobuf.FieldDescriptor();
local APPPEALINFO_CREATETIME_FIELD = protobuf.FieldDescriptor();
local CURRENCYRECORDINFO = protobuf.Descriptor();
local CURRENCYRECORDINFO_ID_FIELD = protobuf.FieldDescriptor();
local CURRENCYRECORDINFO_TXID_FIELD = protobuf.FieldDescriptor();
local CURRENCYRECORDINFO_TXTYPE_FIELD = protobuf.FieldDescriptor();
local CURRENCYRECORDINFO_CHAINID_FIELD = protobuf.FieldDescriptor();
local CURRENCYRECORDINFO_TXDATA_FIELD = protobuf.FieldDescriptor();
local CURRENCYRECORDINFO_FROMADDR_FIELD = protobuf.FieldDescriptor();
local CURRENCYRECORDINFO_TOADDR_FIELD = protobuf.FieldDescriptor();
local CURRENCYRECORDINFO_AMOUNT_FIELD = protobuf.FieldDescriptor();
local CURRENCYRECORDINFO_TXFEE_FIELD = protobuf.FieldDescriptor();
local CURRENCYRECORDINFO_TXSTATUS_FIELD = protobuf.FieldDescriptor();
local CURRENCYRECORDINFO_TXTIME_FIELD = protobuf.FieldDescriptor();
local CURRENCYRECORDINFO_BLOCKHASH_FIELD = protobuf.FieldDescriptor();
local CURRENCYRECORDINFO_RECDSTATUS_FIELD = protobuf.FieldDescriptor();
local CURRENCYRECORDINFO_REMARK_FIELD = protobuf.FieldDescriptor();
local CURRENCYRECORDINFO_CREATETIME_FIELD = protobuf.FieldDescriptor();
local CURRENCYRECORDINFO_SPECIFICTYPE_FIELD = protobuf.FieldDescriptor();
local CURRENCYRECORDINFO_COINID_FIELD = protobuf.FieldDescriptor();
local CURRENCYRECORDINFO_COINNAME_FIELD = protobuf.FieldDescriptor();
local CURRENCYRECORDINFO_ADDRTYPE_FIELD = protobuf.FieldDescriptor();
local CURRENCYRECORDINFO_ADDRNAME_FIELD = protobuf.FieldDescriptor();
local INCOMEEXPENDITUREDETAIL = protobuf.Descriptor();
local INCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD = protobuf.FieldDescriptor();
local INCOMEEXPENDITUREDETAIL_AMOUNT_FIELD = protobuf.FieldDescriptor();
local INCOMEEXPENDITUREDETAIL_AFTERAMOUNT_FIELD = protobuf.FieldDescriptor();
local INCOMEEXPENDITUREDETAIL_CREATETIME_FIELD = protobuf.FieldDescriptor();

VENDORORDERINFO_USERID_FIELD.name = "userid"
VENDORORDERINFO_USERID_FIELD.full_name = ".prootc.vendororderinfo.userid"
VENDORORDERINFO_USERID_FIELD.number = 1
VENDORORDERINFO_USERID_FIELD.index = 0
VENDORORDERINFO_USERID_FIELD.label = 1
VENDORORDERINFO_USERID_FIELD.has_default_value = false
VENDORORDERINFO_USERID_FIELD.default_value = 0
VENDORORDERINFO_USERID_FIELD.type = 5
VENDORORDERINFO_USERID_FIELD.cpp_type = 1

VENDORORDERINFO_HANGID_FIELD.name = "hangid"
VENDORORDERINFO_HANGID_FIELD.full_name = ".prootc.vendororderinfo.hangid"
VENDORORDERINFO_HANGID_FIELD.number = 2
VENDORORDERINFO_HANGID_FIELD.index = 1
VENDORORDERINFO_HANGID_FIELD.label = 1
VENDORORDERINFO_HANGID_FIELD.has_default_value = false
VENDORORDERINFO_HANGID_FIELD.default_value = 0
VENDORORDERINFO_HANGID_FIELD.type = 5
VENDORORDERINFO_HANGID_FIELD.cpp_type = 1

VENDORORDERINFO_TYPE_FIELD.name = "type"
VENDORORDERINFO_TYPE_FIELD.full_name = ".prootc.vendororderinfo.type"
VENDORORDERINFO_TYPE_FIELD.number = 3
VENDORORDERINFO_TYPE_FIELD.index = 2
VENDORORDERINFO_TYPE_FIELD.label = 1
VENDORORDERINFO_TYPE_FIELD.has_default_value = false
VENDORORDERINFO_TYPE_FIELD.default_value = 0
VENDORORDERINFO_TYPE_FIELD.type = 5
VENDORORDERINFO_TYPE_FIELD.cpp_type = 1

VENDORORDERINFO_PRICETYPE_FIELD.name = "pricetype"
VENDORORDERINFO_PRICETYPE_FIELD.full_name = ".prootc.vendororderinfo.pricetype"
VENDORORDERINFO_PRICETYPE_FIELD.number = 4
VENDORORDERINFO_PRICETYPE_FIELD.index = 3
VENDORORDERINFO_PRICETYPE_FIELD.label = 1
VENDORORDERINFO_PRICETYPE_FIELD.has_default_value = false
VENDORORDERINFO_PRICETYPE_FIELD.default_value = 0
VENDORORDERINFO_PRICETYPE_FIELD.type = 5
VENDORORDERINFO_PRICETYPE_FIELD.cpp_type = 1

VENDORORDERINFO_PRICE_FIELD.name = "price"
VENDORORDERINFO_PRICE_FIELD.full_name = ".prootc.vendororderinfo.price"
VENDORORDERINFO_PRICE_FIELD.number = 5
VENDORORDERINFO_PRICE_FIELD.index = 4
VENDORORDERINFO_PRICE_FIELD.label = 1
VENDORORDERINFO_PRICE_FIELD.has_default_value = false
VENDORORDERINFO_PRICE_FIELD.default_value = ""
VENDORORDERINFO_PRICE_FIELD.type = 9
VENDORORDERINFO_PRICE_FIELD.cpp_type = 9

VENDORORDERINFO_AMOUNT_FIELD.name = "amount"
VENDORORDERINFO_AMOUNT_FIELD.full_name = ".prootc.vendororderinfo.amount"
VENDORORDERINFO_AMOUNT_FIELD.number = 6
VENDORORDERINFO_AMOUNT_FIELD.index = 5
VENDORORDERINFO_AMOUNT_FIELD.label = 1
VENDORORDERINFO_AMOUNT_FIELD.has_default_value = false
VENDORORDERINFO_AMOUNT_FIELD.default_value = ""
VENDORORDERINFO_AMOUNT_FIELD.type = 9
VENDORORDERINFO_AMOUNT_FIELD.cpp_type = 9

VENDORORDERINFO_MINMONEY_FIELD.name = "minmoney"
VENDORORDERINFO_MINMONEY_FIELD.full_name = ".prootc.vendororderinfo.minmoney"
VENDORORDERINFO_MINMONEY_FIELD.number = 7
VENDORORDERINFO_MINMONEY_FIELD.index = 6
VENDORORDERINFO_MINMONEY_FIELD.label = 1
VENDORORDERINFO_MINMONEY_FIELD.has_default_value = false
VENDORORDERINFO_MINMONEY_FIELD.default_value = ""
VENDORORDERINFO_MINMONEY_FIELD.type = 9
VENDORORDERINFO_MINMONEY_FIELD.cpp_type = 9

VENDORORDERINFO_MAXMONEY_FIELD.name = "maxmoney"
VENDORORDERINFO_MAXMONEY_FIELD.full_name = ".prootc.vendororderinfo.maxmoney"
VENDORORDERINFO_MAXMONEY_FIELD.number = 8
VENDORORDERINFO_MAXMONEY_FIELD.index = 7
VENDORORDERINFO_MAXMONEY_FIELD.label = 1
VENDORORDERINFO_MAXMONEY_FIELD.has_default_value = false
VENDORORDERINFO_MAXMONEY_FIELD.default_value = ""
VENDORORDERINFO_MAXMONEY_FIELD.type = 9
VENDORORDERINFO_MAXMONEY_FIELD.cpp_type = 9

VENDORORDERINFO_AUTOSWITCH_FIELD.name = "autoswitch"
VENDORORDERINFO_AUTOSWITCH_FIELD.full_name = ".prootc.vendororderinfo.autoswitch"
VENDORORDERINFO_AUTOSWITCH_FIELD.number = 9
VENDORORDERINFO_AUTOSWITCH_FIELD.index = 8
VENDORORDERINFO_AUTOSWITCH_FIELD.label = 1
VENDORORDERINFO_AUTOSWITCH_FIELD.has_default_value = false
VENDORORDERINFO_AUTOSWITCH_FIELD.default_value = 0
VENDORORDERINFO_AUTOSWITCH_FIELD.type = 5
VENDORORDERINFO_AUTOSWITCH_FIELD.cpp_type = 1

VENDORORDERINFO_MESSAGE_FIELD.name = "message"
VENDORORDERINFO_MESSAGE_FIELD.full_name = ".prootc.vendororderinfo.message"
VENDORORDERINFO_MESSAGE_FIELD.number = 10
VENDORORDERINFO_MESSAGE_FIELD.index = 9
VENDORORDERINFO_MESSAGE_FIELD.label = 1
VENDORORDERINFO_MESSAGE_FIELD.has_default_value = false
VENDORORDERINFO_MESSAGE_FIELD.default_value = ""
VENDORORDERINFO_MESSAGE_FIELD.type = 9
VENDORORDERINFO_MESSAGE_FIELD.cpp_type = 9

VENDORORDERINFO_MAXAMOUNT_FIELD.name = "maxamount"
VENDORORDERINFO_MAXAMOUNT_FIELD.full_name = ".prootc.vendororderinfo.maxamount"
VENDORORDERINFO_MAXAMOUNT_FIELD.number = 11
VENDORORDERINFO_MAXAMOUNT_FIELD.index = 10
VENDORORDERINFO_MAXAMOUNT_FIELD.label = 1
VENDORORDERINFO_MAXAMOUNT_FIELD.has_default_value = false
VENDORORDERINFO_MAXAMOUNT_FIELD.default_value = ""
VENDORORDERINFO_MAXAMOUNT_FIELD.type = 9
VENDORORDERINFO_MAXAMOUNT_FIELD.cpp_type = 9

VENDORORDERINFO_ENABLESTATUS_FIELD.name = "enablestatus"
VENDORORDERINFO_ENABLESTATUS_FIELD.full_name = ".prootc.vendororderinfo.enablestatus"
VENDORORDERINFO_ENABLESTATUS_FIELD.number = 12
VENDORORDERINFO_ENABLESTATUS_FIELD.index = 11
VENDORORDERINFO_ENABLESTATUS_FIELD.label = 1
VENDORORDERINFO_ENABLESTATUS_FIELD.has_default_value = false
VENDORORDERINFO_ENABLESTATUS_FIELD.default_value = 0
VENDORORDERINFO_ENABLESTATUS_FIELD.type = 5
VENDORORDERINFO_ENABLESTATUS_FIELD.cpp_type = 1

VENDORORDERINFO_CHANNEL_FIELD.name = "channel"
VENDORORDERINFO_CHANNEL_FIELD.full_name = ".prootc.vendororderinfo.channel"
VENDORORDERINFO_CHANNEL_FIELD.number = 13
VENDORORDERINFO_CHANNEL_FIELD.index = 12
VENDORORDERINFO_CHANNEL_FIELD.label = 1
VENDORORDERINFO_CHANNEL_FIELD.has_default_value = false
VENDORORDERINFO_CHANNEL_FIELD.default_value = ""
VENDORORDERINFO_CHANNEL_FIELD.type = 9
VENDORORDERINFO_CHANNEL_FIELD.cpp_type = 9

VENDORORDERINFO_NICKNAME_FIELD.name = "nickname"
VENDORORDERINFO_NICKNAME_FIELD.full_name = ".prootc.vendororderinfo.nickname"
VENDORORDERINFO_NICKNAME_FIELD.number = 14
VENDORORDERINFO_NICKNAME_FIELD.index = 13
VENDORORDERINFO_NICKNAME_FIELD.label = 1
VENDORORDERINFO_NICKNAME_FIELD.has_default_value = false
VENDORORDERINFO_NICKNAME_FIELD.default_value = ""
VENDORORDERINFO_NICKNAME_FIELD.type = 9
VENDORORDERINFO_NICKNAME_FIELD.cpp_type = 9

VENDORORDERINFO_UNSOLDORDERNUM_FIELD.name = "unsoldordernum"
VENDORORDERINFO_UNSOLDORDERNUM_FIELD.full_name = ".prootc.vendororderinfo.unsoldordernum"
VENDORORDERINFO_UNSOLDORDERNUM_FIELD.number = 15
VENDORORDERINFO_UNSOLDORDERNUM_FIELD.index = 14
VENDORORDERINFO_UNSOLDORDERNUM_FIELD.label = 1
VENDORORDERINFO_UNSOLDORDERNUM_FIELD.has_default_value = false
VENDORORDERINFO_UNSOLDORDERNUM_FIELD.default_value = 0
VENDORORDERINFO_UNSOLDORDERNUM_FIELD.type = 5
VENDORORDERINFO_UNSOLDORDERNUM_FIELD.cpp_type = 1

VENDORORDERINFO_DEALORDERNUM_FIELD.name = "dealordernum"
VENDORORDERINFO_DEALORDERNUM_FIELD.full_name = ".prootc.vendororderinfo.dealordernum"
VENDORORDERINFO_DEALORDERNUM_FIELD.number = 16
VENDORORDERINFO_DEALORDERNUM_FIELD.index = 15
VENDORORDERINFO_DEALORDERNUM_FIELD.label = 1
VENDORORDERINFO_DEALORDERNUM_FIELD.has_default_value = false
VENDORORDERINFO_DEALORDERNUM_FIELD.default_value = 0
VENDORORDERINFO_DEALORDERNUM_FIELD.type = 5
VENDORORDERINFO_DEALORDERNUM_FIELD.cpp_type = 1

VENDORORDERINFO_UNSOLDORDERAMOUNT_FIELD.name = "unsoldorderamount"
VENDORORDERINFO_UNSOLDORDERAMOUNT_FIELD.full_name = ".prootc.vendororderinfo.unsoldorderamount"
VENDORORDERINFO_UNSOLDORDERAMOUNT_FIELD.number = 18
VENDORORDERINFO_UNSOLDORDERAMOUNT_FIELD.index = 16
VENDORORDERINFO_UNSOLDORDERAMOUNT_FIELD.label = 1
VENDORORDERINFO_UNSOLDORDERAMOUNT_FIELD.has_default_value = false
VENDORORDERINFO_UNSOLDORDERAMOUNT_FIELD.default_value = ""
VENDORORDERINFO_UNSOLDORDERAMOUNT_FIELD.type = 9
VENDORORDERINFO_UNSOLDORDERAMOUNT_FIELD.cpp_type = 9

VENDORORDERINFO_DEALORDERAMOUNT_FIELD.name = "dealorderamount"
VENDORORDERINFO_DEALORDERAMOUNT_FIELD.full_name = ".prootc.vendororderinfo.dealorderamount"
VENDORORDERINFO_DEALORDERAMOUNT_FIELD.number = 19
VENDORORDERINFO_DEALORDERAMOUNT_FIELD.index = 17
VENDORORDERINFO_DEALORDERAMOUNT_FIELD.label = 1
VENDORORDERINFO_DEALORDERAMOUNT_FIELD.has_default_value = false
VENDORORDERINFO_DEALORDERAMOUNT_FIELD.default_value = ""
VENDORORDERINFO_DEALORDERAMOUNT_FIELD.type = 9
VENDORORDERINFO_DEALORDERAMOUNT_FIELD.cpp_type = 9

VENDORORDERINFO_PAYLIST_FIELD.name = "paylist"
VENDORORDERINFO_PAYLIST_FIELD.full_name = ".prootc.vendororderinfo.paylist"
VENDORORDERINFO_PAYLIST_FIELD.number = 20
VENDORORDERINFO_PAYLIST_FIELD.index = 18
VENDORORDERINFO_PAYLIST_FIELD.label = 3
VENDORORDERINFO_PAYLIST_FIELD.has_default_value = false
VENDORORDERINFO_PAYLIST_FIELD.default_value = {}
VENDORORDERINFO_PAYLIST_FIELD.type = 5
VENDORORDERINFO_PAYLIST_FIELD.cpp_type = 1

VENDORORDERINFO_CANCELNUM_FIELD.name = "cancelnum"
VENDORORDERINFO_CANCELNUM_FIELD.full_name = ".prootc.vendororderinfo.cancelnum"
VENDORORDERINFO_CANCELNUM_FIELD.number = 21
VENDORORDERINFO_CANCELNUM_FIELD.index = 19
VENDORORDERINFO_CANCELNUM_FIELD.label = 1
VENDORORDERINFO_CANCELNUM_FIELD.has_default_value = false
VENDORORDERINFO_CANCELNUM_FIELD.default_value = 0
VENDORORDERINFO_CANCELNUM_FIELD.type = 5
VENDORORDERINFO_CANCELNUM_FIELD.cpp_type = 1

VENDORORDERINFO_USERTYPE_FIELD.name = "usertype"
VENDORORDERINFO_USERTYPE_FIELD.full_name = ".prootc.vendororderinfo.usertype"
VENDORORDERINFO_USERTYPE_FIELD.number = 22
VENDORORDERINFO_USERTYPE_FIELD.index = 20
VENDORORDERINFO_USERTYPE_FIELD.label = 1
VENDORORDERINFO_USERTYPE_FIELD.has_default_value = false
VENDORORDERINFO_USERTYPE_FIELD.default_value = 0
VENDORORDERINFO_USERTYPE_FIELD.type = 5
VENDORORDERINFO_USERTYPE_FIELD.cpp_type = 1

VENDORORDERINFO_PAYEEACCOUNT_FIELD.name = "payeeaccount"
VENDORORDERINFO_PAYEEACCOUNT_FIELD.full_name = ".prootc.vendororderinfo.payeeaccount"
VENDORORDERINFO_PAYEEACCOUNT_FIELD.number = 23
VENDORORDERINFO_PAYEEACCOUNT_FIELD.index = 21
VENDORORDERINFO_PAYEEACCOUNT_FIELD.label = 1
VENDORORDERINFO_PAYEEACCOUNT_FIELD.has_default_value = false
VENDORORDERINFO_PAYEEACCOUNT_FIELD.default_value = ""
VENDORORDERINFO_PAYEEACCOUNT_FIELD.type = 9
VENDORORDERINFO_PAYEEACCOUNT_FIELD.cpp_type = 9

VENDORORDERINFO_PAYEENAME_FIELD.name = "payeename"
VENDORORDERINFO_PAYEENAME_FIELD.full_name = ".prootc.vendororderinfo.payeename"
VENDORORDERINFO_PAYEENAME_FIELD.number = 24
VENDORORDERINFO_PAYEENAME_FIELD.index = 22
VENDORORDERINFO_PAYEENAME_FIELD.label = 1
VENDORORDERINFO_PAYEENAME_FIELD.has_default_value = false
VENDORORDERINFO_PAYEENAME_FIELD.default_value = ""
VENDORORDERINFO_PAYEENAME_FIELD.type = 9
VENDORORDERINFO_PAYEENAME_FIELD.cpp_type = 9

VENDORORDERINFO_PAYEEBANK_FIELD.name = "payeebank"
VENDORORDERINFO_PAYEEBANK_FIELD.full_name = ".prootc.vendororderinfo.payeebank"
VENDORORDERINFO_PAYEEBANK_FIELD.number = 25
VENDORORDERINFO_PAYEEBANK_FIELD.index = 23
VENDORORDERINFO_PAYEEBANK_FIELD.label = 1
VENDORORDERINFO_PAYEEBANK_FIELD.has_default_value = false
VENDORORDERINFO_PAYEEBANK_FIELD.default_value = ""
VENDORORDERINFO_PAYEEBANK_FIELD.type = 9
VENDORORDERINFO_PAYEEBANK_FIELD.cpp_type = 9

VENDORORDERINFO_PAYEEBANDADDR_FIELD.name = "payeebandaddr"
VENDORORDERINFO_PAYEEBANDADDR_FIELD.full_name = ".prootc.vendororderinfo.payeebandaddr"
VENDORORDERINFO_PAYEEBANDADDR_FIELD.number = 26
VENDORORDERINFO_PAYEEBANDADDR_FIELD.index = 24
VENDORORDERINFO_PAYEEBANDADDR_FIELD.label = 1
VENDORORDERINFO_PAYEEBANDADDR_FIELD.has_default_value = false
VENDORORDERINFO_PAYEEBANDADDR_FIELD.default_value = ""
VENDORORDERINFO_PAYEEBANDADDR_FIELD.type = 9
VENDORORDERINFO_PAYEEBANDADDR_FIELD.cpp_type = 9

VENDORORDERINFO_DEAL_ORDER_FREE_FIELD.name = "deal_order_free"
VENDORORDERINFO_DEAL_ORDER_FREE_FIELD.full_name = ".prootc.vendororderinfo.deal_order_free"
VENDORORDERINFO_DEAL_ORDER_FREE_FIELD.number = 27
VENDORORDERINFO_DEAL_ORDER_FREE_FIELD.index = 25
VENDORORDERINFO_DEAL_ORDER_FREE_FIELD.label = 1
VENDORORDERINFO_DEAL_ORDER_FREE_FIELD.has_default_value = false
VENDORORDERINFO_DEAL_ORDER_FREE_FIELD.default_value = ""
VENDORORDERINFO_DEAL_ORDER_FREE_FIELD.type = 9
VENDORORDERINFO_DEAL_ORDER_FREE_FIELD.cpp_type = 9

VENDORORDERINFO_FREE_FIELD.name = "free"
VENDORORDERINFO_FREE_FIELD.full_name = ".prootc.vendororderinfo.free"
VENDORORDERINFO_FREE_FIELD.number = 28
VENDORORDERINFO_FREE_FIELD.index = 26
VENDORORDERINFO_FREE_FIELD.label = 1
VENDORORDERINFO_FREE_FIELD.has_default_value = false
VENDORORDERINFO_FREE_FIELD.default_value = ""
VENDORORDERINFO_FREE_FIELD.type = 9
VENDORORDERINFO_FREE_FIELD.cpp_type = 9

VENDORORDERINFO_FEERATE_FIELD.name = "feeRate"
VENDORORDERINFO_FEERATE_FIELD.full_name = ".prootc.vendororderinfo.feeRate"
VENDORORDERINFO_FEERATE_FIELD.number = 29
VENDORORDERINFO_FEERATE_FIELD.index = 27
VENDORORDERINFO_FEERATE_FIELD.label = 3
VENDORORDERINFO_FEERATE_FIELD.has_default_value = false
VENDORORDERINFO_FEERATE_FIELD.default_value = {}
VENDORORDERINFO_FEERATE_FIELD.type = 9
VENDORORDERINFO_FEERATE_FIELD.cpp_type = 9

VENDORORDERINFO_ADDFEERATE_FIELD.name = "addfeerate"
VENDORORDERINFO_ADDFEERATE_FIELD.full_name = ".prootc.vendororderinfo.addfeerate"
VENDORORDERINFO_ADDFEERATE_FIELD.number = 30
VENDORORDERINFO_ADDFEERATE_FIELD.index = 28
VENDORORDERINFO_ADDFEERATE_FIELD.label = 1
VENDORORDERINFO_ADDFEERATE_FIELD.has_default_value = false
VENDORORDERINFO_ADDFEERATE_FIELD.default_value = ""
VENDORORDERINFO_ADDFEERATE_FIELD.type = 9
VENDORORDERINFO_ADDFEERATE_FIELD.cpp_type = 9

VENDORORDERINFO_CREATETIME_FIELD.name = "createtime"
VENDORORDERINFO_CREATETIME_FIELD.full_name = ".prootc.vendororderinfo.createtime"
VENDORORDERINFO_CREATETIME_FIELD.number = 31
VENDORORDERINFO_CREATETIME_FIELD.index = 29
VENDORORDERINFO_CREATETIME_FIELD.label = 1
VENDORORDERINFO_CREATETIME_FIELD.has_default_value = false
VENDORORDERINFO_CREATETIME_FIELD.default_value = ""
VENDORORDERINFO_CREATETIME_FIELD.type = 9
VENDORORDERINFO_CREATETIME_FIELD.cpp_type = 9

VENDORORDERINFO_CHANNELDEAL_FIELD.name = "channeldeal"
VENDORORDERINFO_CHANNELDEAL_FIELD.full_name = ".prootc.vendororderinfo.channeldeal"
VENDORORDERINFO_CHANNELDEAL_FIELD.number = 32
VENDORORDERINFO_CHANNELDEAL_FIELD.index = 30
VENDORORDERINFO_CHANNELDEAL_FIELD.label = 1
VENDORORDERINFO_CHANNELDEAL_FIELD.has_default_value = false
VENDORORDERINFO_CHANNELDEAL_FIELD.default_value = 0
VENDORORDERINFO_CHANNELDEAL_FIELD.type = 5
VENDORORDERINFO_CHANNELDEAL_FIELD.cpp_type = 1

VENDORORDERINFO_DEALMODEL_FIELD.name = "dealmodel"
VENDORORDERINFO_DEALMODEL_FIELD.full_name = ".prootc.vendororderinfo.dealmodel"
VENDORORDERINFO_DEALMODEL_FIELD.number = 33
VENDORORDERINFO_DEALMODEL_FIELD.index = 31
VENDORORDERINFO_DEALMODEL_FIELD.label = 1
VENDORORDERINFO_DEALMODEL_FIELD.has_default_value = false
VENDORORDERINFO_DEALMODEL_FIELD.default_value = 0
VENDORORDERINFO_DEALMODEL_FIELD.type = 5
VENDORORDERINFO_DEALMODEL_FIELD.cpp_type = 1

VENDORORDERINFO_PREDICTMONEY_FIELD.name = "predictmoney"
VENDORORDERINFO_PREDICTMONEY_FIELD.full_name = ".prootc.vendororderinfo.predictmoney"
VENDORORDERINFO_PREDICTMONEY_FIELD.number = 34
VENDORORDERINFO_PREDICTMONEY_FIELD.index = 32
VENDORORDERINFO_PREDICTMONEY_FIELD.label = 1
VENDORORDERINFO_PREDICTMONEY_FIELD.has_default_value = false
VENDORORDERINFO_PREDICTMONEY_FIELD.default_value = ""
VENDORORDERINFO_PREDICTMONEY_FIELD.type = 9
VENDORORDERINFO_PREDICTMONEY_FIELD.cpp_type = 9

VENDORORDERINFO_RESTTIME_FIELD.name = "resttime"
VENDORORDERINFO_RESTTIME_FIELD.full_name = ".prootc.vendororderinfo.resttime"
VENDORORDERINFO_RESTTIME_FIELD.number = 35
VENDORORDERINFO_RESTTIME_FIELD.index = 33
VENDORORDERINFO_RESTTIME_FIELD.label = 1
VENDORORDERINFO_RESTTIME_FIELD.has_default_value = false
VENDORORDERINFO_RESTTIME_FIELD.default_value = ""
VENDORORDERINFO_RESTTIME_FIELD.type = 9
VENDORORDERINFO_RESTTIME_FIELD.cpp_type = 9

VENDORORDERINFO_DEALMONERY_FIELD.name = "dealmonery"
VENDORORDERINFO_DEALMONERY_FIELD.full_name = ".prootc.vendororderinfo.dealmonery"
VENDORORDERINFO_DEALMONERY_FIELD.number = 36
VENDORORDERINFO_DEALMONERY_FIELD.index = 34
VENDORORDERINFO_DEALMONERY_FIELD.label = 1
VENDORORDERINFO_DEALMONERY_FIELD.has_default_value = false
VENDORORDERINFO_DEALMONERY_FIELD.default_value = ""
VENDORORDERINFO_DEALMONERY_FIELD.type = 9
VENDORORDERINFO_DEALMONERY_FIELD.cpp_type = 9

VENDORORDERINFO_DEALCOUNT_FIELD.name = "dealcount"
VENDORORDERINFO_DEALCOUNT_FIELD.full_name = ".prootc.vendororderinfo.dealcount"
VENDORORDERINFO_DEALCOUNT_FIELD.number = 37
VENDORORDERINFO_DEALCOUNT_FIELD.index = 35
VENDORORDERINFO_DEALCOUNT_FIELD.label = 1
VENDORORDERINFO_DEALCOUNT_FIELD.has_default_value = false
VENDORORDERINFO_DEALCOUNT_FIELD.default_value = ""
VENDORORDERINFO_DEALCOUNT_FIELD.type = 9
VENDORORDERINFO_DEALCOUNT_FIELD.cpp_type = 9

VENDORORDERINFO_WITHDRAWTYPE_FIELD.name = "withdrawtype"
VENDORORDERINFO_WITHDRAWTYPE_FIELD.full_name = ".prootc.vendororderinfo.withdrawtype"
VENDORORDERINFO_WITHDRAWTYPE_FIELD.number = 38
VENDORORDERINFO_WITHDRAWTYPE_FIELD.index = 36
VENDORORDERINFO_WITHDRAWTYPE_FIELD.label = 1
VENDORORDERINFO_WITHDRAWTYPE_FIELD.has_default_value = false
VENDORORDERINFO_WITHDRAWTYPE_FIELD.default_value = 0
VENDORORDERINFO_WITHDRAWTYPE_FIELD.type = 5
VENDORORDERINFO_WITHDRAWTYPE_FIELD.cpp_type = 1

VENDORORDERINFO_COINID_FIELD.name = "coinid"
VENDORORDERINFO_COINID_FIELD.full_name = ".prootc.vendororderinfo.coinid"
VENDORORDERINFO_COINID_FIELD.number = 39
VENDORORDERINFO_COINID_FIELD.index = 37
VENDORORDERINFO_COINID_FIELD.label = 1
VENDORORDERINFO_COINID_FIELD.has_default_value = false
VENDORORDERINFO_COINID_FIELD.default_value = 0
VENDORORDERINFO_COINID_FIELD.type = 5
VENDORORDERINFO_COINID_FIELD.cpp_type = 1

VENDORORDERINFO_COINNAME_FIELD.name = "coinname"
VENDORORDERINFO_COINNAME_FIELD.full_name = ".prootc.vendororderinfo.coinname"
VENDORORDERINFO_COINNAME_FIELD.number = 40
VENDORORDERINFO_COINNAME_FIELD.index = 38
VENDORORDERINFO_COINNAME_FIELD.label = 1
VENDORORDERINFO_COINNAME_FIELD.has_default_value = false
VENDORORDERINFO_COINNAME_FIELD.default_value = ""
VENDORORDERINFO_COINNAME_FIELD.type = 9
VENDORORDERINFO_COINNAME_FIELD.cpp_type = 9

VENDORORDERINFO_ISEXTERNAL_FIELD.name = "isexternal"
VENDORORDERINFO_ISEXTERNAL_FIELD.full_name = ".prootc.vendororderinfo.isexternal"
VENDORORDERINFO_ISEXTERNAL_FIELD.number = 41
VENDORORDERINFO_ISEXTERNAL_FIELD.index = 39
VENDORORDERINFO_ISEXTERNAL_FIELD.label = 1
VENDORORDERINFO_ISEXTERNAL_FIELD.has_default_value = false
VENDORORDERINFO_ISEXTERNAL_FIELD.default_value = 0
VENDORORDERINFO_ISEXTERNAL_FIELD.type = 5
VENDORORDERINFO_ISEXTERNAL_FIELD.cpp_type = 1

VENDORORDERINFO_UNSOLDORDERMONEY_FIELD.name = "unsoldordermoney"
VENDORORDERINFO_UNSOLDORDERMONEY_FIELD.full_name = ".prootc.vendororderinfo.unsoldordermoney"
VENDORORDERINFO_UNSOLDORDERMONEY_FIELD.number = 42
VENDORORDERINFO_UNSOLDORDERMONEY_FIELD.index = 40
VENDORORDERINFO_UNSOLDORDERMONEY_FIELD.label = 1
VENDORORDERINFO_UNSOLDORDERMONEY_FIELD.has_default_value = false
VENDORORDERINFO_UNSOLDORDERMONEY_FIELD.default_value = ""
VENDORORDERINFO_UNSOLDORDERMONEY_FIELD.type = 9
VENDORORDERINFO_UNSOLDORDERMONEY_FIELD.cpp_type = 9

VENDORORDERINFO_DEALORDERMONEY_FIELD.name = "dealordermoney"
VENDORORDERINFO_DEALORDERMONEY_FIELD.full_name = ".prootc.vendororderinfo.dealordermoney"
VENDORORDERINFO_DEALORDERMONEY_FIELD.number = 43
VENDORORDERINFO_DEALORDERMONEY_FIELD.index = 41
VENDORORDERINFO_DEALORDERMONEY_FIELD.label = 1
VENDORORDERINFO_DEALORDERMONEY_FIELD.has_default_value = false
VENDORORDERINFO_DEALORDERMONEY_FIELD.default_value = ""
VENDORORDERINFO_DEALORDERMONEY_FIELD.type = 9
VENDORORDERINFO_DEALORDERMONEY_FIELD.cpp_type = 9

VENDORORDERINFO_EXTERNALORDERUSERID_FIELD.name = "externalorderuserid"
VENDORORDERINFO_EXTERNALORDERUSERID_FIELD.full_name = ".prootc.vendororderinfo.externalorderuserid"
VENDORORDERINFO_EXTERNALORDERUSERID_FIELD.number = 44
VENDORORDERINFO_EXTERNALORDERUSERID_FIELD.index = 42
VENDORORDERINFO_EXTERNALORDERUSERID_FIELD.label = 1
VENDORORDERINFO_EXTERNALORDERUSERID_FIELD.has_default_value = false
VENDORORDERINFO_EXTERNALORDERUSERID_FIELD.default_value = 0
VENDORORDERINFO_EXTERNALORDERUSERID_FIELD.type = 5
VENDORORDERINFO_EXTERNALORDERUSERID_FIELD.cpp_type = 1

VENDORORDERINFO_ISASSIGNED_FIELD.name = "isassigned"
VENDORORDERINFO_ISASSIGNED_FIELD.full_name = ".prootc.vendororderinfo.isassigned"
VENDORORDERINFO_ISASSIGNED_FIELD.number = 46
VENDORORDERINFO_ISASSIGNED_FIELD.index = 43
VENDORORDERINFO_ISASSIGNED_FIELD.label = 1
VENDORORDERINFO_ISASSIGNED_FIELD.has_default_value = false
VENDORORDERINFO_ISASSIGNED_FIELD.default_value = 0
VENDORORDERINFO_ISASSIGNED_FIELD.type = 5
VENDORORDERINFO_ISASSIGNED_FIELD.cpp_type = 1

VENDORORDERINFO_ISTAKE_FIELD.name = "istake"
VENDORORDERINFO_ISTAKE_FIELD.full_name = ".prootc.vendororderinfo.istake"
VENDORORDERINFO_ISTAKE_FIELD.number = 47
VENDORORDERINFO_ISTAKE_FIELD.index = 44
VENDORORDERINFO_ISTAKE_FIELD.label = 1
VENDORORDERINFO_ISTAKE_FIELD.has_default_value = false
VENDORORDERINFO_ISTAKE_FIELD.default_value = 0
VENDORORDERINFO_ISTAKE_FIELD.type = 5
VENDORORDERINFO_ISTAKE_FIELD.cpp_type = 1

VENDORORDERINFO_WALLETTYPE_FIELD.name = "wallettype"
VENDORORDERINFO_WALLETTYPE_FIELD.full_name = ".prootc.vendororderinfo.wallettype"
VENDORORDERINFO_WALLETTYPE_FIELD.number = 48
VENDORORDERINFO_WALLETTYPE_FIELD.index = 45
VENDORORDERINFO_WALLETTYPE_FIELD.label = 1
VENDORORDERINFO_WALLETTYPE_FIELD.has_default_value = false
VENDORORDERINFO_WALLETTYPE_FIELD.default_value = 0
VENDORORDERINFO_WALLETTYPE_FIELD.type = 5
VENDORORDERINFO_WALLETTYPE_FIELD.cpp_type = 1

VENDORORDERINFO.name = "vendororderinfo"
VENDORORDERINFO.full_name = ".prootc.vendororderinfo"
VENDORORDERINFO.nested_types = {}
VENDORORDERINFO.enum_types = {}
VENDORORDERINFO.fields = {VENDORORDERINFO_USERID_FIELD, VENDORORDERINFO_HANGID_FIELD, VENDORORDERINFO_TYPE_FIELD, VENDORORDERINFO_PRICETYPE_FIELD, VENDORORDERINFO_PRICE_FIELD, VENDORORDERINFO_AMOUNT_FIELD, VENDORORDERINFO_MINMONEY_FIELD, VENDORORDERINFO_MAXMONEY_FIELD, VENDORORDERINFO_AUTOSWITCH_FIELD, VENDORORDERINFO_MESSAGE_FIELD, VENDORORDERINFO_MAXAMOUNT_FIELD, VENDORORDERINFO_ENABLESTATUS_FIELD, VENDORORDERINFO_CHANNEL_FIELD, VENDORORDERINFO_NICKNAME_FIELD, VENDORORDERINFO_UNSOLDORDERNUM_FIELD, VENDORORDERINFO_DEALORDERNUM_FIELD, VENDORORDERINFO_UNSOLDORDERAMOUNT_FIELD, VENDORORDERINFO_DEALORDERAMOUNT_FIELD, VENDORORDERINFO_PAYLIST_FIELD, VENDORORDERINFO_CANCELNUM_FIELD, VENDORORDERINFO_USERTYPE_FIELD, VENDORORDERINFO_PAYEEACCOUNT_FIELD, VENDORORDERINFO_PAYEENAME_FIELD, VENDORORDERINFO_PAYEEBANK_FIELD, VENDORORDERINFO_PAYEEBANDADDR_FIELD, VENDORORDERINFO_DEAL_ORDER_FREE_FIELD, VENDORORDERINFO_FREE_FIELD, VENDORORDERINFO_FEERATE_FIELD, VENDORORDERINFO_ADDFEERATE_FIELD, VENDORORDERINFO_CREATETIME_FIELD, VENDORORDERINFO_CHANNELDEAL_FIELD, VENDORORDERINFO_DEALMODEL_FIELD, VENDORORDERINFO_PREDICTMONEY_FIELD, VENDORORDERINFO_RESTTIME_FIELD, VENDORORDERINFO_DEALMONERY_FIELD, VENDORORDERINFO_DEALCOUNT_FIELD, VENDORORDERINFO_WITHDRAWTYPE_FIELD, VENDORORDERINFO_COINID_FIELD, VENDORORDERINFO_COINNAME_FIELD, VENDORORDERINFO_ISEXTERNAL_FIELD, VENDORORDERINFO_UNSOLDORDERMONEY_FIELD, VENDORORDERINFO_DEALORDERMONEY_FIELD, VENDORORDERINFO_EXTERNALORDERUSERID_FIELD, VENDORORDERINFO_ISASSIGNED_FIELD, VENDORORDERINFO_ISTAKE_FIELD, VENDORORDERINFO_WALLETTYPE_FIELD}
VENDORORDERINFO.is_extendable = false
VENDORORDERINFO.extensions = {}
CUSTOMERORDERINFO_DEALID_FIELD.name = "dealid"
CUSTOMERORDERINFO_DEALID_FIELD.full_name = ".prootc.customerorderinfo.dealid"
CUSTOMERORDERINFO_DEALID_FIELD.number = 1
CUSTOMERORDERINFO_DEALID_FIELD.index = 0
CUSTOMERORDERINFO_DEALID_FIELD.label = 1
CUSTOMERORDERINFO_DEALID_FIELD.has_default_value = false
CUSTOMERORDERINFO_DEALID_FIELD.default_value = 0
CUSTOMERORDERINFO_DEALID_FIELD.type = 5
CUSTOMERORDERINFO_DEALID_FIELD.cpp_type = 1

CUSTOMERORDERINFO_TYPE_FIELD.name = "type"
CUSTOMERORDERINFO_TYPE_FIELD.full_name = ".prootc.customerorderinfo.type"
CUSTOMERORDERINFO_TYPE_FIELD.number = 2
CUSTOMERORDERINFO_TYPE_FIELD.index = 1
CUSTOMERORDERINFO_TYPE_FIELD.label = 1
CUSTOMERORDERINFO_TYPE_FIELD.has_default_value = false
CUSTOMERORDERINFO_TYPE_FIELD.default_value = 0
CUSTOMERORDERINFO_TYPE_FIELD.type = 5
CUSTOMERORDERINFO_TYPE_FIELD.cpp_type = 1

CUSTOMERORDERINFO_VENDORORDERID_FIELD.name = "vendororderid"
CUSTOMERORDERINFO_VENDORORDERID_FIELD.full_name = ".prootc.customerorderinfo.vendororderid"
CUSTOMERORDERINFO_VENDORORDERID_FIELD.number = 3
CUSTOMERORDERINFO_VENDORORDERID_FIELD.index = 2
CUSTOMERORDERINFO_VENDORORDERID_FIELD.label = 1
CUSTOMERORDERINFO_VENDORORDERID_FIELD.has_default_value = false
CUSTOMERORDERINFO_VENDORORDERID_FIELD.default_value = 0
CUSTOMERORDERINFO_VENDORORDERID_FIELD.type = 5
CUSTOMERORDERINFO_VENDORORDERID_FIELD.cpp_type = 1

CUSTOMERORDERINFO_CUSTOMERUSERID_FIELD.name = "customeruserid"
CUSTOMERORDERINFO_CUSTOMERUSERID_FIELD.full_name = ".prootc.customerorderinfo.customeruserid"
CUSTOMERORDERINFO_CUSTOMERUSERID_FIELD.number = 4
CUSTOMERORDERINFO_CUSTOMERUSERID_FIELD.index = 3
CUSTOMERORDERINFO_CUSTOMERUSERID_FIELD.label = 1
CUSTOMERORDERINFO_CUSTOMERUSERID_FIELD.has_default_value = false
CUSTOMERORDERINFO_CUSTOMERUSERID_FIELD.default_value = 0
CUSTOMERORDERINFO_CUSTOMERUSERID_FIELD.type = 5
CUSTOMERORDERINFO_CUSTOMERUSERID_FIELD.cpp_type = 1

CUSTOMERORDERINFO_VENDORUSERID_FIELD.name = "vendoruserid"
CUSTOMERORDERINFO_VENDORUSERID_FIELD.full_name = ".prootc.customerorderinfo.vendoruserid"
CUSTOMERORDERINFO_VENDORUSERID_FIELD.number = 5
CUSTOMERORDERINFO_VENDORUSERID_FIELD.index = 4
CUSTOMERORDERINFO_VENDORUSERID_FIELD.label = 1
CUSTOMERORDERINFO_VENDORUSERID_FIELD.has_default_value = false
CUSTOMERORDERINFO_VENDORUSERID_FIELD.default_value = 0
CUSTOMERORDERINFO_VENDORUSERID_FIELD.type = 5
CUSTOMERORDERINFO_VENDORUSERID_FIELD.cpp_type = 1

CUSTOMERORDERINFO_PRICE_FIELD.name = "price"
CUSTOMERORDERINFO_PRICE_FIELD.full_name = ".prootc.customerorderinfo.price"
CUSTOMERORDERINFO_PRICE_FIELD.number = 6
CUSTOMERORDERINFO_PRICE_FIELD.index = 5
CUSTOMERORDERINFO_PRICE_FIELD.label = 1
CUSTOMERORDERINFO_PRICE_FIELD.has_default_value = false
CUSTOMERORDERINFO_PRICE_FIELD.default_value = ""
CUSTOMERORDERINFO_PRICE_FIELD.type = 9
CUSTOMERORDERINFO_PRICE_FIELD.cpp_type = 9

CUSTOMERORDERINFO_AMOUNT_FIELD.name = "amount"
CUSTOMERORDERINFO_AMOUNT_FIELD.full_name = ".prootc.customerorderinfo.amount"
CUSTOMERORDERINFO_AMOUNT_FIELD.number = 7
CUSTOMERORDERINFO_AMOUNT_FIELD.index = 6
CUSTOMERORDERINFO_AMOUNT_FIELD.label = 1
CUSTOMERORDERINFO_AMOUNT_FIELD.has_default_value = false
CUSTOMERORDERINFO_AMOUNT_FIELD.default_value = ""
CUSTOMERORDERINFO_AMOUNT_FIELD.type = 9
CUSTOMERORDERINFO_AMOUNT_FIELD.cpp_type = 9

CUSTOMERORDERINFO_MONEY_FIELD.name = "money"
CUSTOMERORDERINFO_MONEY_FIELD.full_name = ".prootc.customerorderinfo.money"
CUSTOMERORDERINFO_MONEY_FIELD.number = 8
CUSTOMERORDERINFO_MONEY_FIELD.index = 7
CUSTOMERORDERINFO_MONEY_FIELD.label = 1
CUSTOMERORDERINFO_MONEY_FIELD.has_default_value = false
CUSTOMERORDERINFO_MONEY_FIELD.default_value = ""
CUSTOMERORDERINFO_MONEY_FIELD.type = 9
CUSTOMERORDERINFO_MONEY_FIELD.cpp_type = 9

CUSTOMERORDERINFO_STATUS_FIELD.name = "status"
CUSTOMERORDERINFO_STATUS_FIELD.full_name = ".prootc.customerorderinfo.status"
CUSTOMERORDERINFO_STATUS_FIELD.number = 9
CUSTOMERORDERINFO_STATUS_FIELD.index = 8
CUSTOMERORDERINFO_STATUS_FIELD.label = 1
CUSTOMERORDERINFO_STATUS_FIELD.has_default_value = false
CUSTOMERORDERINFO_STATUS_FIELD.default_value = 0
CUSTOMERORDERINFO_STATUS_FIELD.type = 5
CUSTOMERORDERINFO_STATUS_FIELD.cpp_type = 1

CUSTOMERORDERINFO_MERCHANTORDERID_FIELD.name = "merchantorderid"
CUSTOMERORDERINFO_MERCHANTORDERID_FIELD.full_name = ".prootc.customerorderinfo.merchantorderid"
CUSTOMERORDERINFO_MERCHANTORDERID_FIELD.number = 10
CUSTOMERORDERINFO_MERCHANTORDERID_FIELD.index = 9
CUSTOMERORDERINFO_MERCHANTORDERID_FIELD.label = 1
CUSTOMERORDERINFO_MERCHANTORDERID_FIELD.has_default_value = false
CUSTOMERORDERINFO_MERCHANTORDERID_FIELD.default_value = ""
CUSTOMERORDERINFO_MERCHANTORDERID_FIELD.type = 9
CUSTOMERORDERINFO_MERCHANTORDERID_FIELD.cpp_type = 9

CUSTOMERORDERINFO_FEERATE_FIELD.name = "feerate"
CUSTOMERORDERINFO_FEERATE_FIELD.full_name = ".prootc.customerorderinfo.feerate"
CUSTOMERORDERINFO_FEERATE_FIELD.number = 11
CUSTOMERORDERINFO_FEERATE_FIELD.index = 10
CUSTOMERORDERINFO_FEERATE_FIELD.label = 1
CUSTOMERORDERINFO_FEERATE_FIELD.has_default_value = false
CUSTOMERORDERINFO_FEERATE_FIELD.default_value = ""
CUSTOMERORDERINFO_FEERATE_FIELD.type = 9
CUSTOMERORDERINFO_FEERATE_FIELD.cpp_type = 9

CUSTOMERORDERINFO_FEE_FIELD.name = "fee"
CUSTOMERORDERINFO_FEE_FIELD.full_name = ".prootc.customerorderinfo.fee"
CUSTOMERORDERINFO_FEE_FIELD.number = 12
CUSTOMERORDERINFO_FEE_FIELD.index = 11
CUSTOMERORDERINFO_FEE_FIELD.label = 1
CUSTOMERORDERINFO_FEE_FIELD.has_default_value = false
CUSTOMERORDERINFO_FEE_FIELD.default_value = ""
CUSTOMERORDERINFO_FEE_FIELD.type = 9
CUSTOMERORDERINFO_FEE_FIELD.cpp_type = 9

CUSTOMERORDERINFO_PROOFURL_FIELD.name = "proofurl"
CUSTOMERORDERINFO_PROOFURL_FIELD.full_name = ".prootc.customerorderinfo.proofurl"
CUSTOMERORDERINFO_PROOFURL_FIELD.number = 13
CUSTOMERORDERINFO_PROOFURL_FIELD.index = 12
CUSTOMERORDERINFO_PROOFURL_FIELD.label = 3
CUSTOMERORDERINFO_PROOFURL_FIELD.has_default_value = false
CUSTOMERORDERINFO_PROOFURL_FIELD.default_value = {}
CUSTOMERORDERINFO_PROOFURL_FIELD.type = 9
CUSTOMERORDERINFO_PROOFURL_FIELD.cpp_type = 9

CUSTOMERORDERINFO_PAYTYPELIST_FIELD.name = "paytypelist"
CUSTOMERORDERINFO_PAYTYPELIST_FIELD.full_name = ".prootc.customerorderinfo.paytypelist"
CUSTOMERORDERINFO_PAYTYPELIST_FIELD.number = 14
CUSTOMERORDERINFO_PAYTYPELIST_FIELD.index = 13
CUSTOMERORDERINFO_PAYTYPELIST_FIELD.label = 1
CUSTOMERORDERINFO_PAYTYPELIST_FIELD.has_default_value = false
CUSTOMERORDERINFO_PAYTYPELIST_FIELD.default_value = ""
CUSTOMERORDERINFO_PAYTYPELIST_FIELD.type = 9
CUSTOMERORDERINFO_PAYTYPELIST_FIELD.cpp_type = 9

CUSTOMERORDERINFO_PAYIDLIST_FIELD.name = "payidlist"
CUSTOMERORDERINFO_PAYIDLIST_FIELD.full_name = ".prootc.customerorderinfo.payidlist"
CUSTOMERORDERINFO_PAYIDLIST_FIELD.number = 15
CUSTOMERORDERINFO_PAYIDLIST_FIELD.index = 14
CUSTOMERORDERINFO_PAYIDLIST_FIELD.label = 1
CUSTOMERORDERINFO_PAYIDLIST_FIELD.has_default_value = false
CUSTOMERORDERINFO_PAYIDLIST_FIELD.default_value = ""
CUSTOMERORDERINFO_PAYIDLIST_FIELD.type = 9
CUSTOMERORDERINFO_PAYIDLIST_FIELD.cpp_type = 9

CUSTOMERORDERINFO_CREATETIME_FIELD.name = "createtime"
CUSTOMERORDERINFO_CREATETIME_FIELD.full_name = ".prootc.customerorderinfo.createtime"
CUSTOMERORDERINFO_CREATETIME_FIELD.number = 16
CUSTOMERORDERINFO_CREATETIME_FIELD.index = 15
CUSTOMERORDERINFO_CREATETIME_FIELD.label = 1
CUSTOMERORDERINFO_CREATETIME_FIELD.has_default_value = false
CUSTOMERORDERINFO_CREATETIME_FIELD.default_value = ""
CUSTOMERORDERINFO_CREATETIME_FIELD.type = 9
CUSTOMERORDERINFO_CREATETIME_FIELD.cpp_type = 9

CUSTOMERORDERINFO_PAYTIME_FIELD.name = "paytime"
CUSTOMERORDERINFO_PAYTIME_FIELD.full_name = ".prootc.customerorderinfo.paytime"
CUSTOMERORDERINFO_PAYTIME_FIELD.number = 17
CUSTOMERORDERINFO_PAYTIME_FIELD.index = 16
CUSTOMERORDERINFO_PAYTIME_FIELD.label = 1
CUSTOMERORDERINFO_PAYTIME_FIELD.has_default_value = false
CUSTOMERORDERINFO_PAYTIME_FIELD.default_value = ""
CUSTOMERORDERINFO_PAYTIME_FIELD.type = 9
CUSTOMERORDERINFO_PAYTIME_FIELD.cpp_type = 9

CUSTOMERORDERINFO_PASSTIME_FIELD.name = "passtime"
CUSTOMERORDERINFO_PASSTIME_FIELD.full_name = ".prootc.customerorderinfo.passtime"
CUSTOMERORDERINFO_PASSTIME_FIELD.number = 18
CUSTOMERORDERINFO_PASSTIME_FIELD.index = 17
CUSTOMERORDERINFO_PASSTIME_FIELD.label = 1
CUSTOMERORDERINFO_PASSTIME_FIELD.has_default_value = false
CUSTOMERORDERINFO_PASSTIME_FIELD.default_value = ""
CUSTOMERORDERINFO_PASSTIME_FIELD.type = 9
CUSTOMERORDERINFO_PASSTIME_FIELD.cpp_type = 9

CUSTOMERORDERINFO_CHANNEL_FIELD.name = "channel"
CUSTOMERORDERINFO_CHANNEL_FIELD.full_name = ".prootc.customerorderinfo.channel"
CUSTOMERORDERINFO_CHANNEL_FIELD.number = 19
CUSTOMERORDERINFO_CHANNEL_FIELD.index = 18
CUSTOMERORDERINFO_CHANNEL_FIELD.label = 1
CUSTOMERORDERINFO_CHANNEL_FIELD.has_default_value = false
CUSTOMERORDERINFO_CHANNEL_FIELD.default_value = ""
CUSTOMERORDERINFO_CHANNEL_FIELD.type = 9
CUSTOMERORDERINFO_CHANNEL_FIELD.cpp_type = 9

CUSTOMERORDERINFO_PUBLICPRICE_FIELD.name = "publicprice"
CUSTOMERORDERINFO_PUBLICPRICE_FIELD.full_name = ".prootc.customerorderinfo.publicprice"
CUSTOMERORDERINFO_PUBLICPRICE_FIELD.number = 20
CUSTOMERORDERINFO_PUBLICPRICE_FIELD.index = 19
CUSTOMERORDERINFO_PUBLICPRICE_FIELD.label = 1
CUSTOMERORDERINFO_PUBLICPRICE_FIELD.has_default_value = false
CUSTOMERORDERINFO_PUBLICPRICE_FIELD.default_value = ""
CUSTOMERORDERINFO_PUBLICPRICE_FIELD.type = 9
CUSTOMERORDERINFO_PUBLICPRICE_FIELD.cpp_type = 9

CUSTOMERORDERINFO_FROMTYPE_FIELD.name = "fromtype"
CUSTOMERORDERINFO_FROMTYPE_FIELD.full_name = ".prootc.customerorderinfo.fromtype"
CUSTOMERORDERINFO_FROMTYPE_FIELD.number = 21
CUSTOMERORDERINFO_FROMTYPE_FIELD.index = 20
CUSTOMERORDERINFO_FROMTYPE_FIELD.label = 1
CUSTOMERORDERINFO_FROMTYPE_FIELD.has_default_value = false
CUSTOMERORDERINFO_FROMTYPE_FIELD.default_value = 0
CUSTOMERORDERINFO_FROMTYPE_FIELD.type = 5
CUSTOMERORDERINFO_FROMTYPE_FIELD.cpp_type = 1

CUSTOMERORDERINFO_CUSTOMERUSERNICKNAME_FIELD.name = "customerusernickname"
CUSTOMERORDERINFO_CUSTOMERUSERNICKNAME_FIELD.full_name = ".prootc.customerorderinfo.customerusernickname"
CUSTOMERORDERINFO_CUSTOMERUSERNICKNAME_FIELD.number = 22
CUSTOMERORDERINFO_CUSTOMERUSERNICKNAME_FIELD.index = 21
CUSTOMERORDERINFO_CUSTOMERUSERNICKNAME_FIELD.label = 1
CUSTOMERORDERINFO_CUSTOMERUSERNICKNAME_FIELD.has_default_value = false
CUSTOMERORDERINFO_CUSTOMERUSERNICKNAME_FIELD.default_value = ""
CUSTOMERORDERINFO_CUSTOMERUSERNICKNAME_FIELD.type = 9
CUSTOMERORDERINFO_CUSTOMERUSERNICKNAME_FIELD.cpp_type = 9

CUSTOMERORDERINFO_VENDORUSERNICKNAME_FIELD.name = "vendorusernickname"
CUSTOMERORDERINFO_VENDORUSERNICKNAME_FIELD.full_name = ".prootc.customerorderinfo.vendorusernickname"
CUSTOMERORDERINFO_VENDORUSERNICKNAME_FIELD.number = 23
CUSTOMERORDERINFO_VENDORUSERNICKNAME_FIELD.index = 22
CUSTOMERORDERINFO_VENDORUSERNICKNAME_FIELD.label = 1
CUSTOMERORDERINFO_VENDORUSERNICKNAME_FIELD.has_default_value = false
CUSTOMERORDERINFO_VENDORUSERNICKNAME_FIELD.default_value = ""
CUSTOMERORDERINFO_VENDORUSERNICKNAME_FIELD.type = 9
CUSTOMERORDERINFO_VENDORUSERNICKNAME_FIELD.cpp_type = 9

CUSTOMERORDERINFO_NOTIFYURL_FIELD.name = "notifyurl"
CUSTOMERORDERINFO_NOTIFYURL_FIELD.full_name = ".prootc.customerorderinfo.notifyurl"
CUSTOMERORDERINFO_NOTIFYURL_FIELD.number = 24
CUSTOMERORDERINFO_NOTIFYURL_FIELD.index = 23
CUSTOMERORDERINFO_NOTIFYURL_FIELD.label = 1
CUSTOMERORDERINFO_NOTIFYURL_FIELD.has_default_value = false
CUSTOMERORDERINFO_NOTIFYURL_FIELD.default_value = ""
CUSTOMERORDERINFO_NOTIFYURL_FIELD.type = 9
CUSTOMERORDERINFO_NOTIFYURL_FIELD.cpp_type = 9

CUSTOMERORDERINFO_BODY_FIELD.name = "body"
CUSTOMERORDERINFO_BODY_FIELD.full_name = ".prootc.customerorderinfo.body"
CUSTOMERORDERINFO_BODY_FIELD.number = 25
CUSTOMERORDERINFO_BODY_FIELD.index = 24
CUSTOMERORDERINFO_BODY_FIELD.label = 1
CUSTOMERORDERINFO_BODY_FIELD.has_default_value = false
CUSTOMERORDERINFO_BODY_FIELD.default_value = ""
CUSTOMERORDERINFO_BODY_FIELD.type = 9
CUSTOMERORDERINFO_BODY_FIELD.cpp_type = 9

CUSTOMERORDERINFO_SELLFEERATE_FIELD.name = "sellfeerate"
CUSTOMERORDERINFO_SELLFEERATE_FIELD.full_name = ".prootc.customerorderinfo.sellfeerate"
CUSTOMERORDERINFO_SELLFEERATE_FIELD.number = 26
CUSTOMERORDERINFO_SELLFEERATE_FIELD.index = 25
CUSTOMERORDERINFO_SELLFEERATE_FIELD.label = 1
CUSTOMERORDERINFO_SELLFEERATE_FIELD.has_default_value = false
CUSTOMERORDERINFO_SELLFEERATE_FIELD.default_value = ""
CUSTOMERORDERINFO_SELLFEERATE_FIELD.type = 9
CUSTOMERORDERINFO_SELLFEERATE_FIELD.cpp_type = 9

CUSTOMERORDERINFO_SELLFEE_FIELD.name = "sellfee"
CUSTOMERORDERINFO_SELLFEE_FIELD.full_name = ".prootc.customerorderinfo.sellfee"
CUSTOMERORDERINFO_SELLFEE_FIELD.number = 27
CUSTOMERORDERINFO_SELLFEE_FIELD.index = 26
CUSTOMERORDERINFO_SELLFEE_FIELD.label = 1
CUSTOMERORDERINFO_SELLFEE_FIELD.has_default_value = false
CUSTOMERORDERINFO_SELLFEE_FIELD.default_value = ""
CUSTOMERORDERINFO_SELLFEE_FIELD.type = 9
CUSTOMERORDERINFO_SELLFEE_FIELD.cpp_type = 9

CUSTOMERORDERINFO_CANCELTIME_FIELD.name = "canceltime"
CUSTOMERORDERINFO_CANCELTIME_FIELD.full_name = ".prootc.customerorderinfo.canceltime"
CUSTOMERORDERINFO_CANCELTIME_FIELD.number = 28
CUSTOMERORDERINFO_CANCELTIME_FIELD.index = 27
CUSTOMERORDERINFO_CANCELTIME_FIELD.label = 1
CUSTOMERORDERINFO_CANCELTIME_FIELD.has_default_value = false
CUSTOMERORDERINFO_CANCELTIME_FIELD.default_value = ""
CUSTOMERORDERINFO_CANCELTIME_FIELD.type = 9
CUSTOMERORDERINFO_CANCELTIME_FIELD.cpp_type = 9

CUSTOMERORDERINFO_BUYFEERATE_FIELD.name = "buyfeerate"
CUSTOMERORDERINFO_BUYFEERATE_FIELD.full_name = ".prootc.customerorderinfo.buyfeerate"
CUSTOMERORDERINFO_BUYFEERATE_FIELD.number = 29
CUSTOMERORDERINFO_BUYFEERATE_FIELD.index = 28
CUSTOMERORDERINFO_BUYFEERATE_FIELD.label = 1
CUSTOMERORDERINFO_BUYFEERATE_FIELD.has_default_value = false
CUSTOMERORDERINFO_BUYFEERATE_FIELD.default_value = ""
CUSTOMERORDERINFO_BUYFEERATE_FIELD.type = 9
CUSTOMERORDERINFO_BUYFEERATE_FIELD.cpp_type = 9

CUSTOMERORDERINFO_BUYFEE_FIELD.name = "buyfee"
CUSTOMERORDERINFO_BUYFEE_FIELD.full_name = ".prootc.customerorderinfo.buyfee"
CUSTOMERORDERINFO_BUYFEE_FIELD.number = 30
CUSTOMERORDERINFO_BUYFEE_FIELD.index = 29
CUSTOMERORDERINFO_BUYFEE_FIELD.label = 1
CUSTOMERORDERINFO_BUYFEE_FIELD.has_default_value = false
CUSTOMERORDERINFO_BUYFEE_FIELD.default_value = ""
CUSTOMERORDERINFO_BUYFEE_FIELD.type = 9
CUSTOMERORDERINFO_BUYFEE_FIELD.cpp_type = 9

CUSTOMERORDERINFO_UPDATETIME_FIELD.name = "updatetime"
CUSTOMERORDERINFO_UPDATETIME_FIELD.full_name = ".prootc.customerorderinfo.updatetime"
CUSTOMERORDERINFO_UPDATETIME_FIELD.number = 31
CUSTOMERORDERINFO_UPDATETIME_FIELD.index = 30
CUSTOMERORDERINFO_UPDATETIME_FIELD.label = 1
CUSTOMERORDERINFO_UPDATETIME_FIELD.has_default_value = false
CUSTOMERORDERINFO_UPDATETIME_FIELD.default_value = ""
CUSTOMERORDERINFO_UPDATETIME_FIELD.type = 9
CUSTOMERORDERINFO_UPDATETIME_FIELD.cpp_type = 9

CUSTOMERORDERINFO_DEALTYPE_FIELD.name = "dealtype"
CUSTOMERORDERINFO_DEALTYPE_FIELD.full_name = ".prootc.customerorderinfo.dealtype"
CUSTOMERORDERINFO_DEALTYPE_FIELD.number = 32
CUSTOMERORDERINFO_DEALTYPE_FIELD.index = 31
CUSTOMERORDERINFO_DEALTYPE_FIELD.label = 1
CUSTOMERORDERINFO_DEALTYPE_FIELD.has_default_value = false
CUSTOMERORDERINFO_DEALTYPE_FIELD.default_value = 0
CUSTOMERORDERINFO_DEALTYPE_FIELD.type = 5
CUSTOMERORDERINFO_DEALTYPE_FIELD.cpp_type = 1

CUSTOMERORDERINFO_GETAMOUNT_FIELD.name = "getamount"
CUSTOMERORDERINFO_GETAMOUNT_FIELD.full_name = ".prootc.customerorderinfo.getamount"
CUSTOMERORDERINFO_GETAMOUNT_FIELD.number = 33
CUSTOMERORDERINFO_GETAMOUNT_FIELD.index = 32
CUSTOMERORDERINFO_GETAMOUNT_FIELD.label = 1
CUSTOMERORDERINFO_GETAMOUNT_FIELD.has_default_value = false
CUSTOMERORDERINFO_GETAMOUNT_FIELD.default_value = ""
CUSTOMERORDERINFO_GETAMOUNT_FIELD.type = 9
CUSTOMERORDERINFO_GETAMOUNT_FIELD.cpp_type = 9

CUSTOMERORDERINFO_INCOME_FIELD.name = "income"
CUSTOMERORDERINFO_INCOME_FIELD.full_name = ".prootc.customerorderinfo.income"
CUSTOMERORDERINFO_INCOME_FIELD.number = 34
CUSTOMERORDERINFO_INCOME_FIELD.index = 33
CUSTOMERORDERINFO_INCOME_FIELD.label = 1
CUSTOMERORDERINFO_INCOME_FIELD.has_default_value = false
CUSTOMERORDERINFO_INCOME_FIELD.default_value = ""
CUSTOMERORDERINFO_INCOME_FIELD.type = 9
CUSTOMERORDERINFO_INCOME_FIELD.cpp_type = 9

CUSTOMERORDERINFO_WITHDRAWTYPE_FIELD.name = "withdrawtype"
CUSTOMERORDERINFO_WITHDRAWTYPE_FIELD.full_name = ".prootc.customerorderinfo.withdrawtype"
CUSTOMERORDERINFO_WITHDRAWTYPE_FIELD.number = 35
CUSTOMERORDERINFO_WITHDRAWTYPE_FIELD.index = 34
CUSTOMERORDERINFO_WITHDRAWTYPE_FIELD.label = 1
CUSTOMERORDERINFO_WITHDRAWTYPE_FIELD.has_default_value = false
CUSTOMERORDERINFO_WITHDRAWTYPE_FIELD.default_value = 0
CUSTOMERORDERINFO_WITHDRAWTYPE_FIELD.type = 5
CUSTOMERORDERINFO_WITHDRAWTYPE_FIELD.cpp_type = 1

CUSTOMERORDERINFO_COINID_FIELD.name = "coinid"
CUSTOMERORDERINFO_COINID_FIELD.full_name = ".prootc.customerorderinfo.coinid"
CUSTOMERORDERINFO_COINID_FIELD.number = 36
CUSTOMERORDERINFO_COINID_FIELD.index = 35
CUSTOMERORDERINFO_COINID_FIELD.label = 1
CUSTOMERORDERINFO_COINID_FIELD.has_default_value = false
CUSTOMERORDERINFO_COINID_FIELD.default_value = 0
CUSTOMERORDERINFO_COINID_FIELD.type = 5
CUSTOMERORDERINFO_COINID_FIELD.cpp_type = 1

CUSTOMERORDERINFO_COINNAME_FIELD.name = "coinname"
CUSTOMERORDERINFO_COINNAME_FIELD.full_name = ".prootc.customerorderinfo.coinname"
CUSTOMERORDERINFO_COINNAME_FIELD.number = 37
CUSTOMERORDERINFO_COINNAME_FIELD.index = 36
CUSTOMERORDERINFO_COINNAME_FIELD.label = 1
CUSTOMERORDERINFO_COINNAME_FIELD.has_default_value = false
CUSTOMERORDERINFO_COINNAME_FIELD.default_value = ""
CUSTOMERORDERINFO_COINNAME_FIELD.type = 9
CUSTOMERORDERINFO_COINNAME_FIELD.cpp_type = 9

CUSTOMERORDERINFO_ISEXTERNAL_FIELD.name = "isexternal"
CUSTOMERORDERINFO_ISEXTERNAL_FIELD.full_name = ".prootc.customerorderinfo.isexternal"
CUSTOMERORDERINFO_ISEXTERNAL_FIELD.number = 38
CUSTOMERORDERINFO_ISEXTERNAL_FIELD.index = 37
CUSTOMERORDERINFO_ISEXTERNAL_FIELD.label = 1
CUSTOMERORDERINFO_ISEXTERNAL_FIELD.has_default_value = false
CUSTOMERORDERINFO_ISEXTERNAL_FIELD.default_value = 0
CUSTOMERORDERINFO_ISEXTERNAL_FIELD.type = 5
CUSTOMERORDERINFO_ISEXTERNAL_FIELD.cpp_type = 1

CUSTOMERORDERINFO_AFTERMONEY_FIELD.name = "aftermoney"
CUSTOMERORDERINFO_AFTERMONEY_FIELD.full_name = ".prootc.customerorderinfo.aftermoney"
CUSTOMERORDERINFO_AFTERMONEY_FIELD.number = 39
CUSTOMERORDERINFO_AFTERMONEY_FIELD.index = 38
CUSTOMERORDERINFO_AFTERMONEY_FIELD.label = 1
CUSTOMERORDERINFO_AFTERMONEY_FIELD.has_default_value = false
CUSTOMERORDERINFO_AFTERMONEY_FIELD.default_value = ""
CUSTOMERORDERINFO_AFTERMONEY_FIELD.type = 9
CUSTOMERORDERINFO_AFTERMONEY_FIELD.cpp_type = 9

CUSTOMERORDERINFO_FEEMONEY_FIELD.name = "feemoney"
CUSTOMERORDERINFO_FEEMONEY_FIELD.full_name = ".prootc.customerorderinfo.feemoney"
CUSTOMERORDERINFO_FEEMONEY_FIELD.number = 40
CUSTOMERORDERINFO_FEEMONEY_FIELD.index = 39
CUSTOMERORDERINFO_FEEMONEY_FIELD.label = 1
CUSTOMERORDERINFO_FEEMONEY_FIELD.has_default_value = false
CUSTOMERORDERINFO_FEEMONEY_FIELD.default_value = ""
CUSTOMERORDERINFO_FEEMONEY_FIELD.type = 9
CUSTOMERORDERINFO_FEEMONEY_FIELD.cpp_type = 9

CUSTOMERORDERINFO_TRADEID_FIELD.name = "tradeid"
CUSTOMERORDERINFO_TRADEID_FIELD.full_name = ".prootc.customerorderinfo.tradeid"
CUSTOMERORDERINFO_TRADEID_FIELD.number = 41
CUSTOMERORDERINFO_TRADEID_FIELD.index = 40
CUSTOMERORDERINFO_TRADEID_FIELD.label = 1
CUSTOMERORDERINFO_TRADEID_FIELD.has_default_value = false
CUSTOMERORDERINFO_TRADEID_FIELD.default_value = ""
CUSTOMERORDERINFO_TRADEID_FIELD.type = 9
CUSTOMERORDERINFO_TRADEID_FIELD.cpp_type = 9

CUSTOMERORDERINFO_ISWAIT_FIELD.name = "iswait"
CUSTOMERORDERINFO_ISWAIT_FIELD.full_name = ".prootc.customerorderinfo.iswait"
CUSTOMERORDERINFO_ISWAIT_FIELD.number = 43
CUSTOMERORDERINFO_ISWAIT_FIELD.index = 41
CUSTOMERORDERINFO_ISWAIT_FIELD.label = 1
CUSTOMERORDERINFO_ISWAIT_FIELD.has_default_value = false
CUSTOMERORDERINFO_ISWAIT_FIELD.default_value = 0
CUSTOMERORDERINFO_ISWAIT_FIELD.type = 5
CUSTOMERORDERINFO_ISWAIT_FIELD.cpp_type = 1

CUSTOMERORDERINFO_CHAINTYPE_FIELD.name = "chaintype"
CUSTOMERORDERINFO_CHAINTYPE_FIELD.full_name = ".prootc.customerorderinfo.chaintype"
CUSTOMERORDERINFO_CHAINTYPE_FIELD.number = 44
CUSTOMERORDERINFO_CHAINTYPE_FIELD.index = 42
CUSTOMERORDERINFO_CHAINTYPE_FIELD.label = 1
CUSTOMERORDERINFO_CHAINTYPE_FIELD.has_default_value = false
CUSTOMERORDERINFO_CHAINTYPE_FIELD.default_value = 0
CUSTOMERORDERINFO_CHAINTYPE_FIELD.type = 5
CUSTOMERORDERINFO_CHAINTYPE_FIELD.cpp_type = 1

CUSTOMERORDERINFO_CHAINNAME_FIELD.name = "chainname"
CUSTOMERORDERINFO_CHAINNAME_FIELD.full_name = ".prootc.customerorderinfo.chainname"
CUSTOMERORDERINFO_CHAINNAME_FIELD.number = 45
CUSTOMERORDERINFO_CHAINNAME_FIELD.index = 43
CUSTOMERORDERINFO_CHAINNAME_FIELD.label = 1
CUSTOMERORDERINFO_CHAINNAME_FIELD.has_default_value = false
CUSTOMERORDERINFO_CHAINNAME_FIELD.default_value = ""
CUSTOMERORDERINFO_CHAINNAME_FIELD.type = 9
CUSTOMERORDERINFO_CHAINNAME_FIELD.cpp_type = 9

CUSTOMERORDERINFO_CHAINADDR_FIELD.name = "chainaddr"
CUSTOMERORDERINFO_CHAINADDR_FIELD.full_name = ".prootc.customerorderinfo.chainaddr"
CUSTOMERORDERINFO_CHAINADDR_FIELD.number = 46
CUSTOMERORDERINFO_CHAINADDR_FIELD.index = 44
CUSTOMERORDERINFO_CHAINADDR_FIELD.label = 1
CUSTOMERORDERINFO_CHAINADDR_FIELD.has_default_value = false
CUSTOMERORDERINFO_CHAINADDR_FIELD.default_value = ""
CUSTOMERORDERINFO_CHAINADDR_FIELD.type = 9
CUSTOMERORDERINFO_CHAINADDR_FIELD.cpp_type = 9

CUSTOMERORDERINFO_OWERSHIP_FIELD.name = "owership"
CUSTOMERORDERINFO_OWERSHIP_FIELD.full_name = ".prootc.customerorderinfo.owership"
CUSTOMERORDERINFO_OWERSHIP_FIELD.number = 47
CUSTOMERORDERINFO_OWERSHIP_FIELD.index = 45
CUSTOMERORDERINFO_OWERSHIP_FIELD.label = 1
CUSTOMERORDERINFO_OWERSHIP_FIELD.has_default_value = false
CUSTOMERORDERINFO_OWERSHIP_FIELD.default_value = 0
CUSTOMERORDERINFO_OWERSHIP_FIELD.type = 5
CUSTOMERORDERINFO_OWERSHIP_FIELD.cpp_type = 1

CUSTOMERORDERINFO_WALLETTYPE_FIELD.name = "wallettype"
CUSTOMERORDERINFO_WALLETTYPE_FIELD.full_name = ".prootc.customerorderinfo.wallettype"
CUSTOMERORDERINFO_WALLETTYPE_FIELD.number = 48
CUSTOMERORDERINFO_WALLETTYPE_FIELD.index = 46
CUSTOMERORDERINFO_WALLETTYPE_FIELD.label = 1
CUSTOMERORDERINFO_WALLETTYPE_FIELD.has_default_value = false
CUSTOMERORDERINFO_WALLETTYPE_FIELD.default_value = 0
CUSTOMERORDERINFO_WALLETTYPE_FIELD.type = 5
CUSTOMERORDERINFO_WALLETTYPE_FIELD.cpp_type = 1

CUSTOMERORDERINFO.name = "customerorderinfo"
CUSTOMERORDERINFO.full_name = ".prootc.customerorderinfo"
CUSTOMERORDERINFO.nested_types = {}
CUSTOMERORDERINFO.enum_types = {}
CUSTOMERORDERINFO.fields = {CUSTOMERORDERINFO_DEALID_FIELD, CUSTOMERORDERINFO_TYPE_FIELD, CUSTOMERORDERINFO_VENDORORDERID_FIELD, CUSTOMERORDERINFO_CUSTOMERUSERID_FIELD, CUSTOMERORDERINFO_VENDORUSERID_FIELD, CUSTOMERORDERINFO_PRICE_FIELD, CUSTOMERORDERINFO_AMOUNT_FIELD, CUSTOMERORDERINFO_MONEY_FIELD, CUSTOMERORDERINFO_STATUS_FIELD, CUSTOMERORDERINFO_MERCHANTORDERID_FIELD, CUSTOMERORDERINFO_FEERATE_FIELD, CUSTOMERORDERINFO_FEE_FIELD, CUSTOMERORDERINFO_PROOFURL_FIELD, CUSTOMERORDERINFO_PAYTYPELIST_FIELD, CUSTOMERORDERINFO_PAYIDLIST_FIELD, CUSTOMERORDERINFO_CREATETIME_FIELD, CUSTOMERORDERINFO_PAYTIME_FIELD, CUSTOMERORDERINFO_PASSTIME_FIELD, CUSTOMERORDERINFO_CHANNEL_FIELD, CUSTOMERORDERINFO_PUBLICPRICE_FIELD, CUSTOMERORDERINFO_FROMTYPE_FIELD, CUSTOMERORDERINFO_CUSTOMERUSERNICKNAME_FIELD, CUSTOMERORDERINFO_VENDORUSERNICKNAME_FIELD, CUSTOMERORDERINFO_NOTIFYURL_FIELD, CUSTOMERORDERINFO_BODY_FIELD, CUSTOMERORDERINFO_SELLFEERATE_FIELD, CUSTOMERORDERINFO_SELLFEE_FIELD, CUSTOMERORDERINFO_CANCELTIME_FIELD, CUSTOMERORDERINFO_BUYFEERATE_FIELD, CUSTOMERORDERINFO_BUYFEE_FIELD, CUSTOMERORDERINFO_UPDATETIME_FIELD, CUSTOMERORDERINFO_DEALTYPE_FIELD, CUSTOMERORDERINFO_GETAMOUNT_FIELD, CUSTOMERORDERINFO_INCOME_FIELD, CUSTOMERORDERINFO_WITHDRAWTYPE_FIELD, CUSTOMERORDERINFO_COINID_FIELD, CUSTOMERORDERINFO_COINNAME_FIELD, CUSTOMERORDERINFO_ISEXTERNAL_FIELD, CUSTOMERORDERINFO_AFTERMONEY_FIELD, CUSTOMERORDERINFO_FEEMONEY_FIELD, CUSTOMERORDERINFO_TRADEID_FIELD, CUSTOMERORDERINFO_ISWAIT_FIELD, CUSTOMERORDERINFO_CHAINTYPE_FIELD, CUSTOMERORDERINFO_CHAINNAME_FIELD, CUSTOMERORDERINFO_CHAINADDR_FIELD, CUSTOMERORDERINFO_OWERSHIP_FIELD, CUSTOMERORDERINFO_WALLETTYPE_FIELD}
CUSTOMERORDERINFO.is_extendable = false
CUSTOMERORDERINFO.extensions = {}
APPPEALINFO_APPEALID_FIELD.name = "appealid"
APPPEALINFO_APPEALID_FIELD.full_name = ".prootc.apppealinfo.appealid"
APPPEALINFO_APPEALID_FIELD.number = 1
APPPEALINFO_APPEALID_FIELD.index = 0
APPPEALINFO_APPEALID_FIELD.label = 1
APPPEALINFO_APPEALID_FIELD.has_default_value = false
APPPEALINFO_APPEALID_FIELD.default_value = 0
APPPEALINFO_APPEALID_FIELD.type = 5
APPPEALINFO_APPEALID_FIELD.cpp_type = 1

APPPEALINFO_FROMUSERID_FIELD.name = "fromuserid"
APPPEALINFO_FROMUSERID_FIELD.full_name = ".prootc.apppealinfo.fromuserid"
APPPEALINFO_FROMUSERID_FIELD.number = 2
APPPEALINFO_FROMUSERID_FIELD.index = 1
APPPEALINFO_FROMUSERID_FIELD.label = 1
APPPEALINFO_FROMUSERID_FIELD.has_default_value = false
APPPEALINFO_FROMUSERID_FIELD.default_value = 0
APPPEALINFO_FROMUSERID_FIELD.type = 5
APPPEALINFO_FROMUSERID_FIELD.cpp_type = 1

APPPEALINFO_FROMUSERNICKNAME_FIELD.name = "fromusernickname"
APPPEALINFO_FROMUSERNICKNAME_FIELD.full_name = ".prootc.apppealinfo.fromusernickname"
APPPEALINFO_FROMUSERNICKNAME_FIELD.number = 3
APPPEALINFO_FROMUSERNICKNAME_FIELD.index = 2
APPPEALINFO_FROMUSERNICKNAME_FIELD.label = 1
APPPEALINFO_FROMUSERNICKNAME_FIELD.has_default_value = false
APPPEALINFO_FROMUSERNICKNAME_FIELD.default_value = ""
APPPEALINFO_FROMUSERNICKNAME_FIELD.type = 9
APPPEALINFO_FROMUSERNICKNAME_FIELD.cpp_type = 9

APPPEALINFO_TOUSERID_FIELD.name = "touserid"
APPPEALINFO_TOUSERID_FIELD.full_name = ".prootc.apppealinfo.touserid"
APPPEALINFO_TOUSERID_FIELD.number = 4
APPPEALINFO_TOUSERID_FIELD.index = 3
APPPEALINFO_TOUSERID_FIELD.label = 1
APPPEALINFO_TOUSERID_FIELD.has_default_value = false
APPPEALINFO_TOUSERID_FIELD.default_value = 0
APPPEALINFO_TOUSERID_FIELD.type = 5
APPPEALINFO_TOUSERID_FIELD.cpp_type = 1

APPPEALINFO_TOUSERNICKNAME_FIELD.name = "tousernickname"
APPPEALINFO_TOUSERNICKNAME_FIELD.full_name = ".prootc.apppealinfo.tousernickname"
APPPEALINFO_TOUSERNICKNAME_FIELD.number = 5
APPPEALINFO_TOUSERNICKNAME_FIELD.index = 4
APPPEALINFO_TOUSERNICKNAME_FIELD.label = 1
APPPEALINFO_TOUSERNICKNAME_FIELD.has_default_value = false
APPPEALINFO_TOUSERNICKNAME_FIELD.default_value = ""
APPPEALINFO_TOUSERNICKNAME_FIELD.type = 9
APPPEALINFO_TOUSERNICKNAME_FIELD.cpp_type = 9

APPPEALINFO_ORDERID_FIELD.name = "orderid"
APPPEALINFO_ORDERID_FIELD.full_name = ".prootc.apppealinfo.orderid"
APPPEALINFO_ORDERID_FIELD.number = 6
APPPEALINFO_ORDERID_FIELD.index = 5
APPPEALINFO_ORDERID_FIELD.label = 1
APPPEALINFO_ORDERID_FIELD.has_default_value = false
APPPEALINFO_ORDERID_FIELD.default_value = 0
APPPEALINFO_ORDERID_FIELD.type = 5
APPPEALINFO_ORDERID_FIELD.cpp_type = 1

APPPEALINFO_REASON_FIELD.name = "reason"
APPPEALINFO_REASON_FIELD.full_name = ".prootc.apppealinfo.reason"
APPPEALINFO_REASON_FIELD.number = 7
APPPEALINFO_REASON_FIELD.index = 6
APPPEALINFO_REASON_FIELD.label = 1
APPPEALINFO_REASON_FIELD.has_default_value = false
APPPEALINFO_REASON_FIELD.default_value = ""
APPPEALINFO_REASON_FIELD.type = 9
APPPEALINFO_REASON_FIELD.cpp_type = 9

APPPEALINFO_DESCRIPTION_FIELD.name = "description"
APPPEALINFO_DESCRIPTION_FIELD.full_name = ".prootc.apppealinfo.description"
APPPEALINFO_DESCRIPTION_FIELD.number = 8
APPPEALINFO_DESCRIPTION_FIELD.index = 7
APPPEALINFO_DESCRIPTION_FIELD.label = 1
APPPEALINFO_DESCRIPTION_FIELD.has_default_value = false
APPPEALINFO_DESCRIPTION_FIELD.default_value = ""
APPPEALINFO_DESCRIPTION_FIELD.type = 9
APPPEALINFO_DESCRIPTION_FIELD.cpp_type = 9

APPPEALINFO_PROOFURL_FIELD.name = "proofurl"
APPPEALINFO_PROOFURL_FIELD.full_name = ".prootc.apppealinfo.proofurl"
APPPEALINFO_PROOFURL_FIELD.number = 9
APPPEALINFO_PROOFURL_FIELD.index = 8
APPPEALINFO_PROOFURL_FIELD.label = 1
APPPEALINFO_PROOFURL_FIELD.has_default_value = false
APPPEALINFO_PROOFURL_FIELD.default_value = ""
APPPEALINFO_PROOFURL_FIELD.type = 9
APPPEALINFO_PROOFURL_FIELD.cpp_type = 9

APPPEALINFO_REPLYREASON_FIELD.name = "replyreason"
APPPEALINFO_REPLYREASON_FIELD.full_name = ".prootc.apppealinfo.replyreason"
APPPEALINFO_REPLYREASON_FIELD.number = 10
APPPEALINFO_REPLYREASON_FIELD.index = 9
APPPEALINFO_REPLYREASON_FIELD.label = 1
APPPEALINFO_REPLYREASON_FIELD.has_default_value = false
APPPEALINFO_REPLYREASON_FIELD.default_value = ""
APPPEALINFO_REPLYREASON_FIELD.type = 9
APPPEALINFO_REPLYREASON_FIELD.cpp_type = 9

APPPEALINFO_REPLYDESCRIPTION_FIELD.name = "replydescription"
APPPEALINFO_REPLYDESCRIPTION_FIELD.full_name = ".prootc.apppealinfo.replydescription"
APPPEALINFO_REPLYDESCRIPTION_FIELD.number = 11
APPPEALINFO_REPLYDESCRIPTION_FIELD.index = 10
APPPEALINFO_REPLYDESCRIPTION_FIELD.label = 1
APPPEALINFO_REPLYDESCRIPTION_FIELD.has_default_value = false
APPPEALINFO_REPLYDESCRIPTION_FIELD.default_value = ""
APPPEALINFO_REPLYDESCRIPTION_FIELD.type = 9
APPPEALINFO_REPLYDESCRIPTION_FIELD.cpp_type = 9

APPPEALINFO_REPLYPROOFURL_FIELD.name = "replyproofurl"
APPPEALINFO_REPLYPROOFURL_FIELD.full_name = ".prootc.apppealinfo.replyproofurl"
APPPEALINFO_REPLYPROOFURL_FIELD.number = 12
APPPEALINFO_REPLYPROOFURL_FIELD.index = 11
APPPEALINFO_REPLYPROOFURL_FIELD.label = 1
APPPEALINFO_REPLYPROOFURL_FIELD.has_default_value = false
APPPEALINFO_REPLYPROOFURL_FIELD.default_value = ""
APPPEALINFO_REPLYPROOFURL_FIELD.type = 9
APPPEALINFO_REPLYPROOFURL_FIELD.cpp_type = 9

APPPEALINFO_REPLYTIME_FIELD.name = "replytime"
APPPEALINFO_REPLYTIME_FIELD.full_name = ".prootc.apppealinfo.replytime"
APPPEALINFO_REPLYTIME_FIELD.number = 13
APPPEALINFO_REPLYTIME_FIELD.index = 12
APPPEALINFO_REPLYTIME_FIELD.label = 1
APPPEALINFO_REPLYTIME_FIELD.has_default_value = false
APPPEALINFO_REPLYTIME_FIELD.default_value = ""
APPPEALINFO_REPLYTIME_FIELD.type = 9
APPPEALINFO_REPLYTIME_FIELD.cpp_type = 9

APPPEALINFO_SYSREMARK_FIELD.name = "sysremark"
APPPEALINFO_SYSREMARK_FIELD.full_name = ".prootc.apppealinfo.sysremark"
APPPEALINFO_SYSREMARK_FIELD.number = 14
APPPEALINFO_SYSREMARK_FIELD.index = 13
APPPEALINFO_SYSREMARK_FIELD.label = 1
APPPEALINFO_SYSREMARK_FIELD.has_default_value = false
APPPEALINFO_SYSREMARK_FIELD.default_value = ""
APPPEALINFO_SYSREMARK_FIELD.type = 9
APPPEALINFO_SYSREMARK_FIELD.cpp_type = 9

APPPEALINFO_STATUS_FIELD.name = "status"
APPPEALINFO_STATUS_FIELD.full_name = ".prootc.apppealinfo.status"
APPPEALINFO_STATUS_FIELD.number = 15
APPPEALINFO_STATUS_FIELD.index = 14
APPPEALINFO_STATUS_FIELD.label = 1
APPPEALINFO_STATUS_FIELD.has_default_value = false
APPPEALINFO_STATUS_FIELD.default_value = 0
APPPEALINFO_STATUS_FIELD.type = 5
APPPEALINFO_STATUS_FIELD.cpp_type = 1

APPPEALINFO_NEEDAUDIT_FIELD.name = "needaudit"
APPPEALINFO_NEEDAUDIT_FIELD.full_name = ".prootc.apppealinfo.needaudit"
APPPEALINFO_NEEDAUDIT_FIELD.number = 16
APPPEALINFO_NEEDAUDIT_FIELD.index = 15
APPPEALINFO_NEEDAUDIT_FIELD.label = 1
APPPEALINFO_NEEDAUDIT_FIELD.has_default_value = false
APPPEALINFO_NEEDAUDIT_FIELD.default_value = 0
APPPEALINFO_NEEDAUDIT_FIELD.type = 5
APPPEALINFO_NEEDAUDIT_FIELD.cpp_type = 1

APPPEALINFO_CREATETIME_FIELD.name = "createtime"
APPPEALINFO_CREATETIME_FIELD.full_name = ".prootc.apppealinfo.createtime"
APPPEALINFO_CREATETIME_FIELD.number = 17
APPPEALINFO_CREATETIME_FIELD.index = 16
APPPEALINFO_CREATETIME_FIELD.label = 1
APPPEALINFO_CREATETIME_FIELD.has_default_value = false
APPPEALINFO_CREATETIME_FIELD.default_value = ""
APPPEALINFO_CREATETIME_FIELD.type = 9
APPPEALINFO_CREATETIME_FIELD.cpp_type = 9

APPPEALINFO.name = "apppealinfo"
APPPEALINFO.full_name = ".prootc.apppealinfo"
APPPEALINFO.nested_types = {}
APPPEALINFO.enum_types = {}
APPPEALINFO.fields = {APPPEALINFO_APPEALID_FIELD, APPPEALINFO_FROMUSERID_FIELD, APPPEALINFO_FROMUSERNICKNAME_FIELD, APPPEALINFO_TOUSERID_FIELD, APPPEALINFO_TOUSERNICKNAME_FIELD, APPPEALINFO_ORDERID_FIELD, APPPEALINFO_REASON_FIELD, APPPEALINFO_DESCRIPTION_FIELD, APPPEALINFO_PROOFURL_FIELD, APPPEALINFO_REPLYREASON_FIELD, APPPEALINFO_REPLYDESCRIPTION_FIELD, APPPEALINFO_REPLYPROOFURL_FIELD, APPPEALINFO_REPLYTIME_FIELD, APPPEALINFO_SYSREMARK_FIELD, APPPEALINFO_STATUS_FIELD, APPPEALINFO_NEEDAUDIT_FIELD, APPPEALINFO_CREATETIME_FIELD}
APPPEALINFO.is_extendable = false
APPPEALINFO.extensions = {}
CURRENCYRECORDINFO_ID_FIELD.name = "id"
CURRENCYRECORDINFO_ID_FIELD.full_name = ".prootc.currencyrecordinfo.id"
CURRENCYRECORDINFO_ID_FIELD.number = 1
CURRENCYRECORDINFO_ID_FIELD.index = 0
CURRENCYRECORDINFO_ID_FIELD.label = 1
CURRENCYRECORDINFO_ID_FIELD.has_default_value = false
CURRENCYRECORDINFO_ID_FIELD.default_value = 0
CURRENCYRECORDINFO_ID_FIELD.type = 5
CURRENCYRECORDINFO_ID_FIELD.cpp_type = 1

CURRENCYRECORDINFO_TXID_FIELD.name = "txid"
CURRENCYRECORDINFO_TXID_FIELD.full_name = ".prootc.currencyrecordinfo.txid"
CURRENCYRECORDINFO_TXID_FIELD.number = 2
CURRENCYRECORDINFO_TXID_FIELD.index = 1
CURRENCYRECORDINFO_TXID_FIELD.label = 1
CURRENCYRECORDINFO_TXID_FIELD.has_default_value = false
CURRENCYRECORDINFO_TXID_FIELD.default_value = ""
CURRENCYRECORDINFO_TXID_FIELD.type = 9
CURRENCYRECORDINFO_TXID_FIELD.cpp_type = 9

CURRENCYRECORDINFO_TXTYPE_FIELD.name = "txtype"
CURRENCYRECORDINFO_TXTYPE_FIELD.full_name = ".prootc.currencyrecordinfo.txtype"
CURRENCYRECORDINFO_TXTYPE_FIELD.number = 3
CURRENCYRECORDINFO_TXTYPE_FIELD.index = 2
CURRENCYRECORDINFO_TXTYPE_FIELD.label = 1
CURRENCYRECORDINFO_TXTYPE_FIELD.has_default_value = false
CURRENCYRECORDINFO_TXTYPE_FIELD.default_value = 0
CURRENCYRECORDINFO_TXTYPE_FIELD.type = 5
CURRENCYRECORDINFO_TXTYPE_FIELD.cpp_type = 1

CURRENCYRECORDINFO_CHAINID_FIELD.name = "chainid"
CURRENCYRECORDINFO_CHAINID_FIELD.full_name = ".prootc.currencyrecordinfo.chainid"
CURRENCYRECORDINFO_CHAINID_FIELD.number = 4
CURRENCYRECORDINFO_CHAINID_FIELD.index = 3
CURRENCYRECORDINFO_CHAINID_FIELD.label = 1
CURRENCYRECORDINFO_CHAINID_FIELD.has_default_value = false
CURRENCYRECORDINFO_CHAINID_FIELD.default_value = ""
CURRENCYRECORDINFO_CHAINID_FIELD.type = 9
CURRENCYRECORDINFO_CHAINID_FIELD.cpp_type = 9

CURRENCYRECORDINFO_TXDATA_FIELD.name = "txdata"
CURRENCYRECORDINFO_TXDATA_FIELD.full_name = ".prootc.currencyrecordinfo.txdata"
CURRENCYRECORDINFO_TXDATA_FIELD.number = 5
CURRENCYRECORDINFO_TXDATA_FIELD.index = 4
CURRENCYRECORDINFO_TXDATA_FIELD.label = 1
CURRENCYRECORDINFO_TXDATA_FIELD.has_default_value = false
CURRENCYRECORDINFO_TXDATA_FIELD.default_value = ""
CURRENCYRECORDINFO_TXDATA_FIELD.type = 9
CURRENCYRECORDINFO_TXDATA_FIELD.cpp_type = 9

CURRENCYRECORDINFO_FROMADDR_FIELD.name = "fromaddr"
CURRENCYRECORDINFO_FROMADDR_FIELD.full_name = ".prootc.currencyrecordinfo.fromaddr"
CURRENCYRECORDINFO_FROMADDR_FIELD.number = 6
CURRENCYRECORDINFO_FROMADDR_FIELD.index = 5
CURRENCYRECORDINFO_FROMADDR_FIELD.label = 1
CURRENCYRECORDINFO_FROMADDR_FIELD.has_default_value = false
CURRENCYRECORDINFO_FROMADDR_FIELD.default_value = ""
CURRENCYRECORDINFO_FROMADDR_FIELD.type = 9
CURRENCYRECORDINFO_FROMADDR_FIELD.cpp_type = 9

CURRENCYRECORDINFO_TOADDR_FIELD.name = "toaddr"
CURRENCYRECORDINFO_TOADDR_FIELD.full_name = ".prootc.currencyrecordinfo.toaddr"
CURRENCYRECORDINFO_TOADDR_FIELD.number = 7
CURRENCYRECORDINFO_TOADDR_FIELD.index = 6
CURRENCYRECORDINFO_TOADDR_FIELD.label = 1
CURRENCYRECORDINFO_TOADDR_FIELD.has_default_value = false
CURRENCYRECORDINFO_TOADDR_FIELD.default_value = ""
CURRENCYRECORDINFO_TOADDR_FIELD.type = 9
CURRENCYRECORDINFO_TOADDR_FIELD.cpp_type = 9

CURRENCYRECORDINFO_AMOUNT_FIELD.name = "amount"
CURRENCYRECORDINFO_AMOUNT_FIELD.full_name = ".prootc.currencyrecordinfo.amount"
CURRENCYRECORDINFO_AMOUNT_FIELD.number = 8
CURRENCYRECORDINFO_AMOUNT_FIELD.index = 7
CURRENCYRECORDINFO_AMOUNT_FIELD.label = 1
CURRENCYRECORDINFO_AMOUNT_FIELD.has_default_value = false
CURRENCYRECORDINFO_AMOUNT_FIELD.default_value = ""
CURRENCYRECORDINFO_AMOUNT_FIELD.type = 9
CURRENCYRECORDINFO_AMOUNT_FIELD.cpp_type = 9

CURRENCYRECORDINFO_TXFEE_FIELD.name = "txfee"
CURRENCYRECORDINFO_TXFEE_FIELD.full_name = ".prootc.currencyrecordinfo.txfee"
CURRENCYRECORDINFO_TXFEE_FIELD.number = 9
CURRENCYRECORDINFO_TXFEE_FIELD.index = 8
CURRENCYRECORDINFO_TXFEE_FIELD.label = 1
CURRENCYRECORDINFO_TXFEE_FIELD.has_default_value = false
CURRENCYRECORDINFO_TXFEE_FIELD.default_value = ""
CURRENCYRECORDINFO_TXFEE_FIELD.type = 9
CURRENCYRECORDINFO_TXFEE_FIELD.cpp_type = 9

CURRENCYRECORDINFO_TXSTATUS_FIELD.name = "txstatus"
CURRENCYRECORDINFO_TXSTATUS_FIELD.full_name = ".prootc.currencyrecordinfo.txstatus"
CURRENCYRECORDINFO_TXSTATUS_FIELD.number = 10
CURRENCYRECORDINFO_TXSTATUS_FIELD.index = 9
CURRENCYRECORDINFO_TXSTATUS_FIELD.label = 1
CURRENCYRECORDINFO_TXSTATUS_FIELD.has_default_value = false
CURRENCYRECORDINFO_TXSTATUS_FIELD.default_value = 0
CURRENCYRECORDINFO_TXSTATUS_FIELD.type = 5
CURRENCYRECORDINFO_TXSTATUS_FIELD.cpp_type = 1

CURRENCYRECORDINFO_TXTIME_FIELD.name = "txtime"
CURRENCYRECORDINFO_TXTIME_FIELD.full_name = ".prootc.currencyrecordinfo.txtime"
CURRENCYRECORDINFO_TXTIME_FIELD.number = 11
CURRENCYRECORDINFO_TXTIME_FIELD.index = 10
CURRENCYRECORDINFO_TXTIME_FIELD.label = 1
CURRENCYRECORDINFO_TXTIME_FIELD.has_default_value = false
CURRENCYRECORDINFO_TXTIME_FIELD.default_value = ""
CURRENCYRECORDINFO_TXTIME_FIELD.type = 9
CURRENCYRECORDINFO_TXTIME_FIELD.cpp_type = 9

CURRENCYRECORDINFO_BLOCKHASH_FIELD.name = "blockhash"
CURRENCYRECORDINFO_BLOCKHASH_FIELD.full_name = ".prootc.currencyrecordinfo.blockhash"
CURRENCYRECORDINFO_BLOCKHASH_FIELD.number = 12
CURRENCYRECORDINFO_BLOCKHASH_FIELD.index = 11
CURRENCYRECORDINFO_BLOCKHASH_FIELD.label = 1
CURRENCYRECORDINFO_BLOCKHASH_FIELD.has_default_value = false
CURRENCYRECORDINFO_BLOCKHASH_FIELD.default_value = ""
CURRENCYRECORDINFO_BLOCKHASH_FIELD.type = 9
CURRENCYRECORDINFO_BLOCKHASH_FIELD.cpp_type = 9

CURRENCYRECORDINFO_RECDSTATUS_FIELD.name = "recdstatus"
CURRENCYRECORDINFO_RECDSTATUS_FIELD.full_name = ".prootc.currencyrecordinfo.recdstatus"
CURRENCYRECORDINFO_RECDSTATUS_FIELD.number = 13
CURRENCYRECORDINFO_RECDSTATUS_FIELD.index = 12
CURRENCYRECORDINFO_RECDSTATUS_FIELD.label = 1
CURRENCYRECORDINFO_RECDSTATUS_FIELD.has_default_value = false
CURRENCYRECORDINFO_RECDSTATUS_FIELD.default_value = 0
CURRENCYRECORDINFO_RECDSTATUS_FIELD.type = 5
CURRENCYRECORDINFO_RECDSTATUS_FIELD.cpp_type = 1

CURRENCYRECORDINFO_REMARK_FIELD.name = "remark"
CURRENCYRECORDINFO_REMARK_FIELD.full_name = ".prootc.currencyrecordinfo.remark"
CURRENCYRECORDINFO_REMARK_FIELD.number = 14
CURRENCYRECORDINFO_REMARK_FIELD.index = 13
CURRENCYRECORDINFO_REMARK_FIELD.label = 1
CURRENCYRECORDINFO_REMARK_FIELD.has_default_value = false
CURRENCYRECORDINFO_REMARK_FIELD.default_value = ""
CURRENCYRECORDINFO_REMARK_FIELD.type = 9
CURRENCYRECORDINFO_REMARK_FIELD.cpp_type = 9

CURRENCYRECORDINFO_CREATETIME_FIELD.name = "createtime"
CURRENCYRECORDINFO_CREATETIME_FIELD.full_name = ".prootc.currencyrecordinfo.createtime"
CURRENCYRECORDINFO_CREATETIME_FIELD.number = 15
CURRENCYRECORDINFO_CREATETIME_FIELD.index = 14
CURRENCYRECORDINFO_CREATETIME_FIELD.label = 1
CURRENCYRECORDINFO_CREATETIME_FIELD.has_default_value = false
CURRENCYRECORDINFO_CREATETIME_FIELD.default_value = ""
CURRENCYRECORDINFO_CREATETIME_FIELD.type = 9
CURRENCYRECORDINFO_CREATETIME_FIELD.cpp_type = 9

CURRENCYRECORDINFO_SPECIFICTYPE_FIELD.name = "specifictype"
CURRENCYRECORDINFO_SPECIFICTYPE_FIELD.full_name = ".prootc.currencyrecordinfo.specifictype"
CURRENCYRECORDINFO_SPECIFICTYPE_FIELD.number = 16
CURRENCYRECORDINFO_SPECIFICTYPE_FIELD.index = 15
CURRENCYRECORDINFO_SPECIFICTYPE_FIELD.label = 1
CURRENCYRECORDINFO_SPECIFICTYPE_FIELD.has_default_value = false
CURRENCYRECORDINFO_SPECIFICTYPE_FIELD.default_value = 0
CURRENCYRECORDINFO_SPECIFICTYPE_FIELD.type = 5
CURRENCYRECORDINFO_SPECIFICTYPE_FIELD.cpp_type = 1

CURRENCYRECORDINFO_COINID_FIELD.name = "coinid"
CURRENCYRECORDINFO_COINID_FIELD.full_name = ".prootc.currencyrecordinfo.coinid"
CURRENCYRECORDINFO_COINID_FIELD.number = 17
CURRENCYRECORDINFO_COINID_FIELD.index = 16
CURRENCYRECORDINFO_COINID_FIELD.label = 1
CURRENCYRECORDINFO_COINID_FIELD.has_default_value = false
CURRENCYRECORDINFO_COINID_FIELD.default_value = 0
CURRENCYRECORDINFO_COINID_FIELD.type = 5
CURRENCYRECORDINFO_COINID_FIELD.cpp_type = 1

CURRENCYRECORDINFO_COINNAME_FIELD.name = "coinname"
CURRENCYRECORDINFO_COINNAME_FIELD.full_name = ".prootc.currencyrecordinfo.coinname"
CURRENCYRECORDINFO_COINNAME_FIELD.number = 18
CURRENCYRECORDINFO_COINNAME_FIELD.index = 17
CURRENCYRECORDINFO_COINNAME_FIELD.label = 1
CURRENCYRECORDINFO_COINNAME_FIELD.has_default_value = false
CURRENCYRECORDINFO_COINNAME_FIELD.default_value = ""
CURRENCYRECORDINFO_COINNAME_FIELD.type = 9
CURRENCYRECORDINFO_COINNAME_FIELD.cpp_type = 9

CURRENCYRECORDINFO_ADDRTYPE_FIELD.name = "addrtype"
CURRENCYRECORDINFO_ADDRTYPE_FIELD.full_name = ".prootc.currencyrecordinfo.addrtype"
CURRENCYRECORDINFO_ADDRTYPE_FIELD.number = 19
CURRENCYRECORDINFO_ADDRTYPE_FIELD.index = 18
CURRENCYRECORDINFO_ADDRTYPE_FIELD.label = 1
CURRENCYRECORDINFO_ADDRTYPE_FIELD.has_default_value = false
CURRENCYRECORDINFO_ADDRTYPE_FIELD.default_value = 0
CURRENCYRECORDINFO_ADDRTYPE_FIELD.type = 5
CURRENCYRECORDINFO_ADDRTYPE_FIELD.cpp_type = 1

CURRENCYRECORDINFO_ADDRNAME_FIELD.name = "addrname"
CURRENCYRECORDINFO_ADDRNAME_FIELD.full_name = ".prootc.currencyrecordinfo.addrname"
CURRENCYRECORDINFO_ADDRNAME_FIELD.number = 20
CURRENCYRECORDINFO_ADDRNAME_FIELD.index = 19
CURRENCYRECORDINFO_ADDRNAME_FIELD.label = 1
CURRENCYRECORDINFO_ADDRNAME_FIELD.has_default_value = false
CURRENCYRECORDINFO_ADDRNAME_FIELD.default_value = ""
CURRENCYRECORDINFO_ADDRNAME_FIELD.type = 9
CURRENCYRECORDINFO_ADDRNAME_FIELD.cpp_type = 9

CURRENCYRECORDINFO.name = "currencyrecordinfo"
CURRENCYRECORDINFO.full_name = ".prootc.currencyrecordinfo"
CURRENCYRECORDINFO.nested_types = {}
CURRENCYRECORDINFO.enum_types = {}
CURRENCYRECORDINFO.fields = {CURRENCYRECORDINFO_ID_FIELD, CURRENCYRECORDINFO_TXID_FIELD, CURRENCYRECORDINFO_TXTYPE_FIELD, CURRENCYRECORDINFO_CHAINID_FIELD, CURRENCYRECORDINFO_TXDATA_FIELD, CURRENCYRECORDINFO_FROMADDR_FIELD, CURRENCYRECORDINFO_TOADDR_FIELD, CURRENCYRECORDINFO_AMOUNT_FIELD, CURRENCYRECORDINFO_TXFEE_FIELD, CURRENCYRECORDINFO_TXSTATUS_FIELD, CURRENCYRECORDINFO_TXTIME_FIELD, CURRENCYRECORDINFO_BLOCKHASH_FIELD, CURRENCYRECORDINFO_RECDSTATUS_FIELD, CURRENCYRECORDINFO_REMARK_FIELD, CURRENCYRECORDINFO_CREATETIME_FIELD, CURRENCYRECORDINFO_SPECIFICTYPE_FIELD, CURRENCYRECORDINFO_COINID_FIELD, CURRENCYRECORDINFO_COINNAME_FIELD, CURRENCYRECORDINFO_ADDRTYPE_FIELD, CURRENCYRECORDINFO_ADDRNAME_FIELD}
CURRENCYRECORDINFO.is_extendable = false
CURRENCYRECORDINFO.extensions = {}
INCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.name = "detailtype"
INCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.full_name = ".prootc.incomeexpendituredetail.detailtype"
INCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.number = 1
INCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.index = 0
INCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.label = 1
INCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.has_default_value = false
INCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.default_value = 0
INCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.type = 5
INCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.cpp_type = 1

INCOMEEXPENDITUREDETAIL_AMOUNT_FIELD.name = "amount"
INCOMEEXPENDITUREDETAIL_AMOUNT_FIELD.full_name = ".prootc.incomeexpendituredetail.amount"
INCOMEEXPENDITUREDETAIL_AMOUNT_FIELD.number = 2
INCOMEEXPENDITUREDETAIL_AMOUNT_FIELD.index = 1
INCOMEEXPENDITUREDETAIL_AMOUNT_FIELD.label = 1
INCOMEEXPENDITUREDETAIL_AMOUNT_FIELD.has_default_value = false
INCOMEEXPENDITUREDETAIL_AMOUNT_FIELD.default_value = ""
INCOMEEXPENDITUREDETAIL_AMOUNT_FIELD.type = 9
INCOMEEXPENDITUREDETAIL_AMOUNT_FIELD.cpp_type = 9

INCOMEEXPENDITUREDETAIL_AFTERAMOUNT_FIELD.name = "afteramount"
INCOMEEXPENDITUREDETAIL_AFTERAMOUNT_FIELD.full_name = ".prootc.incomeexpendituredetail.afteramount"
INCOMEEXPENDITUREDETAIL_AFTERAMOUNT_FIELD.number = 3
INCOMEEXPENDITUREDETAIL_AFTERAMOUNT_FIELD.index = 2
INCOMEEXPENDITUREDETAIL_AFTERAMOUNT_FIELD.label = 1
INCOMEEXPENDITUREDETAIL_AFTERAMOUNT_FIELD.has_default_value = false
INCOMEEXPENDITUREDETAIL_AFTERAMOUNT_FIELD.default_value = ""
INCOMEEXPENDITUREDETAIL_AFTERAMOUNT_FIELD.type = 9
INCOMEEXPENDITUREDETAIL_AFTERAMOUNT_FIELD.cpp_type = 9

INCOMEEXPENDITUREDETAIL_CREATETIME_FIELD.name = "createtime"
INCOMEEXPENDITUREDETAIL_CREATETIME_FIELD.full_name = ".prootc.incomeexpendituredetail.createtime"
INCOMEEXPENDITUREDETAIL_CREATETIME_FIELD.number = 4
INCOMEEXPENDITUREDETAIL_CREATETIME_FIELD.index = 3
INCOMEEXPENDITUREDETAIL_CREATETIME_FIELD.label = 1
INCOMEEXPENDITUREDETAIL_CREATETIME_FIELD.has_default_value = false
INCOMEEXPENDITUREDETAIL_CREATETIME_FIELD.default_value = ""
INCOMEEXPENDITUREDETAIL_CREATETIME_FIELD.type = 9
INCOMEEXPENDITUREDETAIL_CREATETIME_FIELD.cpp_type = 9

INCOMEEXPENDITUREDETAIL.name = "incomeexpendituredetail"
INCOMEEXPENDITUREDETAIL.full_name = ".prootc.incomeexpendituredetail"
INCOMEEXPENDITUREDETAIL.nested_types = {}
INCOMEEXPENDITUREDETAIL.enum_types = {}
INCOMEEXPENDITUREDETAIL.fields = {INCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD, INCOMEEXPENDITUREDETAIL_AMOUNT_FIELD, INCOMEEXPENDITUREDETAIL_AFTERAMOUNT_FIELD, INCOMEEXPENDITUREDETAIL_CREATETIME_FIELD}
INCOMEEXPENDITUREDETAIL.is_extendable = false
INCOMEEXPENDITUREDETAIL.extensions = {}

apppealinfo = protobuf.Message(APPPEALINFO)
currencyrecordinfo = protobuf.Message(CURRENCYRECORDINFO)
customerorderinfo = protobuf.Message(CUSTOMERORDERINFO)
incomeexpendituredetail = protobuf.Message(INCOMEEXPENDITUREDETAIL)
vendororderinfo = protobuf.Message(VENDORORDERINFO)

----------nimol modify---------
ST_ORDER_PB_APPPEALINFO = APPPEALINFO
ST_ORDER_PB_CURRENCYRECORDINFO = CURRENCYRECORDINFO
ST_ORDER_PB_CUSTOMERORDERINFO = CUSTOMERORDERINFO
ST_ORDER_PB_INCOMEEXPENDITUREDETAIL = INCOMEEXPENDITUREDETAIL
ST_ORDER_PB_VENDORORDERINFO = VENDORORDERINFO
