--通知模块


--链接上之后，创建一个请求，携带加密后的参数
PacketCode[4001] = {server = 4001, client = 4002, des = "cgchatcrate", func = "ChatCreate"}
PacketCode[4002] = {server = 4001, client = 4002, des = "gcchatcrate", func = "ChatCreate"}


PacketCode[4004] = {server = 4003, client = 4004, des = "gcchatupdate", func = "ChatUpdate"}


--超时，如果H5端长时间没有操作，会自动超时
PacketCode[4007] = {server = 4007, client = 4008, des = "cgchattimeout", func = "ChatTimeOut"}
PacketCode[4008] = {server = 4007, client = 4008, des = "gcchattimeout", func = "ChatTimeOut"}

--发送聊天内容
PacketCode[4009] = {server = 4009, client = 4010, des = "cgchatsend", func = "ChatSend"}
PacketCode[4010] = {server = 4009, client = 4010, des = "gcchatsend", func = "ChatSend"}

--主动推送聊天内容
--PacketCode[4011] = {server = 4011, client = 4012, des = "cgchatsend", func = "ChatSend"}
PacketCode[4012] = {server = 4011, client = 4012, des = "gcchatrecv", func = "ChatRecv"}



--心跳包
PacketCode[1027] = {server = 1027, client = 1028, des = "cgheartbeat", func = "Heartbeat"}
PacketCode[1028] = {server = 1027, client = 1028, des = "gcheartbeat", func = "Heartbeat"}

--重连
PacketCode[1029] = {server = 1029, client = 1030, des = "cgreconnect", func = "ReConnect"}
PacketCode[1030] = {server = 1029, client = 1030, des = "gcreconnect", func = "ReConnect"}


