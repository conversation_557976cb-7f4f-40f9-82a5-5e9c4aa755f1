PacketInfo = {}
PacketInfo.msgBegin = "head"
PacketInfo.headSize = 12
PacketInfo.beginSsize = 4
PacketInfo.buffSize = 4
PacketInfo.packetIDSize = 2
PacketInfo.operatorIDSize = 2
PacketInfo.buffMaxSize = 10240


PacketCode = { }
ReturnCode = {}

--手机号注册
PacketCode[1001] = {server = 1001, client = 1002, des = "cgregister", func = "Register"}
PacketCode[1002] = {server = 1001, client = 1002, des = "gcregister", func = "Register"}

--登陆
PacketCode[1003] = {server = 1003, client = 1004, des = "cglogin", func = "Login"}
PacketCode[1004] = {server = 1003, client = 1004, des = "gclogin", func = "Login"}

--踢出玩家
PacketCode[1006] = {server = 1005, client = 1006, des = "gckituser", func = "KitUser"}

--忘记登录密码
PacketCode[1007] = {server = 1007, client = 1008, des = "cgforgetloginpwd", func = "ForgetLoginPwd"}
PacketCode[1008] = {server = 1007, client = 1008, des = "gcforgetloginpwd", func = "ForgetLoginPwd"}

--修改登录密码
PacketCode[1009] = {server = 1009, client = 1010, des = "cgupdateloginpwd", func = "UpdateLoginPwd"}
PacketCode[1010] = {server = 1009, client = 1010, des = "gcupdateloginpwd", func = "UpdateLoginPwd"}

--设置资金密码
PacketCode[1011] = {server = 1011, client = 1012, des = "cgsetfundpwd", func = "SetFundPwd"}
PacketCode[1012] = {server = 1011, client = 1012, des = "gcsetfundpwd", func = "SetFundPwd"}

--忘记资金密码
PacketCode[1013] = {server = 1013, client = 1014, des = "cgforgetfundpwd", func = "ForgetFundPwd"}
PacketCode[1014] = {server = 1013, client = 1014, des = "gcforgetfundpwd", func = "ForgetFundPwd"}

--修改昵称 
PacketCode[1015] = {server = 1015, client = 1016, des = "cgupdatenickname", func = "UpdateNickname"}
PacketCode[1016] = {server = 1015, client = 1016, des = "gcupdatenickname", func = "UpdateNickname"}

--添加收款方式
PacketCode[1017] = {server = 1017, client = 1018, des = "cgaddpay", func = "AddPay"}
PacketCode[1018] = {server = 1017, client = 1018, des = "gcaddpay", func = "AddPay"}

--修改收款方式限额
PacketCode[1019] = {server = 1019, client = 1020, des = "cgmodifypay", func = "ModifyPay"}
PacketCode[1020] = {server = 1019, client = 1020, des = "gcmodifypay", func = "ModifyPay"}

--删除收款方式
PacketCode[1021] = {server = 1021, client = 1022, des = "cgdeletepay", func = "DeletePay"}
PacketCode[1022] = {server = 1021, client = 1022, des = "gcdeletepay", func = "DeletePay"}

--失效/生效收款方式
PacketCode[1023] = {server = 1023, client = 1024, des = "cgpaystatus", func = "PayStatus"}
PacketCode[1024] = {server = 1023, client = 1024, des = "gcpaystatus", func = "PayStatus"}

--我的收款方式
PacketCode[1025] = {server = 1025, client = 1026, des = "cgpaylist", func = "Paylist"}
PacketCode[1026] = {server = 1025, client = 1026, des = "gcpaylist", func = "Paylist"}

--心跳包
PacketCode[1027] = {server = 1027, client = 1028, des = "cgheartbeat", func = "Heartbeat"}
PacketCode[1028] = {server = 1027, client = 1028, des = "gcheartbeat", func = "Heartbeat"}

--重连
PacketCode[1029] = {server = 1029, client = 1030, des = "cgreconnect", func = "ReConnect"}
PacketCode[1030] = {server = 1029, client = 1030, des = "gcreconnect", func = "ReConnect"}

--更新用户信息
PacketCode[1032] = {server = 1031, client = 1032, des = "gcupdateuserinfo", func = "UpdateUserInfo"}

--我的业绩
PacketCode[1033] = {server = 1033, client = 1034, des = "cgmyperformance", func = "MyPerformance"}
PacketCode[1034] = {server = 1033, client = 1034, des = "gcmyperformance", func = "MyPerformance"}

--我的业绩
PacketCode[1035] = {server = 1035, client = 1036, des = "cgsetsubinfo", func = "SetSubInfo"}
PacketCode[1036] = {server = 1035, client = 1036, des = "gcsetsubinfo", func = "SetSubInfo"}

--检查资金密码
PacketCode[1037] = {server = 1037, client = 1038, des = "cgcheckfundpwd", func = "CheckFundPwd"}
PacketCode[1038] = {server = 1037, client = 1038, des = "gccheckfundpwd", func = "CheckFundPwd"}

--获取用户信息
PacketCode[1039] = {server = 1039, client = 1040, des = "cggetuserinfo", func = "GetUserInfo"}
PacketCode[1040] = {server = 1039, client = 1040, des = "gcgetuserinfo", func = "GetUserInfo"}

--转让能量值
PacketCode[1041] = {server = 1041, client = 1042, des = "cgtransferenergyvalue", func = "TransferEnergyValue"}
PacketCode[1042] = {server = 1041, client = 1042, des = "gctransferenergyvalue", func = "TransferEnergyValue"}

ReturnCode["human_authcode_not_exist"] = {1001, "请输入正确验证码或手机"}
ReturnCode["human_user_not_exist"] = {1002,"用户不存在"}
ReturnCode["human_pass_err"] = {1003,"密码错误"}
ReturnCode["human_not_support"] = {1004, "暂不支持这种方式"}
ReturnCode["human_create_user_err"] = {1005, "创建用户失败"}
ReturnCode["human_phone_exist"] = {1006, "手机号码已被注册"}
ReturnCode["human_parameter_error"] = {1007, "参数错误"}
ReturnCode["human_phone_not_exist"] = {1008,"手机号码还未注册"}
ReturnCode["human_pwd_err_1"] = {1009,"原密码错误"}
ReturnCode["human_pwd_err_2"] = {1010,"新密码与源密码相同"}
ReturnCode["human_pwd_err_3"] = {1011,"密码不能空"}
ReturnCode["human_fundpwd_err_1"] = {1012,"资金密码已被设置"}
ReturnCode["human_nickname_err_1"] = {1013,"昵称不能为空"}
ReturnCode["human_payinfo_exist"] = {1014,"该账号已经被绑定"}
ReturnCode["human_add_err_1"] = {1015,"添加失败"}
ReturnCode["human_payinfo_not_exist"] = {1016,"该账号未被绑定"}
ReturnCode["human_invitecode_error"] = {1017,"邀请码错误"}
ReturnCode["human_fundpassword_not_exist"] = {1018,"请先设置资金密码"}