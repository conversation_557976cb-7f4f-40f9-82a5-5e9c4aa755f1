--2000~3000 交易


--挂单
PacketCode[2001] = {server = 2001, client = 2002, des = "cghangorder", func = "HangOrder"}
PacketCode[2002] = {server = 2001, client = 2002, des = "gchangorder", func = "HangOrder"}

--修改挂单
PacketCode[2003] = {server = 2003, client = 2004, des = "cgmodifyorder", func = "ModifyOrder"}
PacketCode[2004] = {server = 2003, client = 2004, des = "gcmodifyorder", func = "ModifyOrder"}

--取消挂单
PacketCode[2005] = {server = 2005, client = 2006, des = "cgdisableorder", func = "DisableOrder"}
PacketCode[2006] = {server = 2005, client = 2006, des = "gcdisableorder", func = "DisableOrder"}

--查看自己的挂单
PacketCode[2007] = {server = 2007, client = 2008, des = "cgorderlist", func = "OrderList"}
PacketCode[2008] = {server = 2007, client = 2008, des = "gcorderlist", func = "OrderList"}

--快捷买卖币
PacketCode[2009] = {server = 2009, client = 2010, des = "cgquickdeal", func = "QuickDeal"}
PacketCode[2010] = {server = 2009, client = 2010, des = "gcquickdeal", func = "QuickDeal"}

--交易
PacketCode[2011] = {server = 2011, client = 2012, des = "cgdeal", func = "Deal"}
PacketCode[2012] = {server = 2011, client = 2012, des = "gcdeal", func = "Deal"}

--确认支付
PacketCode[2013] = {server = 2013, client = 2014, des = "cgpaid", func = "Paid"}
PacketCode[2014] = {server = 2013, client = 2014, des = "gcpaid", func = "Paid"}

--放行
PacketCode[2015] = {server = 2015, client = 2016, des = "cgpass", func = "Pass"}
PacketCode[2016] = {server = 2015, client = 2016, des = "gcpass", func = "Pass"}

--剩余取消交易剩余次数
PacketCode[2017] = {server = 2017, client = 2018, des = "cgcancelleftcount", func = "CancelLeftCount"}
PacketCode[2018] = {server = 2017, client = 2018, des = "gccancelleftcount", func = "CancelLeftCount"}

--取消交易订单
PacketCode[2019] = {server = 2019, client = 2020, des = "cgcancel", func = "Cancel"} 
PacketCode[2020] = {server = 2019, client = 2020, des = "gccancel", func = "Cancel"}

--申诉
PacketCode[2021] = {server = 2021, client = 2022, des = "cgappeal", func = "Appeal"}
PacketCode[2022] = {server = 2021, client = 2022, des = "gcappeal", func = "Appeal"}

--回复申诉
PacketCode[2023] = {server = 2023, client = 2024, des = "cgappealreply", func = "AppealReply"}
PacketCode[2024] = {server = 2023, client = 2024, des = "gcappealreply", func = "AppealReply"}

--申诉详情
PacketCode[2025] = {server = 2025, client = 2026, des = "cgappealdetail", func = "AppealDetail"}
PacketCode[2026] = {server = 2025, client = 2026, des = "gcappealdetail", func = "AppealDetail"}

--申诉列表
PacketCode[2027] = {server = 2027, client = 2028, des = "cgappeallist", func = "AppealList"}
PacketCode[2028] = {server = 2027, client = 2028, des = "gcappeallist", func = "AppealList"}

--订单详情
PacketCode[2029] = {server = 2029, client = 2030, des = "cgdetail", func = "Detail"}
PacketCode[2030] = {server = 2029, client = 2030, des = "gcdetail", func = "Detail"}

--收入统计(币商)
PacketCode[2031] = {server = 2031, client = 2032, des = "cgincomestat", func = "IncomeStat"}
PacketCode[2032] = {server = 2031, client = 2032, des = "gcincomestat", func = "IncomeStat"}

--支出统计(币商)
PacketCode[2033] = {server = 2033, client = 2034, des = "cgexpensestat", func = "ExpenseStat"}
PacketCode[2034] = {server = 2033, client = 2034, des = "gcexpensestat", func = "ExpenseStat"}

--今日售出/买入
PacketCode[2035] = {server = 2035, client = 2036, des = "cgtodayflow", func = "TodayFlow"}
PacketCode[2036] = {server = 2035, client = 2036, des = "gctodayflow", func = "TodayFlow"}

--交易记录
PacketCode[2037] = {server = 2037, client = 2038, des = "cgdealrecdlist", func = "DealRecdList"}
PacketCode[2038] = {server = 2037, client = 2038, des = "gedealrecdlist", func = "DealRecdList"}

--待处理的订单
PacketCode[2041] = {server = 2041, client = 2042, des = "cgworkinglist", func = "WorkingList"}
PacketCode[2042] = {server = 2041, client = 2042, des = "gcworkinglist", func = "WorkingList"}

--顾客订单列表(查看币商的挂单)
PacketCode[2043] = {server = 2043, client = 2044, des = "cgcustomerorderlist", func = "CustomerOrderList"}
PacketCode[2044] = {server = 2043, client = 2044, des = "gccustomerorderlist", func = "CustomerOrderList"}

--提币/转账
PacketCode[2045] = {server = 2045, client = 2046, des = "cgwithdraw", func = "Withdraw"}
PacketCode[2046] = {server = 2045, client = 2046, des = "gcwithdraw", func = "Withdraw"}

--币币交易记录
PacketCode[2047] = {server = 2047, client = 2048, des = "cgwithdrawrecord", func = "WithdrawRecord"}
PacketCode[2048] = {server = 2047, client = 2048, des = "gcwithdrawrecord", func = "WithdrawRecord"}

--汇率查询
PacketCode[2049] = {server = 2049, client = 2050, des = "cgexchangerate", func = "ExchangeRate"}
PacketCode[2050] = {server = 2049, client = 2050, des = "gcexchangerate", func = "ExchangeRate"}

--检查账号
PacketCode[2051] = {server = 2051, client = 2052, des = "cgcheckaddr", func = "CheckAddr"}
PacketCode[2052] = {server = 2051, client = 2052, des = "gccheckaddr", func = "CheckAddr"}

--托管钱包充值
PacketCode[2053] = {server = 2053, client = 2054, des = "cgtwalletrecharge", func = "TWalletRecharge"}
PacketCode[2054] = {server = 2053, client = 2054, des = "gctwalletrecharge", func = "TWalletRecharge"}

--提交TXID
PacketCode[2055] = {server = 2055, client = 2056, des = "cgsubmittxid", func = "SubmitTXID"}
PacketCode[2056] = {server = 2055, client = 2056, des = "gcsubmittxid", func = "SubmitTXID"}

--托管钱包充值记录
PacketCode[2057] = {server = 2057, client = 2058, des = "cgtwalletrecord", func = "TWalletRecord"}
PacketCode[2058] = {server = 2057, client = 2058, des = "gctwalletrecord", func = "TWalletRecord"}

--提币转账手续
PacketCode[2059] = {server = 2059, client = 2060, des = "cggetwithdrawalfee", func = "GetWithdrawalFee"}
PacketCode[2060] = {server = 2059, client = 2060, des = "gcgetwithdrawalfee", func = "GetWithdrawalFee"}

--修改支付凭证
PacketCode[2061] = {server = 2061, client = 2062, des = "cgmodifyproofurl", func = "ModifyProofUrl"}
PacketCode[2062] = {server = 2061, client = 2062, des = "gcmodifyproofurl", func = "ModifyProofUrl"}

--通知客户端新增挂单
PacketCode[2064] = {server = 2063, client = 2064, des = "gcupdatevendororder", func = "UpdateVendorOrder"}

--收入支出明细
PacketCode[2065] = {server = 2065, client = 2066, des = "cgincomeexpendituredetail", func = "IncomeExpenditureDetail"}
PacketCode[2066] = {server = 2065, client = 2066, des = "gcincomeexpendituredetail", func = "IncomeExpenditureDetail"}

--激活订单
PacketCode[2067] = {server = 2067, client = 2068, des = "cgorderactivation", func = "OrderActivation"}
PacketCode[2068] = {server = 2067, client = 2068, des = "gcorderactivation", func = "OrderActivation"}


--搜索交易订单
PacketCode[2069] = {server = 2069, client = 2070, des = "cgsearchdealrecdlist", func = "SearchDealRecdList"}
PacketCode[2070] = {server = 2069, client = 2070, des = "gcsearchdealrecdlist", func = "SearchDealRecdList"}


ReturnCode["user_pay_not_exist"] = {2001,"请先绑定收款方式"}
ReturnCode["user_forzen_money_fail"] = {2002,"冻结资金失败"}

ReturnCode["get_out_price_fail"] = {2003,"获取价格失败"}

ReturnCode["order_not_exist"] = {2004,"该订单不存在"}

ReturnCode["order_is_working"] = {2005,"还有正在进行中的订单"}

ReturnCode["quick_deal_not_match"] = {2006,"匹配不到对应的订单"}

ReturnCode["deal_order_not_exist"] = {2007,"交易的订单已经失效"}

ReturnCode["deal_order_not_match"] = {2007,"挂单已经失效，请选择其他挂单"}

ReturnCode["deal_order_not_enough"] = {2007,"挂单余额不足，请选择其他挂单"}

ReturnCode["deal_order_type_error"] = {2008,"交易类型不一致，请选择正确的交易类型"}

ReturnCode["hang_money_not_enough"] = {2009,"账户余额不足"}

ReturnCode["hang_order_error"] = {2010,"挂单失败"}

ReturnCode["hang_modify_not_auto"] = {2011,"自动挂单不能修改价格"}

ReturnCode["hang_is_working"] = {2012,"还有正在进行中的交易"}

ReturnCode["hang_seller_no_money"] = {2013,"卖家可用余额不足"}

ReturnCode["hang_deal_err1"] = {2014,"输入数量请在限额之内"}