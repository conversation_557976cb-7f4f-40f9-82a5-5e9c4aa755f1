--通知模块


--公告列表
PacketCode[1101] = {server = 1101, client = 1102, des = "cgnoticelist", func = "NoticeList"}
PacketCode[1102] = {server = 1101, client = 1102, des = "gcnoticelist", func = "NoticeList"}

--公告详情
PacketCode[1103] = {server = 1103, client = 1104, des = "cgnoticedetail", func = "NoticeDetail"}
PacketCode[1104] = {server = 1103, client = 1104, des = "gcnoticedetail", func = "NoticeDetail"}

--消息通知列表
PacketCode[1105] = {server = 1105, client = 1106, des = "cgnoticemessagelist", func = "NoticMessageList"}
PacketCode[1106] = {server = 1105, client = 1106, des = "gcnoticemessagelist", func = "NoticMessageList"}

--阅读消息
PacketCode[1107] = {server = 1107, client = 1108, des = "cgnoticemessageread", func = "NoticMessageRead"}
PacketCode[1108] = {server = 1107, client = 1108, des = "gcnoticemessageread", func = "NoticMessageRead"}

--删除消息
PacketCode[1109] = {server = 1109, client = 1110, des = "cgnoticemessagedelete", func = "NoticMessageDelete"}
PacketCode[1110] = {server = 1109, client = 1110, des = "gcnoticemessagedelete", func = "NoticMessageDelete"}


ReturnCode["notice_message_not_exist"] = {1101, "消息不存在！"}
