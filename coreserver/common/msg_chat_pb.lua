-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local st_chat_pb = require("st_chat_pb")
----------nimol modify---------
local ST_CHAT_PB_CHATDATA = st_chat_pb.ST_CHAT_PB_CHATDATA
module('msg_chat_pb')


local CGCHATCRATE = protobuf.Descriptor();
local CGCHATCRATE_CHANNEL_FIELD = protobuf.FieldDescriptor();
local CGCHATCRATE_PCORDERID_FIELD = protobuf.FieldDescriptor();
local CGCHATCRATE_USERID_FIELD = protobuf.FieldDescriptor();
local GCCHATCRATE = protobuf.Descriptor();
local GCCHATCRATE_RESULT_FIELD = protobuf.FieldDescriptor();
local GCCHATCRATE_MSG_FIELD = protobuf.FieldDescriptor();
local GCCHATCRATE_CHANNEL_FIELD = protobuf.FieldDescriptor();
local GCCHATCRATE_PCORDERID_FIELD = protobuf.FieldDescriptor();
local GCCHATCRATE_STATE_FIELD = protobuf.FieldDescriptor();
local GCCHATCRATE_MONEY_FIELD = protobuf.FieldDescriptor();
local GCCHATCRATE_TIMEOUT_FIELD = protobuf.FieldDescriptor();
local GCCHATCRATE_PAYID_FIELD = protobuf.FieldDescriptor();
local GCCHATCRATE_PAYACCOUNT_FIELD = protobuf.FieldDescriptor();
local GCCHATCRATE_PAYUSERNAME_FIELD = protobuf.FieldDescriptor();
local GCCHATCRATE_PAYBANKNAME_FIELD = protobuf.FieldDescriptor();
local GCCHATCRATE_PAYBANKADDRESS_FIELD = protobuf.FieldDescriptor();
local GCCHATCRATE_PAYURL_FIELD = protobuf.FieldDescriptor();
local GCCHATCRATE_CHATLIST_FIELD = protobuf.FieldDescriptor();
local GCCHATCRATE_COINPRICE_FIELD = protobuf.FieldDescriptor();
local GCCHATCRATE_COINAMOUNT_FIELD = protobuf.FieldDescriptor();
local GCCHATCRATE_ORDERTIME_FIELD = protobuf.FieldDescriptor();
local CGCHATTIMEOUT = protobuf.Descriptor();
local CGCHATTIMEOUT_CHANNEL_FIELD = protobuf.FieldDescriptor();
local CGCHATTIMEOUT_PCORDERID_FIELD = protobuf.FieldDescriptor();
local GCCHATTIMEOUT = protobuf.Descriptor();
local GCCHATTIMEOUT_RESULT_FIELD = protobuf.FieldDescriptor();
local GCCHATTIMEOUT_MSG_FIELD = protobuf.FieldDescriptor();
local CGCHATSEND = protobuf.Descriptor();
local CGCHATSEND_CHANNEL_FIELD = protobuf.FieldDescriptor();
local CGCHATSEND_PCORDERID_FIELD = protobuf.FieldDescriptor();
local CGCHATSEND_USERID_FIELD = protobuf.FieldDescriptor();
local CGCHATSEND_MSGTYPE_FIELD = protobuf.FieldDescriptor();
local CGCHATSEND_MSG_FIELD = protobuf.FieldDescriptor();
local GCCHATSEND = protobuf.Descriptor();
local GCCHATSEND_RESULT_FIELD = protobuf.FieldDescriptor();
local GCCHATSEND_MSG_FIELD = protobuf.FieldDescriptor();
local GCCHATSEND_CDATA_FIELD = protobuf.FieldDescriptor();
local GCCHATRECV = protobuf.Descriptor();
local GCCHATRECV_RESULT_FIELD = protobuf.FieldDescriptor();
local GCCHATRECV_PCORDERID_FIELD = protobuf.FieldDescriptor();
local GCCHATRECV_CDATA_FIELD = protobuf.FieldDescriptor();
local GCCHATRECV_MSG_FIELD = protobuf.FieldDescriptor();
local GCCHATRECV_TARGET_FIELD = protobuf.FieldDescriptor();
local GCCHATUPDATE = protobuf.Descriptor();
local GCCHATUPDATE_RESULT_FIELD = protobuf.FieldDescriptor();
local GCCHATUPDATE_MSG_FIELD = protobuf.FieldDescriptor();
local GCCHATUPDATE_PCORDERID_FIELD = protobuf.FieldDescriptor();
local GCCHATUPDATE_STATE_FIELD = protobuf.FieldDescriptor();
local GCCHATUPDATE_TARGET_FIELD = protobuf.FieldDescriptor();

CGCHATCRATE_CHANNEL_FIELD.name = "channel"
CGCHATCRATE_CHANNEL_FIELD.full_name = ".prootc.cgchatcrate.channel"
CGCHATCRATE_CHANNEL_FIELD.number = 1
CGCHATCRATE_CHANNEL_FIELD.index = 0
CGCHATCRATE_CHANNEL_FIELD.label = 1
CGCHATCRATE_CHANNEL_FIELD.has_default_value = false
CGCHATCRATE_CHANNEL_FIELD.default_value = ""
CGCHATCRATE_CHANNEL_FIELD.type = 9
CGCHATCRATE_CHANNEL_FIELD.cpp_type = 9

CGCHATCRATE_PCORDERID_FIELD.name = "pcorderid"
CGCHATCRATE_PCORDERID_FIELD.full_name = ".prootc.cgchatcrate.pcorderid"
CGCHATCRATE_PCORDERID_FIELD.number = 2
CGCHATCRATE_PCORDERID_FIELD.index = 1
CGCHATCRATE_PCORDERID_FIELD.label = 1
CGCHATCRATE_PCORDERID_FIELD.has_default_value = false
CGCHATCRATE_PCORDERID_FIELD.default_value = ""
CGCHATCRATE_PCORDERID_FIELD.type = 9
CGCHATCRATE_PCORDERID_FIELD.cpp_type = 9

CGCHATCRATE_USERID_FIELD.name = "userid"
CGCHATCRATE_USERID_FIELD.full_name = ".prootc.cgchatcrate.userid"
CGCHATCRATE_USERID_FIELD.number = 3
CGCHATCRATE_USERID_FIELD.index = 2
CGCHATCRATE_USERID_FIELD.label = 1
CGCHATCRATE_USERID_FIELD.has_default_value = false
CGCHATCRATE_USERID_FIELD.default_value = 0
CGCHATCRATE_USERID_FIELD.type = 5
CGCHATCRATE_USERID_FIELD.cpp_type = 1

CGCHATCRATE.name = "cgchatcrate"
CGCHATCRATE.full_name = ".prootc.cgchatcrate"
CGCHATCRATE.nested_types = {}
CGCHATCRATE.enum_types = {}
CGCHATCRATE.fields = {CGCHATCRATE_CHANNEL_FIELD, CGCHATCRATE_PCORDERID_FIELD, CGCHATCRATE_USERID_FIELD}
CGCHATCRATE.is_extendable = false
CGCHATCRATE.extensions = {}
GCCHATCRATE_RESULT_FIELD.name = "result"
GCCHATCRATE_RESULT_FIELD.full_name = ".prootc.gcchatcrate.result"
GCCHATCRATE_RESULT_FIELD.number = 1
GCCHATCRATE_RESULT_FIELD.index = 0
GCCHATCRATE_RESULT_FIELD.label = 1
GCCHATCRATE_RESULT_FIELD.has_default_value = false
GCCHATCRATE_RESULT_FIELD.default_value = 0
GCCHATCRATE_RESULT_FIELD.type = 5
GCCHATCRATE_RESULT_FIELD.cpp_type = 1

GCCHATCRATE_MSG_FIELD.name = "msg"
GCCHATCRATE_MSG_FIELD.full_name = ".prootc.gcchatcrate.msg"
GCCHATCRATE_MSG_FIELD.number = 2
GCCHATCRATE_MSG_FIELD.index = 1
GCCHATCRATE_MSG_FIELD.label = 1
GCCHATCRATE_MSG_FIELD.has_default_value = false
GCCHATCRATE_MSG_FIELD.default_value = ""
GCCHATCRATE_MSG_FIELD.type = 9
GCCHATCRATE_MSG_FIELD.cpp_type = 9

GCCHATCRATE_CHANNEL_FIELD.name = "channel"
GCCHATCRATE_CHANNEL_FIELD.full_name = ".prootc.gcchatcrate.channel"
GCCHATCRATE_CHANNEL_FIELD.number = 3
GCCHATCRATE_CHANNEL_FIELD.index = 2
GCCHATCRATE_CHANNEL_FIELD.label = 1
GCCHATCRATE_CHANNEL_FIELD.has_default_value = false
GCCHATCRATE_CHANNEL_FIELD.default_value = ""
GCCHATCRATE_CHANNEL_FIELD.type = 9
GCCHATCRATE_CHANNEL_FIELD.cpp_type = 9

GCCHATCRATE_PCORDERID_FIELD.name = "pcorderid"
GCCHATCRATE_PCORDERID_FIELD.full_name = ".prootc.gcchatcrate.pcorderid"
GCCHATCRATE_PCORDERID_FIELD.number = 4
GCCHATCRATE_PCORDERID_FIELD.index = 3
GCCHATCRATE_PCORDERID_FIELD.label = 1
GCCHATCRATE_PCORDERID_FIELD.has_default_value = false
GCCHATCRATE_PCORDERID_FIELD.default_value = ""
GCCHATCRATE_PCORDERID_FIELD.type = 9
GCCHATCRATE_PCORDERID_FIELD.cpp_type = 9

GCCHATCRATE_STATE_FIELD.name = "state"
GCCHATCRATE_STATE_FIELD.full_name = ".prootc.gcchatcrate.state"
GCCHATCRATE_STATE_FIELD.number = 5
GCCHATCRATE_STATE_FIELD.index = 4
GCCHATCRATE_STATE_FIELD.label = 1
GCCHATCRATE_STATE_FIELD.has_default_value = false
GCCHATCRATE_STATE_FIELD.default_value = 0
GCCHATCRATE_STATE_FIELD.type = 5
GCCHATCRATE_STATE_FIELD.cpp_type = 1

GCCHATCRATE_MONEY_FIELD.name = "money"
GCCHATCRATE_MONEY_FIELD.full_name = ".prootc.gcchatcrate.money"
GCCHATCRATE_MONEY_FIELD.number = 6
GCCHATCRATE_MONEY_FIELD.index = 5
GCCHATCRATE_MONEY_FIELD.label = 1
GCCHATCRATE_MONEY_FIELD.has_default_value = false
GCCHATCRATE_MONEY_FIELD.default_value = ""
GCCHATCRATE_MONEY_FIELD.type = 9
GCCHATCRATE_MONEY_FIELD.cpp_type = 9

GCCHATCRATE_TIMEOUT_FIELD.name = "timeout"
GCCHATCRATE_TIMEOUT_FIELD.full_name = ".prootc.gcchatcrate.timeout"
GCCHATCRATE_TIMEOUT_FIELD.number = 7
GCCHATCRATE_TIMEOUT_FIELD.index = 6
GCCHATCRATE_TIMEOUT_FIELD.label = 1
GCCHATCRATE_TIMEOUT_FIELD.has_default_value = false
GCCHATCRATE_TIMEOUT_FIELD.default_value = 0
GCCHATCRATE_TIMEOUT_FIELD.type = 5
GCCHATCRATE_TIMEOUT_FIELD.cpp_type = 1

GCCHATCRATE_PAYID_FIELD.name = "payid"
GCCHATCRATE_PAYID_FIELD.full_name = ".prootc.gcchatcrate.payid"
GCCHATCRATE_PAYID_FIELD.number = 8
GCCHATCRATE_PAYID_FIELD.index = 7
GCCHATCRATE_PAYID_FIELD.label = 1
GCCHATCRATE_PAYID_FIELD.has_default_value = false
GCCHATCRATE_PAYID_FIELD.default_value = ""
GCCHATCRATE_PAYID_FIELD.type = 9
GCCHATCRATE_PAYID_FIELD.cpp_type = 9

GCCHATCRATE_PAYACCOUNT_FIELD.name = "payaccount"
GCCHATCRATE_PAYACCOUNT_FIELD.full_name = ".prootc.gcchatcrate.payaccount"
GCCHATCRATE_PAYACCOUNT_FIELD.number = 9
GCCHATCRATE_PAYACCOUNT_FIELD.index = 8
GCCHATCRATE_PAYACCOUNT_FIELD.label = 1
GCCHATCRATE_PAYACCOUNT_FIELD.has_default_value = false
GCCHATCRATE_PAYACCOUNT_FIELD.default_value = ""
GCCHATCRATE_PAYACCOUNT_FIELD.type = 9
GCCHATCRATE_PAYACCOUNT_FIELD.cpp_type = 9

GCCHATCRATE_PAYUSERNAME_FIELD.name = "payusername"
GCCHATCRATE_PAYUSERNAME_FIELD.full_name = ".prootc.gcchatcrate.payusername"
GCCHATCRATE_PAYUSERNAME_FIELD.number = 10
GCCHATCRATE_PAYUSERNAME_FIELD.index = 9
GCCHATCRATE_PAYUSERNAME_FIELD.label = 1
GCCHATCRATE_PAYUSERNAME_FIELD.has_default_value = false
GCCHATCRATE_PAYUSERNAME_FIELD.default_value = ""
GCCHATCRATE_PAYUSERNAME_FIELD.type = 9
GCCHATCRATE_PAYUSERNAME_FIELD.cpp_type = 9

GCCHATCRATE_PAYBANKNAME_FIELD.name = "paybankname"
GCCHATCRATE_PAYBANKNAME_FIELD.full_name = ".prootc.gcchatcrate.paybankname"
GCCHATCRATE_PAYBANKNAME_FIELD.number = 12
GCCHATCRATE_PAYBANKNAME_FIELD.index = 10
GCCHATCRATE_PAYBANKNAME_FIELD.label = 1
GCCHATCRATE_PAYBANKNAME_FIELD.has_default_value = false
GCCHATCRATE_PAYBANKNAME_FIELD.default_value = ""
GCCHATCRATE_PAYBANKNAME_FIELD.type = 9
GCCHATCRATE_PAYBANKNAME_FIELD.cpp_type = 9

GCCHATCRATE_PAYBANKADDRESS_FIELD.name = "paybankaddress"
GCCHATCRATE_PAYBANKADDRESS_FIELD.full_name = ".prootc.gcchatcrate.paybankaddress"
GCCHATCRATE_PAYBANKADDRESS_FIELD.number = 13
GCCHATCRATE_PAYBANKADDRESS_FIELD.index = 11
GCCHATCRATE_PAYBANKADDRESS_FIELD.label = 1
GCCHATCRATE_PAYBANKADDRESS_FIELD.has_default_value = false
GCCHATCRATE_PAYBANKADDRESS_FIELD.default_value = ""
GCCHATCRATE_PAYBANKADDRESS_FIELD.type = 9
GCCHATCRATE_PAYBANKADDRESS_FIELD.cpp_type = 9

GCCHATCRATE_PAYURL_FIELD.name = "payurl"
GCCHATCRATE_PAYURL_FIELD.full_name = ".prootc.gcchatcrate.payurl"
GCCHATCRATE_PAYURL_FIELD.number = 14
GCCHATCRATE_PAYURL_FIELD.index = 12
GCCHATCRATE_PAYURL_FIELD.label = 1
GCCHATCRATE_PAYURL_FIELD.has_default_value = false
GCCHATCRATE_PAYURL_FIELD.default_value = ""
GCCHATCRATE_PAYURL_FIELD.type = 9
GCCHATCRATE_PAYURL_FIELD.cpp_type = 9

GCCHATCRATE_CHATLIST_FIELD.name = "chatlist"
GCCHATCRATE_CHATLIST_FIELD.full_name = ".prootc.gcchatcrate.chatlist"
GCCHATCRATE_CHATLIST_FIELD.number = 15
GCCHATCRATE_CHATLIST_FIELD.index = 13
GCCHATCRATE_CHATLIST_FIELD.label = 3
GCCHATCRATE_CHATLIST_FIELD.has_default_value = false
GCCHATCRATE_CHATLIST_FIELD.default_value = {}
GCCHATCRATE_CHATLIST_FIELD.message_type = ST_CHAT_PB_CHATDATA
GCCHATCRATE_CHATLIST_FIELD.type = 11
GCCHATCRATE_CHATLIST_FIELD.cpp_type = 10

GCCHATCRATE_COINPRICE_FIELD.name = "coinprice"
GCCHATCRATE_COINPRICE_FIELD.full_name = ".prootc.gcchatcrate.coinprice"
GCCHATCRATE_COINPRICE_FIELD.number = 16
GCCHATCRATE_COINPRICE_FIELD.index = 14
GCCHATCRATE_COINPRICE_FIELD.label = 1
GCCHATCRATE_COINPRICE_FIELD.has_default_value = false
GCCHATCRATE_COINPRICE_FIELD.default_value = ""
GCCHATCRATE_COINPRICE_FIELD.type = 9
GCCHATCRATE_COINPRICE_FIELD.cpp_type = 9

GCCHATCRATE_COINAMOUNT_FIELD.name = "coinamount"
GCCHATCRATE_COINAMOUNT_FIELD.full_name = ".prootc.gcchatcrate.coinamount"
GCCHATCRATE_COINAMOUNT_FIELD.number = 17
GCCHATCRATE_COINAMOUNT_FIELD.index = 15
GCCHATCRATE_COINAMOUNT_FIELD.label = 1
GCCHATCRATE_COINAMOUNT_FIELD.has_default_value = false
GCCHATCRATE_COINAMOUNT_FIELD.default_value = ""
GCCHATCRATE_COINAMOUNT_FIELD.type = 9
GCCHATCRATE_COINAMOUNT_FIELD.cpp_type = 9

GCCHATCRATE_ORDERTIME_FIELD.name = "ordertime"
GCCHATCRATE_ORDERTIME_FIELD.full_name = ".prootc.gcchatcrate.ordertime"
GCCHATCRATE_ORDERTIME_FIELD.number = 18
GCCHATCRATE_ORDERTIME_FIELD.index = 16
GCCHATCRATE_ORDERTIME_FIELD.label = 1
GCCHATCRATE_ORDERTIME_FIELD.has_default_value = false
GCCHATCRATE_ORDERTIME_FIELD.default_value = ""
GCCHATCRATE_ORDERTIME_FIELD.type = 9
GCCHATCRATE_ORDERTIME_FIELD.cpp_type = 9

GCCHATCRATE.name = "gcchatcrate"
GCCHATCRATE.full_name = ".prootc.gcchatcrate"
GCCHATCRATE.nested_types = {}
GCCHATCRATE.enum_types = {}
GCCHATCRATE.fields = {GCCHATCRATE_RESULT_FIELD, GCCHATCRATE_MSG_FIELD, GCCHATCRATE_CHANNEL_FIELD, GCCHATCRATE_PCORDERID_FIELD, GCCHATCRATE_STATE_FIELD, GCCHATCRATE_MONEY_FIELD, GCCHATCRATE_TIMEOUT_FIELD, GCCHATCRATE_PAYID_FIELD, GCCHATCRATE_PAYACCOUNT_FIELD, GCCHATCRATE_PAYUSERNAME_FIELD, GCCHATCRATE_PAYBANKNAME_FIELD, GCCHATCRATE_PAYBANKADDRESS_FIELD, GCCHATCRATE_PAYURL_FIELD, GCCHATCRATE_CHATLIST_FIELD, GCCHATCRATE_COINPRICE_FIELD, GCCHATCRATE_COINAMOUNT_FIELD, GCCHATCRATE_ORDERTIME_FIELD}
GCCHATCRATE.is_extendable = false
GCCHATCRATE.extensions = {}
CGCHATTIMEOUT_CHANNEL_FIELD.name = "channel"
CGCHATTIMEOUT_CHANNEL_FIELD.full_name = ".prootc.cgchattimeout.channel"
CGCHATTIMEOUT_CHANNEL_FIELD.number = 1
CGCHATTIMEOUT_CHANNEL_FIELD.index = 0
CGCHATTIMEOUT_CHANNEL_FIELD.label = 1
CGCHATTIMEOUT_CHANNEL_FIELD.has_default_value = false
CGCHATTIMEOUT_CHANNEL_FIELD.default_value = ""
CGCHATTIMEOUT_CHANNEL_FIELD.type = 9
CGCHATTIMEOUT_CHANNEL_FIELD.cpp_type = 9

CGCHATTIMEOUT_PCORDERID_FIELD.name = "pcorderid"
CGCHATTIMEOUT_PCORDERID_FIELD.full_name = ".prootc.cgchattimeout.pcorderid"
CGCHATTIMEOUT_PCORDERID_FIELD.number = 2
CGCHATTIMEOUT_PCORDERID_FIELD.index = 1
CGCHATTIMEOUT_PCORDERID_FIELD.label = 1
CGCHATTIMEOUT_PCORDERID_FIELD.has_default_value = false
CGCHATTIMEOUT_PCORDERID_FIELD.default_value = ""
CGCHATTIMEOUT_PCORDERID_FIELD.type = 9
CGCHATTIMEOUT_PCORDERID_FIELD.cpp_type = 9

CGCHATTIMEOUT.name = "cgchattimeout"
CGCHATTIMEOUT.full_name = ".prootc.cgchattimeout"
CGCHATTIMEOUT.nested_types = {}
CGCHATTIMEOUT.enum_types = {}
CGCHATTIMEOUT.fields = {CGCHATTIMEOUT_CHANNEL_FIELD, CGCHATTIMEOUT_PCORDERID_FIELD}
CGCHATTIMEOUT.is_extendable = false
CGCHATTIMEOUT.extensions = {}
GCCHATTIMEOUT_RESULT_FIELD.name = "result"
GCCHATTIMEOUT_RESULT_FIELD.full_name = ".prootc.gcchattimeout.result"
GCCHATTIMEOUT_RESULT_FIELD.number = 1
GCCHATTIMEOUT_RESULT_FIELD.index = 0
GCCHATTIMEOUT_RESULT_FIELD.label = 1
GCCHATTIMEOUT_RESULT_FIELD.has_default_value = false
GCCHATTIMEOUT_RESULT_FIELD.default_value = 0
GCCHATTIMEOUT_RESULT_FIELD.type = 5
GCCHATTIMEOUT_RESULT_FIELD.cpp_type = 1

GCCHATTIMEOUT_MSG_FIELD.name = "msg"
GCCHATTIMEOUT_MSG_FIELD.full_name = ".prootc.gcchattimeout.msg"
GCCHATTIMEOUT_MSG_FIELD.number = 2
GCCHATTIMEOUT_MSG_FIELD.index = 1
GCCHATTIMEOUT_MSG_FIELD.label = 1
GCCHATTIMEOUT_MSG_FIELD.has_default_value = false
GCCHATTIMEOUT_MSG_FIELD.default_value = ""
GCCHATTIMEOUT_MSG_FIELD.type = 9
GCCHATTIMEOUT_MSG_FIELD.cpp_type = 9

GCCHATTIMEOUT.name = "gcchattimeout"
GCCHATTIMEOUT.full_name = ".prootc.gcchattimeout"
GCCHATTIMEOUT.nested_types = {}
GCCHATTIMEOUT.enum_types = {}
GCCHATTIMEOUT.fields = {GCCHATTIMEOUT_RESULT_FIELD, GCCHATTIMEOUT_MSG_FIELD}
GCCHATTIMEOUT.is_extendable = false
GCCHATTIMEOUT.extensions = {}
CGCHATSEND_CHANNEL_FIELD.name = "channel"
CGCHATSEND_CHANNEL_FIELD.full_name = ".prootc.cgchatsend.channel"
CGCHATSEND_CHANNEL_FIELD.number = 1
CGCHATSEND_CHANNEL_FIELD.index = 0
CGCHATSEND_CHANNEL_FIELD.label = 1
CGCHATSEND_CHANNEL_FIELD.has_default_value = false
CGCHATSEND_CHANNEL_FIELD.default_value = ""
CGCHATSEND_CHANNEL_FIELD.type = 9
CGCHATSEND_CHANNEL_FIELD.cpp_type = 9

CGCHATSEND_PCORDERID_FIELD.name = "pcorderid"
CGCHATSEND_PCORDERID_FIELD.full_name = ".prootc.cgchatsend.pcorderid"
CGCHATSEND_PCORDERID_FIELD.number = 2
CGCHATSEND_PCORDERID_FIELD.index = 1
CGCHATSEND_PCORDERID_FIELD.label = 1
CGCHATSEND_PCORDERID_FIELD.has_default_value = false
CGCHATSEND_PCORDERID_FIELD.default_value = ""
CGCHATSEND_PCORDERID_FIELD.type = 9
CGCHATSEND_PCORDERID_FIELD.cpp_type = 9

CGCHATSEND_USERID_FIELD.name = "userid"
CGCHATSEND_USERID_FIELD.full_name = ".prootc.cgchatsend.userid"
CGCHATSEND_USERID_FIELD.number = 3
CGCHATSEND_USERID_FIELD.index = 2
CGCHATSEND_USERID_FIELD.label = 1
CGCHATSEND_USERID_FIELD.has_default_value = false
CGCHATSEND_USERID_FIELD.default_value = 0
CGCHATSEND_USERID_FIELD.type = 5
CGCHATSEND_USERID_FIELD.cpp_type = 1

CGCHATSEND_MSGTYPE_FIELD.name = "msgtype"
CGCHATSEND_MSGTYPE_FIELD.full_name = ".prootc.cgchatsend.msgtype"
CGCHATSEND_MSGTYPE_FIELD.number = 4
CGCHATSEND_MSGTYPE_FIELD.index = 3
CGCHATSEND_MSGTYPE_FIELD.label = 1
CGCHATSEND_MSGTYPE_FIELD.has_default_value = false
CGCHATSEND_MSGTYPE_FIELD.default_value = ""
CGCHATSEND_MSGTYPE_FIELD.type = 9
CGCHATSEND_MSGTYPE_FIELD.cpp_type = 9

CGCHATSEND_MSG_FIELD.name = "msg"
CGCHATSEND_MSG_FIELD.full_name = ".prootc.cgchatsend.msg"
CGCHATSEND_MSG_FIELD.number = 5
CGCHATSEND_MSG_FIELD.index = 4
CGCHATSEND_MSG_FIELD.label = 1
CGCHATSEND_MSG_FIELD.has_default_value = false
CGCHATSEND_MSG_FIELD.default_value = ""
CGCHATSEND_MSG_FIELD.type = 9
CGCHATSEND_MSG_FIELD.cpp_type = 9

CGCHATSEND.name = "cgchatsend"
CGCHATSEND.full_name = ".prootc.cgchatsend"
CGCHATSEND.nested_types = {}
CGCHATSEND.enum_types = {}
CGCHATSEND.fields = {CGCHATSEND_CHANNEL_FIELD, CGCHATSEND_PCORDERID_FIELD, CGCHATSEND_USERID_FIELD, CGCHATSEND_MSGTYPE_FIELD, CGCHATSEND_MSG_FIELD}
CGCHATSEND.is_extendable = false
CGCHATSEND.extensions = {}
GCCHATSEND_RESULT_FIELD.name = "result"
GCCHATSEND_RESULT_FIELD.full_name = ".prootc.gcchatsend.result"
GCCHATSEND_RESULT_FIELD.number = 1
GCCHATSEND_RESULT_FIELD.index = 0
GCCHATSEND_RESULT_FIELD.label = 1
GCCHATSEND_RESULT_FIELD.has_default_value = false
GCCHATSEND_RESULT_FIELD.default_value = 0
GCCHATSEND_RESULT_FIELD.type = 5
GCCHATSEND_RESULT_FIELD.cpp_type = 1

GCCHATSEND_MSG_FIELD.name = "msg"
GCCHATSEND_MSG_FIELD.full_name = ".prootc.gcchatsend.msg"
GCCHATSEND_MSG_FIELD.number = 2
GCCHATSEND_MSG_FIELD.index = 1
GCCHATSEND_MSG_FIELD.label = 1
GCCHATSEND_MSG_FIELD.has_default_value = false
GCCHATSEND_MSG_FIELD.default_value = ""
GCCHATSEND_MSG_FIELD.type = 9
GCCHATSEND_MSG_FIELD.cpp_type = 9

GCCHATSEND_CDATA_FIELD.name = "cdata"
GCCHATSEND_CDATA_FIELD.full_name = ".prootc.gcchatsend.cdata"
GCCHATSEND_CDATA_FIELD.number = 3
GCCHATSEND_CDATA_FIELD.index = 2
GCCHATSEND_CDATA_FIELD.label = 1
GCCHATSEND_CDATA_FIELD.has_default_value = false
GCCHATSEND_CDATA_FIELD.default_value = nil
GCCHATSEND_CDATA_FIELD.message_type = ST_CHAT_PB_CHATDATA
GCCHATSEND_CDATA_FIELD.type = 11
GCCHATSEND_CDATA_FIELD.cpp_type = 10

GCCHATSEND.name = "gcchatsend"
GCCHATSEND.full_name = ".prootc.gcchatsend"
GCCHATSEND.nested_types = {}
GCCHATSEND.enum_types = {}
GCCHATSEND.fields = {GCCHATSEND_RESULT_FIELD, GCCHATSEND_MSG_FIELD, GCCHATSEND_CDATA_FIELD}
GCCHATSEND.is_extendable = false
GCCHATSEND.extensions = {}
GCCHATRECV_RESULT_FIELD.name = "result"
GCCHATRECV_RESULT_FIELD.full_name = ".prootc.gcchatrecv.result"
GCCHATRECV_RESULT_FIELD.number = 1
GCCHATRECV_RESULT_FIELD.index = 0
GCCHATRECV_RESULT_FIELD.label = 1
GCCHATRECV_RESULT_FIELD.has_default_value = false
GCCHATRECV_RESULT_FIELD.default_value = 0
GCCHATRECV_RESULT_FIELD.type = 5
GCCHATRECV_RESULT_FIELD.cpp_type = 1

GCCHATRECV_PCORDERID_FIELD.name = "pcorderid"
GCCHATRECV_PCORDERID_FIELD.full_name = ".prootc.gcchatrecv.pcorderid"
GCCHATRECV_PCORDERID_FIELD.number = 2
GCCHATRECV_PCORDERID_FIELD.index = 1
GCCHATRECV_PCORDERID_FIELD.label = 1
GCCHATRECV_PCORDERID_FIELD.has_default_value = false
GCCHATRECV_PCORDERID_FIELD.default_value = ""
GCCHATRECV_PCORDERID_FIELD.type = 9
GCCHATRECV_PCORDERID_FIELD.cpp_type = 9

GCCHATRECV_CDATA_FIELD.name = "cdata"
GCCHATRECV_CDATA_FIELD.full_name = ".prootc.gcchatrecv.cdata"
GCCHATRECV_CDATA_FIELD.number = 3
GCCHATRECV_CDATA_FIELD.index = 2
GCCHATRECV_CDATA_FIELD.label = 1
GCCHATRECV_CDATA_FIELD.has_default_value = false
GCCHATRECV_CDATA_FIELD.default_value = nil
GCCHATRECV_CDATA_FIELD.message_type = ST_CHAT_PB_CHATDATA
GCCHATRECV_CDATA_FIELD.type = 11
GCCHATRECV_CDATA_FIELD.cpp_type = 10

GCCHATRECV_MSG_FIELD.name = "msg"
GCCHATRECV_MSG_FIELD.full_name = ".prootc.gcchatrecv.msg"
GCCHATRECV_MSG_FIELD.number = 4
GCCHATRECV_MSG_FIELD.index = 3
GCCHATRECV_MSG_FIELD.label = 1
GCCHATRECV_MSG_FIELD.has_default_value = false
GCCHATRECV_MSG_FIELD.default_value = ""
GCCHATRECV_MSG_FIELD.type = 9
GCCHATRECV_MSG_FIELD.cpp_type = 9

GCCHATRECV_TARGET_FIELD.name = "target"
GCCHATRECV_TARGET_FIELD.full_name = ".prootc.gcchatrecv.target"
GCCHATRECV_TARGET_FIELD.number = 5
GCCHATRECV_TARGET_FIELD.index = 4
GCCHATRECV_TARGET_FIELD.label = 1
GCCHATRECV_TARGET_FIELD.has_default_value = false
GCCHATRECV_TARGET_FIELD.default_value = ""
GCCHATRECV_TARGET_FIELD.type = 9
GCCHATRECV_TARGET_FIELD.cpp_type = 9

GCCHATRECV.name = "gcchatrecv"
GCCHATRECV.full_name = ".prootc.gcchatrecv"
GCCHATRECV.nested_types = {}
GCCHATRECV.enum_types = {}
GCCHATRECV.fields = {GCCHATRECV_RESULT_FIELD, GCCHATRECV_PCORDERID_FIELD, GCCHATRECV_CDATA_FIELD, GCCHATRECV_MSG_FIELD, GCCHATRECV_TARGET_FIELD}
GCCHATRECV.is_extendable = false
GCCHATRECV.extensions = {}
GCCHATUPDATE_RESULT_FIELD.name = "result"
GCCHATUPDATE_RESULT_FIELD.full_name = ".prootc.gcchatupdate.result"
GCCHATUPDATE_RESULT_FIELD.number = 1
GCCHATUPDATE_RESULT_FIELD.index = 0
GCCHATUPDATE_RESULT_FIELD.label = 1
GCCHATUPDATE_RESULT_FIELD.has_default_value = false
GCCHATUPDATE_RESULT_FIELD.default_value = 0
GCCHATUPDATE_RESULT_FIELD.type = 5
GCCHATUPDATE_RESULT_FIELD.cpp_type = 1

GCCHATUPDATE_MSG_FIELD.name = "msg"
GCCHATUPDATE_MSG_FIELD.full_name = ".prootc.gcchatupdate.msg"
GCCHATUPDATE_MSG_FIELD.number = 2
GCCHATUPDATE_MSG_FIELD.index = 1
GCCHATUPDATE_MSG_FIELD.label = 1
GCCHATUPDATE_MSG_FIELD.has_default_value = false
GCCHATUPDATE_MSG_FIELD.default_value = ""
GCCHATUPDATE_MSG_FIELD.type = 9
GCCHATUPDATE_MSG_FIELD.cpp_type = 9

GCCHATUPDATE_PCORDERID_FIELD.name = "pcorderid"
GCCHATUPDATE_PCORDERID_FIELD.full_name = ".prootc.gcchatupdate.pcorderid"
GCCHATUPDATE_PCORDERID_FIELD.number = 3
GCCHATUPDATE_PCORDERID_FIELD.index = 2
GCCHATUPDATE_PCORDERID_FIELD.label = 1
GCCHATUPDATE_PCORDERID_FIELD.has_default_value = false
GCCHATUPDATE_PCORDERID_FIELD.default_value = ""
GCCHATUPDATE_PCORDERID_FIELD.type = 9
GCCHATUPDATE_PCORDERID_FIELD.cpp_type = 9

GCCHATUPDATE_STATE_FIELD.name = "state"
GCCHATUPDATE_STATE_FIELD.full_name = ".prootc.gcchatupdate.state"
GCCHATUPDATE_STATE_FIELD.number = 4
GCCHATUPDATE_STATE_FIELD.index = 3
GCCHATUPDATE_STATE_FIELD.label = 1
GCCHATUPDATE_STATE_FIELD.has_default_value = false
GCCHATUPDATE_STATE_FIELD.default_value = 0
GCCHATUPDATE_STATE_FIELD.type = 5
GCCHATUPDATE_STATE_FIELD.cpp_type = 1

GCCHATUPDATE_TARGET_FIELD.name = "target"
GCCHATUPDATE_TARGET_FIELD.full_name = ".prootc.gcchatupdate.target"
GCCHATUPDATE_TARGET_FIELD.number = 5
GCCHATUPDATE_TARGET_FIELD.index = 4
GCCHATUPDATE_TARGET_FIELD.label = 1
GCCHATUPDATE_TARGET_FIELD.has_default_value = false
GCCHATUPDATE_TARGET_FIELD.default_value = ""
GCCHATUPDATE_TARGET_FIELD.type = 9
GCCHATUPDATE_TARGET_FIELD.cpp_type = 9

GCCHATUPDATE.name = "gcchatupdate"
GCCHATUPDATE.full_name = ".prootc.gcchatupdate"
GCCHATUPDATE.nested_types = {}
GCCHATUPDATE.enum_types = {}
GCCHATUPDATE.fields = {GCCHATUPDATE_RESULT_FIELD, GCCHATUPDATE_MSG_FIELD, GCCHATUPDATE_PCORDERID_FIELD, GCCHATUPDATE_STATE_FIELD, GCCHATUPDATE_TARGET_FIELD}
GCCHATUPDATE.is_extendable = false
GCCHATUPDATE.extensions = {}

cgchatcrate = protobuf.Message(CGCHATCRATE)
cgchatsend = protobuf.Message(CGCHATSEND)
cgchattimeout = protobuf.Message(CGCHATTIMEOUT)
gcchatcrate = protobuf.Message(GCCHATCRATE)
gcchatrecv = protobuf.Message(GCCHATRECV)
gcchatsend = protobuf.Message(GCCHATSEND)
gcchattimeout = protobuf.Message(GCCHATTIMEOUT)
gcchatupdate = protobuf.Message(GCCHATUPDATE)

----------nimol modify---------
MSG_CHAT_PB_CGCHATCRATE = CGCHATCRATE
MSG_CHAT_PB_CGCHATSEND = CGCHATSEND
MSG_CHAT_PB_CGCHATTIMEOUT = CGCHATTIMEOUT
MSG_CHAT_PB_GCCHATCRATE = GCCHATCRATE
MSG_CHAT_PB_GCCHATRECV = GCCHATRECV
MSG_CHAT_PB_GCCHATSEND = GCCHATSEND
MSG_CHAT_PB_GCCHATTIMEOUT = GCCHATTIMEOUT
MSG_CHAT_PB_GCCHATUPDATE = GCCHATUPDATE
