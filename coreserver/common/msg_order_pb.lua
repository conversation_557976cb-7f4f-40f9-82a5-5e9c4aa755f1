-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf"
local st_human_pb = require("st_human_pb")
local st_order_pb = require("st_order_pb")
----------nimol modify---------
local ST_ORDER_PB_APPPEALINFO = st_order_pb.ST_ORDER_PB_APPPEALINFO
local ST_ORDER_PB_CUSTOMERORDERINFO = st_order_pb.ST_ORDER_PB_CUSTOMERORDERINFO
local ST_ORDER_PB_INCOMEEXPENDITUREDETAIL = st_order_pb.ST_ORDER_PB_INCOMEEXPENDITUREDETAIL
local ST_ORDER_PB_VENDORORDERINFO = st_order_pb.ST_ORDER_PB_VENDORORDERINFO
module('msg_order_pb')


local CGHANGORDER = protobuf.Descriptor();
local CGHANGORDER_USERID_FIELD = protobuf.FieldDescriptor();
local CGHANGORDER_TYPE_FIELD = protobuf.FieldDescriptor();
local CGHANGORDER_PRICETYPE_FIELD = protobuf.FieldDescriptor();
local CGHANGORDER_PRICE_FIELD = protobuf.FieldDescriptor();
local CGHANGORDER_AMOUNT_FIELD = protobuf.FieldDescriptor();
local CGHANGORDER_MINMONEY_FIELD = protobuf.FieldDescriptor();
local CGHANGORDER_MAXMONEY_FIELD = protobuf.FieldDescriptor();
local CGHANGORDER_AUTOSWITCH_FIELD = protobuf.FieldDescriptor();
local CGHANGORDER_MESSAGE_FIELD = protobuf.FieldDescriptor();
local CGHANGORDER_FUNDPWD_FIELD = protobuf.FieldDescriptor();
local CGHANGORDER_COINID_FIELD = protobuf.FieldDescriptor();
local GCHANGORDER = protobuf.Descriptor();
local GCHANGORDER_RESULT_FIELD = protobuf.FieldDescriptor();
local GCHANGORDER_MSG_FIELD = protobuf.FieldDescriptor();
local GCHANGORDER_VOINFO_FIELD = protobuf.FieldDescriptor();
local CGMODIFYORDER = protobuf.Descriptor();
local CGMODIFYORDER_USERID_FIELD = protobuf.FieldDescriptor();
local CGMODIFYORDER_HANGID_FIELD = protobuf.FieldDescriptor();
local CGMODIFYORDER_TYPE_FIELD = protobuf.FieldDescriptor();
local CGMODIFYORDER_PRICETYPE_FIELD = protobuf.FieldDescriptor();
local CGMODIFYORDER_PRICE_FIELD = protobuf.FieldDescriptor();
local CGMODIFYORDER_AMOUNT_FIELD = protobuf.FieldDescriptor();
local CGMODIFYORDER_MINMONEY_FIELD = protobuf.FieldDescriptor();
local CGMODIFYORDER_MAXMONEY_FIELD = protobuf.FieldDescriptor();
local CGMODIFYORDER_AUTOSWITCH_FIELD = protobuf.FieldDescriptor();
local CGMODIFYORDER_MESSAGE_FIELD = protobuf.FieldDescriptor();
local CGMODIFYORDER_ENABLESTATUS_FIELD = protobuf.FieldDescriptor();
local GCMODIFYORDER = protobuf.Descriptor();
local GCMODIFYORDER_RESULT_FIELD = protobuf.FieldDescriptor();
local GCMODIFYORDER_MSG_FIELD = protobuf.FieldDescriptor();
local GCMODIFYORDER_VOINFO_FIELD = protobuf.FieldDescriptor();
local CGDISABLEORDER = protobuf.Descriptor();
local CGDISABLEORDER_USERID_FIELD = protobuf.FieldDescriptor();
local CGDISABLEORDER_HANGID_FIELD = protobuf.FieldDescriptor();
local GCDISABLEORDER = protobuf.Descriptor();
local GCDISABLEORDER_RESULT_FIELD = protobuf.FieldDescriptor();
local GCDISABLEORDER_MSG_FIELD = protobuf.FieldDescriptor();
local CGORDERLIST = protobuf.Descriptor();
local CGORDERLIST_USERID_FIELD = protobuf.FieldDescriptor();
local CGORDERLIST_ORDERTYPE_FIELD = protobuf.FieldDescriptor();
local CGORDERLIST_PAGENUM_FIELD = protobuf.FieldDescriptor();
local CGORDERLIST_PAGESIZE_FIELD = protobuf.FieldDescriptor();
local GCORDERLIST = protobuf.Descriptor();
local GCORDERLIST_RESULT_FIELD = protobuf.FieldDescriptor();
local GCORDERLIST_MSG_FIELD = protobuf.FieldDescriptor();
local GCORDERLIST_VOLIST_FIELD = protobuf.FieldDescriptor();
local GCORDERLIST_DEALNUM_FIELD = protobuf.FieldDescriptor();
local GCORDERLIST_ORDERTYPE_FIELD = protobuf.FieldDescriptor();
local GCORDERLIST_PAGENUM_FIELD = protobuf.FieldDescriptor();
local GCORDERLIST_PAGESIZE_FIELD = protobuf.FieldDescriptor();
local CGQUICKDEAL = protobuf.Descriptor();
local CGQUICKDEAL_USERID_FIELD = protobuf.FieldDescriptor();
local CGQUICKDEAL_HANGID_FIELD = protobuf.FieldDescriptor();
local CGQUICKDEAL_DEALTYPE_FIELD = protobuf.FieldDescriptor();
local CGQUICKDEAL_AMOUNT_FIELD = protobuf.FieldDescriptor();
local CGQUICKDEAL_FUNDPWD_FIELD = protobuf.FieldDescriptor();
local CGQUICKDEAL_MONEYTYPE_FIELD = protobuf.FieldDescriptor();
local CGQUICKDEAL_PAYTYPE_FIELD = protobuf.FieldDescriptor();
local CGQUICKDEAL_CIONID_FIELD = protobuf.FieldDescriptor();
local GCQUICKDEAL = protobuf.Descriptor();
local GCQUICKDEAL_RESULT_FIELD = protobuf.FieldDescriptor();
local GCQUICKDEAL_MSG_FIELD = protobuf.FieldDescriptor();
local GCQUICKDEAL_COINFO_FIELD = protobuf.FieldDescriptor();
local CGDEAL = protobuf.Descriptor();
local CGDEAL_USERID_FIELD = protobuf.FieldDescriptor();
local CGDEAL_HANGID_FIELD = protobuf.FieldDescriptor();
local CGDEAL_DEALTYPE_FIELD = protobuf.FieldDescriptor();
local CGDEAL_AMOUNT_FIELD = protobuf.FieldDescriptor();
local CGDEAL_FUNDPWD_FIELD = protobuf.FieldDescriptor();
local CGDEAL_PRICE_FIELD = protobuf.FieldDescriptor();
local CGDEAL_MONEYTYPE_FIELD = protobuf.FieldDescriptor();
local CGDEAL_PAYTYPE_FIELD = protobuf.FieldDescriptor();
local GCDEAL = protobuf.Descriptor();
local GCDEAL_RESULT_FIELD = protobuf.FieldDescriptor();
local GCDEAL_MSG_FIELD = protobuf.FieldDescriptor();
local GCDEAL_COINFO_FIELD = protobuf.FieldDescriptor();
local CGPAID = protobuf.Descriptor();
local CGPAID_USERID_FIELD = protobuf.FieldDescriptor();
local CGPAID_DEALID_FIELD = protobuf.FieldDescriptor();
local CGPAID_FUNDPWD_FIELD = protobuf.FieldDescriptor();
local CGPAID_PROOFURL_FIELD = protobuf.FieldDescriptor();
local GCPAID = protobuf.Descriptor();
local GCPAID_RESULT_FIELD = protobuf.FieldDescriptor();
local GCPAID_MSG_FIELD = protobuf.FieldDescriptor();
local GCPAID_COINFO_FIELD = protobuf.FieldDescriptor();
local CGPASS = protobuf.Descriptor();
local CGPASS_USERID_FIELD = protobuf.FieldDescriptor();
local CGPASS_DEALID_FIELD = protobuf.FieldDescriptor();
local CGPASS_FUNDPWD_FIELD = protobuf.FieldDescriptor();
local GCPASS = protobuf.Descriptor();
local GCPASS_RESULT_FIELD = protobuf.FieldDescriptor();
local GCPASS_MSG_FIELD = protobuf.FieldDescriptor();
local GCPASS_COINFO_FIELD = protobuf.FieldDescriptor();
local CGCANCELLEFTCOUNT = protobuf.Descriptor();
local CGCANCELLEFTCOUNT_USERID_FIELD = protobuf.FieldDescriptor();
local GCCANCELLEFTCOUNT = protobuf.Descriptor();
local GCCANCELLEFTCOUNT_RESULT_FIELD = protobuf.FieldDescriptor();
local GCCANCELLEFTCOUNT_MSG_FIELD = protobuf.FieldDescriptor();
local GCCANCELLEFTCOUNT_LEFTCOUNT_FIELD = protobuf.FieldDescriptor();
local CGCANCEL = protobuf.Descriptor();
local CGCANCEL_USERID_FIELD = protobuf.FieldDescriptor();
local CGCANCEL_DEALID_FIELD = protobuf.FieldDescriptor();
local GCCANCEL = protobuf.Descriptor();
local GCCANCEL_RESULT_FIELD = protobuf.FieldDescriptor();
local GCCANCEL_MSG_FIELD = protobuf.FieldDescriptor();
local GCCANCEL_COINFO_FIELD = protobuf.FieldDescriptor();
local CGAPPEAL = protobuf.Descriptor();
local CGAPPEAL_USERID_FIELD = protobuf.FieldDescriptor();
local CGAPPEAL_DEALID_FIELD = protobuf.FieldDescriptor();
local CGAPPEAL_REASON_FIELD = protobuf.FieldDescriptor();
local CGAPPEAL_DESCRIPTION_FIELD = protobuf.FieldDescriptor();
local CGAPPEAL_PROOFURL_FIELD = protobuf.FieldDescriptor();
local GCAPPEAL = protobuf.Descriptor();
local GCAPPEAL_RESULT_FIELD = protobuf.FieldDescriptor();
local GCAPPEAL_MSG_FIELD = protobuf.FieldDescriptor();
local GCAPPEAL_INFO_FIELD = protobuf.FieldDescriptor();
local GCAPPEAL_COINFO_FIELD = protobuf.FieldDescriptor();
local CGAPPEALREPLY = protobuf.Descriptor();
local CGAPPEALREPLY_USERID_FIELD = protobuf.FieldDescriptor();
local CGAPPEALREPLY_DEALID_FIELD = protobuf.FieldDescriptor();
local CGAPPEALREPLY_REASON_FIELD = protobuf.FieldDescriptor();
local CGAPPEALREPLY_DESCRIPTION_FIELD = protobuf.FieldDescriptor();
local CGAPPEALREPLY_PROOFURL_FIELD = protobuf.FieldDescriptor();
local GCAPPEALREPLY = protobuf.Descriptor();
local GCAPPEALREPLY_RESULT_FIELD = protobuf.FieldDescriptor();
local GCAPPEALREPLY_MSG_FIELD = protobuf.FieldDescriptor();
local GCAPPEALREPLY_COINFO_FIELD = protobuf.FieldDescriptor();
local CGAPPEALDETAIL = protobuf.Descriptor();
local CGAPPEALDETAIL_USERID_FIELD = protobuf.FieldDescriptor();
local CGAPPEALDETAIL_DEALID_FIELD = protobuf.FieldDescriptor();
local GCAPPEALDETAIL = protobuf.Descriptor();
local GCAPPEALDETAIL_RESULT_FIELD = protobuf.FieldDescriptor();
local GCAPPEALDETAIL_MSG_FIELD = protobuf.FieldDescriptor();
local GCAPPEALDETAIL_COINFO_FIELD = protobuf.FieldDescriptor();
local CGAPPEALLIST = protobuf.Descriptor();
local CGAPPEALLIST_USERID_FIELD = protobuf.FieldDescriptor();
local GCAPPEALLIST = protobuf.Descriptor();
local CGCUSTOMERORDERLIST = protobuf.Descriptor();
local CGCUSTOMERORDERLIST_USERID_FIELD = protobuf.FieldDescriptor();
local CGCUSTOMERORDERLIST_ORDERTYPE_FIELD = protobuf.FieldDescriptor();
local CGCUSTOMERORDERLIST_PAGENUM_FIELD = protobuf.FieldDescriptor();
local CGCUSTOMERORDERLIST_PAGESIZE_FIELD = protobuf.FieldDescriptor();
local CGCUSTOMERORDERLIST_COINID_FIELD = protobuf.FieldDescriptor();
local GCCUSTOMERORDERLIST = protobuf.Descriptor();
local GCCUSTOMERORDERLIST_RESULT_FIELD = protobuf.FieldDescriptor();
local GCCUSTOMERORDERLIST_MSG_FIELD = protobuf.FieldDescriptor();
local GCCUSTOMERORDERLIST_VOLIST_FIELD = protobuf.FieldDescriptor();
local GCCUSTOMERORDERLIST_ORDERTYPE_FIELD = protobuf.FieldDescriptor();
local GCCUSTOMERORDERLIST_PAGENUM_FIELD = protobuf.FieldDescriptor();
local GCCUSTOMERORDERLIST_PAGESIZE_FIELD = protobuf.FieldDescriptor();
local GCCUSTOMERORDERLIST_COINID_FIELD = protobuf.FieldDescriptor();
local CGINCOMEEXPENDITUREDETAIL = protobuf.Descriptor();
local CGINCOMEEXPENDITUREDETAIL_USERID_FIELD = protobuf.FieldDescriptor();
local CGINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD = protobuf.FieldDescriptor();
local CGINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD = protobuf.FieldDescriptor();
local CGINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD = protobuf.FieldDescriptor();
local CGINCOMEEXPENDITUREDETAIL_COINID_FIELD = protobuf.FieldDescriptor();
local GCINCOMEEXPENDITUREDETAIL = protobuf.Descriptor();
local GCINCOMEEXPENDITUREDETAIL_RESULT_FIELD = protobuf.FieldDescriptor();
local GCINCOMEEXPENDITUREDETAIL_MSG_FIELD = protobuf.FieldDescriptor();
local GCINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD = protobuf.FieldDescriptor();
local GCINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD = protobuf.FieldDescriptor();
local GCINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD = protobuf.FieldDescriptor();
local GCINCOMEEXPENDITUREDETAIL_PARAMETER1_FIELD = protobuf.FieldDescriptor();
local GCINCOMEEXPENDITUREDETAIL_PARAMETER2_FIELD = protobuf.FieldDescriptor();
local GCINCOMEEXPENDITUREDETAIL_DETAILLIST_FIELD = protobuf.FieldDescriptor();
local GCINCOMEEXPENDITUREDETAIL_COINID_FIELD = protobuf.FieldDescriptor();
local CGORDERACTIVATION = protobuf.Descriptor();
local CGORDERACTIVATION_USERID_FIELD = protobuf.FieldDescriptor();
local CGORDERACTIVATION_DEALID_FIELD = protobuf.FieldDescriptor();
local CGORDERACTIVATION_FUNDPWD_FIELD = protobuf.FieldDescriptor();
local GCORDERACTIVATION = protobuf.Descriptor();
local GCORDERACTIVATION_RESULT_FIELD = protobuf.FieldDescriptor();
local GCORDERACTIVATION_MSG_FIELD = protobuf.FieldDescriptor();
local GCORDERACTIVATION_COINFO_FIELD = protobuf.FieldDescriptor();

CGHANGORDER_USERID_FIELD.name = "userid"
CGHANGORDER_USERID_FIELD.full_name = ".prootc.cghangorder.userid"
CGHANGORDER_USERID_FIELD.number = 1
CGHANGORDER_USERID_FIELD.index = 0
CGHANGORDER_USERID_FIELD.label = 1
CGHANGORDER_USERID_FIELD.has_default_value = false
CGHANGORDER_USERID_FIELD.default_value = 0
CGHANGORDER_USERID_FIELD.type = 5
CGHANGORDER_USERID_FIELD.cpp_type = 1

CGHANGORDER_TYPE_FIELD.name = "type"
CGHANGORDER_TYPE_FIELD.full_name = ".prootc.cghangorder.type"
CGHANGORDER_TYPE_FIELD.number = 2
CGHANGORDER_TYPE_FIELD.index = 1
CGHANGORDER_TYPE_FIELD.label = 1
CGHANGORDER_TYPE_FIELD.has_default_value = false
CGHANGORDER_TYPE_FIELD.default_value = 0
CGHANGORDER_TYPE_FIELD.type = 5
CGHANGORDER_TYPE_FIELD.cpp_type = 1

CGHANGORDER_PRICETYPE_FIELD.name = "pricetype"
CGHANGORDER_PRICETYPE_FIELD.full_name = ".prootc.cghangorder.pricetype"
CGHANGORDER_PRICETYPE_FIELD.number = 3
CGHANGORDER_PRICETYPE_FIELD.index = 2
CGHANGORDER_PRICETYPE_FIELD.label = 1
CGHANGORDER_PRICETYPE_FIELD.has_default_value = false
CGHANGORDER_PRICETYPE_FIELD.default_value = 0
CGHANGORDER_PRICETYPE_FIELD.type = 5
CGHANGORDER_PRICETYPE_FIELD.cpp_type = 1

CGHANGORDER_PRICE_FIELD.name = "price"
CGHANGORDER_PRICE_FIELD.full_name = ".prootc.cghangorder.price"
CGHANGORDER_PRICE_FIELD.number = 4
CGHANGORDER_PRICE_FIELD.index = 3
CGHANGORDER_PRICE_FIELD.label = 1
CGHANGORDER_PRICE_FIELD.has_default_value = false
CGHANGORDER_PRICE_FIELD.default_value = ""
CGHANGORDER_PRICE_FIELD.type = 9
CGHANGORDER_PRICE_FIELD.cpp_type = 9

CGHANGORDER_AMOUNT_FIELD.name = "amount"
CGHANGORDER_AMOUNT_FIELD.full_name = ".prootc.cghangorder.amount"
CGHANGORDER_AMOUNT_FIELD.number = 5
CGHANGORDER_AMOUNT_FIELD.index = 4
CGHANGORDER_AMOUNT_FIELD.label = 1
CGHANGORDER_AMOUNT_FIELD.has_default_value = false
CGHANGORDER_AMOUNT_FIELD.default_value = ""
CGHANGORDER_AMOUNT_FIELD.type = 9
CGHANGORDER_AMOUNT_FIELD.cpp_type = 9

CGHANGORDER_MINMONEY_FIELD.name = "minmoney"
CGHANGORDER_MINMONEY_FIELD.full_name = ".prootc.cghangorder.minmoney"
CGHANGORDER_MINMONEY_FIELD.number = 6
CGHANGORDER_MINMONEY_FIELD.index = 5
CGHANGORDER_MINMONEY_FIELD.label = 1
CGHANGORDER_MINMONEY_FIELD.has_default_value = false
CGHANGORDER_MINMONEY_FIELD.default_value = ""
CGHANGORDER_MINMONEY_FIELD.type = 9
CGHANGORDER_MINMONEY_FIELD.cpp_type = 9

CGHANGORDER_MAXMONEY_FIELD.name = "maxmoney"
CGHANGORDER_MAXMONEY_FIELD.full_name = ".prootc.cghangorder.maxmoney"
CGHANGORDER_MAXMONEY_FIELD.number = 7
CGHANGORDER_MAXMONEY_FIELD.index = 6
CGHANGORDER_MAXMONEY_FIELD.label = 1
CGHANGORDER_MAXMONEY_FIELD.has_default_value = false
CGHANGORDER_MAXMONEY_FIELD.default_value = ""
CGHANGORDER_MAXMONEY_FIELD.type = 9
CGHANGORDER_MAXMONEY_FIELD.cpp_type = 9

CGHANGORDER_AUTOSWITCH_FIELD.name = "autoswitch"
CGHANGORDER_AUTOSWITCH_FIELD.full_name = ".prootc.cghangorder.autoswitch"
CGHANGORDER_AUTOSWITCH_FIELD.number = 8
CGHANGORDER_AUTOSWITCH_FIELD.index = 7
CGHANGORDER_AUTOSWITCH_FIELD.label = 1
CGHANGORDER_AUTOSWITCH_FIELD.has_default_value = false
CGHANGORDER_AUTOSWITCH_FIELD.default_value = 0
CGHANGORDER_AUTOSWITCH_FIELD.type = 5
CGHANGORDER_AUTOSWITCH_FIELD.cpp_type = 1

CGHANGORDER_MESSAGE_FIELD.name = "message"
CGHANGORDER_MESSAGE_FIELD.full_name = ".prootc.cghangorder.message"
CGHANGORDER_MESSAGE_FIELD.number = 9
CGHANGORDER_MESSAGE_FIELD.index = 8
CGHANGORDER_MESSAGE_FIELD.label = 1
CGHANGORDER_MESSAGE_FIELD.has_default_value = false
CGHANGORDER_MESSAGE_FIELD.default_value = ""
CGHANGORDER_MESSAGE_FIELD.type = 9
CGHANGORDER_MESSAGE_FIELD.cpp_type = 9

CGHANGORDER_FUNDPWD_FIELD.name = "fundpwd"
CGHANGORDER_FUNDPWD_FIELD.full_name = ".prootc.cghangorder.fundpwd"
CGHANGORDER_FUNDPWD_FIELD.number = 10
CGHANGORDER_FUNDPWD_FIELD.index = 9
CGHANGORDER_FUNDPWD_FIELD.label = 1
CGHANGORDER_FUNDPWD_FIELD.has_default_value = false
CGHANGORDER_FUNDPWD_FIELD.default_value = ""
CGHANGORDER_FUNDPWD_FIELD.type = 9
CGHANGORDER_FUNDPWD_FIELD.cpp_type = 9

CGHANGORDER_COINID_FIELD.name = "coinid"
CGHANGORDER_COINID_FIELD.full_name = ".prootc.cghangorder.coinid"
CGHANGORDER_COINID_FIELD.number = 11
CGHANGORDER_COINID_FIELD.index = 10
CGHANGORDER_COINID_FIELD.label = 1
CGHANGORDER_COINID_FIELD.has_default_value = false
CGHANGORDER_COINID_FIELD.default_value = 0
CGHANGORDER_COINID_FIELD.type = 5
CGHANGORDER_COINID_FIELD.cpp_type = 1

CGHANGORDER.name = "cghangorder"
CGHANGORDER.full_name = ".prootc.cghangorder"
CGHANGORDER.nested_types = {}
CGHANGORDER.enum_types = {}
CGHANGORDER.fields = {CGHANGORDER_USERID_FIELD, CGHANGORDER_TYPE_FIELD, CGHANGORDER_PRICETYPE_FIELD, CGHANGORDER_PRICE_FIELD, CGHANGORDER_AMOUNT_FIELD, CGHANGORDER_MINMONEY_FIELD, CGHANGORDER_MAXMONEY_FIELD, CGHANGORDER_AUTOSWITCH_FIELD, CGHANGORDER_MESSAGE_FIELD, CGHANGORDER_FUNDPWD_FIELD, CGHANGORDER_COINID_FIELD}
CGHANGORDER.is_extendable = false
CGHANGORDER.extensions = {}
GCHANGORDER_RESULT_FIELD.name = "result"
GCHANGORDER_RESULT_FIELD.full_name = ".prootc.gchangorder.result"
GCHANGORDER_RESULT_FIELD.number = 1
GCHANGORDER_RESULT_FIELD.index = 0
GCHANGORDER_RESULT_FIELD.label = 1
GCHANGORDER_RESULT_FIELD.has_default_value = false
GCHANGORDER_RESULT_FIELD.default_value = 0
GCHANGORDER_RESULT_FIELD.type = 5
GCHANGORDER_RESULT_FIELD.cpp_type = 1

GCHANGORDER_MSG_FIELD.name = "msg"
GCHANGORDER_MSG_FIELD.full_name = ".prootc.gchangorder.msg"
GCHANGORDER_MSG_FIELD.number = 2
GCHANGORDER_MSG_FIELD.index = 1
GCHANGORDER_MSG_FIELD.label = 1
GCHANGORDER_MSG_FIELD.has_default_value = false
GCHANGORDER_MSG_FIELD.default_value = ""
GCHANGORDER_MSG_FIELD.type = 9
GCHANGORDER_MSG_FIELD.cpp_type = 9

GCHANGORDER_VOINFO_FIELD.name = "voInfo"
GCHANGORDER_VOINFO_FIELD.full_name = ".prootc.gchangorder.voInfo"
GCHANGORDER_VOINFO_FIELD.number = 3
GCHANGORDER_VOINFO_FIELD.index = 2
GCHANGORDER_VOINFO_FIELD.label = 1
GCHANGORDER_VOINFO_FIELD.has_default_value = false
GCHANGORDER_VOINFO_FIELD.default_value = nil
GCHANGORDER_VOINFO_FIELD.message_type = ST_ORDER_PB_VENDORORDERINFO
GCHANGORDER_VOINFO_FIELD.type = 11
GCHANGORDER_VOINFO_FIELD.cpp_type = 10

GCHANGORDER.name = "gchangorder"
GCHANGORDER.full_name = ".prootc.gchangorder"
GCHANGORDER.nested_types = {}
GCHANGORDER.enum_types = {}
GCHANGORDER.fields = {GCHANGORDER_RESULT_FIELD, GCHANGORDER_MSG_FIELD, GCHANGORDER_VOINFO_FIELD}
GCHANGORDER.is_extendable = false
GCHANGORDER.extensions = {}
CGMODIFYORDER_USERID_FIELD.name = "userid"
CGMODIFYORDER_USERID_FIELD.full_name = ".prootc.cgmodifyorder.userid"
CGMODIFYORDER_USERID_FIELD.number = 1
CGMODIFYORDER_USERID_FIELD.index = 0
CGMODIFYORDER_USERID_FIELD.label = 1
CGMODIFYORDER_USERID_FIELD.has_default_value = false
CGMODIFYORDER_USERID_FIELD.default_value = 0
CGMODIFYORDER_USERID_FIELD.type = 5
CGMODIFYORDER_USERID_FIELD.cpp_type = 1

CGMODIFYORDER_HANGID_FIELD.name = "hangid"
CGMODIFYORDER_HANGID_FIELD.full_name = ".prootc.cgmodifyorder.hangid"
CGMODIFYORDER_HANGID_FIELD.number = 2
CGMODIFYORDER_HANGID_FIELD.index = 1
CGMODIFYORDER_HANGID_FIELD.label = 1
CGMODIFYORDER_HANGID_FIELD.has_default_value = false
CGMODIFYORDER_HANGID_FIELD.default_value = 0
CGMODIFYORDER_HANGID_FIELD.type = 5
CGMODIFYORDER_HANGID_FIELD.cpp_type = 1

CGMODIFYORDER_TYPE_FIELD.name = "type"
CGMODIFYORDER_TYPE_FIELD.full_name = ".prootc.cgmodifyorder.type"
CGMODIFYORDER_TYPE_FIELD.number = 3
CGMODIFYORDER_TYPE_FIELD.index = 2
CGMODIFYORDER_TYPE_FIELD.label = 1
CGMODIFYORDER_TYPE_FIELD.has_default_value = false
CGMODIFYORDER_TYPE_FIELD.default_value = 0
CGMODIFYORDER_TYPE_FIELD.type = 5
CGMODIFYORDER_TYPE_FIELD.cpp_type = 1

CGMODIFYORDER_PRICETYPE_FIELD.name = "pricetype"
CGMODIFYORDER_PRICETYPE_FIELD.full_name = ".prootc.cgmodifyorder.pricetype"
CGMODIFYORDER_PRICETYPE_FIELD.number = 4
CGMODIFYORDER_PRICETYPE_FIELD.index = 3
CGMODIFYORDER_PRICETYPE_FIELD.label = 1
CGMODIFYORDER_PRICETYPE_FIELD.has_default_value = false
CGMODIFYORDER_PRICETYPE_FIELD.default_value = 0
CGMODIFYORDER_PRICETYPE_FIELD.type = 5
CGMODIFYORDER_PRICETYPE_FIELD.cpp_type = 1

CGMODIFYORDER_PRICE_FIELD.name = "price"
CGMODIFYORDER_PRICE_FIELD.full_name = ".prootc.cgmodifyorder.price"
CGMODIFYORDER_PRICE_FIELD.number = 5
CGMODIFYORDER_PRICE_FIELD.index = 4
CGMODIFYORDER_PRICE_FIELD.label = 1
CGMODIFYORDER_PRICE_FIELD.has_default_value = false
CGMODIFYORDER_PRICE_FIELD.default_value = ""
CGMODIFYORDER_PRICE_FIELD.type = 9
CGMODIFYORDER_PRICE_FIELD.cpp_type = 9

CGMODIFYORDER_AMOUNT_FIELD.name = "amount"
CGMODIFYORDER_AMOUNT_FIELD.full_name = ".prootc.cgmodifyorder.amount"
CGMODIFYORDER_AMOUNT_FIELD.number = 6
CGMODIFYORDER_AMOUNT_FIELD.index = 5
CGMODIFYORDER_AMOUNT_FIELD.label = 1
CGMODIFYORDER_AMOUNT_FIELD.has_default_value = false
CGMODIFYORDER_AMOUNT_FIELD.default_value = ""
CGMODIFYORDER_AMOUNT_FIELD.type = 9
CGMODIFYORDER_AMOUNT_FIELD.cpp_type = 9

CGMODIFYORDER_MINMONEY_FIELD.name = "minmoney"
CGMODIFYORDER_MINMONEY_FIELD.full_name = ".prootc.cgmodifyorder.minmoney"
CGMODIFYORDER_MINMONEY_FIELD.number = 7
CGMODIFYORDER_MINMONEY_FIELD.index = 6
CGMODIFYORDER_MINMONEY_FIELD.label = 1
CGMODIFYORDER_MINMONEY_FIELD.has_default_value = false
CGMODIFYORDER_MINMONEY_FIELD.default_value = ""
CGMODIFYORDER_MINMONEY_FIELD.type = 9
CGMODIFYORDER_MINMONEY_FIELD.cpp_type = 9

CGMODIFYORDER_MAXMONEY_FIELD.name = "maxmoney"
CGMODIFYORDER_MAXMONEY_FIELD.full_name = ".prootc.cgmodifyorder.maxmoney"
CGMODIFYORDER_MAXMONEY_FIELD.number = 8
CGMODIFYORDER_MAXMONEY_FIELD.index = 7
CGMODIFYORDER_MAXMONEY_FIELD.label = 1
CGMODIFYORDER_MAXMONEY_FIELD.has_default_value = false
CGMODIFYORDER_MAXMONEY_FIELD.default_value = ""
CGMODIFYORDER_MAXMONEY_FIELD.type = 9
CGMODIFYORDER_MAXMONEY_FIELD.cpp_type = 9

CGMODIFYORDER_AUTOSWITCH_FIELD.name = "autoswitch"
CGMODIFYORDER_AUTOSWITCH_FIELD.full_name = ".prootc.cgmodifyorder.autoswitch"
CGMODIFYORDER_AUTOSWITCH_FIELD.number = 9
CGMODIFYORDER_AUTOSWITCH_FIELD.index = 8
CGMODIFYORDER_AUTOSWITCH_FIELD.label = 1
CGMODIFYORDER_AUTOSWITCH_FIELD.has_default_value = false
CGMODIFYORDER_AUTOSWITCH_FIELD.default_value = 0
CGMODIFYORDER_AUTOSWITCH_FIELD.type = 5
CGMODIFYORDER_AUTOSWITCH_FIELD.cpp_type = 1

CGMODIFYORDER_MESSAGE_FIELD.name = "message"
CGMODIFYORDER_MESSAGE_FIELD.full_name = ".prootc.cgmodifyorder.message"
CGMODIFYORDER_MESSAGE_FIELD.number = 10
CGMODIFYORDER_MESSAGE_FIELD.index = 9
CGMODIFYORDER_MESSAGE_FIELD.label = 1
CGMODIFYORDER_MESSAGE_FIELD.has_default_value = false
CGMODIFYORDER_MESSAGE_FIELD.default_value = ""
CGMODIFYORDER_MESSAGE_FIELD.type = 9
CGMODIFYORDER_MESSAGE_FIELD.cpp_type = 9

CGMODIFYORDER_ENABLESTATUS_FIELD.name = "enablestatus"
CGMODIFYORDER_ENABLESTATUS_FIELD.full_name = ".prootc.cgmodifyorder.enablestatus"
CGMODIFYORDER_ENABLESTATUS_FIELD.number = 11
CGMODIFYORDER_ENABLESTATUS_FIELD.index = 10
CGMODIFYORDER_ENABLESTATUS_FIELD.label = 1
CGMODIFYORDER_ENABLESTATUS_FIELD.has_default_value = false
CGMODIFYORDER_ENABLESTATUS_FIELD.default_value = 0
CGMODIFYORDER_ENABLESTATUS_FIELD.type = 5
CGMODIFYORDER_ENABLESTATUS_FIELD.cpp_type = 1

CGMODIFYORDER.name = "cgmodifyorder"
CGMODIFYORDER.full_name = ".prootc.cgmodifyorder"
CGMODIFYORDER.nested_types = {}
CGMODIFYORDER.enum_types = {}
CGMODIFYORDER.fields = {CGMODIFYORDER_USERID_FIELD, CGMODIFYORDER_HANGID_FIELD, CGMODIFYORDER_TYPE_FIELD, CGMODIFYORDER_PRICETYPE_FIELD, CGMODIFYORDER_PRICE_FIELD, CGMODIFYORDER_AMOUNT_FIELD, CGMODIFYORDER_MINMONEY_FIELD, CGMODIFYORDER_MAXMONEY_FIELD, CGMODIFYORDER_AUTOSWITCH_FIELD, CGMODIFYORDER_MESSAGE_FIELD, CGMODIFYORDER_ENABLESTATUS_FIELD}
CGMODIFYORDER.is_extendable = false
CGMODIFYORDER.extensions = {}
GCMODIFYORDER_RESULT_FIELD.name = "result"
GCMODIFYORDER_RESULT_FIELD.full_name = ".prootc.gcmodifyorder.result"
GCMODIFYORDER_RESULT_FIELD.number = 1
GCMODIFYORDER_RESULT_FIELD.index = 0
GCMODIFYORDER_RESULT_FIELD.label = 1
GCMODIFYORDER_RESULT_FIELD.has_default_value = false
GCMODIFYORDER_RESULT_FIELD.default_value = 0
GCMODIFYORDER_RESULT_FIELD.type = 5
GCMODIFYORDER_RESULT_FIELD.cpp_type = 1

GCMODIFYORDER_MSG_FIELD.name = "msg"
GCMODIFYORDER_MSG_FIELD.full_name = ".prootc.gcmodifyorder.msg"
GCMODIFYORDER_MSG_FIELD.number = 2
GCMODIFYORDER_MSG_FIELD.index = 1
GCMODIFYORDER_MSG_FIELD.label = 1
GCMODIFYORDER_MSG_FIELD.has_default_value = false
GCMODIFYORDER_MSG_FIELD.default_value = ""
GCMODIFYORDER_MSG_FIELD.type = 9
GCMODIFYORDER_MSG_FIELD.cpp_type = 9

GCMODIFYORDER_VOINFO_FIELD.name = "voInfo"
GCMODIFYORDER_VOINFO_FIELD.full_name = ".prootc.gcmodifyorder.voInfo"
GCMODIFYORDER_VOINFO_FIELD.number = 3
GCMODIFYORDER_VOINFO_FIELD.index = 2
GCMODIFYORDER_VOINFO_FIELD.label = 1
GCMODIFYORDER_VOINFO_FIELD.has_default_value = false
GCMODIFYORDER_VOINFO_FIELD.default_value = nil
GCMODIFYORDER_VOINFO_FIELD.message_type = ST_ORDER_PB_VENDORORDERINFO
GCMODIFYORDER_VOINFO_FIELD.type = 11
GCMODIFYORDER_VOINFO_FIELD.cpp_type = 10

GCMODIFYORDER.name = "gcmodifyorder"
GCMODIFYORDER.full_name = ".prootc.gcmodifyorder"
GCMODIFYORDER.nested_types = {}
GCMODIFYORDER.enum_types = {}
GCMODIFYORDER.fields = {GCMODIFYORDER_RESULT_FIELD, GCMODIFYORDER_MSG_FIELD, GCMODIFYORDER_VOINFO_FIELD}
GCMODIFYORDER.is_extendable = false
GCMODIFYORDER.extensions = {}
CGDISABLEORDER_USERID_FIELD.name = "userid"
CGDISABLEORDER_USERID_FIELD.full_name = ".prootc.cgdisableorder.userid"
CGDISABLEORDER_USERID_FIELD.number = 1
CGDISABLEORDER_USERID_FIELD.index = 0
CGDISABLEORDER_USERID_FIELD.label = 1
CGDISABLEORDER_USERID_FIELD.has_default_value = false
CGDISABLEORDER_USERID_FIELD.default_value = 0
CGDISABLEORDER_USERID_FIELD.type = 5
CGDISABLEORDER_USERID_FIELD.cpp_type = 1

CGDISABLEORDER_HANGID_FIELD.name = "hangid"
CGDISABLEORDER_HANGID_FIELD.full_name = ".prootc.cgdisableorder.hangid"
CGDISABLEORDER_HANGID_FIELD.number = 2
CGDISABLEORDER_HANGID_FIELD.index = 1
CGDISABLEORDER_HANGID_FIELD.label = 1
CGDISABLEORDER_HANGID_FIELD.has_default_value = false
CGDISABLEORDER_HANGID_FIELD.default_value = 0
CGDISABLEORDER_HANGID_FIELD.type = 5
CGDISABLEORDER_HANGID_FIELD.cpp_type = 1

CGDISABLEORDER.name = "cgdisableorder"
CGDISABLEORDER.full_name = ".prootc.cgdisableorder"
CGDISABLEORDER.nested_types = {}
CGDISABLEORDER.enum_types = {}
CGDISABLEORDER.fields = {CGDISABLEORDER_USERID_FIELD, CGDISABLEORDER_HANGID_FIELD}
CGDISABLEORDER.is_extendable = false
CGDISABLEORDER.extensions = {}
GCDISABLEORDER_RESULT_FIELD.name = "result"
GCDISABLEORDER_RESULT_FIELD.full_name = ".prootc.gcdisableorder.result"
GCDISABLEORDER_RESULT_FIELD.number = 1
GCDISABLEORDER_RESULT_FIELD.index = 0
GCDISABLEORDER_RESULT_FIELD.label = 1
GCDISABLEORDER_RESULT_FIELD.has_default_value = false
GCDISABLEORDER_RESULT_FIELD.default_value = 0
GCDISABLEORDER_RESULT_FIELD.type = 5
GCDISABLEORDER_RESULT_FIELD.cpp_type = 1

GCDISABLEORDER_MSG_FIELD.name = "msg"
GCDISABLEORDER_MSG_FIELD.full_name = ".prootc.gcdisableorder.msg"
GCDISABLEORDER_MSG_FIELD.number = 2
GCDISABLEORDER_MSG_FIELD.index = 1
GCDISABLEORDER_MSG_FIELD.label = 1
GCDISABLEORDER_MSG_FIELD.has_default_value = false
GCDISABLEORDER_MSG_FIELD.default_value = ""
GCDISABLEORDER_MSG_FIELD.type = 9
GCDISABLEORDER_MSG_FIELD.cpp_type = 9

GCDISABLEORDER.name = "gcdisableorder"
GCDISABLEORDER.full_name = ".prootc.gcdisableorder"
GCDISABLEORDER.nested_types = {}
GCDISABLEORDER.enum_types = {}
GCDISABLEORDER.fields = {GCDISABLEORDER_RESULT_FIELD, GCDISABLEORDER_MSG_FIELD}
GCDISABLEORDER.is_extendable = false
GCDISABLEORDER.extensions = {}
CGORDERLIST_USERID_FIELD.name = "userid"
CGORDERLIST_USERID_FIELD.full_name = ".prootc.cgorderlist.userid"
CGORDERLIST_USERID_FIELD.number = 1
CGORDERLIST_USERID_FIELD.index = 0
CGORDERLIST_USERID_FIELD.label = 1
CGORDERLIST_USERID_FIELD.has_default_value = false
CGORDERLIST_USERID_FIELD.default_value = 0
CGORDERLIST_USERID_FIELD.type = 5
CGORDERLIST_USERID_FIELD.cpp_type = 1

CGORDERLIST_ORDERTYPE_FIELD.name = "ordertype"
CGORDERLIST_ORDERTYPE_FIELD.full_name = ".prootc.cgorderlist.ordertype"
CGORDERLIST_ORDERTYPE_FIELD.number = 2
CGORDERLIST_ORDERTYPE_FIELD.index = 1
CGORDERLIST_ORDERTYPE_FIELD.label = 1
CGORDERLIST_ORDERTYPE_FIELD.has_default_value = false
CGORDERLIST_ORDERTYPE_FIELD.default_value = 0
CGORDERLIST_ORDERTYPE_FIELD.type = 5
CGORDERLIST_ORDERTYPE_FIELD.cpp_type = 1

CGORDERLIST_PAGENUM_FIELD.name = "pagenum"
CGORDERLIST_PAGENUM_FIELD.full_name = ".prootc.cgorderlist.pagenum"
CGORDERLIST_PAGENUM_FIELD.number = 3
CGORDERLIST_PAGENUM_FIELD.index = 2
CGORDERLIST_PAGENUM_FIELD.label = 1
CGORDERLIST_PAGENUM_FIELD.has_default_value = false
CGORDERLIST_PAGENUM_FIELD.default_value = 0
CGORDERLIST_PAGENUM_FIELD.type = 5
CGORDERLIST_PAGENUM_FIELD.cpp_type = 1

CGORDERLIST_PAGESIZE_FIELD.name = "pagesize"
CGORDERLIST_PAGESIZE_FIELD.full_name = ".prootc.cgorderlist.pagesize"
CGORDERLIST_PAGESIZE_FIELD.number = 4
CGORDERLIST_PAGESIZE_FIELD.index = 3
CGORDERLIST_PAGESIZE_FIELD.label = 1
CGORDERLIST_PAGESIZE_FIELD.has_default_value = false
CGORDERLIST_PAGESIZE_FIELD.default_value = 0
CGORDERLIST_PAGESIZE_FIELD.type = 5
CGORDERLIST_PAGESIZE_FIELD.cpp_type = 1

CGORDERLIST.name = "cgorderlist"
CGORDERLIST.full_name = ".prootc.cgorderlist"
CGORDERLIST.nested_types = {}
CGORDERLIST.enum_types = {}
CGORDERLIST.fields = {CGORDERLIST_USERID_FIELD, CGORDERLIST_ORDERTYPE_FIELD, CGORDERLIST_PAGENUM_FIELD, CGORDERLIST_PAGESIZE_FIELD}
CGORDERLIST.is_extendable = false
CGORDERLIST.extensions = {}
GCORDERLIST_RESULT_FIELD.name = "result"
GCORDERLIST_RESULT_FIELD.full_name = ".prootc.gcorderlist.result"
GCORDERLIST_RESULT_FIELD.number = 1
GCORDERLIST_RESULT_FIELD.index = 0
GCORDERLIST_RESULT_FIELD.label = 1
GCORDERLIST_RESULT_FIELD.has_default_value = false
GCORDERLIST_RESULT_FIELD.default_value = 0
GCORDERLIST_RESULT_FIELD.type = 5
GCORDERLIST_RESULT_FIELD.cpp_type = 1

GCORDERLIST_MSG_FIELD.name = "msg"
GCORDERLIST_MSG_FIELD.full_name = ".prootc.gcorderlist.msg"
GCORDERLIST_MSG_FIELD.number = 2
GCORDERLIST_MSG_FIELD.index = 1
GCORDERLIST_MSG_FIELD.label = 1
GCORDERLIST_MSG_FIELD.has_default_value = false
GCORDERLIST_MSG_FIELD.default_value = 0
GCORDERLIST_MSG_FIELD.type = 5
GCORDERLIST_MSG_FIELD.cpp_type = 1

GCORDERLIST_VOLIST_FIELD.name = "volist"
GCORDERLIST_VOLIST_FIELD.full_name = ".prootc.gcorderlist.volist"
GCORDERLIST_VOLIST_FIELD.number = 3
GCORDERLIST_VOLIST_FIELD.index = 2
GCORDERLIST_VOLIST_FIELD.label = 3
GCORDERLIST_VOLIST_FIELD.has_default_value = false
GCORDERLIST_VOLIST_FIELD.default_value = {}
GCORDERLIST_VOLIST_FIELD.message_type = ST_ORDER_PB_VENDORORDERINFO
GCORDERLIST_VOLIST_FIELD.type = 11
GCORDERLIST_VOLIST_FIELD.cpp_type = 10

GCORDERLIST_DEALNUM_FIELD.name = "dealnum"
GCORDERLIST_DEALNUM_FIELD.full_name = ".prootc.gcorderlist.dealnum"
GCORDERLIST_DEALNUM_FIELD.number = 4
GCORDERLIST_DEALNUM_FIELD.index = 3
GCORDERLIST_DEALNUM_FIELD.label = 3
GCORDERLIST_DEALNUM_FIELD.has_default_value = false
GCORDERLIST_DEALNUM_FIELD.default_value = {}
GCORDERLIST_DEALNUM_FIELD.type = 5
GCORDERLIST_DEALNUM_FIELD.cpp_type = 1

GCORDERLIST_ORDERTYPE_FIELD.name = "ordertype"
GCORDERLIST_ORDERTYPE_FIELD.full_name = ".prootc.gcorderlist.ordertype"
GCORDERLIST_ORDERTYPE_FIELD.number = 5
GCORDERLIST_ORDERTYPE_FIELD.index = 4
GCORDERLIST_ORDERTYPE_FIELD.label = 1
GCORDERLIST_ORDERTYPE_FIELD.has_default_value = false
GCORDERLIST_ORDERTYPE_FIELD.default_value = 0
GCORDERLIST_ORDERTYPE_FIELD.type = 5
GCORDERLIST_ORDERTYPE_FIELD.cpp_type = 1

GCORDERLIST_PAGENUM_FIELD.name = "pagenum"
GCORDERLIST_PAGENUM_FIELD.full_name = ".prootc.gcorderlist.pagenum"
GCORDERLIST_PAGENUM_FIELD.number = 6
GCORDERLIST_PAGENUM_FIELD.index = 5
GCORDERLIST_PAGENUM_FIELD.label = 1
GCORDERLIST_PAGENUM_FIELD.has_default_value = false
GCORDERLIST_PAGENUM_FIELD.default_value = 0
GCORDERLIST_PAGENUM_FIELD.type = 5
GCORDERLIST_PAGENUM_FIELD.cpp_type = 1

GCORDERLIST_PAGESIZE_FIELD.name = "pagesize"
GCORDERLIST_PAGESIZE_FIELD.full_name = ".prootc.gcorderlist.pagesize"
GCORDERLIST_PAGESIZE_FIELD.number = 7
GCORDERLIST_PAGESIZE_FIELD.index = 6
GCORDERLIST_PAGESIZE_FIELD.label = 1
GCORDERLIST_PAGESIZE_FIELD.has_default_value = false
GCORDERLIST_PAGESIZE_FIELD.default_value = 0
GCORDERLIST_PAGESIZE_FIELD.type = 5
GCORDERLIST_PAGESIZE_FIELD.cpp_type = 1

GCORDERLIST.name = "gcorderlist"
GCORDERLIST.full_name = ".prootc.gcorderlist"
GCORDERLIST.nested_types = {}
GCORDERLIST.enum_types = {}
GCORDERLIST.fields = {GCORDERLIST_RESULT_FIELD, GCORDERLIST_MSG_FIELD, GCORDERLIST_VOLIST_FIELD, GCORDERLIST_DEALNUM_FIELD, GCORDERLIST_ORDERTYPE_FIELD, GCORDERLIST_PAGENUM_FIELD, GCORDERLIST_PAGESIZE_FIELD}
GCORDERLIST.is_extendable = false
GCORDERLIST.extensions = {}
CGQUICKDEAL_USERID_FIELD.name = "userid"
CGQUICKDEAL_USERID_FIELD.full_name = ".prootc.cgquickdeal.userid"
CGQUICKDEAL_USERID_FIELD.number = 1
CGQUICKDEAL_USERID_FIELD.index = 0
CGQUICKDEAL_USERID_FIELD.label = 1
CGQUICKDEAL_USERID_FIELD.has_default_value = false
CGQUICKDEAL_USERID_FIELD.default_value = 0
CGQUICKDEAL_USERID_FIELD.type = 5
CGQUICKDEAL_USERID_FIELD.cpp_type = 1

CGQUICKDEAL_HANGID_FIELD.name = "hangid"
CGQUICKDEAL_HANGID_FIELD.full_name = ".prootc.cgquickdeal.hangid"
CGQUICKDEAL_HANGID_FIELD.number = 2
CGQUICKDEAL_HANGID_FIELD.index = 1
CGQUICKDEAL_HANGID_FIELD.label = 1
CGQUICKDEAL_HANGID_FIELD.has_default_value = false
CGQUICKDEAL_HANGID_FIELD.default_value = 0
CGQUICKDEAL_HANGID_FIELD.type = 5
CGQUICKDEAL_HANGID_FIELD.cpp_type = 1

CGQUICKDEAL_DEALTYPE_FIELD.name = "dealtype"
CGQUICKDEAL_DEALTYPE_FIELD.full_name = ".prootc.cgquickdeal.dealtype"
CGQUICKDEAL_DEALTYPE_FIELD.number = 3
CGQUICKDEAL_DEALTYPE_FIELD.index = 2
CGQUICKDEAL_DEALTYPE_FIELD.label = 1
CGQUICKDEAL_DEALTYPE_FIELD.has_default_value = false
CGQUICKDEAL_DEALTYPE_FIELD.default_value = 0
CGQUICKDEAL_DEALTYPE_FIELD.type = 5
CGQUICKDEAL_DEALTYPE_FIELD.cpp_type = 1

CGQUICKDEAL_AMOUNT_FIELD.name = "amount"
CGQUICKDEAL_AMOUNT_FIELD.full_name = ".prootc.cgquickdeal.amount"
CGQUICKDEAL_AMOUNT_FIELD.number = 4
CGQUICKDEAL_AMOUNT_FIELD.index = 3
CGQUICKDEAL_AMOUNT_FIELD.label = 1
CGQUICKDEAL_AMOUNT_FIELD.has_default_value = false
CGQUICKDEAL_AMOUNT_FIELD.default_value = ""
CGQUICKDEAL_AMOUNT_FIELD.type = 9
CGQUICKDEAL_AMOUNT_FIELD.cpp_type = 9

CGQUICKDEAL_FUNDPWD_FIELD.name = "fundPwd"
CGQUICKDEAL_FUNDPWD_FIELD.full_name = ".prootc.cgquickdeal.fundPwd"
CGQUICKDEAL_FUNDPWD_FIELD.number = 5
CGQUICKDEAL_FUNDPWD_FIELD.index = 4
CGQUICKDEAL_FUNDPWD_FIELD.label = 1
CGQUICKDEAL_FUNDPWD_FIELD.has_default_value = false
CGQUICKDEAL_FUNDPWD_FIELD.default_value = ""
CGQUICKDEAL_FUNDPWD_FIELD.type = 9
CGQUICKDEAL_FUNDPWD_FIELD.cpp_type = 9

CGQUICKDEAL_MONEYTYPE_FIELD.name = "moneytype"
CGQUICKDEAL_MONEYTYPE_FIELD.full_name = ".prootc.cgquickdeal.moneytype"
CGQUICKDEAL_MONEYTYPE_FIELD.number = 6
CGQUICKDEAL_MONEYTYPE_FIELD.index = 5
CGQUICKDEAL_MONEYTYPE_FIELD.label = 1
CGQUICKDEAL_MONEYTYPE_FIELD.has_default_value = false
CGQUICKDEAL_MONEYTYPE_FIELD.default_value = 0
CGQUICKDEAL_MONEYTYPE_FIELD.type = 5
CGQUICKDEAL_MONEYTYPE_FIELD.cpp_type = 1

CGQUICKDEAL_PAYTYPE_FIELD.name = "paytype"
CGQUICKDEAL_PAYTYPE_FIELD.full_name = ".prootc.cgquickdeal.paytype"
CGQUICKDEAL_PAYTYPE_FIELD.number = 7
CGQUICKDEAL_PAYTYPE_FIELD.index = 6
CGQUICKDEAL_PAYTYPE_FIELD.label = 1
CGQUICKDEAL_PAYTYPE_FIELD.has_default_value = false
CGQUICKDEAL_PAYTYPE_FIELD.default_value = 0
CGQUICKDEAL_PAYTYPE_FIELD.type = 5
CGQUICKDEAL_PAYTYPE_FIELD.cpp_type = 1

CGQUICKDEAL_CIONID_FIELD.name = "cionid"
CGQUICKDEAL_CIONID_FIELD.full_name = ".prootc.cgquickdeal.cionid"
CGQUICKDEAL_CIONID_FIELD.number = 8
CGQUICKDEAL_CIONID_FIELD.index = 7
CGQUICKDEAL_CIONID_FIELD.label = 1
CGQUICKDEAL_CIONID_FIELD.has_default_value = false
CGQUICKDEAL_CIONID_FIELD.default_value = 0
CGQUICKDEAL_CIONID_FIELD.type = 5
CGQUICKDEAL_CIONID_FIELD.cpp_type = 1

CGQUICKDEAL.name = "cgquickdeal"
CGQUICKDEAL.full_name = ".prootc.cgquickdeal"
CGQUICKDEAL.nested_types = {}
CGQUICKDEAL.enum_types = {}
CGQUICKDEAL.fields = {CGQUICKDEAL_USERID_FIELD, CGQUICKDEAL_HANGID_FIELD, CGQUICKDEAL_DEALTYPE_FIELD, CGQUICKDEAL_AMOUNT_FIELD, CGQUICKDEAL_FUNDPWD_FIELD, CGQUICKDEAL_MONEYTYPE_FIELD, CGQUICKDEAL_PAYTYPE_FIELD, CGQUICKDEAL_CIONID_FIELD}
CGQUICKDEAL.is_extendable = false
CGQUICKDEAL.extensions = {}
GCQUICKDEAL_RESULT_FIELD.name = "result"
GCQUICKDEAL_RESULT_FIELD.full_name = ".prootc.gcquickdeal.result"
GCQUICKDEAL_RESULT_FIELD.number = 1
GCQUICKDEAL_RESULT_FIELD.index = 0
GCQUICKDEAL_RESULT_FIELD.label = 1
GCQUICKDEAL_RESULT_FIELD.has_default_value = false
GCQUICKDEAL_RESULT_FIELD.default_value = 0
GCQUICKDEAL_RESULT_FIELD.type = 5
GCQUICKDEAL_RESULT_FIELD.cpp_type = 1

GCQUICKDEAL_MSG_FIELD.name = "msg"
GCQUICKDEAL_MSG_FIELD.full_name = ".prootc.gcquickdeal.msg"
GCQUICKDEAL_MSG_FIELD.number = 2
GCQUICKDEAL_MSG_FIELD.index = 1
GCQUICKDEAL_MSG_FIELD.label = 1
GCQUICKDEAL_MSG_FIELD.has_default_value = false
GCQUICKDEAL_MSG_FIELD.default_value = ""
GCQUICKDEAL_MSG_FIELD.type = 9
GCQUICKDEAL_MSG_FIELD.cpp_type = 9

GCQUICKDEAL_COINFO_FIELD.name = "coinfo"
GCQUICKDEAL_COINFO_FIELD.full_name = ".prootc.gcquickdeal.coinfo"
GCQUICKDEAL_COINFO_FIELD.number = 3
GCQUICKDEAL_COINFO_FIELD.index = 2
GCQUICKDEAL_COINFO_FIELD.label = 1
GCQUICKDEAL_COINFO_FIELD.has_default_value = false
GCQUICKDEAL_COINFO_FIELD.default_value = nil
GCQUICKDEAL_COINFO_FIELD.message_type = ST_ORDER_PB_CUSTOMERORDERINFO
GCQUICKDEAL_COINFO_FIELD.type = 11
GCQUICKDEAL_COINFO_FIELD.cpp_type = 10

GCQUICKDEAL.name = "gcquickdeal"
GCQUICKDEAL.full_name = ".prootc.gcquickdeal"
GCQUICKDEAL.nested_types = {}
GCQUICKDEAL.enum_types = {}
GCQUICKDEAL.fields = {GCQUICKDEAL_RESULT_FIELD, GCQUICKDEAL_MSG_FIELD, GCQUICKDEAL_COINFO_FIELD}
GCQUICKDEAL.is_extendable = false
GCQUICKDEAL.extensions = {}
CGDEAL_USERID_FIELD.name = "userid"
CGDEAL_USERID_FIELD.full_name = ".prootc.cgdeal.userid"
CGDEAL_USERID_FIELD.number = 1
CGDEAL_USERID_FIELD.index = 0
CGDEAL_USERID_FIELD.label = 1
CGDEAL_USERID_FIELD.has_default_value = false
CGDEAL_USERID_FIELD.default_value = 0
CGDEAL_USERID_FIELD.type = 5
CGDEAL_USERID_FIELD.cpp_type = 1

CGDEAL_HANGID_FIELD.name = "hangid"
CGDEAL_HANGID_FIELD.full_name = ".prootc.cgdeal.hangid"
CGDEAL_HANGID_FIELD.number = 2
CGDEAL_HANGID_FIELD.index = 1
CGDEAL_HANGID_FIELD.label = 1
CGDEAL_HANGID_FIELD.has_default_value = false
CGDEAL_HANGID_FIELD.default_value = 0
CGDEAL_HANGID_FIELD.type = 5
CGDEAL_HANGID_FIELD.cpp_type = 1

CGDEAL_DEALTYPE_FIELD.name = "dealtype"
CGDEAL_DEALTYPE_FIELD.full_name = ".prootc.cgdeal.dealtype"
CGDEAL_DEALTYPE_FIELD.number = 3
CGDEAL_DEALTYPE_FIELD.index = 2
CGDEAL_DEALTYPE_FIELD.label = 1
CGDEAL_DEALTYPE_FIELD.has_default_value = false
CGDEAL_DEALTYPE_FIELD.default_value = 0
CGDEAL_DEALTYPE_FIELD.type = 5
CGDEAL_DEALTYPE_FIELD.cpp_type = 1

CGDEAL_AMOUNT_FIELD.name = "amount"
CGDEAL_AMOUNT_FIELD.full_name = ".prootc.cgdeal.amount"
CGDEAL_AMOUNT_FIELD.number = 4
CGDEAL_AMOUNT_FIELD.index = 3
CGDEAL_AMOUNT_FIELD.label = 1
CGDEAL_AMOUNT_FIELD.has_default_value = false
CGDEAL_AMOUNT_FIELD.default_value = ""
CGDEAL_AMOUNT_FIELD.type = 9
CGDEAL_AMOUNT_FIELD.cpp_type = 9

CGDEAL_FUNDPWD_FIELD.name = "fundPwd"
CGDEAL_FUNDPWD_FIELD.full_name = ".prootc.cgdeal.fundPwd"
CGDEAL_FUNDPWD_FIELD.number = 5
CGDEAL_FUNDPWD_FIELD.index = 4
CGDEAL_FUNDPWD_FIELD.label = 1
CGDEAL_FUNDPWD_FIELD.has_default_value = false
CGDEAL_FUNDPWD_FIELD.default_value = ""
CGDEAL_FUNDPWD_FIELD.type = 9
CGDEAL_FUNDPWD_FIELD.cpp_type = 9

CGDEAL_PRICE_FIELD.name = "price"
CGDEAL_PRICE_FIELD.full_name = ".prootc.cgdeal.price"
CGDEAL_PRICE_FIELD.number = 6
CGDEAL_PRICE_FIELD.index = 5
CGDEAL_PRICE_FIELD.label = 1
CGDEAL_PRICE_FIELD.has_default_value = false
CGDEAL_PRICE_FIELD.default_value = ""
CGDEAL_PRICE_FIELD.type = 9
CGDEAL_PRICE_FIELD.cpp_type = 9

CGDEAL_MONEYTYPE_FIELD.name = "moneytype"
CGDEAL_MONEYTYPE_FIELD.full_name = ".prootc.cgdeal.moneytype"
CGDEAL_MONEYTYPE_FIELD.number = 7
CGDEAL_MONEYTYPE_FIELD.index = 6
CGDEAL_MONEYTYPE_FIELD.label = 1
CGDEAL_MONEYTYPE_FIELD.has_default_value = false
CGDEAL_MONEYTYPE_FIELD.default_value = 0
CGDEAL_MONEYTYPE_FIELD.type = 5
CGDEAL_MONEYTYPE_FIELD.cpp_type = 1

CGDEAL_PAYTYPE_FIELD.name = "paytype"
CGDEAL_PAYTYPE_FIELD.full_name = ".prootc.cgdeal.paytype"
CGDEAL_PAYTYPE_FIELD.number = 8
CGDEAL_PAYTYPE_FIELD.index = 7
CGDEAL_PAYTYPE_FIELD.label = 1
CGDEAL_PAYTYPE_FIELD.has_default_value = false
CGDEAL_PAYTYPE_FIELD.default_value = 0
CGDEAL_PAYTYPE_FIELD.type = 5
CGDEAL_PAYTYPE_FIELD.cpp_type = 1

CGDEAL.name = "cgdeal"
CGDEAL.full_name = ".prootc.cgdeal"
CGDEAL.nested_types = {}
CGDEAL.enum_types = {}
CGDEAL.fields = {CGDEAL_USERID_FIELD, CGDEAL_HANGID_FIELD, CGDEAL_DEALTYPE_FIELD, CGDEAL_AMOUNT_FIELD, CGDEAL_FUNDPWD_FIELD, CGDEAL_PRICE_FIELD, CGDEAL_MONEYTYPE_FIELD, CGDEAL_PAYTYPE_FIELD}
CGDEAL.is_extendable = false
CGDEAL.extensions = {}
GCDEAL_RESULT_FIELD.name = "result"
GCDEAL_RESULT_FIELD.full_name = ".prootc.gcdeal.result"
GCDEAL_RESULT_FIELD.number = 1
GCDEAL_RESULT_FIELD.index = 0
GCDEAL_RESULT_FIELD.label = 1
GCDEAL_RESULT_FIELD.has_default_value = false
GCDEAL_RESULT_FIELD.default_value = 0
GCDEAL_RESULT_FIELD.type = 5
GCDEAL_RESULT_FIELD.cpp_type = 1

GCDEAL_MSG_FIELD.name = "msg"
GCDEAL_MSG_FIELD.full_name = ".prootc.gcdeal.msg"
GCDEAL_MSG_FIELD.number = 2
GCDEAL_MSG_FIELD.index = 1
GCDEAL_MSG_FIELD.label = 1
GCDEAL_MSG_FIELD.has_default_value = false
GCDEAL_MSG_FIELD.default_value = ""
GCDEAL_MSG_FIELD.type = 9
GCDEAL_MSG_FIELD.cpp_type = 9

GCDEAL_COINFO_FIELD.name = "coinfo"
GCDEAL_COINFO_FIELD.full_name = ".prootc.gcdeal.coinfo"
GCDEAL_COINFO_FIELD.number = 3
GCDEAL_COINFO_FIELD.index = 2
GCDEAL_COINFO_FIELD.label = 1
GCDEAL_COINFO_FIELD.has_default_value = false
GCDEAL_COINFO_FIELD.default_value = nil
GCDEAL_COINFO_FIELD.message_type = ST_ORDER_PB_CUSTOMERORDERINFO
GCDEAL_COINFO_FIELD.type = 11
GCDEAL_COINFO_FIELD.cpp_type = 10

GCDEAL.name = "gcdeal"
GCDEAL.full_name = ".prootc.gcdeal"
GCDEAL.nested_types = {}
GCDEAL.enum_types = {}
GCDEAL.fields = {GCDEAL_RESULT_FIELD, GCDEAL_MSG_FIELD, GCDEAL_COINFO_FIELD}
GCDEAL.is_extendable = false
GCDEAL.extensions = {}
CGPAID_USERID_FIELD.name = "userid"
CGPAID_USERID_FIELD.full_name = ".prootc.cgpaid.userid"
CGPAID_USERID_FIELD.number = 1
CGPAID_USERID_FIELD.index = 0
CGPAID_USERID_FIELD.label = 1
CGPAID_USERID_FIELD.has_default_value = false
CGPAID_USERID_FIELD.default_value = 0
CGPAID_USERID_FIELD.type = 5
CGPAID_USERID_FIELD.cpp_type = 1

CGPAID_DEALID_FIELD.name = "dealid"
CGPAID_DEALID_FIELD.full_name = ".prootc.cgpaid.dealid"
CGPAID_DEALID_FIELD.number = 2
CGPAID_DEALID_FIELD.index = 1
CGPAID_DEALID_FIELD.label = 1
CGPAID_DEALID_FIELD.has_default_value = false
CGPAID_DEALID_FIELD.default_value = 0
CGPAID_DEALID_FIELD.type = 5
CGPAID_DEALID_FIELD.cpp_type = 1

CGPAID_FUNDPWD_FIELD.name = "fundPwd"
CGPAID_FUNDPWD_FIELD.full_name = ".prootc.cgpaid.fundPwd"
CGPAID_FUNDPWD_FIELD.number = 3
CGPAID_FUNDPWD_FIELD.index = 2
CGPAID_FUNDPWD_FIELD.label = 1
CGPAID_FUNDPWD_FIELD.has_default_value = false
CGPAID_FUNDPWD_FIELD.default_value = ""
CGPAID_FUNDPWD_FIELD.type = 9
CGPAID_FUNDPWD_FIELD.cpp_type = 9

CGPAID_PROOFURL_FIELD.name = "proofurl"
CGPAID_PROOFURL_FIELD.full_name = ".prootc.cgpaid.proofurl"
CGPAID_PROOFURL_FIELD.number = 4
CGPAID_PROOFURL_FIELD.index = 3
CGPAID_PROOFURL_FIELD.label = 3
CGPAID_PROOFURL_FIELD.has_default_value = false
CGPAID_PROOFURL_FIELD.default_value = {}
CGPAID_PROOFURL_FIELD.type = 9
CGPAID_PROOFURL_FIELD.cpp_type = 9

CGPAID.name = "cgpaid"
CGPAID.full_name = ".prootc.cgpaid"
CGPAID.nested_types = {}
CGPAID.enum_types = {}
CGPAID.fields = {CGPAID_USERID_FIELD, CGPAID_DEALID_FIELD, CGPAID_FUNDPWD_FIELD, CGPAID_PROOFURL_FIELD}
CGPAID.is_extendable = false
CGPAID.extensions = {}
GCPAID_RESULT_FIELD.name = "result"
GCPAID_RESULT_FIELD.full_name = ".prootc.gcpaid.result"
GCPAID_RESULT_FIELD.number = 1
GCPAID_RESULT_FIELD.index = 0
GCPAID_RESULT_FIELD.label = 1
GCPAID_RESULT_FIELD.has_default_value = false
GCPAID_RESULT_FIELD.default_value = 0
GCPAID_RESULT_FIELD.type = 5
GCPAID_RESULT_FIELD.cpp_type = 1

GCPAID_MSG_FIELD.name = "msg"
GCPAID_MSG_FIELD.full_name = ".prootc.gcpaid.msg"
GCPAID_MSG_FIELD.number = 2
GCPAID_MSG_FIELD.index = 1
GCPAID_MSG_FIELD.label = 1
GCPAID_MSG_FIELD.has_default_value = false
GCPAID_MSG_FIELD.default_value = ""
GCPAID_MSG_FIELD.type = 9
GCPAID_MSG_FIELD.cpp_type = 9

GCPAID_COINFO_FIELD.name = "coinfo"
GCPAID_COINFO_FIELD.full_name = ".prootc.gcpaid.coinfo"
GCPAID_COINFO_FIELD.number = 3
GCPAID_COINFO_FIELD.index = 2
GCPAID_COINFO_FIELD.label = 1
GCPAID_COINFO_FIELD.has_default_value = false
GCPAID_COINFO_FIELD.default_value = nil
GCPAID_COINFO_FIELD.message_type = ST_ORDER_PB_CUSTOMERORDERINFO
GCPAID_COINFO_FIELD.type = 11
GCPAID_COINFO_FIELD.cpp_type = 10

GCPAID.name = "gcpaid"
GCPAID.full_name = ".prootc.gcpaid"
GCPAID.nested_types = {}
GCPAID.enum_types = {}
GCPAID.fields = {GCPAID_RESULT_FIELD, GCPAID_MSG_FIELD, GCPAID_COINFO_FIELD}
GCPAID.is_extendable = false
GCPAID.extensions = {}
CGPASS_USERID_FIELD.name = "userid"
CGPASS_USERID_FIELD.full_name = ".prootc.cgpass.userid"
CGPASS_USERID_FIELD.number = 1
CGPASS_USERID_FIELD.index = 0
CGPASS_USERID_FIELD.label = 1
CGPASS_USERID_FIELD.has_default_value = false
CGPASS_USERID_FIELD.default_value = 0
CGPASS_USERID_FIELD.type = 5
CGPASS_USERID_FIELD.cpp_type = 1

CGPASS_DEALID_FIELD.name = "dealid"
CGPASS_DEALID_FIELD.full_name = ".prootc.cgpass.dealid"
CGPASS_DEALID_FIELD.number = 2
CGPASS_DEALID_FIELD.index = 1
CGPASS_DEALID_FIELD.label = 1
CGPASS_DEALID_FIELD.has_default_value = false
CGPASS_DEALID_FIELD.default_value = 0
CGPASS_DEALID_FIELD.type = 5
CGPASS_DEALID_FIELD.cpp_type = 1

CGPASS_FUNDPWD_FIELD.name = "fundPwd"
CGPASS_FUNDPWD_FIELD.full_name = ".prootc.cgpass.fundPwd"
CGPASS_FUNDPWD_FIELD.number = 3
CGPASS_FUNDPWD_FIELD.index = 2
CGPASS_FUNDPWD_FIELD.label = 1
CGPASS_FUNDPWD_FIELD.has_default_value = false
CGPASS_FUNDPWD_FIELD.default_value = ""
CGPASS_FUNDPWD_FIELD.type = 9
CGPASS_FUNDPWD_FIELD.cpp_type = 9

CGPASS.name = "cgpass"
CGPASS.full_name = ".prootc.cgpass"
CGPASS.nested_types = {}
CGPASS.enum_types = {}
CGPASS.fields = {CGPASS_USERID_FIELD, CGPASS_DEALID_FIELD, CGPASS_FUNDPWD_FIELD}
CGPASS.is_extendable = false
CGPASS.extensions = {}
GCPASS_RESULT_FIELD.name = "result"
GCPASS_RESULT_FIELD.full_name = ".prootc.gcpass.result"
GCPASS_RESULT_FIELD.number = 1
GCPASS_RESULT_FIELD.index = 0
GCPASS_RESULT_FIELD.label = 1
GCPASS_RESULT_FIELD.has_default_value = false
GCPASS_RESULT_FIELD.default_value = 0
GCPASS_RESULT_FIELD.type = 5
GCPASS_RESULT_FIELD.cpp_type = 1

GCPASS_MSG_FIELD.name = "msg"
GCPASS_MSG_FIELD.full_name = ".prootc.gcpass.msg"
GCPASS_MSG_FIELD.number = 2
GCPASS_MSG_FIELD.index = 1
GCPASS_MSG_FIELD.label = 1
GCPASS_MSG_FIELD.has_default_value = false
GCPASS_MSG_FIELD.default_value = ""
GCPASS_MSG_FIELD.type = 9
GCPASS_MSG_FIELD.cpp_type = 9

GCPASS_COINFO_FIELD.name = "coinfo"
GCPASS_COINFO_FIELD.full_name = ".prootc.gcpass.coinfo"
GCPASS_COINFO_FIELD.number = 3
GCPASS_COINFO_FIELD.index = 2
GCPASS_COINFO_FIELD.label = 1
GCPASS_COINFO_FIELD.has_default_value = false
GCPASS_COINFO_FIELD.default_value = nil
GCPASS_COINFO_FIELD.message_type = ST_ORDER_PB_CUSTOMERORDERINFO
GCPASS_COINFO_FIELD.type = 11
GCPASS_COINFO_FIELD.cpp_type = 10

GCPASS.name = "gcpass"
GCPASS.full_name = ".prootc.gcpass"
GCPASS.nested_types = {}
GCPASS.enum_types = {}
GCPASS.fields = {GCPASS_RESULT_FIELD, GCPASS_MSG_FIELD, GCPASS_COINFO_FIELD}
GCPASS.is_extendable = false
GCPASS.extensions = {}
CGCANCELLEFTCOUNT_USERID_FIELD.name = "userid"
CGCANCELLEFTCOUNT_USERID_FIELD.full_name = ".prootc.cgcancelleftcount.userid"
CGCANCELLEFTCOUNT_USERID_FIELD.number = 1
CGCANCELLEFTCOUNT_USERID_FIELD.index = 0
CGCANCELLEFTCOUNT_USERID_FIELD.label = 1
CGCANCELLEFTCOUNT_USERID_FIELD.has_default_value = false
CGCANCELLEFTCOUNT_USERID_FIELD.default_value = 0
CGCANCELLEFTCOUNT_USERID_FIELD.type = 5
CGCANCELLEFTCOUNT_USERID_FIELD.cpp_type = 1

CGCANCELLEFTCOUNT.name = "cgcancelleftcount"
CGCANCELLEFTCOUNT.full_name = ".prootc.cgcancelleftcount"
CGCANCELLEFTCOUNT.nested_types = {}
CGCANCELLEFTCOUNT.enum_types = {}
CGCANCELLEFTCOUNT.fields = {CGCANCELLEFTCOUNT_USERID_FIELD}
CGCANCELLEFTCOUNT.is_extendable = false
CGCANCELLEFTCOUNT.extensions = {}
GCCANCELLEFTCOUNT_RESULT_FIELD.name = "result"
GCCANCELLEFTCOUNT_RESULT_FIELD.full_name = ".prootc.gccancelleftcount.result"
GCCANCELLEFTCOUNT_RESULT_FIELD.number = 1
GCCANCELLEFTCOUNT_RESULT_FIELD.index = 0
GCCANCELLEFTCOUNT_RESULT_FIELD.label = 1
GCCANCELLEFTCOUNT_RESULT_FIELD.has_default_value = false
GCCANCELLEFTCOUNT_RESULT_FIELD.default_value = 0
GCCANCELLEFTCOUNT_RESULT_FIELD.type = 5
GCCANCELLEFTCOUNT_RESULT_FIELD.cpp_type = 1

GCCANCELLEFTCOUNT_MSG_FIELD.name = "msg"
GCCANCELLEFTCOUNT_MSG_FIELD.full_name = ".prootc.gccancelleftcount.msg"
GCCANCELLEFTCOUNT_MSG_FIELD.number = 2
GCCANCELLEFTCOUNT_MSG_FIELD.index = 1
GCCANCELLEFTCOUNT_MSG_FIELD.label = 1
GCCANCELLEFTCOUNT_MSG_FIELD.has_default_value = false
GCCANCELLEFTCOUNT_MSG_FIELD.default_value = ""
GCCANCELLEFTCOUNT_MSG_FIELD.type = 9
GCCANCELLEFTCOUNT_MSG_FIELD.cpp_type = 9

GCCANCELLEFTCOUNT_LEFTCOUNT_FIELD.name = "leftcount"
GCCANCELLEFTCOUNT_LEFTCOUNT_FIELD.full_name = ".prootc.gccancelleftcount.leftcount"
GCCANCELLEFTCOUNT_LEFTCOUNT_FIELD.number = 3
GCCANCELLEFTCOUNT_LEFTCOUNT_FIELD.index = 2
GCCANCELLEFTCOUNT_LEFTCOUNT_FIELD.label = 1
GCCANCELLEFTCOUNT_LEFTCOUNT_FIELD.has_default_value = false
GCCANCELLEFTCOUNT_LEFTCOUNT_FIELD.default_value = 0
GCCANCELLEFTCOUNT_LEFTCOUNT_FIELD.type = 5
GCCANCELLEFTCOUNT_LEFTCOUNT_FIELD.cpp_type = 1

GCCANCELLEFTCOUNT.name = "gccancelleftcount"
GCCANCELLEFTCOUNT.full_name = ".prootc.gccancelleftcount"
GCCANCELLEFTCOUNT.nested_types = {}
GCCANCELLEFTCOUNT.enum_types = {}
GCCANCELLEFTCOUNT.fields = {GCCANCELLEFTCOUNT_RESULT_FIELD, GCCANCELLEFTCOUNT_MSG_FIELD, GCCANCELLEFTCOUNT_LEFTCOUNT_FIELD}
GCCANCELLEFTCOUNT.is_extendable = false
GCCANCELLEFTCOUNT.extensions = {}
CGCANCEL_USERID_FIELD.name = "userid"
CGCANCEL_USERID_FIELD.full_name = ".prootc.cgcancel.userid"
CGCANCEL_USERID_FIELD.number = 1
CGCANCEL_USERID_FIELD.index = 0
CGCANCEL_USERID_FIELD.label = 1
CGCANCEL_USERID_FIELD.has_default_value = false
CGCANCEL_USERID_FIELD.default_value = 0
CGCANCEL_USERID_FIELD.type = 5
CGCANCEL_USERID_FIELD.cpp_type = 1

CGCANCEL_DEALID_FIELD.name = "dealid"
CGCANCEL_DEALID_FIELD.full_name = ".prootc.cgcancel.dealid"
CGCANCEL_DEALID_FIELD.number = 2
CGCANCEL_DEALID_FIELD.index = 1
CGCANCEL_DEALID_FIELD.label = 1
CGCANCEL_DEALID_FIELD.has_default_value = false
CGCANCEL_DEALID_FIELD.default_value = 0
CGCANCEL_DEALID_FIELD.type = 5
CGCANCEL_DEALID_FIELD.cpp_type = 1

CGCANCEL.name = "cgcancel"
CGCANCEL.full_name = ".prootc.cgcancel"
CGCANCEL.nested_types = {}
CGCANCEL.enum_types = {}
CGCANCEL.fields = {CGCANCEL_USERID_FIELD, CGCANCEL_DEALID_FIELD}
CGCANCEL.is_extendable = false
CGCANCEL.extensions = {}
GCCANCEL_RESULT_FIELD.name = "result"
GCCANCEL_RESULT_FIELD.full_name = ".prootc.gccancel.result"
GCCANCEL_RESULT_FIELD.number = 1
GCCANCEL_RESULT_FIELD.index = 0
GCCANCEL_RESULT_FIELD.label = 1
GCCANCEL_RESULT_FIELD.has_default_value = false
GCCANCEL_RESULT_FIELD.default_value = 0
GCCANCEL_RESULT_FIELD.type = 5
GCCANCEL_RESULT_FIELD.cpp_type = 1

GCCANCEL_MSG_FIELD.name = "msg"
GCCANCEL_MSG_FIELD.full_name = ".prootc.gccancel.msg"
GCCANCEL_MSG_FIELD.number = 2
GCCANCEL_MSG_FIELD.index = 1
GCCANCEL_MSG_FIELD.label = 1
GCCANCEL_MSG_FIELD.has_default_value = false
GCCANCEL_MSG_FIELD.default_value = ""
GCCANCEL_MSG_FIELD.type = 9
GCCANCEL_MSG_FIELD.cpp_type = 9

GCCANCEL_COINFO_FIELD.name = "coinfo"
GCCANCEL_COINFO_FIELD.full_name = ".prootc.gccancel.coinfo"
GCCANCEL_COINFO_FIELD.number = 3
GCCANCEL_COINFO_FIELD.index = 2
GCCANCEL_COINFO_FIELD.label = 1
GCCANCEL_COINFO_FIELD.has_default_value = false
GCCANCEL_COINFO_FIELD.default_value = nil
GCCANCEL_COINFO_FIELD.message_type = ST_ORDER_PB_CUSTOMERORDERINFO
GCCANCEL_COINFO_FIELD.type = 11
GCCANCEL_COINFO_FIELD.cpp_type = 10

GCCANCEL.name = "gccancel"
GCCANCEL.full_name = ".prootc.gccancel"
GCCANCEL.nested_types = {}
GCCANCEL.enum_types = {}
GCCANCEL.fields = {GCCANCEL_RESULT_FIELD, GCCANCEL_MSG_FIELD, GCCANCEL_COINFO_FIELD}
GCCANCEL.is_extendable = false
GCCANCEL.extensions = {}
CGAPPEAL_USERID_FIELD.name = "userid"
CGAPPEAL_USERID_FIELD.full_name = ".prootc.cgappeal.userid"
CGAPPEAL_USERID_FIELD.number = 1
CGAPPEAL_USERID_FIELD.index = 0
CGAPPEAL_USERID_FIELD.label = 1
CGAPPEAL_USERID_FIELD.has_default_value = false
CGAPPEAL_USERID_FIELD.default_value = 0
CGAPPEAL_USERID_FIELD.type = 5
CGAPPEAL_USERID_FIELD.cpp_type = 1

CGAPPEAL_DEALID_FIELD.name = "dealid"
CGAPPEAL_DEALID_FIELD.full_name = ".prootc.cgappeal.dealid"
CGAPPEAL_DEALID_FIELD.number = 2
CGAPPEAL_DEALID_FIELD.index = 1
CGAPPEAL_DEALID_FIELD.label = 1
CGAPPEAL_DEALID_FIELD.has_default_value = false
CGAPPEAL_DEALID_FIELD.default_value = 0
CGAPPEAL_DEALID_FIELD.type = 5
CGAPPEAL_DEALID_FIELD.cpp_type = 1

CGAPPEAL_REASON_FIELD.name = "reason"
CGAPPEAL_REASON_FIELD.full_name = ".prootc.cgappeal.reason"
CGAPPEAL_REASON_FIELD.number = 3
CGAPPEAL_REASON_FIELD.index = 2
CGAPPEAL_REASON_FIELD.label = 1
CGAPPEAL_REASON_FIELD.has_default_value = false
CGAPPEAL_REASON_FIELD.default_value = ""
CGAPPEAL_REASON_FIELD.type = 9
CGAPPEAL_REASON_FIELD.cpp_type = 9

CGAPPEAL_DESCRIPTION_FIELD.name = "description"
CGAPPEAL_DESCRIPTION_FIELD.full_name = ".prootc.cgappeal.description"
CGAPPEAL_DESCRIPTION_FIELD.number = 4
CGAPPEAL_DESCRIPTION_FIELD.index = 3
CGAPPEAL_DESCRIPTION_FIELD.label = 1
CGAPPEAL_DESCRIPTION_FIELD.has_default_value = false
CGAPPEAL_DESCRIPTION_FIELD.default_value = ""
CGAPPEAL_DESCRIPTION_FIELD.type = 9
CGAPPEAL_DESCRIPTION_FIELD.cpp_type = 9

CGAPPEAL_PROOFURL_FIELD.name = "proofurl"
CGAPPEAL_PROOFURL_FIELD.full_name = ".prootc.cgappeal.proofurl"
CGAPPEAL_PROOFURL_FIELD.number = 5
CGAPPEAL_PROOFURL_FIELD.index = 4
CGAPPEAL_PROOFURL_FIELD.label = 1
CGAPPEAL_PROOFURL_FIELD.has_default_value = false
CGAPPEAL_PROOFURL_FIELD.default_value = ""
CGAPPEAL_PROOFURL_FIELD.type = 9
CGAPPEAL_PROOFURL_FIELD.cpp_type = 9

CGAPPEAL.name = "cgappeal"
CGAPPEAL.full_name = ".prootc.cgappeal"
CGAPPEAL.nested_types = {}
CGAPPEAL.enum_types = {}
CGAPPEAL.fields = {CGAPPEAL_USERID_FIELD, CGAPPEAL_DEALID_FIELD, CGAPPEAL_REASON_FIELD, CGAPPEAL_DESCRIPTION_FIELD, CGAPPEAL_PROOFURL_FIELD}
CGAPPEAL.is_extendable = false
CGAPPEAL.extensions = {}
GCAPPEAL_RESULT_FIELD.name = "result"
GCAPPEAL_RESULT_FIELD.full_name = ".prootc.gcappeal.result"
GCAPPEAL_RESULT_FIELD.number = 1
GCAPPEAL_RESULT_FIELD.index = 0
GCAPPEAL_RESULT_FIELD.label = 1
GCAPPEAL_RESULT_FIELD.has_default_value = false
GCAPPEAL_RESULT_FIELD.default_value = 0
GCAPPEAL_RESULT_FIELD.type = 5
GCAPPEAL_RESULT_FIELD.cpp_type = 1

GCAPPEAL_MSG_FIELD.name = "msg"
GCAPPEAL_MSG_FIELD.full_name = ".prootc.gcappeal.msg"
GCAPPEAL_MSG_FIELD.number = 2
GCAPPEAL_MSG_FIELD.index = 1
GCAPPEAL_MSG_FIELD.label = 1
GCAPPEAL_MSG_FIELD.has_default_value = false
GCAPPEAL_MSG_FIELD.default_value = ""
GCAPPEAL_MSG_FIELD.type = 9
GCAPPEAL_MSG_FIELD.cpp_type = 9

GCAPPEAL_INFO_FIELD.name = "info"
GCAPPEAL_INFO_FIELD.full_name = ".prootc.gcappeal.info"
GCAPPEAL_INFO_FIELD.number = 3
GCAPPEAL_INFO_FIELD.index = 2
GCAPPEAL_INFO_FIELD.label = 1
GCAPPEAL_INFO_FIELD.has_default_value = false
GCAPPEAL_INFO_FIELD.default_value = nil
GCAPPEAL_INFO_FIELD.message_type = ST_ORDER_PB_APPPEALINFO
GCAPPEAL_INFO_FIELD.type = 11
GCAPPEAL_INFO_FIELD.cpp_type = 10

GCAPPEAL_COINFO_FIELD.name = "coinfo"
GCAPPEAL_COINFO_FIELD.full_name = ".prootc.gcappeal.coinfo"
GCAPPEAL_COINFO_FIELD.number = 4
GCAPPEAL_COINFO_FIELD.index = 3
GCAPPEAL_COINFO_FIELD.label = 1
GCAPPEAL_COINFO_FIELD.has_default_value = false
GCAPPEAL_COINFO_FIELD.default_value = nil
GCAPPEAL_COINFO_FIELD.message_type = ST_ORDER_PB_CUSTOMERORDERINFO
GCAPPEAL_COINFO_FIELD.type = 11
GCAPPEAL_COINFO_FIELD.cpp_type = 10

GCAPPEAL.name = "gcappeal"
GCAPPEAL.full_name = ".prootc.gcappeal"
GCAPPEAL.nested_types = {}
GCAPPEAL.enum_types = {}
GCAPPEAL.fields = {GCAPPEAL_RESULT_FIELD, GCAPPEAL_MSG_FIELD, GCAPPEAL_INFO_FIELD, GCAPPEAL_COINFO_FIELD}
GCAPPEAL.is_extendable = false
GCAPPEAL.extensions = {}
CGAPPEALREPLY_USERID_FIELD.name = "userid"
CGAPPEALREPLY_USERID_FIELD.full_name = ".prootc.cgappealreply.userid"
CGAPPEALREPLY_USERID_FIELD.number = 1
CGAPPEALREPLY_USERID_FIELD.index = 0
CGAPPEALREPLY_USERID_FIELD.label = 1
CGAPPEALREPLY_USERID_FIELD.has_default_value = false
CGAPPEALREPLY_USERID_FIELD.default_value = 0
CGAPPEALREPLY_USERID_FIELD.type = 5
CGAPPEALREPLY_USERID_FIELD.cpp_type = 1

CGAPPEALREPLY_DEALID_FIELD.name = "dealid"
CGAPPEALREPLY_DEALID_FIELD.full_name = ".prootc.cgappealreply.dealid"
CGAPPEALREPLY_DEALID_FIELD.number = 2
CGAPPEALREPLY_DEALID_FIELD.index = 1
CGAPPEALREPLY_DEALID_FIELD.label = 1
CGAPPEALREPLY_DEALID_FIELD.has_default_value = false
CGAPPEALREPLY_DEALID_FIELD.default_value = 0
CGAPPEALREPLY_DEALID_FIELD.type = 5
CGAPPEALREPLY_DEALID_FIELD.cpp_type = 1

CGAPPEALREPLY_REASON_FIELD.name = "reason"
CGAPPEALREPLY_REASON_FIELD.full_name = ".prootc.cgappealreply.reason"
CGAPPEALREPLY_REASON_FIELD.number = 3
CGAPPEALREPLY_REASON_FIELD.index = 2
CGAPPEALREPLY_REASON_FIELD.label = 1
CGAPPEALREPLY_REASON_FIELD.has_default_value = false
CGAPPEALREPLY_REASON_FIELD.default_value = ""
CGAPPEALREPLY_REASON_FIELD.type = 9
CGAPPEALREPLY_REASON_FIELD.cpp_type = 9

CGAPPEALREPLY_DESCRIPTION_FIELD.name = "description"
CGAPPEALREPLY_DESCRIPTION_FIELD.full_name = ".prootc.cgappealreply.description"
CGAPPEALREPLY_DESCRIPTION_FIELD.number = 4
CGAPPEALREPLY_DESCRIPTION_FIELD.index = 3
CGAPPEALREPLY_DESCRIPTION_FIELD.label = 1
CGAPPEALREPLY_DESCRIPTION_FIELD.has_default_value = false
CGAPPEALREPLY_DESCRIPTION_FIELD.default_value = ""
CGAPPEALREPLY_DESCRIPTION_FIELD.type = 9
CGAPPEALREPLY_DESCRIPTION_FIELD.cpp_type = 9

CGAPPEALREPLY_PROOFURL_FIELD.name = "proofurl"
CGAPPEALREPLY_PROOFURL_FIELD.full_name = ".prootc.cgappealreply.proofurl"
CGAPPEALREPLY_PROOFURL_FIELD.number = 5
CGAPPEALREPLY_PROOFURL_FIELD.index = 4
CGAPPEALREPLY_PROOFURL_FIELD.label = 1
CGAPPEALREPLY_PROOFURL_FIELD.has_default_value = false
CGAPPEALREPLY_PROOFURL_FIELD.default_value = ""
CGAPPEALREPLY_PROOFURL_FIELD.type = 9
CGAPPEALREPLY_PROOFURL_FIELD.cpp_type = 9

CGAPPEALREPLY.name = "cgappealreply"
CGAPPEALREPLY.full_name = ".prootc.cgappealreply"
CGAPPEALREPLY.nested_types = {}
CGAPPEALREPLY.enum_types = {}
CGAPPEALREPLY.fields = {CGAPPEALREPLY_USERID_FIELD, CGAPPEALREPLY_DEALID_FIELD, CGAPPEALREPLY_REASON_FIELD, CGAPPEALREPLY_DESCRIPTION_FIELD, CGAPPEALREPLY_PROOFURL_FIELD}
CGAPPEALREPLY.is_extendable = false
CGAPPEALREPLY.extensions = {}
GCAPPEALREPLY_RESULT_FIELD.name = "result"
GCAPPEALREPLY_RESULT_FIELD.full_name = ".prootc.gcappealreply.result"
GCAPPEALREPLY_RESULT_FIELD.number = 1
GCAPPEALREPLY_RESULT_FIELD.index = 0
GCAPPEALREPLY_RESULT_FIELD.label = 1
GCAPPEALREPLY_RESULT_FIELD.has_default_value = false
GCAPPEALREPLY_RESULT_FIELD.default_value = 0
GCAPPEALREPLY_RESULT_FIELD.type = 5
GCAPPEALREPLY_RESULT_FIELD.cpp_type = 1

GCAPPEALREPLY_MSG_FIELD.name = "msg"
GCAPPEALREPLY_MSG_FIELD.full_name = ".prootc.gcappealreply.msg"
GCAPPEALREPLY_MSG_FIELD.number = 2
GCAPPEALREPLY_MSG_FIELD.index = 1
GCAPPEALREPLY_MSG_FIELD.label = 1
GCAPPEALREPLY_MSG_FIELD.has_default_value = false
GCAPPEALREPLY_MSG_FIELD.default_value = ""
GCAPPEALREPLY_MSG_FIELD.type = 9
GCAPPEALREPLY_MSG_FIELD.cpp_type = 9

GCAPPEALREPLY_COINFO_FIELD.name = "coinfo"
GCAPPEALREPLY_COINFO_FIELD.full_name = ".prootc.gcappealreply.coinfo"
GCAPPEALREPLY_COINFO_FIELD.number = 3
GCAPPEALREPLY_COINFO_FIELD.index = 2
GCAPPEALREPLY_COINFO_FIELD.label = 1
GCAPPEALREPLY_COINFO_FIELD.has_default_value = false
GCAPPEALREPLY_COINFO_FIELD.default_value = nil
GCAPPEALREPLY_COINFO_FIELD.message_type = ST_ORDER_PB_CUSTOMERORDERINFO
GCAPPEALREPLY_COINFO_FIELD.type = 11
GCAPPEALREPLY_COINFO_FIELD.cpp_type = 10

GCAPPEALREPLY.name = "gcappealreply"
GCAPPEALREPLY.full_name = ".prootc.gcappealreply"
GCAPPEALREPLY.nested_types = {}
GCAPPEALREPLY.enum_types = {}
GCAPPEALREPLY.fields = {GCAPPEALREPLY_RESULT_FIELD, GCAPPEALREPLY_MSG_FIELD, GCAPPEALREPLY_COINFO_FIELD}
GCAPPEALREPLY.is_extendable = false
GCAPPEALREPLY.extensions = {}
CGAPPEALDETAIL_USERID_FIELD.name = "userid"
CGAPPEALDETAIL_USERID_FIELD.full_name = ".prootc.cgappealdetail.userid"
CGAPPEALDETAIL_USERID_FIELD.number = 1
CGAPPEALDETAIL_USERID_FIELD.index = 0
CGAPPEALDETAIL_USERID_FIELD.label = 1
CGAPPEALDETAIL_USERID_FIELD.has_default_value = false
CGAPPEALDETAIL_USERID_FIELD.default_value = 0
CGAPPEALDETAIL_USERID_FIELD.type = 5
CGAPPEALDETAIL_USERID_FIELD.cpp_type = 1

CGAPPEALDETAIL_DEALID_FIELD.name = "dealid"
CGAPPEALDETAIL_DEALID_FIELD.full_name = ".prootc.cgappealdetail.dealid"
CGAPPEALDETAIL_DEALID_FIELD.number = 2
CGAPPEALDETAIL_DEALID_FIELD.index = 1
CGAPPEALDETAIL_DEALID_FIELD.label = 1
CGAPPEALDETAIL_DEALID_FIELD.has_default_value = false
CGAPPEALDETAIL_DEALID_FIELD.default_value = 0
CGAPPEALDETAIL_DEALID_FIELD.type = 5
CGAPPEALDETAIL_DEALID_FIELD.cpp_type = 1

CGAPPEALDETAIL.name = "cgappealdetail"
CGAPPEALDETAIL.full_name = ".prootc.cgappealdetail"
CGAPPEALDETAIL.nested_types = {}
CGAPPEALDETAIL.enum_types = {}
CGAPPEALDETAIL.fields = {CGAPPEALDETAIL_USERID_FIELD, CGAPPEALDETAIL_DEALID_FIELD}
CGAPPEALDETAIL.is_extendable = false
CGAPPEALDETAIL.extensions = {}
GCAPPEALDETAIL_RESULT_FIELD.name = "result"
GCAPPEALDETAIL_RESULT_FIELD.full_name = ".prootc.gcappealdetail.result"
GCAPPEALDETAIL_RESULT_FIELD.number = 1
GCAPPEALDETAIL_RESULT_FIELD.index = 0
GCAPPEALDETAIL_RESULT_FIELD.label = 1
GCAPPEALDETAIL_RESULT_FIELD.has_default_value = false
GCAPPEALDETAIL_RESULT_FIELD.default_value = 0
GCAPPEALDETAIL_RESULT_FIELD.type = 5
GCAPPEALDETAIL_RESULT_FIELD.cpp_type = 1

GCAPPEALDETAIL_MSG_FIELD.name = "msg"
GCAPPEALDETAIL_MSG_FIELD.full_name = ".prootc.gcappealdetail.msg"
GCAPPEALDETAIL_MSG_FIELD.number = 2
GCAPPEALDETAIL_MSG_FIELD.index = 1
GCAPPEALDETAIL_MSG_FIELD.label = 1
GCAPPEALDETAIL_MSG_FIELD.has_default_value = false
GCAPPEALDETAIL_MSG_FIELD.default_value = ""
GCAPPEALDETAIL_MSG_FIELD.type = 9
GCAPPEALDETAIL_MSG_FIELD.cpp_type = 9

GCAPPEALDETAIL_COINFO_FIELD.name = "coinfo"
GCAPPEALDETAIL_COINFO_FIELD.full_name = ".prootc.gcappealdetail.coinfo"
GCAPPEALDETAIL_COINFO_FIELD.number = 3
GCAPPEALDETAIL_COINFO_FIELD.index = 2
GCAPPEALDETAIL_COINFO_FIELD.label = 1
GCAPPEALDETAIL_COINFO_FIELD.has_default_value = false
GCAPPEALDETAIL_COINFO_FIELD.default_value = nil
GCAPPEALDETAIL_COINFO_FIELD.message_type = ST_ORDER_PB_CUSTOMERORDERINFO
GCAPPEALDETAIL_COINFO_FIELD.type = 11
GCAPPEALDETAIL_COINFO_FIELD.cpp_type = 10

GCAPPEALDETAIL.name = "gcappealdetail"
GCAPPEALDETAIL.full_name = ".prootc.gcappealdetail"
GCAPPEALDETAIL.nested_types = {}
GCAPPEALDETAIL.enum_types = {}
GCAPPEALDETAIL.fields = {GCAPPEALDETAIL_RESULT_FIELD, GCAPPEALDETAIL_MSG_FIELD, GCAPPEALDETAIL_COINFO_FIELD}
GCAPPEALDETAIL.is_extendable = false
GCAPPEALDETAIL.extensions = {}
CGAPPEALLIST_USERID_FIELD.name = "userid"
CGAPPEALLIST_USERID_FIELD.full_name = ".prootc.cgappeallist.userid"
CGAPPEALLIST_USERID_FIELD.number = 1
CGAPPEALLIST_USERID_FIELD.index = 0
CGAPPEALLIST_USERID_FIELD.label = 1
CGAPPEALLIST_USERID_FIELD.has_default_value = false
CGAPPEALLIST_USERID_FIELD.default_value = 0
CGAPPEALLIST_USERID_FIELD.type = 5
CGAPPEALLIST_USERID_FIELD.cpp_type = 1

CGAPPEALLIST.name = "cgappeallist"
CGAPPEALLIST.full_name = ".prootc.cgappeallist"
CGAPPEALLIST.nested_types = {}
CGAPPEALLIST.enum_types = {}
CGAPPEALLIST.fields = {CGAPPEALLIST_USERID_FIELD}
CGAPPEALLIST.is_extendable = false
CGAPPEALLIST.extensions = {}
GCAPPEALLIST.name = "gcappeallist"
GCAPPEALLIST.full_name = ".prootc.gcappeallist"
GCAPPEALLIST.nested_types = {}
GCAPPEALLIST.enum_types = {}
GCAPPEALLIST.fields = {}
GCAPPEALLIST.is_extendable = false
GCAPPEALLIST.extensions = {}
CGCUSTOMERORDERLIST_USERID_FIELD.name = "userid"
CGCUSTOMERORDERLIST_USERID_FIELD.full_name = ".prootc.cgcustomerorderlist.userid"
CGCUSTOMERORDERLIST_USERID_FIELD.number = 1
CGCUSTOMERORDERLIST_USERID_FIELD.index = 0
CGCUSTOMERORDERLIST_USERID_FIELD.label = 1
CGCUSTOMERORDERLIST_USERID_FIELD.has_default_value = false
CGCUSTOMERORDERLIST_USERID_FIELD.default_value = 0
CGCUSTOMERORDERLIST_USERID_FIELD.type = 5
CGCUSTOMERORDERLIST_USERID_FIELD.cpp_type = 1

CGCUSTOMERORDERLIST_ORDERTYPE_FIELD.name = "ordertype"
CGCUSTOMERORDERLIST_ORDERTYPE_FIELD.full_name = ".prootc.cgcustomerorderlist.ordertype"
CGCUSTOMERORDERLIST_ORDERTYPE_FIELD.number = 2
CGCUSTOMERORDERLIST_ORDERTYPE_FIELD.index = 1
CGCUSTOMERORDERLIST_ORDERTYPE_FIELD.label = 1
CGCUSTOMERORDERLIST_ORDERTYPE_FIELD.has_default_value = false
CGCUSTOMERORDERLIST_ORDERTYPE_FIELD.default_value = 0
CGCUSTOMERORDERLIST_ORDERTYPE_FIELD.type = 5
CGCUSTOMERORDERLIST_ORDERTYPE_FIELD.cpp_type = 1

CGCUSTOMERORDERLIST_PAGENUM_FIELD.name = "pagenum"
CGCUSTOMERORDERLIST_PAGENUM_FIELD.full_name = ".prootc.cgcustomerorderlist.pagenum"
CGCUSTOMERORDERLIST_PAGENUM_FIELD.number = 3
CGCUSTOMERORDERLIST_PAGENUM_FIELD.index = 2
CGCUSTOMERORDERLIST_PAGENUM_FIELD.label = 1
CGCUSTOMERORDERLIST_PAGENUM_FIELD.has_default_value = false
CGCUSTOMERORDERLIST_PAGENUM_FIELD.default_value = 0
CGCUSTOMERORDERLIST_PAGENUM_FIELD.type = 5
CGCUSTOMERORDERLIST_PAGENUM_FIELD.cpp_type = 1

CGCUSTOMERORDERLIST_PAGESIZE_FIELD.name = "pagesize"
CGCUSTOMERORDERLIST_PAGESIZE_FIELD.full_name = ".prootc.cgcustomerorderlist.pagesize"
CGCUSTOMERORDERLIST_PAGESIZE_FIELD.number = 4
CGCUSTOMERORDERLIST_PAGESIZE_FIELD.index = 3
CGCUSTOMERORDERLIST_PAGESIZE_FIELD.label = 1
CGCUSTOMERORDERLIST_PAGESIZE_FIELD.has_default_value = false
CGCUSTOMERORDERLIST_PAGESIZE_FIELD.default_value = 0
CGCUSTOMERORDERLIST_PAGESIZE_FIELD.type = 5
CGCUSTOMERORDERLIST_PAGESIZE_FIELD.cpp_type = 1

CGCUSTOMERORDERLIST_COINID_FIELD.name = "coinid"
CGCUSTOMERORDERLIST_COINID_FIELD.full_name = ".prootc.cgcustomerorderlist.coinid"
CGCUSTOMERORDERLIST_COINID_FIELD.number = 5
CGCUSTOMERORDERLIST_COINID_FIELD.index = 4
CGCUSTOMERORDERLIST_COINID_FIELD.label = 1
CGCUSTOMERORDERLIST_COINID_FIELD.has_default_value = false
CGCUSTOMERORDERLIST_COINID_FIELD.default_value = 0
CGCUSTOMERORDERLIST_COINID_FIELD.type = 5
CGCUSTOMERORDERLIST_COINID_FIELD.cpp_type = 1

CGCUSTOMERORDERLIST.name = "cgcustomerorderlist"
CGCUSTOMERORDERLIST.full_name = ".prootc.cgcustomerorderlist"
CGCUSTOMERORDERLIST.nested_types = {}
CGCUSTOMERORDERLIST.enum_types = {}
CGCUSTOMERORDERLIST.fields = {CGCUSTOMERORDERLIST_USERID_FIELD, CGCUSTOMERORDERLIST_ORDERTYPE_FIELD, CGCUSTOMERORDERLIST_PAGENUM_FIELD, CGCUSTOMERORDERLIST_PAGESIZE_FIELD, CGCUSTOMERORDERLIST_COINID_FIELD}
CGCUSTOMERORDERLIST.is_extendable = false
CGCUSTOMERORDERLIST.extensions = {}
GCCUSTOMERORDERLIST_RESULT_FIELD.name = "result"
GCCUSTOMERORDERLIST_RESULT_FIELD.full_name = ".prootc.gccustomerorderlist.result"
GCCUSTOMERORDERLIST_RESULT_FIELD.number = 1
GCCUSTOMERORDERLIST_RESULT_FIELD.index = 0
GCCUSTOMERORDERLIST_RESULT_FIELD.label = 1
GCCUSTOMERORDERLIST_RESULT_FIELD.has_default_value = false
GCCUSTOMERORDERLIST_RESULT_FIELD.default_value = 0
GCCUSTOMERORDERLIST_RESULT_FIELD.type = 5
GCCUSTOMERORDERLIST_RESULT_FIELD.cpp_type = 1

GCCUSTOMERORDERLIST_MSG_FIELD.name = "msg"
GCCUSTOMERORDERLIST_MSG_FIELD.full_name = ".prootc.gccustomerorderlist.msg"
GCCUSTOMERORDERLIST_MSG_FIELD.number = 2
GCCUSTOMERORDERLIST_MSG_FIELD.index = 1
GCCUSTOMERORDERLIST_MSG_FIELD.label = 1
GCCUSTOMERORDERLIST_MSG_FIELD.has_default_value = false
GCCUSTOMERORDERLIST_MSG_FIELD.default_value = ""
GCCUSTOMERORDERLIST_MSG_FIELD.type = 9
GCCUSTOMERORDERLIST_MSG_FIELD.cpp_type = 9

GCCUSTOMERORDERLIST_VOLIST_FIELD.name = "volist"
GCCUSTOMERORDERLIST_VOLIST_FIELD.full_name = ".prootc.gccustomerorderlist.volist"
GCCUSTOMERORDERLIST_VOLIST_FIELD.number = 3
GCCUSTOMERORDERLIST_VOLIST_FIELD.index = 2
GCCUSTOMERORDERLIST_VOLIST_FIELD.label = 3
GCCUSTOMERORDERLIST_VOLIST_FIELD.has_default_value = false
GCCUSTOMERORDERLIST_VOLIST_FIELD.default_value = {}
GCCUSTOMERORDERLIST_VOLIST_FIELD.message_type = ST_ORDER_PB_VENDORORDERINFO
GCCUSTOMERORDERLIST_VOLIST_FIELD.type = 11
GCCUSTOMERORDERLIST_VOLIST_FIELD.cpp_type = 10

GCCUSTOMERORDERLIST_ORDERTYPE_FIELD.name = "ordertype"
GCCUSTOMERORDERLIST_ORDERTYPE_FIELD.full_name = ".prootc.gccustomerorderlist.ordertype"
GCCUSTOMERORDERLIST_ORDERTYPE_FIELD.number = 4
GCCUSTOMERORDERLIST_ORDERTYPE_FIELD.index = 3
GCCUSTOMERORDERLIST_ORDERTYPE_FIELD.label = 1
GCCUSTOMERORDERLIST_ORDERTYPE_FIELD.has_default_value = false
GCCUSTOMERORDERLIST_ORDERTYPE_FIELD.default_value = 0
GCCUSTOMERORDERLIST_ORDERTYPE_FIELD.type = 5
GCCUSTOMERORDERLIST_ORDERTYPE_FIELD.cpp_type = 1

GCCUSTOMERORDERLIST_PAGENUM_FIELD.name = "pagenum"
GCCUSTOMERORDERLIST_PAGENUM_FIELD.full_name = ".prootc.gccustomerorderlist.pagenum"
GCCUSTOMERORDERLIST_PAGENUM_FIELD.number = 5
GCCUSTOMERORDERLIST_PAGENUM_FIELD.index = 4
GCCUSTOMERORDERLIST_PAGENUM_FIELD.label = 1
GCCUSTOMERORDERLIST_PAGENUM_FIELD.has_default_value = false
GCCUSTOMERORDERLIST_PAGENUM_FIELD.default_value = 0
GCCUSTOMERORDERLIST_PAGENUM_FIELD.type = 5
GCCUSTOMERORDERLIST_PAGENUM_FIELD.cpp_type = 1

GCCUSTOMERORDERLIST_PAGESIZE_FIELD.name = "pagesize"
GCCUSTOMERORDERLIST_PAGESIZE_FIELD.full_name = ".prootc.gccustomerorderlist.pagesize"
GCCUSTOMERORDERLIST_PAGESIZE_FIELD.number = 6
GCCUSTOMERORDERLIST_PAGESIZE_FIELD.index = 5
GCCUSTOMERORDERLIST_PAGESIZE_FIELD.label = 1
GCCUSTOMERORDERLIST_PAGESIZE_FIELD.has_default_value = false
GCCUSTOMERORDERLIST_PAGESIZE_FIELD.default_value = 0
GCCUSTOMERORDERLIST_PAGESIZE_FIELD.type = 5
GCCUSTOMERORDERLIST_PAGESIZE_FIELD.cpp_type = 1

GCCUSTOMERORDERLIST_COINID_FIELD.name = "coinid"
GCCUSTOMERORDERLIST_COINID_FIELD.full_name = ".prootc.gccustomerorderlist.coinid"
GCCUSTOMERORDERLIST_COINID_FIELD.number = 7
GCCUSTOMERORDERLIST_COINID_FIELD.index = 6
GCCUSTOMERORDERLIST_COINID_FIELD.label = 1
GCCUSTOMERORDERLIST_COINID_FIELD.has_default_value = false
GCCUSTOMERORDERLIST_COINID_FIELD.default_value = 0
GCCUSTOMERORDERLIST_COINID_FIELD.type = 5
GCCUSTOMERORDERLIST_COINID_FIELD.cpp_type = 1

GCCUSTOMERORDERLIST.name = "gccustomerorderlist"
GCCUSTOMERORDERLIST.full_name = ".prootc.gccustomerorderlist"
GCCUSTOMERORDERLIST.nested_types = {}
GCCUSTOMERORDERLIST.enum_types = {}
GCCUSTOMERORDERLIST.fields = {GCCUSTOMERORDERLIST_RESULT_FIELD, GCCUSTOMERORDERLIST_MSG_FIELD, GCCUSTOMERORDERLIST_VOLIST_FIELD, GCCUSTOMERORDERLIST_ORDERTYPE_FIELD, GCCUSTOMERORDERLIST_PAGENUM_FIELD, GCCUSTOMERORDERLIST_PAGESIZE_FIELD, GCCUSTOMERORDERLIST_COINID_FIELD}
GCCUSTOMERORDERLIST.is_extendable = false
GCCUSTOMERORDERLIST.extensions = {}
CGINCOMEEXPENDITUREDETAIL_USERID_FIELD.name = "userid"
CGINCOMEEXPENDITUREDETAIL_USERID_FIELD.full_name = ".prootc.cgincomeexpendituredetail.userid"
CGINCOMEEXPENDITUREDETAIL_USERID_FIELD.number = 1
CGINCOMEEXPENDITUREDETAIL_USERID_FIELD.index = 0
CGINCOMEEXPENDITUREDETAIL_USERID_FIELD.label = 1
CGINCOMEEXPENDITUREDETAIL_USERID_FIELD.has_default_value = false
CGINCOMEEXPENDITUREDETAIL_USERID_FIELD.default_value = 0
CGINCOMEEXPENDITUREDETAIL_USERID_FIELD.type = 5
CGINCOMEEXPENDITUREDETAIL_USERID_FIELD.cpp_type = 1

CGINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.name = "detailtype"
CGINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.full_name = ".prootc.cgincomeexpendituredetail.detailtype"
CGINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.number = 2
CGINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.index = 1
CGINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.label = 1
CGINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.has_default_value = false
CGINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.default_value = 0
CGINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.type = 5
CGINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.cpp_type = 1

CGINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD.name = "pagenum"
CGINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD.full_name = ".prootc.cgincomeexpendituredetail.pagenum"
CGINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD.number = 3
CGINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD.index = 2
CGINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD.label = 1
CGINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD.has_default_value = false
CGINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD.default_value = 0
CGINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD.type = 5
CGINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD.cpp_type = 1

CGINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD.name = "pagesize"
CGINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD.full_name = ".prootc.cgincomeexpendituredetail.pagesize"
CGINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD.number = 4
CGINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD.index = 3
CGINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD.label = 1
CGINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD.has_default_value = false
CGINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD.default_value = 0
CGINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD.type = 5
CGINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD.cpp_type = 1

CGINCOMEEXPENDITUREDETAIL_COINID_FIELD.name = "coinid"
CGINCOMEEXPENDITUREDETAIL_COINID_FIELD.full_name = ".prootc.cgincomeexpendituredetail.coinid"
CGINCOMEEXPENDITUREDETAIL_COINID_FIELD.number = 5
CGINCOMEEXPENDITUREDETAIL_COINID_FIELD.index = 4
CGINCOMEEXPENDITUREDETAIL_COINID_FIELD.label = 1
CGINCOMEEXPENDITUREDETAIL_COINID_FIELD.has_default_value = false
CGINCOMEEXPENDITUREDETAIL_COINID_FIELD.default_value = 0
CGINCOMEEXPENDITUREDETAIL_COINID_FIELD.type = 5
CGINCOMEEXPENDITUREDETAIL_COINID_FIELD.cpp_type = 1

CGINCOMEEXPENDITUREDETAIL.name = "cgincomeexpendituredetail"
CGINCOMEEXPENDITUREDETAIL.full_name = ".prootc.cgincomeexpendituredetail"
CGINCOMEEXPENDITUREDETAIL.nested_types = {}
CGINCOMEEXPENDITUREDETAIL.enum_types = {}
CGINCOMEEXPENDITUREDETAIL.fields = {CGINCOMEEXPENDITUREDETAIL_USERID_FIELD, CGINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD, CGINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD, CGINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD, CGINCOMEEXPENDITUREDETAIL_COINID_FIELD}
CGINCOMEEXPENDITUREDETAIL.is_extendable = false
CGINCOMEEXPENDITUREDETAIL.extensions = {}
GCINCOMEEXPENDITUREDETAIL_RESULT_FIELD.name = "result"
GCINCOMEEXPENDITUREDETAIL_RESULT_FIELD.full_name = ".prootc.gcincomeexpendituredetail.result"
GCINCOMEEXPENDITUREDETAIL_RESULT_FIELD.number = 1
GCINCOMEEXPENDITUREDETAIL_RESULT_FIELD.index = 0
GCINCOMEEXPENDITUREDETAIL_RESULT_FIELD.label = 1
GCINCOMEEXPENDITUREDETAIL_RESULT_FIELD.has_default_value = false
GCINCOMEEXPENDITUREDETAIL_RESULT_FIELD.default_value = 0
GCINCOMEEXPENDITUREDETAIL_RESULT_FIELD.type = 5
GCINCOMEEXPENDITUREDETAIL_RESULT_FIELD.cpp_type = 1

GCINCOMEEXPENDITUREDETAIL_MSG_FIELD.name = "msg"
GCINCOMEEXPENDITUREDETAIL_MSG_FIELD.full_name = ".prootc.gcincomeexpendituredetail.msg"
GCINCOMEEXPENDITUREDETAIL_MSG_FIELD.number = 2
GCINCOMEEXPENDITUREDETAIL_MSG_FIELD.index = 1
GCINCOMEEXPENDITUREDETAIL_MSG_FIELD.label = 1
GCINCOMEEXPENDITUREDETAIL_MSG_FIELD.has_default_value = false
GCINCOMEEXPENDITUREDETAIL_MSG_FIELD.default_value = ""
GCINCOMEEXPENDITUREDETAIL_MSG_FIELD.type = 9
GCINCOMEEXPENDITUREDETAIL_MSG_FIELD.cpp_type = 9

GCINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.name = "detailtype"
GCINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.full_name = ".prootc.gcincomeexpendituredetail.detailtype"
GCINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.number = 3
GCINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.index = 2
GCINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.label = 1
GCINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.has_default_value = false
GCINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.default_value = 0
GCINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.type = 5
GCINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD.cpp_type = 1

GCINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD.name = "pagenum"
GCINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD.full_name = ".prootc.gcincomeexpendituredetail.pagenum"
GCINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD.number = 4
GCINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD.index = 3
GCINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD.label = 1
GCINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD.has_default_value = false
GCINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD.default_value = 0
GCINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD.type = 5
GCINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD.cpp_type = 1

GCINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD.name = "pagesize"
GCINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD.full_name = ".prootc.gcincomeexpendituredetail.pagesize"
GCINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD.number = 5
GCINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD.index = 4
GCINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD.label = 1
GCINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD.has_default_value = false
GCINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD.default_value = 0
GCINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD.type = 5
GCINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD.cpp_type = 1

GCINCOMEEXPENDITUREDETAIL_PARAMETER1_FIELD.name = "parameter1"
GCINCOMEEXPENDITUREDETAIL_PARAMETER1_FIELD.full_name = ".prootc.gcincomeexpendituredetail.parameter1"
GCINCOMEEXPENDITUREDETAIL_PARAMETER1_FIELD.number = 6
GCINCOMEEXPENDITUREDETAIL_PARAMETER1_FIELD.index = 5
GCINCOMEEXPENDITUREDETAIL_PARAMETER1_FIELD.label = 1
GCINCOMEEXPENDITUREDETAIL_PARAMETER1_FIELD.has_default_value = false
GCINCOMEEXPENDITUREDETAIL_PARAMETER1_FIELD.default_value = ""
GCINCOMEEXPENDITUREDETAIL_PARAMETER1_FIELD.type = 9
GCINCOMEEXPENDITUREDETAIL_PARAMETER1_FIELD.cpp_type = 9

GCINCOMEEXPENDITUREDETAIL_PARAMETER2_FIELD.name = "parameter2"
GCINCOMEEXPENDITUREDETAIL_PARAMETER2_FIELD.full_name = ".prootc.gcincomeexpendituredetail.parameter2"
GCINCOMEEXPENDITUREDETAIL_PARAMETER2_FIELD.number = 7
GCINCOMEEXPENDITUREDETAIL_PARAMETER2_FIELD.index = 6
GCINCOMEEXPENDITUREDETAIL_PARAMETER2_FIELD.label = 1
GCINCOMEEXPENDITUREDETAIL_PARAMETER2_FIELD.has_default_value = false
GCINCOMEEXPENDITUREDETAIL_PARAMETER2_FIELD.default_value = ""
GCINCOMEEXPENDITUREDETAIL_PARAMETER2_FIELD.type = 9
GCINCOMEEXPENDITUREDETAIL_PARAMETER2_FIELD.cpp_type = 9

GCINCOMEEXPENDITUREDETAIL_DETAILLIST_FIELD.name = "detaillist"
GCINCOMEEXPENDITUREDETAIL_DETAILLIST_FIELD.full_name = ".prootc.gcincomeexpendituredetail.detaillist"
GCINCOMEEXPENDITUREDETAIL_DETAILLIST_FIELD.number = 8
GCINCOMEEXPENDITUREDETAIL_DETAILLIST_FIELD.index = 7
GCINCOMEEXPENDITUREDETAIL_DETAILLIST_FIELD.label = 3
GCINCOMEEXPENDITUREDETAIL_DETAILLIST_FIELD.has_default_value = false
GCINCOMEEXPENDITUREDETAIL_DETAILLIST_FIELD.default_value = {}
GCINCOMEEXPENDITUREDETAIL_DETAILLIST_FIELD.message_type = ST_ORDER_PB_INCOMEEXPENDITUREDETAIL
GCINCOMEEXPENDITUREDETAIL_DETAILLIST_FIELD.type = 11
GCINCOMEEXPENDITUREDETAIL_DETAILLIST_FIELD.cpp_type = 10

GCINCOMEEXPENDITUREDETAIL_COINID_FIELD.name = "coinid"
GCINCOMEEXPENDITUREDETAIL_COINID_FIELD.full_name = ".prootc.gcincomeexpendituredetail.coinid"
GCINCOMEEXPENDITUREDETAIL_COINID_FIELD.number = 9
GCINCOMEEXPENDITUREDETAIL_COINID_FIELD.index = 8
GCINCOMEEXPENDITUREDETAIL_COINID_FIELD.label = 1
GCINCOMEEXPENDITUREDETAIL_COINID_FIELD.has_default_value = false
GCINCOMEEXPENDITUREDETAIL_COINID_FIELD.default_value = 0
GCINCOMEEXPENDITUREDETAIL_COINID_FIELD.type = 5
GCINCOMEEXPENDITUREDETAIL_COINID_FIELD.cpp_type = 1

GCINCOMEEXPENDITUREDETAIL.name = "gcincomeexpendituredetail"
GCINCOMEEXPENDITUREDETAIL.full_name = ".prootc.gcincomeexpendituredetail"
GCINCOMEEXPENDITUREDETAIL.nested_types = {}
GCINCOMEEXPENDITUREDETAIL.enum_types = {}
GCINCOMEEXPENDITUREDETAIL.fields = {GCINCOMEEXPENDITUREDETAIL_RESULT_FIELD, GCINCOMEEXPENDITUREDETAIL_MSG_FIELD, GCINCOMEEXPENDITUREDETAIL_DETAILTYPE_FIELD, GCINCOMEEXPENDITUREDETAIL_PAGENUM_FIELD, GCINCOMEEXPENDITUREDETAIL_PAGESIZE_FIELD, GCINCOMEEXPENDITUREDETAIL_PARAMETER1_FIELD, GCINCOMEEXPENDITUREDETAIL_PARAMETER2_FIELD, GCINCOMEEXPENDITUREDETAIL_DETAILLIST_FIELD, GCINCOMEEXPENDITUREDETAIL_COINID_FIELD}
GCINCOMEEXPENDITUREDETAIL.is_extendable = false
GCINCOMEEXPENDITUREDETAIL.extensions = {}
CGORDERACTIVATION_USERID_FIELD.name = "userid"
CGORDERACTIVATION_USERID_FIELD.full_name = ".prootc.cgorderactivation.userid"
CGORDERACTIVATION_USERID_FIELD.number = 1
CGORDERACTIVATION_USERID_FIELD.index = 0
CGORDERACTIVATION_USERID_FIELD.label = 1
CGORDERACTIVATION_USERID_FIELD.has_default_value = false
CGORDERACTIVATION_USERID_FIELD.default_value = 0
CGORDERACTIVATION_USERID_FIELD.type = 5
CGORDERACTIVATION_USERID_FIELD.cpp_type = 1

CGORDERACTIVATION_DEALID_FIELD.name = "dealid"
CGORDERACTIVATION_DEALID_FIELD.full_name = ".prootc.cgorderactivation.dealid"
CGORDERACTIVATION_DEALID_FIELD.number = 2
CGORDERACTIVATION_DEALID_FIELD.index = 1
CGORDERACTIVATION_DEALID_FIELD.label = 1
CGORDERACTIVATION_DEALID_FIELD.has_default_value = false
CGORDERACTIVATION_DEALID_FIELD.default_value = 0
CGORDERACTIVATION_DEALID_FIELD.type = 5
CGORDERACTIVATION_DEALID_FIELD.cpp_type = 1

CGORDERACTIVATION_FUNDPWD_FIELD.name = "fundpwd"
CGORDERACTIVATION_FUNDPWD_FIELD.full_name = ".prootc.cgorderactivation.fundpwd"
CGORDERACTIVATION_FUNDPWD_FIELD.number = 3
CGORDERACTIVATION_FUNDPWD_FIELD.index = 2
CGORDERACTIVATION_FUNDPWD_FIELD.label = 1
CGORDERACTIVATION_FUNDPWD_FIELD.has_default_value = false
CGORDERACTIVATION_FUNDPWD_FIELD.default_value = ""
CGORDERACTIVATION_FUNDPWD_FIELD.type = 9
CGORDERACTIVATION_FUNDPWD_FIELD.cpp_type = 9

CGORDERACTIVATION.name = "cgorderactivation"
CGORDERACTIVATION.full_name = ".prootc.cgorderactivation"
CGORDERACTIVATION.nested_types = {}
CGORDERACTIVATION.enum_types = {}
CGORDERACTIVATION.fields = {CGORDERACTIVATION_USERID_FIELD, CGORDERACTIVATION_DEALID_FIELD, CGORDERACTIVATION_FUNDPWD_FIELD}
CGORDERACTIVATION.is_extendable = false
CGORDERACTIVATION.extensions = {}
GCORDERACTIVATION_RESULT_FIELD.name = "result"
GCORDERACTIVATION_RESULT_FIELD.full_name = ".prootc.gcorderactivation.result"
GCORDERACTIVATION_RESULT_FIELD.number = 1
GCORDERACTIVATION_RESULT_FIELD.index = 0
GCORDERACTIVATION_RESULT_FIELD.label = 1
GCORDERACTIVATION_RESULT_FIELD.has_default_value = false
GCORDERACTIVATION_RESULT_FIELD.default_value = 0
GCORDERACTIVATION_RESULT_FIELD.type = 5
GCORDERACTIVATION_RESULT_FIELD.cpp_type = 1

GCORDERACTIVATION_MSG_FIELD.name = "msg"
GCORDERACTIVATION_MSG_FIELD.full_name = ".prootc.gcorderactivation.msg"
GCORDERACTIVATION_MSG_FIELD.number = 2
GCORDERACTIVATION_MSG_FIELD.index = 1
GCORDERACTIVATION_MSG_FIELD.label = 1
GCORDERACTIVATION_MSG_FIELD.has_default_value = false
GCORDERACTIVATION_MSG_FIELD.default_value = ""
GCORDERACTIVATION_MSG_FIELD.type = 9
GCORDERACTIVATION_MSG_FIELD.cpp_type = 9

GCORDERACTIVATION_COINFO_FIELD.name = "coinfo"
GCORDERACTIVATION_COINFO_FIELD.full_name = ".prootc.gcorderactivation.coinfo"
GCORDERACTIVATION_COINFO_FIELD.number = 3
GCORDERACTIVATION_COINFO_FIELD.index = 2
GCORDERACTIVATION_COINFO_FIELD.label = 1
GCORDERACTIVATION_COINFO_FIELD.has_default_value = false
GCORDERACTIVATION_COINFO_FIELD.default_value = nil
GCORDERACTIVATION_COINFO_FIELD.message_type = ST_ORDER_PB_CUSTOMERORDERINFO
GCORDERACTIVATION_COINFO_FIELD.type = 11
GCORDERACTIVATION_COINFO_FIELD.cpp_type = 10

GCORDERACTIVATION.name = "gcorderactivation"
GCORDERACTIVATION.full_name = ".prootc.gcorderactivation"
GCORDERACTIVATION.nested_types = {}
GCORDERACTIVATION.enum_types = {}
GCORDERACTIVATION.fields = {GCORDERACTIVATION_RESULT_FIELD, GCORDERACTIVATION_MSG_FIELD, GCORDERACTIVATION_COINFO_FIELD}
GCORDERACTIVATION.is_extendable = false
GCORDERACTIVATION.extensions = {}

cgappeal = protobuf.Message(CGAPPEAL)
cgappealdetail = protobuf.Message(CGAPPEALDETAIL)
cgappeallist = protobuf.Message(CGAPPEALLIST)
cgappealreply = protobuf.Message(CGAPPEALREPLY)
cgcancel = protobuf.Message(CGCANCEL)
cgcancelleftcount = protobuf.Message(CGCANCELLEFTCOUNT)
cgcustomerorderlist = protobuf.Message(CGCUSTOMERORDERLIST)
cgdeal = protobuf.Message(CGDEAL)
cgdisableorder = protobuf.Message(CGDISABLEORDER)
cghangorder = protobuf.Message(CGHANGORDER)
cgincomeexpendituredetail = protobuf.Message(CGINCOMEEXPENDITUREDETAIL)
cgmodifyorder = protobuf.Message(CGMODIFYORDER)
cgorderactivation = protobuf.Message(CGORDERACTIVATION)
cgorderlist = protobuf.Message(CGORDERLIST)
cgpaid = protobuf.Message(CGPAID)
cgpass = protobuf.Message(CGPASS)
cgquickdeal = protobuf.Message(CGQUICKDEAL)
gcappeal = protobuf.Message(GCAPPEAL)
gcappealdetail = protobuf.Message(GCAPPEALDETAIL)
gcappeallist = protobuf.Message(GCAPPEALLIST)
gcappealreply = protobuf.Message(GCAPPEALREPLY)
gccancel = protobuf.Message(GCCANCEL)
gccancelleftcount = protobuf.Message(GCCANCELLEFTCOUNT)
gccustomerorderlist = protobuf.Message(GCCUSTOMERORDERLIST)
gcdeal = protobuf.Message(GCDEAL)
gcdisableorder = protobuf.Message(GCDISABLEORDER)
gchangorder = protobuf.Message(GCHANGORDER)
gcincomeexpendituredetail = protobuf.Message(GCINCOMEEXPENDITUREDETAIL)
gcmodifyorder = protobuf.Message(GCMODIFYORDER)
gcorderactivation = protobuf.Message(GCORDERACTIVATION)
gcorderlist = protobuf.Message(GCORDERLIST)
gcpaid = protobuf.Message(GCPAID)
gcpass = protobuf.Message(GCPASS)
gcquickdeal = protobuf.Message(GCQUICKDEAL)

----------nimol modify---------
MSG_ORDER_PB_CGAPPEAL = CGAPPEAL
MSG_ORDER_PB_CGAPPEALDETAIL = CGAPPEALDETAIL
MSG_ORDER_PB_CGAPPEALLIST = CGAPPEALLIST
MSG_ORDER_PB_CGAPPEALREPLY = CGAPPEALREPLY
MSG_ORDER_PB_CGCANCEL = CGCANCEL
MSG_ORDER_PB_CGCANCELLEFTCOUNT = CGCANCELLEFTCOUNT
MSG_ORDER_PB_CGCUSTOMERORDERLIST = CGCUSTOMERORDERLIST
MSG_ORDER_PB_CGDEAL = CGDEAL
MSG_ORDER_PB_CGDISABLEORDER = CGDISABLEORDER
MSG_ORDER_PB_CGHANGORDER = CGHANGORDER
MSG_ORDER_PB_CGINCOMEEXPENDITUREDETAIL = CGINCOMEEXPENDITUREDETAIL
MSG_ORDER_PB_CGMODIFYORDER = CGMODIFYORDER
MSG_ORDER_PB_CGORDERACTIVATION = CGORDERACTIVATION
MSG_ORDER_PB_CGORDERLIST = CGORDERLIST
MSG_ORDER_PB_CGPAID = CGPAID
MSG_ORDER_PB_CGPASS = CGPASS
MSG_ORDER_PB_CGQUICKDEAL = CGQUICKDEAL
MSG_ORDER_PB_GCAPPEAL = GCAPPEAL
MSG_ORDER_PB_GCAPPEALDETAIL = GCAPPEALDETAIL
MSG_ORDER_PB_GCAPPEALLIST = GCAPPEALLIST
MSG_ORDER_PB_GCAPPEALREPLY = GCAPPEALREPLY
MSG_ORDER_PB_GCCANCEL = GCCANCEL
MSG_ORDER_PB_GCCANCELLEFTCOUNT = GCCANCELLEFTCOUNT
MSG_ORDER_PB_GCCUSTOMERORDERLIST = GCCUSTOMERORDERLIST
MSG_ORDER_PB_GCDEAL = GCDEAL
MSG_ORDER_PB_GCDISABLEORDER = GCDISABLEORDER
MSG_ORDER_PB_GCHANGORDER = GCHANGORDER
MSG_ORDER_PB_GCINCOMEEXPENDITUREDETAIL = GCINCOMEEXPENDITUREDETAIL
MSG_ORDER_PB_GCMODIFYORDER = GCMODIFYORDER
MSG_ORDER_PB_GCORDERACTIVATION = GCORDERACTIVATION
MSG_ORDER_PB_GCORDERLIST = GCORDERLIST
MSG_ORDER_PB_GCPAID = GCPAID
MSG_ORDER_PB_GCPASS = GCPASS
MSG_ORDER_PB_GCQUICKDEAL = GCQUICKDEAL
