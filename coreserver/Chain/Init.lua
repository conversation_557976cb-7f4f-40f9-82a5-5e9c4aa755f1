

require("Chain.Model.AddressPreModel")
require("Chain.Model.SynchroChainDataModel")


require("Chain.Services.ChainServices")


require("Chain.Worker.GetPreAddress")
require("Chain.Worker.SynchroChainWork")
require("Chain.Worker.GetUsdtFreePrice")


g_redisIndex[AddressPreModel.redis_index] = {index = g_redisInfo.redis_two, key = UserInfoModel.redis_index, link = 1} 
g_redisIndex[SynchroChainDataModel.redis_index] = {index = g_chain_redisInfo.database, key = OnlineModel.redis_index, link = 1} 


