ChainServices = {}


function ChainServices.Init(userid)
	
	--通知挂卖单的人，有卖单，需要付款
	ChainServices.redis_remote = redisConnect.new(g_chain_redisInfo.host,g_chain_redisInfo.pass,g_chain_redisInfo.port)
	return true
end


function ChainServices.ServerLoop()
	
	--同步去取数据
	local tm = TimeUtils.GetTableTime()
	if false == AddressPreModel.ExistGetWorker()  and math.mod(tm.sec,5) == 0 then

		processWork("GetPreAddress",TimeUtils.GetTimeString())   --如果没有的话，那么立刻去取地址回来

	end
	
	--暂定每10秒钟从结点服务器同步一次数据到中心服务器
	
	if math.mod(tm.sec, 5) == 0 then
		--每5秒同步一次
		processWork("SynchroChainWork", TimeUtils.GetTimeString())
	end
	
	if tm.sec == 0 then
		--每一分钟同步一次
		processWork("GetUsdtFreePrice", TimeUtils.GetTimeString())
	end
	
	
	return true
end





