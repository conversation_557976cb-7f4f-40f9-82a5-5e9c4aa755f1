module("GetPreAddress", package.seeall)

function work(buffer)


	if true == AddressPreModel.ExistGetWorker() then
		return
	end
	
	AddressPreModel.MarkGetWorker()   --做好这个标记，防止在这里重复去取地址
	

	local sqlCase = "SELECT DISTINCT channel from dy_channel_info where id>0"
	
	mysqlItem:executeQuery(sqlCase)
	local channelList = {}
	for i = 1,1000 do
		local getData = mysqlItem:fetch()
		if getData == nil then
			break
		end
		table.insert(channelList, getData)
	end


	local sendData = {}
	sendData["jsonrpc"] = "2.0"
	sendData["method"] = "get_eth_address"
	sendData["id"] = 4
	sendData["params"] = {}
	sendData["params"][1] = "OTC"
	
	for k, channel in ipairs(channelList) do
		sendData["params"][1] = channel
		local getLen = AddressPreModel.GetPreAddressLen(channel)
		
		for i = 1, (g_marketDefine.max_pre_address_len - getLen) do

			local getData = HttpPostRPC(g_url_chain__node, luajson.encode(sendData))

			getArr = luajson.decode(getData)
			
			if type(getArr) == "table" then
				if getArr['result'] ~= nil and getArr['result']['msg'] == "OK" then
					AddressPreModel.PushNewAddress( getArr['result']['address'],'password',channel)
				else
					LogFile("get_eth_address", "get error data="..getData)
				end
			else
				LogFile("get_eth_address", "parse error data="..getData)
			end
			
		end
		
		
	end
	
	--[[
	sendData = {}
	sendData["jsonrpc"] = "2.0"
	sendData["method"] = "get_trx_address"
	sendData["id"] = 4
	sendData["params"] = {}
	sendData["params"][1] = "OTC"
	
	for k, channel in ipairs(channelList) do
		sendData["params"][1] = channel
		local getLen = AddressPreModel.GetPreTRCAddressLen(channel)
		
		for i = 1, (g_marketDefine.max_pre_address_len - getLen) do

			local getData = HttpPostRPC(g_url_trc_node, luajson.encode(sendData))

			getArr = luajson.decode(getData)
			
			if type(getArr) == "table" then
				if getArr['result'] ~= nil and getArr['result']['msg'] == "OK" then
					AddressPreModel.PushNewTRCAddress( getArr['result']['address'],'password',channel)
				else
					LogFile("get_trx_address", "get error data="..getData)
				end
			else
				LogFile("get_trx_address", "parse error data="..getData)
			end
			
		end
		
		
	end	
	]]
	
	AddressPreModel.DelGetWorker()

end

