module("GetUsdtFreePrice", package.seeall)

function work(buffer)


	local getData = HttpGet("http://8.210.121.220:8888/api/usdt", "")
	


	if string.find(getData,"buy") == nil or string.find(getData,"sell") == nil then
		LogFile("GetPrice", getData)
		return
	end
	
	
	if getData ~= nil and getData ~= "" then
		local jsonData = luajson.decode(getData)
		if jsonData['buy'] ~= nil  then
			
			local buyPrice = jsonData['buy']
			local sellPrice = jsonData['sell']
			if tonumber(buyPrice) > tonumber(sellPrice) then
				buyPrice = sellPrice
			end
			local sqlCase = "update dy_coin_info set free_price="..buyPrice..", buy_price="..buyPrice..", sell_price="..buyPrice.." where id>0 and is_change=0 and (coin_id=2003 or coin_id=2004) "

			LogFile("priceDetails", sqlCase)
			mysqlItem:execute(sqlCase)
			
			local userList = OnlineModel.GetOnlineUserList()
			for k,v in ipairs(userList) do 
				local uInfo = UserInfoModel.GetUserInfo(v) 
				if uInfo ~= nil then
					local gcmsg = msg_order2_pb.gcexchangerate()
					gcmsg.buyrate = tostring(CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.hang_buy, g_marketDefine.currency_type.USDT, true, uInfo.userid))
					gcmsg.sellrate = tostring(CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.hang_sell, g_marketDefine.currency_type.USDT, true, uInfo.userid))
					gcmsg.result = 0
					SendMessage(uInfo.userid, PacketCode[2050].client, gcmsg:ByteSize(),gcmsg:SerializeToString())
				end
			end
		end
	end
end

