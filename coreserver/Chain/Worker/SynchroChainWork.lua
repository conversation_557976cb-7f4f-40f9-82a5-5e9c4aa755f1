module("SynchroChainWork", package.seeall)

function work(buffer)

	--这个是在缓存中，把缓存的数据同步到中心数据库中
	
	--取到了新的数据。然后通知加币，加币的时候，同时通知用户加钱
	--同时在币币交易中，插入记录

	local redisRemote = redisConnect.new(g_chain_redisInfo.host,g_chain_redisInfo.pass,g_chain_redisInfo.port)
	
	local sqlCase = "SELECT DISTINCT channel from dy_channel_info where id>0"
	
	mysqlItem:executeQuery(sqlCase)
	local channelList = {}
	table.insert( channelList, "DEFAULT" )
	for i = 1,1000 do
		local getData = mysqlItem:fetch()
		if getData == nil then
			break
		end
		table.insert(channelList, getData)
	end
	
	
	
	
	for k,channel in ipairs( channelList ) do
	
		while true do
			local getData =  redisRemote:rpop( g_chain_redisInfo.blockchain_txlist..channel, SynchroChainDataModel.redis_index )  --redisRemote:rpop( SynchroChainDataModel.blockchain_txlist..channel, SynchroChainDataModel.redis_index )
												
			if getData == nil then
				break
			end

			local arrData = luajson.decode(getData)
			
			if arrData['tx_type'] == nil then
				--未知的交易类型
				LogFile("chain-unknow",getData)
			else
				
				if arrData['tx_type'] == "transfer_in" then
					--转入
					SynchroChainDataModel.InsertInto(arrData)
					
				elseif arrData['tx_type'] == "fee_out" then
					--给托管地址打手续费
					SynchroChainDataModel.InsertIntoFee(arrData)
					
				elseif arrData['tx_type'] == "collect_in" then
					--自己归集
					SynchroChainDataModel.InsertIntoCollect(arrData)
				else
					LogFile("chain-unknow",getData)
				end
				
			end
			
		end
	end
	
	

	
end