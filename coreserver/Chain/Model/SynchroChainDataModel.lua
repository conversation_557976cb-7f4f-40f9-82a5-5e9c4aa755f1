
SynchroChainDataModel = {}

SynchroChainDataModel.redis_index = "redis_synchrochain"


SynchroChainDataModel.tx_type = {}




function SynchroChainDataModel.Init()
	SynchroChainDataModel.tx_type[1] = "transfer_in"    --从外面转币进来。
	SynchroChainDataModel.tx_type[2] = "transfer_out"   --从里面转币出去
	SynchroChainDataModel.tx_type[3] = "free_out"       --给托管地址打手续费
	SynchroChainDataModel.tx_type[4] = "tx_type"        --归集到冷钱包

end


function SynchroChainDataModel.InsertInto(arrData)

	--先检查是不是USDT
	if arrData["tx_data"] ~= "TetherUSD" then
		LogFile("in_exist", "错误的资产类型："..luajson.encode(arrData))
		return   ----------这里不能往下走了
	end
	--先检查tx_id存不存在
	
	local sqlCase = "select id from dy_block_chain_trans where tx_id='"..arrData['tx_id'].."'"
	mysqlItem:executeQuery(sqlCase)
	
	local sqlData = mysqlItem:fetch()
	
	if sqlData ~= nil then
		--这个tx_id已经存在，记录到log中
		LogFile("in_exist", luajson.encode(arrData))
		return   ----------这里不能往下走了
	end
	
	local sqlCase = "select id from dy_customer_order where chain_addr='"..arrData['to_addr']
		.."' and pay_id=8401 and status=1"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData ~= nil then
		ThreadManager.DealLock(sqlData)
		local coinfo = CustomerOrderModel.GetCustomerOrderInfo(sqlData)
		if coinfo ~= nil then
			local uInfo = UserInfoModel.GetUserInfo(coinfo.customeruserid)
			if uInfo ~= nil then
				local userid = 0
				local hostingAdds = coinfo.owership == 0 and 2 or 3
				local hostingStatus = 1
				local relatedOrder = 0
				local addrType = 0
				local addrName = 0
				local platformID = 0
				if arrData['chain_id'] == "ETH_CHAIN" then
					addrType = 101
					addrName = "ERC-20"
				elseif arrData['chain_id'] == "TRX_CHAIN" then
					addrType = 102
					addrName = "TRC-20"
				end
				
				local cAmount = math.floor(tonumber(coinfo.amount) * 10000)/10000
				if cAmount == (math.floor(tonumber(arrData['amount'] * 10000))/10000) then
					VendorOrderService.PassOrderCoinPay(uInfo, coinfo, arrData['tx_id'])
					userid = uInfo.userid
					hostingStatus = 2
					relatedOrder = coinfo.dealid
					platformID = uInfo.platformid
				end
		
				--币支付订单
				local sqlCase = "INSERT INTO `dy_block_chain_trans`(`tx_id`,`tx_type`,`chain_id`,`tx_data`,`from_addr`,`to_addr`,`amount`,`tx_fee`,"
				.."`tx_status`,`tx_time`,`recd_status`,`channel`,`userid`,`specific_type`,`get_amount`,`hosting_adds`,`hosting_status`,`related_order`,`addr_type`,`addr_name`,`platform_id`)"
				.."VALUES('"..arrData['tx_id'].."','1','"..arrData['chain_id'].."','"..arrData['tx_data'].."','"..arrData['from_addr'].."','"..arrData['to_addr'].."',"..arrData['amount']
				..",0,"..arrData['tx_status']..",'"..arrData['tx_time'].."',106,'"..arrData['channel'].."',"
				..userid..",5,"..arrData['amount']..","..hostingAdds..","..hostingStatus..",'"..relatedOrder.."',"..addrType..",'"..addrName.."',"..platformID..")"
				mysqlItem:execute(sqlCase)
				
				local sqlCase = "select id from dy_block_chain_trans where tx_id='"..arrData['tx_id'].."' and id>0"
				
				mysqlItem:executeQuery(sqlCase)
				
				local sqlData = mysqlItem:fetch()
				if sqlData == nil then
					LogFile("in_error", "insert into block chain failed tx_id="..arrData['tx_id']..", data="..luajson.decode(arrData))
					return false
				end
				ThreadManager.DealUnLock(coinfo.dealid)
				return 
			end
		end
		ThreadManager.DealLock(sqlData)
	end
	
	
	--检查是托管地址还是私人地址
	local isPrivate = false
	local userid = 0
	local hostingAdds = 0
	local hostingStatus = 0
	local matchStatus = 0
	local matchRmg = "TXID不符"
	local relatedOrder = ""
	local fee = 0
	local addrType = 0
	local addrName = 0
	if arrData['chain_id'] == "ETH_CHAIN" then
		addrType = 101
		addrName = "ERC-20"
	elseif arrData['chain_id'] == "TRX_CHAIN" then
		addrType = 102
		addrName = "TRC-20"
	end
	local sqlCase = "select * from dy_trusteeship_address_pre where coin_addr='"..arrData['to_addr'].."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		isPrivate = true 
		local sqlCase = "select user_id from dy_user_address_pre where coin_addr='"..arrData['to_addr'].."' and use_type=0"
		mysqlItem:executeQuery(sqlCase)
		local sqlData = mysqlItem:fetch()
		if sqlData ~= nil then
			userid = tonumber(sqlData)
		end
		if addrType == 101 then
			fee = 2
		elseif addrType == 102 then
			fee = 3
		end
	else
		hostingAdds = 1
		hostingStatus = 1
		
		--检查是否存在未完成的托管钱包订单
		local sqlCase = "select `create`,amount,order_id,userid from dy_trusteeship_wallet_order where user_txid='"..arrData['tx_id'].."' and (tx_status=0 or tx_status=2)"
		mysqlItem:executeQuery(sqlCase) 
		local sqlData = mysqlItem:fetch({})
		if sqlData ~= nil then
			relatedOrder = sqlData[3]
			if tonumber(arrData['tx_time']) > TimeUtils.GetTime(sqlData[1])  then
				if string.format("%.4f",sqlData[2]) == string.format("%.4f",arrData['amount']) then
					hostingStatus = 2 
					userid = tonumber(sqlData[4])
					matchStatus = 1
					matchRmg = "完全匹配"
				else
					matchRmg = "金额不符"
				end 
				
			else
				matchRmg = "时间不符"
			end
		end
		
		local sqlCase = "select channel from dy_trusteeship_address_pre where coin_addr='"..arrData['to_addr'].."' and status=0  and type=1"
		
		mysqlItem:executeQuery(sqlCase) 
		local sqlData = mysqlItem:fetch()
		if sqlData ~= nil then
			arrData['channel'] = sqlData
		end
	end
	
	--查询渠道的平台ID
	local sqlCase = "select platform_id from dy_channel_info where channel='"..arrData['channel'].."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	local platformID = 0
	if sqlData ~= nil then
		platformID = tonumber(sqlData) or 0
	end

	local sqlCase = "INSERT INTO `dy_block_chain_trans`(`tx_id`,`tx_type`,`chain_id`,`tx_data`,`from_addr`,`to_addr`,`amount`,`tx_fee`,"
		.."`tx_status`,`tx_time`,`recd_status`,`channel`,`userid`,`specific_type`,`get_amount`,`hosting_adds`,`hosting_status`,`related_order`,`addr_type`,`addr_name`,`platform_id`)" .."VALUES('"..arrData['tx_id'].."','1','"
		..arrData['chain_id'].."','"..arrData['tx_data'].."','"..arrData['from_addr'].."','"..arrData['to_addr'].."',"..arrData['amount']
		..","..fee..","..arrData['tx_status']..",'"..arrData['tx_time'].."',106,'"..arrData['channel'].."',"
		..userid..",1,"..(arrData['amount'] - fee)..","..hostingAdds..","..hostingStatus..",'"..relatedOrder.."',"..addrType..",'"..addrName.."',"..platformID..")"
	mysqlItem:execute(sqlCase)
	
	sqlCase = "select id from dy_block_chain_trans where tx_id='"..arrData['tx_id'].."' and id>0"
	
	mysqlItem:executeQuery(sqlCase)
	
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		LogFile("in_error", "insert into block chain failed tx_id="..arrData['tx_id']..", data="..luajson.decode(arrData))
		return false
	end
	
	if tonumber(arrData['tx_status']) == 0 and isPrivate == true then
	
		local uInfo = UserInfoModel.GetUserInfo(userid)
		if uInfo ~= nil then
			local price = CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.deal_buy, g_marketDefine.currency_type.USDT, true, uInfo.userid)
			local money = price * tonumber(arrData['amount'] - fee)
			UserInfoModel.AddErcUsdtAmount( uInfo, arrData['amount'] - fee, sqlData ,0, "充到私人钱包，增加资金",g_humanDefine.fund_details.type_recharge_currency,money)
			UserInfoModel.SendErcUsdtAmount(uInfo)
			LogDispatch.userRecharge(uInfo.userid, arrData['amount'] , fee,arrData['amount']- fee, false)
			
			if uInfo.dealcointype == 1 then
				--结算货币类型如果是法币把法币账户也结算一下
				UserInfoModel.AddErcFCAmount(uInfo, money, sqlData, 0, "充到私人钱包，增加资金", g_humanDefine.fund_details.type_recharge_currency)
			end
			
			local sqlCase = "update dy_block_chain_trans set money="..money..", price="..price.." where id='"..sqlData.."'"
			mysqlItem:execute(sqlCase)
			
			local sysInfo = UserInfoModel.GetSysUserInfo(uInfo.platformid)
			if sysInfo ~= nil then
				local allPrice = CoinInfoService.Erc20USDTPrice("ALL", g_marketDefine.deal_buy, g_marketDefine.currency_type.USDT, false, 0)
				UserInfoModel.AddErcUsdtAmount(sysInfo, fee, sqlData, 0, "充到私人钱包手续费， 归入系统用户", g_humanDefine.fund_details.type_handling_fee, allPrice*fee)
				
				local sqlCase = "insert into log_commission_details(order_id,order_type,currency_type,source_userid,source_nick,target_userid,target_nick,amount,"
				.."dea_price,money_count,free_rate,free_count,commission_rate,commission_count,platform_id) values("..sqlData..",5,2003,"
				..uInfo.userid..",'"..uInfo.nickname.."',"..sysInfo.userid..",'"..sysInfo.nickname.."',"..arrData['amount']..",0,0,0,"..fee..",0,"..fee..","..uInfo.platformid..")"
				mysqlItem:execute(sqlCase)
				
				LogDispatch.sysIncome(uInfo.userid, fee)
			end
		else
			LogFile("coin_error",luajson.encode(arrData))
		end
		
	elseif tonumber(arrData['tx_status']) == 0 and isPrivate == false then
	
		if hostingStatus == 2 then
			local uInfo = UserInfoModel.GetUserInfo(userid)
			if uInfo ~= nil then
				local price = CoinInfoService.Erc20USDTPrice(uInfo.channel, g_marketDefine.deal_buy, g_marketDefine.currency_type.USDT, true, uInfo.userid)
				local money = price * tonumber(arrData['amount'] - fee)
				UserInfoModel.AddErcUsdtAmount( uInfo, arrData['amount'] - fee, sqlData ,0, "充到托管钱包，增加资金",g_humanDefine.fund_details.type_recharge_currency,money)
				UserInfoModel.SendErcUsdtAmount(uInfo)
				LogDispatch.userRecharge(uInfo.userid, arrData['amount'] , fee, arrData['amount']- fee, true)
				
				if uInfo.dealcointype == 1 then
					--结算货币类型如果是法币把法币账户也结算一下
					UserInfoModel.AddErcFCAmount(uInfo, money, sqlData, 0, "充到托管钱包，增加资金", g_humanDefine.fund_details.type_recharge_currency)
				end
				
				local sqlCase = "update dy_block_chain_trans set money="..money..", price="..price.." where id='"..sqlData.."'"
				mysqlItem:execute(sqlCase)
			else
				LogFile("coin_error",luajson.encode(arrData))
			end
		end
		
		local sqlCase = "update dy_trusteeship_wallet_order set remarks='"..matchRmg.."', status="..matchStatus..", actual_amount="..(arrData['amount']- fee).." where order_id='"..relatedOrder.."'"
		mysqlItem:execute(sqlCase)
	end
	
	
	return true
end


function SynchroChainDataModel.InsertIntoFee(arrData)


	local tx_fee = arrData['tx_fee'] / g_marketDefine.block_chain_fee_multiple

	local sqlCase = "INSERT INTO `dy_block_chain_fee`(`tx_id`,`tx_type`,`chain_id`,`tx_data`,`from_addr`,`to_addr`,`amount`,`tx_fee`,`tx_status`,`tx_time`,`channel`) VALUES('"..arrData['tx_id'].."','"..arrData['tx_type']..
	"','"..arrData['chain_id'].."','"..arrData['tx_data'].."','"..arrData['from_addr'].."','"..arrData['to_addr'].."',"..arrData['amount']..","..tx_fee..","..arrData['tx_status']..",'"..arrData['tx_time'].."','"..arrData['channel'].."')"

	LogFile("mysql",sqlCase)


	mysqlItem:execute(sqlCase)
	
	sqlCase = "select id from dy_block_chain_fee where tx_id='"..arrData['tx_id'].."' and id>0"
	
	mysqlItem:executeQuery(sqlCase)
	
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		LogFile("fee_error", "insert into block chain failed tx_id="..arrData['tx_id']..", data="..luajson.decode(arrData))
		return false
	end

	
	
	return true	
	
end

function SynchroChainDataModel.InsertIntoCollect(arrData)

	local sqlCase = "select platform_id from dy_channel_info where  channel='"..arrData['channel'].."'"
	mysqlItem:executeQuery(sqlCase)
	local sqlData = mysqlItem:fetch()
	local platformID = 0
	if sqlData ~= nil then
		platformID = tonumber(sqlData)
	end
	
	local tx_fee = arrData['tx_fee'] / g_marketDefine.block_chain_fee_multiple
	local sqlCase = "INSERT INTO `dy_block_chain_collect`(`tx_id`,`in_tx_id`,`tx_type`,`chain_id`,`tx_data`,`from_addr`,`to_addr`,`amount`,`tx_fee`,`tx_status`,`tx_time`,`channel`,`platform_id`) VALUES('"..arrData['tx_id'].."','"..arrData['in_tx_id'].."','"..arrData['tx_type']..
	"','"..arrData['chain_id'].."','"..arrData['tx_data'].."','"..arrData['from_addr'].."','"..arrData['to_addr'].."',"..arrData['amount']..","..tx_fee..","..arrData['tx_status']..",'"..arrData['tx_time'].."','"..arrData['channel'].."',"..platformID..")"


	mysqlItem:execute(sqlCase)
	
	sqlCase = "select id from dy_block_chain_collect where tx_id='"..arrData['tx_id'].."' and id>0"
	
	mysqlItem:executeQuery(sqlCase)
	
	local sqlData = mysqlItem:fetch()
	if sqlData == nil then
		LogFile("collect_error", "insert into block chain failed tx_id="..arrData['tx_id']..", data="..luajson.encode(arrData))
		return false
	end
	
	--查询渠道的子平台ID
	if platformID ~= 0 then
		local dealType = 5
		local dayStr = TimeUtils.GetDayString()
		LogModel.CheckPlatformDaily(platformID, dayStr, dealType, 0) 
		local sqlCase = "update log_subsystem_daily set deal_count=deal_count+1 where platform_id="
			..platformID.." and dateid='"..dayStr.."' and deal_type="..dealType.." and payid=0"
		mysqlItem:execute(sqlCase)
	end

	return true	
	
end
