
AddressPreModel = {}

AddressPreModel.redis_index = "redis_addresspre"

AddressPreModel.pre_list = "address_pre_list_"   --服务器中，保持着30个空闲的地址。一点有使用的。立马去补充回来

AddressPreModel.pre_trc_list = "address_pre_trc_list_"   --服务器中，保持着30个空闲的地址。一点有使用的。立马去补充回来

AddressPreModel.address_get_mark = "address_get_mark"


function AddressPreModel.Init()
	

	local sqlCase = "SELECT DISTINCT channel from dy_channel_info where id>0"
	
	mysqlItem:executeQuery(sqlCase)
	local channelList = {}
	table.insert( channelList, "DEFAULT" )
	for i = 1,1000 do
		local getData = mysqlItem:fetch()
		if getData == nil then
			break
		end
		table.insert(channelList, getData)
	end	
	
	
	redisItem:del( AddressPreModel.address_get_mark, AddressPreModel.redis_index )
	
	
	for k,v_channel in ipairs( channelList ) do

		redisItem:del( AddressPreModel.pre_list..v_channel, AddressPreModel.redis_index )
		redisItem:del( AddressPreModel.pre_trc_list..v_channel, AddressPreModel.redis_index )
		
		local sqlCase = "select coin_addr,channel,addr_type from dy_user_address_pre where id > 0 and user_id=0 and channel='"..v_channel.."'"
		mysqlItem:executeQuery(sqlCase)

		
		for i = 0, 100 do
			local sqlData = mysqlItem:fetch({})
			if sqlData == nil then
				break
			end
			
			if tonumber(sqlData[3]) == 101 then
			
				redisItem:rpush( AddressPreModel.pre_list..sqlData[2], sqlData[1], AddressPreModel.redis_index )
			elseif tonumber(sqlData[3]) == 102 then
				redisItem:rpush( AddressPreModel.pre_trc_list..sqlData[2], sqlData[1], AddressPreModel.redis_index )
			end
		end		

	end
	
	


end

--渠道ERC的地址
function AddressPreModel.GetNewAddress(channel)

	local getAddress = redisItem:lpop( AddressPreModel.pre_list..channel, AddressPreModel.redis_index )
	
	return getAddress
end

--取的TRC的地址
function AddressPreModel.GetNewTRCAddress(channel)

	local getAddress = redisItem:lpop( AddressPreModel.pre_trc_list..channel, AddressPreModel.redis_index )
	
	return getAddress
end


--同步userid到数据库中，TRC地址
function AddressPreModel.UpdatePreAddress(address, channel, userid)
	local sqlCase = "update dy_user_address_pre set user_id="..userid.." where id>0 and coin_addr='"..address.."'"
	mysqlItem:execute(sqlCase)
	--同时，还需要把这个同步到nodejs的服务器中。
	

end

--同步userid到数据库中,TRC地址
function AddressPreModel.UpdatePreTRCAddress(address, channel, userid)
	local sqlCase = "update dy_user_address_pre set user_id="..userid.." where id>0 and coin_addr='"..address.."'"
	mysqlItem:execute(sqlCase)
	--同时，还需要把这个同步到nodejs的服务器中。
	

end

--同步userid到数据库中，TRC地址
function AddressPreModel.UpdatePreAddressCoinPay(address, channel, userid)
	local sqlCase = "update dy_user_address_pre set user_id="..userid..", use_type=1 where id>0 and coin_addr='"..address.."'"
	mysqlItem:execute(sqlCase)
	--同时，还需要把这个同步到nodejs的服务器中。
	

end

--同步userid到数据库中,TRC地址
function AddressPreModel.UpdatePreTRCAddressCoinPay(address, channel, userid)
	local sqlCase = "update dy_user_address_pre set user_id="..userid..", use_type=1 where id>0 and coin_addr='"..address.."'"
	mysqlItem:execute(sqlCase)
	--同时，还需要把这个同步到nodejs的服务器中。
	

end

--更新erc20的地址
function AddressPreModel.PushNewAddress(newAddress, passowrd,channel)
	local sqlCase = "insert into dy_user_address_pre(coin_addr, password,channel,addr_type,addr_name) values('"..newAddress.."','"..passowrd.."','"..channel.."',101,'ERC-20')"
	mysqlItem:execute(sqlCase)
	redisItem:rpush( AddressPreModel.pre_list..channel, newAddress, AddressPreModel.redis_index )
end

--更新trc20的地址
function AddressPreModel.PushNewTRCAddress(newAddress, passowrd,channel)
	local sqlCase = "insert into dy_user_address_pre(coin_addr, password,channel,addr_type,addr_name) values('"..newAddress.."','"..passowrd.."','"..channel.."',102,'TRC-20')"
	mysqlItem:execute(sqlCase)
	redisItem:rpush( AddressPreModel.pre_trc_list..channel, newAddress, AddressPreModel.redis_index )
end


--取得ERC长度
function AddressPreModel.GetPreAddressLen(channel)
	
	local getLen = redisItem:llen( AddressPreModel.pre_list..channel, AddressPreModel.redis_index )
	
	return getLen == nil and 0 or tonumber(getLen)
	
end

--取得TRC长度
function AddressPreModel.GetPreTRCAddressLen(channel)
	
	local getLen = redisItem:llen( AddressPreModel.pre_trc_list..channel, AddressPreModel.redis_index )
	
	return getLen == nil and 0 or tonumber(getLen)
	
end

function AddressPreModel.MarkGetWorker()
	redisItem:setex(AddressPreModel.address_get_mark, 30, 1, AddressPreModel.redis_index )
end

function AddressPreModel.DelGetWorker()
	redisItem:del( AddressPreModel.address_get_mark, AddressPreModel.redis_index )
end

function AddressPreModel.ExistGetWorker()
	local isExist = redisItem:exists( AddressPreModel.address_get_mark, AddressPreModel.redis_index )
	return isExist
end


