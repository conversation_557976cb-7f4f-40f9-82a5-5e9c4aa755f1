# OTC Core Server 部署配置总结

本文档总结了为您的 Quart Web 框架项目生成的完整部署配置。

## 📁 生成的文件结构

```
otc-core-server/
├── deploy/
│   ├── README.md                           # 详细部署指南
│   ├── nginx/
│   │   └── otc-core.conf                   # Nginx 反向代理配置
│   ├── systemd/
│   │   ├── otc-core.service                # 基础 systemd 服务
│   │   └── otc-core-gunicorn.service       # Gunicorn systemd 服务（推荐）
│   ├── scripts/
│   │   ├── deploy.sh                       # 自动部署脚本
│   │   └── update.sh                       # 应用更新脚本
│   ├── config/
│   │   ├── .env.production                 # 生产环境配置模板
│   │   └── README.md                       # 配置说明文档
│   └── docker/
│       ├── Dockerfile                      # Docker 镜像构建文件
│       ├── docker-compose.yml              # Docker Compose 配置
│       ├── .env.example                    # Docker 环境变量示例
│       └── nginx/
│           └── nginx.conf                  # Docker 环境 Nginx 配置
├── app/modules/health/
│   └── __init__.py                         # 健康检查端点
└── DEPLOYMENT_SUMMARY.md                   # 本文档
```

## 🚀 快速部署选项

### 选项 1: 传统 Linux 服务器部署（推荐）

```bash
# 1. 下载并运行自动部署脚本
wget https://raw.githubusercontent.com/your-org/otc-core-server/main/deploy/scripts/deploy.sh
chmod +x deploy.sh
sudo ./deploy.sh

# 2. 配置环境变量
sudo nano /opt/otc-core-server/.env

# 3. 重启服务
sudo systemctl restart otc-core-gunicorn
```

### 选项 2: Docker 容器化部署

```bash
# 1. 克隆项目
git clone https://github.com/your-org/otc-core-server.git
cd otc-core-server/deploy/docker

# 2. 配置环境变量
cp .env.example .env
nano .env

# 3. 启动服务
docker-compose up -d
```

## 🔧 核心配置文件说明

### 1. Nginx 配置 (`deploy/nginx/otc-core.conf`)
- ✅ HTTPS 重定向和 SSL 配置
- ✅ 反向代理到 Quart 应用 (端口 6002)
- ✅ WebSocket 支持 (`/ucoin` 端点)
- ✅ 静态文件服务
- ✅ 安全头设置
- ✅ Gzip 压缩
- ✅ 限流保护
- ✅ CORS 支持

### 2. Systemd 服务 (`deploy/systemd/otc-core-gunicorn.service`)
- ✅ Gunicorn WSGI 服务器
- ✅ 4 个 worker 进程
- ✅ 自动重启策略
- ✅ 安全沙箱设置
- ✅ 日志管理

### 3. 生产环境配置 (`deploy/config/.env.production`)
- ✅ 数据库连接配置
- ✅ Redis 缓存配置
- ✅ 安全密钥设置
- ✅ 第三方服务集成
- ✅ 监控和日志配置

## 🛡️ 安全特性

- **SSL/TLS 加密**: 强制 HTTPS 重定向
- **安全头**: X-Frame-Options, X-XSS-Protection 等
- **限流保护**: API 和登录端点限流
- **防火墙配置**: UFW 防火墙规则
- **用户隔离**: 专用 `otc` 用户运行应用
- **文件权限**: 严格的文件访问控制

## 📊 监控和健康检查

### 健康检查端点
- `/health` - 完整健康检查（数据库、Redis）
- `/ready` - 就绪检查
- `/live` - 存活检查

### 日志管理
- **应用日志**: `/opt/otc-core-server/logs/`
- **系统日志**: `journalctl -u otc-core-gunicorn`
- **Nginx 日志**: `/var/log/nginx/otc-core-*.log`
- **日志轮转**: 自动配置

## 🔄 运维操作

### 服务管理
```bash
# 启动服务
sudo systemctl start otc-core-gunicorn

# 停止服务
sudo systemctl stop otc-core-gunicorn

# 重启服务
sudo systemctl restart otc-core-gunicorn

# 查看状态
sudo systemctl status otc-core-gunicorn

# 查看日志
sudo journalctl -u otc-core-gunicorn -f
```

### 应用更新
```bash
# 使用更新脚本
sudo /opt/otc-core-server/deploy/scripts/update.sh

# 手动更新
cd /opt/otc-core-server
sudo systemctl stop otc-core-gunicorn
sudo -u otc git pull origin main
sudo -u otc ./venv/bin/pip install -r requirements.txt --upgrade
sudo systemctl start otc-core-gunicorn
```

## 🎯 性能优化

### Gunicorn 配置
- **Workers**: 4 个（可根据 CPU 核心数调整）
- **Worker 类**: `quart.worker.GunicornWorker`
- **连接数**: 1000 个并发连接
- **超时**: 60 秒

### Nginx 优化
- **Gzip 压缩**: 启用
- **静态文件缓存**: 1年缓存
- **连接保持**: 启用
- **缓冲优化**: 配置

### 数据库优化
- **连接池**: 20 个连接
- **最大溢出**: 30 个连接
- **连接回收**: 1小时

## 📋 部署检查清单

### 部署前准备
- [ ] 服务器满足最低配置要求
- [ ] 域名 DNS 解析配置
- [ ] SSL 证书准备
- [ ] 数据库服务器准备
- [ ] Redis 服务器准备

### 配置检查
- [ ] 修改 `.env` 文件中的所有密码和密钥
- [ ] 更新 Nginx 配置中的域名
- [ ] 配置数据库连接信息
- [ ] 设置 Redis 连接信息
- [ ] 配置第三方服务 API 密钥

### 安全检查
- [ ] 更改所有默认密码
- [ ] 配置防火墙规则
- [ ] 启用 SSL 证书
- [ ] 配置 fail2ban
- [ ] 设置文件权限

### 功能验证
- [ ] 应用服务正常启动
- [ ] 数据库连接正常
- [ ] Redis 连接正常
- [ ] WebSocket 连接正常
- [ ] 健康检查端点响应正常
- [ ] 静态文件访问正常

## 🆘 故障排除

### 常见问题
1. **服务启动失败**: 检查 `journalctl -u otc-core-gunicorn`
2. **数据库连接失败**: 验证数据库配置和权限
3. **Redis 连接失败**: 检查 Redis 服务状态
4. **Nginx 配置错误**: 运行 `nginx -t` 测试配置
5. **SSL 证书问题**: 检查证书路径和权限

### 性能问题
1. **响应慢**: 检查数据库查询和 Redis 缓存
2. **内存使用高**: 调整 Gunicorn worker 数量
3. **CPU 使用高**: 优化应用代码和数据库查询

## 📞 技术支持

如需技术支持，请提供以下信息：
- 错误日志内容
- 系统配置信息
- 部署环境描述
- 问题复现步骤

---

**注意**: 请确保在生产环境中修改所有默认密码和密钥，并定期进行安全更新。

**版本**: 1.0.0  
**更新日期**: 2025-09-04
