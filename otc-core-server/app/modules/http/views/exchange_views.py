from app.shared.libs.base_view import BaseView
from typing import Optional


class UpdateOrderInfoView(BaseView):
    """更新订单信息视图模型"""
    orderID: int
    payeeAccount: Optional[str] = ""
    payeeName: Optional[str] = ""
    payeeBank: Optional[str] = ""
    tradeId: Optional[str] = ""
    sign: str


class UpdateOrderStatusView(BaseView):
    """更新订单状态视图模型"""
    orderID: Optional[int] = None
    status: int  # 1-成功, 2-取消, 3-待放行, 4-激活
    proofurl: Optional[str] = ""
    tradeId: str
    sign: str
