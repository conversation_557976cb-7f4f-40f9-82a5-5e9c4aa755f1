from app.shared.libs.base_view import BaseView
from typing import Optional


class BuyView(BaseView):
    """购买下单视图模型"""
    merchantUserId: int
    money: float  # 金额（人民币）
    payType: int  # 支付类型
    outOrderNo: str  # 三方平台订单
    notifyUrl: str  # 通知回调
    body: str  # 商户客户的订单，给商户返回的参数
    timestamp: str  # 时间戳
    addrType: int  # 地址类型101-erc20 102-trc20
    IP: str  # 客户端IP
    payer: Optional[str] = ""  # 付款人
    sign: str  # 签证


class SellView(BaseView):
    """出售下单视图模型"""
    merchantUserId: int
    amount: float  # 币数量
    payType: int  # 支付类型
    outOrderNo: str  # 三方平台订单
    notifyUrl: str  # 通知回调
    body: str  # 商户客户的订单，给商户返回的参数
    timestamp: str  # 时间戳
    addrType: int  # 地址类型101-erc20 102-trc20
    IP: str  # 客户端IP
    sign: str  # 签证
