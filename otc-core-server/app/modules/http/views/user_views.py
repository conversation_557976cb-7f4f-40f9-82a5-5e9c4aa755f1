from app.shared.libs.base_view import BaseView
from typing import Optional


class CreateChannelAgentView(BaseView):
    """创建渠道商视图模型"""
    channel: str  # 渠道标识
    phonenum: str  # 联系方式
    account: str  # 后台账号
    password: str  # 后台密码
    web_account: str  # app账号
    web_password: str  # app密码
    channel_name: str  # 渠道名字
    channel_deal: int  # 交易类型 0-公开 1-私有
    fund_scheduling: int  # 允许资金调度 0-允许 1-不允许
    is_external: int  # 是否外部渠道 0-不是 1-是
    sign: str  # 签证
