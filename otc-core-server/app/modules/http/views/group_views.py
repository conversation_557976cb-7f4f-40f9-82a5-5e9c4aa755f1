from app.shared.libs.base_view import BaseView
from typing import Optional


class CreateGroupView(BaseView):
    """创建分组视图模型"""
    groupName: str
    channel: str
    platformID: int
    sign: str


class SetGroupMemberView(BaseView):
    """设置小组成员视图模型"""
    groupID: int
    userid: int
    optType: int  # 1-添加 2-删除
    sign: str


class SetGroupOrdersView(BaseView):
    """设置小组接单视图模型"""
    groupID: int
    optID: int  # 1-充值开关 2-提现开关 3-代付开关
    status: int  # 0-关闭 1-开启
    sign: str


class DelGroupView(BaseView):
    """删除分组视图模型"""
    groupID: int
    sign: str
