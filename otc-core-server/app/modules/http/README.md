# Http模块重构说明

本模块是从原Lua代码的Http模块重构而来的Python实现，保持了原有的功能逻辑不变。

## 模块结构

```
app/modules/http/
├── __init__.py                 # 模块初始化和蓝图注册
├── controllers/                # 控制器层
│   ├── __init__.py
│   ├── exchange_controller.py  # 交易所相关接口
│   ├── group_controller.py     # 分组管理接口
│   ├── pay_center_controller.py # 支付中心接口
│   ├── pay_info_controller.py  # 支付信息管理接口
│   ├── platform_controller.py # 平台相关接口
│   └── user_controller.py      # 用户管理接口
├── services/                   # 服务层
│   ├── __init__.py
│   ├── exchange_service.py     # 交易所业务逻辑
│   ├── group_service.py        # 分组管理业务逻辑
│   ├── pay_center_service.py   # 支付中心业务逻辑
│   ├── pay_info_service.py     # 支付信息管理业务逻辑
│   ├── platform_service.py    # 平台业务逻辑
│   └── user_service.py         # 用户管理业务逻辑
├── views/                      # 视图模型层
│   ├── __init__.py
│   ├── exchange_views.py       # 交易所请求/响应模型
│   ├── group_views.py          # 分组管理请求/响应模型
│   ├── pay_center_views.py     # 支付中心请求/响应模型
│   ├── pay_info_views.py       # 支付信息管理请求/响应模型
│   ├── platform_views.py      # 平台请求/响应模型
│   └── user_views.py           # 用户管理请求/响应模型
├── utils/                      # 工具类
│   ├── __init__.py
│   └── crypto_utils.py         # 加密工具（MD5签名验证等）
└── README.md                   # 本文档
```

## 功能对照表

### HttpExchange模块
| 原Lua函数 | Python接口 | 功能描述 |
|-----------|------------|----------|
| updateOrderInfo | POST /exchange/updateOrderInfo | 更新订单信息 |
| updateOrderStatus | POST /exchange/updateOrderStatus | 更新订单状态 |

### HttpGroup模块
| 原Lua函数 | Python接口 | 功能描述 |
|-----------|------------|----------|
| creategroup | POST /group/creategroup | 创建分组 |
| setgroupmember | POST /group/setgroupmember | 设置小组成员 |
| setgrouporders | POST /group/setgrouporders | 设置小组接单 |
| delgrouporders | POST /group/delgrouporders | 删除分组 |

### HttpPayCenter模块
| 原Lua函数 | Python接口 | 功能描述 |
|-----------|------------|----------|
| buy | POST /paycenter/buy | 购买下单（买币） |
| sell | POST /paycenter/sell | 出售下单（卖币） |

### HttpPayInfo模块
| 原Lua函数 | Python接口 | 功能描述 |
|-----------|------------|----------|
| setPayInfoSwitch | POST /payinfo/setPayInfoSwitch | 设置收款账号开关 |

### HttpUser模块
| 原Lua函数 | Python接口 | 功能描述 |
|-----------|------------|----------|
| createChannelAgent | POST /user/createChannelAgent | 创建渠道商 |

## 主要特性

1. **保持原有逻辑**: 所有业务逻辑与原Lua代码保持一致
2. **签名验证**: 保留了原有的MD5签名验证机制
3. **错误处理**: 统一的错误处理和响应格式
4. **数据验证**: 使用Pydantic进行请求参数验证
5. **异步支持**: 全面支持异步操作
6. **数据库操作**: 使用SQLAlchemy进行数据库操作

## 使用示例

### 更新订单信息
```python
POST /api/http/exchange/updateOrderInfo
{
    "orderID": 12345,
    "payeeAccount": "****************",
    "payeeName": "张三",
    "payeeBank": "中国银行",
    "tradeId": "T123456789",
    "sign": "md5_signature_here"
}
```

### 创建分组
```python
POST /api/http/group/creategroup
{
    "groupName": "测试分组",
    "channel": "channel001",
    "platformID": 1,
    "sign": "md5_signature_here"
}
```

## 注意事项

1. 所有接口都需要提供正确的MD5签名
2. 签名密钥默认为"aaaaaaaaaaaaaaaa"
3. 数据库表结构需要与原系统保持一致
4. 部分复杂业务逻辑可能需要根据实际情况进一步完善
