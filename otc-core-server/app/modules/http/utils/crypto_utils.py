import hashlib
from typing import Dict, Any


def verify_md5_signature(data: Dict[str, Any], signature: str, secret_key: str = "aaaaaaaaaaaaaaaa") -> bool:
    """
    验证MD5签名
    对应原Lua中的EncryptWithMD5函数
    
    Args:
        data: 待签名的数据字典
        signature: 签名字符串
        secret_key: 密钥
    
    Returns:
        bool: 签名是否正确
    """
    # 按照原Lua逻辑生成签名
    encrypt_str = generate_md5_signature(data, secret_key)
    return encrypt_str == signature


def generate_md5_signature(data: Dict[str, Any], secret_key: str = "aaaaaaaaaaaaaaaa") -> str:
    """
    生成MD5签名
    
    Args:
        data: 待签名的数据字典
        secret_key: 密钥
    
    Returns:
        str: MD5签名
    """
    # 将数据转换为字符串并排序（模拟原Lua逻辑）
    sorted_items = sorted(data.items())
    sign_str = ""
    for key, value in sorted_items:
        if value is not None:
            sign_str += f"{key}={value}&"
    
    # 添加密钥
    sign_str += f"key={secret_key}"
    
    # 生成MD5
    md5_hash = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
    return md5_hash
