from quart import Blueprint
from app.shared.response import Response
from app.shared.decorators import validator
from app.modules.http.services.user_service import UserService
from app.modules.http.views.user_views import CreateChannelAgentView

user_bp = Blueprint('user', __name__)
user_service = UserService()


@user_bp.route('/createChannelAgent', methods=['POST'])
@validator(required=CreateChannelAgentView)
async def create_channel_agent(body: CreateChannelAgentView):
    """
    创建渠道商
    
    参数:
    - channel: 渠道标识
    - phonenum: 联系方式
    - account: 后台账号
    - password: 后台密码
    - web_account: app账号
    - web_password: app密码
    - channel_name: 渠道名字
    - channel_deal: 交易类型 0-公开 1-私有
    - fund_scheduling: 允许资金调度 0-允许 1-不允许
    - is_external: 是否外部渠道 0-不是 1-是
    - sign: 签证
    """
    result = await user_service.create_channel_agent(body)
    return Response.success(result), 200
