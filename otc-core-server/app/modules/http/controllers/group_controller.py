from quart import Blueprint
from app.shared.response import Response
from app.shared.decorators import validator
from app.modules.http.services.group_service import GroupService
from app.modules.http.views.group_views import (
    CreateGroupView, SetGroupMemberView, SetGroupOrdersView, DelGroupView
)

group_bp = Blueprint('group', __name__)
group_service = GroupService()


@group_bp.route('/creategroup', methods=['POST'])
@validator(required=CreateGroupView)
async def create_group(body: CreateGroupView):
    """
    创建分组
    
    参数:
    - groupName: 组名
    - channel: 渠道
    - platformID: 创建的平台ID
    - sign: 签证
    """
    result = await group_service.create_group(body)
    return Response.success(result), 200


@group_bp.route('/setgroupmember', methods=['POST'])
@validator(required=SetGroupMemberView)
async def set_group_member(body: SetGroupMemberView):
    """
    设置小组成员
    
    参数:
    - groupID: 小组ID
    - userid: 用户ID
    - optType: 1-添加 2-删除
    - sign: 签证
    """
    result = await group_service.set_group_member(body)
    return Response.success(result), 200


@group_bp.route('/setgrouporders', methods=['POST'])
@validator(required=SetGroupOrdersView)
async def set_group_orders(body: SetGroupOrdersView):
    """
    设置小组接单
    
    参数:
    - groupID: 小组ID
    - optID: 1-充值开关 2-提现开关 3-代付开关
    - status: 0-关闭 1-开启
    - sign: 签证
    """
    result = await group_service.set_group_orders(body)
    return Response.success(result), 200


@group_bp.route('/delgrouporders', methods=['POST'])
@validator(required=DelGroupView)
async def del_group(body: DelGroupView):
    """
    删除分组
    
    参数:
    - groupID: 小组ID
    - sign: 签证
    """
    result = await group_service.del_group(body)
    return Response.success(result), 200
