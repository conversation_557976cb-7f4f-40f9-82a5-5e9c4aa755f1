from quart import Blueprint
from app.shared.response import Response
from app.shared.decorators import validator
from app.modules.http.services.platform_service import PlatformService
from app.modules.http.views.platform_views import PlatformView

platform_bp = Blueprint('platform', __name__)
platform_service = PlatformService()


@platform_bp.route('/platform', methods=['POST'])
@validator(required=PlatformView)
async def platform_operation(body: PlatformView):
    """
    平台相关操作
    
    参数根据具体的平台功能需求定义
    """
    result = await platform_service.handle_platform_operation(body)
    return Response.success(result), 200
