from quart import Blueprint
from app.shared.response import Response
from app.shared.decorators import validator
from app.modules.http.services.pay_info_service import PayInfoService
from app.modules.http.views.pay_info_views import SetPayInfoSwitchView

pay_info_bp = Blueprint('pay_info', __name__)
pay_info_service = PayInfoService()


@pay_info_bp.route('/setPayInfoSwitch', methods=['POST'])
@validator(required=SetPayInfoSwitchView)
async def set_pay_info_switch(body: SetPayInfoSwitchView):
    """
    设置收款账号开关
    
    参数:
    - payId: 支付方式ID
    - switch: 0-失效 1-正常
    - sign: 签证
    """
    result = await pay_info_service.set_pay_info_switch(body)
    return Response.success(result), 200
