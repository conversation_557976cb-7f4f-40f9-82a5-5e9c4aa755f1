from quart import Blueprint
from app.shared.response import Response
from app.shared.decorators import validator
from app.modules.http.services.pay_center_service import PayCenterService
from app.modules.http.views.pay_center_views import BuyView, SellView

pay_center_bp = Blueprint('pay_center', __name__)
pay_center_service = PayCenterService()


@pay_center_bp.route('/buy', methods=['POST'])
@validator(required=BuyView)
async def buy(body: BuyView):
    """
    购买下单（买币）
    
    参数:
    - merchantUserId: 商户ID
    - money: 金额（人民币）
    - payType: 支付类型
    - outOrderNo: 三方平台订单
    - notifyUrl: 通知回调
    - body: 商户客户的订单，给商户返回的参数
    - timestamp: 时间戳
    - addrType: 地址类型101-erc20 102-trc20
    - IP: 客户端IP
    - payer: 付款人
    - sign: 签证
    """
    result = await pay_center_service.buy(body)
    return Response.success(result), 200


@pay_center_bp.route('/sell', methods=['POST'])
@validator(required=SellView)
async def sell(body: SellView):
    """
    出售下单（卖币）
    
    参数:
    - merchantUserId: 商户ID
    - amount: 币数量
    - payType: 支付类型
    - outOrderNo: 三方平台订单
    - notifyUrl: 通知回调
    - body: 商户客户的订单，给商户返回的参数
    - timestamp: 时间戳
    - addrType: 地址类型101-erc20 102-trc20
    - IP: 客户端IP
    - sign: 签证
    """
    result = await pay_center_service.sell(body)
    return Response.success(result), 200
