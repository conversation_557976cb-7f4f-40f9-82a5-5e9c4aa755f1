/*
Navicat MySQL Data Transfer

Source Server         : biyi测试
Source Server Version : 50731
Source Host           : ************:3306
Source Database       : db_coreotc

Target Server Type    : MYSQL
Target Server Version : 50731
File Encoding         : 65001

Date: 2021-05-12 18:26:42
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for ag_agent_info
-- ----------------------------
DROP TABLE IF EXISTS `ag_agent_info`;
CREATE TABLE `ag_agent_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '代理这边自增长的ID',
  `userid` int(11) unsigned DEFAULT '0',
  `account` varchar(60) DEFAULT '' COMMENT '登入后台的账号，如果是币商用户，那么默认是空',
  `password` varchar(60) DEFAULT '' COMMENT '登入后台的密码',
  `nickname` varchar(60) DEFAULT '' COMMENT '昵称',
  `address` varchar(60) DEFAULT '' COMMENT '由钱包统一分配的地址',
  `phonenum` varchar(15) DEFAULT '0' COMMENT '手机号码',
  `channel` varchar(60) DEFAULT '' COMMENT '所属于那个渠道',
  `flag` varchar(60) DEFAULT '0' COMMENT '代理的标识',
  `type` int(11) DEFAULT '0' COMMENT '代理的类型：1：币商代理，2：商户代理',
  `paytypelist` varchar(255) DEFAULT '0' COMMENT '支持的支付类型，这里是展示',
  `payidlist` varchar(255) DEFAULT '0' COMMENT '具体的支付ID的。提现具体的业务',
  `usdt_all_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '作为代理，赚到的佣金总额',
  `usdt_remain_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '剩余usdt的余额\\n',
  `money_all_amount` decimal(20,2) DEFAULT '0.00',
  `momey_remain_amount` decimal(20,2) DEFAULT '0.00',
  `bank_name` varchar(60) DEFAULT '' COMMENT '支行名称',
  `bank_addr` varchar(255) DEFAULT '' COMMENT '银行地址',
  `bank_account` varchar(128) DEFAULT '' COMMENT '银行账号',
  `bank_user` varchar(128) DEFAULT '' COMMENT '银行用户名',
  `create_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `agent_level` int(4) DEFAULT '0' COMMENT '代理登记：1为最高',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Table structure for ag_relation
-- ----------------------------
DROP TABLE IF EXISTS `ag_relation`;
CREATE TABLE `ag_relation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) DEFAULT '0' COMMENT 'userid',
  `bind_userid` int(11) DEFAULT '0' COMMENT '绑定的用户的ID',
  `level` int(4) DEFAULT '0' COMMENT '层级关系',
  `team_usdt_amount` decimal(20,10) DEFAULT '0.**********',
  `team_money_amount` decimal(20,2) DEFAULT '0.00',
  `dir_usdt_amount` decimal(20,10) DEFAULT '0.**********',
  `dir_money_amount` decimal(20,2) DEFAULT '0.00',
  `team_usdt_last_month` decimal(20,10) DEFAULT '0.**********',
  `team_money_last_month` decimal(20,2) DEFAULT '0.00',
  `team_usdt_month` decimal(20,10) DEFAULT '0.**********',
  `team_money_month` decimal(20,2) DEFAULT '0.00',
  `dir_usdt_last_month` decimal(20,10) DEFAULT '0.**********',
  `dir_money_last_month` decimal(20,2) DEFAULT '0.00',
  `dir_usdt_month` decimal(20,10) DEFAULT '0.**********',
  `dir_money_month` decimal(20,2) DEFAULT '0.00',
  `team_usdt_last_week` decimal(20,10) DEFAULT '0.**********',
  `team_money_last_week` decimal(20,2) DEFAULT '0.00',
  `team_usdt_week` decimal(20,10) DEFAULT '0.**********',
  `team_money_week` decimal(20,2) DEFAULT '0.00',
  `dir_usdt_last_week` decimal(20,10) DEFAULT '0.**********',
  `dir_money_last_week` decimal(20,2) DEFAULT '0.00',
  `dir_usdt_week` decimal(20,10) DEFAULT '0.**********',
  `dir_money_week` decimal(20,2) DEFAULT '0.00',
  `team_usdt_last_day` decimal(20,10) DEFAULT '0.**********',
  `team_money_last_day` decimal(20,2) DEFAULT '0.00',
  `team_usdt_today` decimal(20,10) DEFAULT '0.**********',
  `team_money_today` decimal(20,2) DEFAULT '0.00',
  `dir_usdt_last_day` decimal(20,10) DEFAULT '0.**********',
  `dir_money_last_day` decimal(20,2) DEFAULT '0.00',
  `dir_usdt_today` decimal(20,10) DEFAULT '0.**********',
  `dir_money_today` decimal(20,2) DEFAULT '0.00',
  `update_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3652 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='代理关系表';

-- ----------------------------
-- Table structure for dy_app
-- ----------------------------
DROP TABLE IF EXISTS `dy_app`;
CREATE TABLE `dy_app` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `channel_id` int(11) NOT NULL DEFAULT '0' COMMENT '跟渠道表的关联id',
  `app_name` varchar(255) COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT 'app名称',
  `url` varchar(255) COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT 'app下载链接',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='app管理';

-- ----------------------------
-- Table structure for dy_appeal_recd
-- ----------------------------
DROP TABLE IF EXISTS `dy_appeal_recd`;
CREATE TABLE `dy_appeal_recd` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `from_user_id` int(11) NOT NULL DEFAULT '0' COMMENT '申诉人userId',
  `to_user_id` int(11) NOT NULL DEFAULT '0' COMMENT '被申诉人userId',
  `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '相关联的订单id',
  `reason` varchar(255) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '申诉理由',
  `description` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '详细描述',
  `proof_url` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '相关凭证url',
  `reply_reason` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '回复理由',
  `reply_description` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '回复详细描述',
  `reply_proof_url` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '回复相关凭证',
  `reply_time` datetime DEFAULT NULL COMMENT '回复时间',
  `sys_remark` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '客服备注',
  `status` int(1) NOT NULL DEFAULT '0' COMMENT '状态(0-新建,1-关闭,2-客服取消,3-客服放行)',
  `need_audit` int(1) NOT NULL DEFAULT '0' COMMENT '需要审核 (0-不需要, 1-需要) 默认0',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;

-- ----------------------------
-- Table structure for dy_authcode
-- ----------------------------
DROP TABLE IF EXISTS `dy_authcode`;
CREATE TABLE `dy_authcode` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account` varchar(45) DEFAULT '' COMMENT '账号，手机号或邮箱号',
  `timemark` int(11) DEFAULT '0' COMMENT '发送时间',
  `code` varchar(45) DEFAULT '' COMMENT '验证码',
  `state` int(11) DEFAULT '0' COMMENT '状态 0 未使用 1 已使用',
  `channel` varchar(11) DEFAULT '' COMMENT '渠道号',
  `type` int(11) DEFAULT '0' COMMENT '类型 0 手机验证码 1 邮箱验证码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9335 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for dy_bill_repair
-- ----------------------------
DROP TABLE IF EXISTS `dy_bill_repair`;
CREATE TABLE `dy_bill_repair` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单id ',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '调整类型 1系统补款  2系统扣款',
  `userid` int(11) NOT NULL DEFAULT '0' COMMENT '申请人用户id',
  `nickname` varchar(255) COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '申请人用户名',
  `coin_type` varchar(255) COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '币种',
  `amount` decimal(20,4) NOT NULL DEFAULT '0.0000' COMMENT '调账数量',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '调账的状态  0全部 1待审核 2驳回 3通过',
  `reason` varchar(255) COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '调账理由',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请调账时间',
  `check_time` datetime DEFAULT NULL COMMENT '审核时间',
  `check_userid` int(11) NOT NULL DEFAULT '0' COMMENT '审核人id',
  `check_username` varchar(255) COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '审核人名',
  `check_ip` varchar(255) COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '审核人ip',
  `view_type` int(11) NOT NULL DEFAULT '1' COMMENT '发起页面  1.手动调整页面发起调账订单2.冷钱包监控页面发起调账',
  `trans_order_id` varchar(255) COLLATE utf8_bin DEFAULT '' COMMENT '订单  dy_block_chain_trans',
  `price` decimal(20,4) NOT NULL DEFAULT '0.0000' COMMENT '成交单价',
  `opt_userid` int(11) NOT NULL DEFAULT '0' COMMENT '下单人id',
  `opt_from` int(11) NOT NULL DEFAULT '0' COMMENT '下单人来源，1=user_info，2=sub_user_info',
  `check_from` int(11) NOT NULL DEFAULT '0' COMMENT '审核人来源，1=user_info，2=sub_user_info',
  `opt_username` varchar(255) COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '下单人名',
  `coin_id` int(11) DEFAULT '0' COMMENT '币种ID',
  `money` decimal(20,4) DEFAULT '0.0000' COMMENT '法币数量',
  `platform_id` int(11) DEFAULT '0' COMMENT '平台编号',
  PRIMARY KEY (`id`),
  KEY `userid_index` (`userid`) USING BTREE,
  KEY `coin_type_index` (`coin_type`) USING BTREE,
  KEY `status_index` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=223 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='调账表';

-- ----------------------------
-- Table structure for dy_block_chain_collect
-- ----------------------------
DROP TABLE IF EXISTS `dy_block_chain_collect`;
CREATE TABLE `dy_block_chain_collect` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `tx_id` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '交易id',
  `in_tx_id` varchar(255) COLLATE utf8mb4_bin DEFAULT '',
  `tx_type` varchar(50) COLLATE utf8mb4_bin DEFAULT '' COMMENT '交易类型(转入/转出)  1进(充值-buy) 2出(提币-sell)',
  `chain_id` varchar(50) COLLATE utf8mb4_bin DEFAULT '' COMMENT '所属公链',
  `tx_data` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '交易信息--币种',
  `from_addr` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '发起地址',
  `to_addr` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '目标地址',
  `amount` decimal(20,10) DEFAULT '0.**********' COMMENT '转账数量',
  `tx_fee` decimal(20,10) DEFAULT '0.**********' COMMENT '交易手续费',
  `tx_status` int(4) DEFAULT '0' COMMENT '交易状态',
  `tx_time` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '交易时间',
  `block_hash` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '区块hash',
  `remark` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '更新时间',
  `channel` varchar(45) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '对应的渠道',
  `coin_id` int(11) DEFAULT '0' COMMENT '币种ID',
  `coin_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '币种名字',
  `addr_type` int(11) DEFAULT '0' COMMENT '地址类型 101-e20 102 t20',
  `addr_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '地址名字',
  `platform_id` int(11) DEFAULT '0' COMMENT '平台编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1932 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='区块链交易记录--归拢记录';

-- ----------------------------
-- Table structure for dy_block_chain_fee
-- ----------------------------
DROP TABLE IF EXISTS `dy_block_chain_fee`;
CREATE TABLE `dy_block_chain_fee` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `tx_id` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '交易id',
  `in_tx_id` varchar(255) COLLATE utf8mb4_bin DEFAULT '',
  `tx_type` varchar(50) COLLATE utf8mb4_bin DEFAULT '' COMMENT '交易类型(转入/转出)  1进(充值-buy) 2出(提币-sell)',
  `chain_id` varchar(50) COLLATE utf8mb4_bin DEFAULT '' COMMENT '所属公链',
  `tx_data` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '交易信息',
  `from_addr` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '发起地址',
  `to_addr` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '目标地址',
  `amount` decimal(20,10) DEFAULT '0.**********' COMMENT '转账数量',
  `tx_fee` decimal(20,10) DEFAULT '0.**********' COMMENT '交易手续费',
  `tx_status` int(4) DEFAULT '0' COMMENT '交易状态',
  `tx_time` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '交易时间',
  `block_hash` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '区块hash',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `channel` varchar(45) COLLATE utf8mb4_bin DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=388 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='区块链交易记录--手续费';

-- ----------------------------
-- Table structure for dy_block_chain_trans
-- ----------------------------
DROP TABLE IF EXISTS `dy_block_chain_trans`;
CREATE TABLE `dy_block_chain_trans` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `tx_id` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '交易id',
  `in_tx_id` varchar(255) COLLATE utf8mb4_bin DEFAULT '',
  `tx_type` varchar(50) COLLATE utf8mb4_bin DEFAULT '' COMMENT '交易类型(转入/转出)  1进(充值-buy) 2出(提币-sell)',
  `chain_id` varchar(50) COLLATE utf8mb4_bin DEFAULT '' COMMENT '所属公链',
  `tx_data` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '交易信息',
  `from_addr` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '发起地址',
  `to_addr` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '目标地址',
  `amount` decimal(20,10) DEFAULT '0.**********' COMMENT '转账数量',
  `tx_fee` decimal(20,10) DEFAULT '0.**********' COMMENT '交易手续费',
  `tx_status` int(4) DEFAULT '0' COMMENT '交易状态',
  `tx_time` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '交易时间',
  `block_hash` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '区块hash',
  `recd_status` int(4) DEFAULT '0' COMMENT '记录状态,101=自审中，102=自审不过，103=平台审中，104=平台审不过，105=打币中， 106=成功, 107=扣币失败，已取消',
  `remark` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '更新时间',
  `put_status` int(4) DEFAULT '0' COMMENT '归拢状态，0：不需要归拢，1：没归拢，2：已经归拢',
  `userid` int(11) DEFAULT '0' COMMENT '对应的用户ID',
  `channel` varchar(45) COLLATE utf8mb4_bin DEFAULT '0' COMMENT '对应的渠道',
  `user_type` int(4) DEFAULT '0' COMMENT '用户类型：100=币商，200=商户用户，300=商户',
  `put_address` varchar(45) COLLATE utf8mb4_bin DEFAULT '' COMMENT '归拢地址',
  `put_txid` varchar(145) COLLATE utf8mb4_bin DEFAULT '' COMMENT '归拢的txid',
  `put_time` varchar(145) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '归拢时间',
  `get_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '实际到账数量',
  `specific_type` int(4) DEFAULT '0' COMMENT '详细类型 1-充币 2-提币 3-转入 4-转出 5-币支付',
  `txid` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '转账凭证',
  `coin_payer` int(11) DEFAULT '0' COMMENT '打币人ID',
  `coin_payer_nickname` varchar(45) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '打币人昵称',
  `coin_payer_remark` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '打币人备注',
  `hosting_adds` int(4) DEFAULT '0' COMMENT '0 -私人地址 1-托管地址 2-币支付系统地址 3 币支付商户地址',
  `hosting_status` int(4) DEFAULT '0' COMMENT '0-默认状态  1 未匹配 2 已匹配 3 已入账 4补款审核中 5补款被驳回',
  `related_order` varchar(45) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '相关订单(手动添加的冷钱包id)',
  `bill_repair_id` int(11) DEFAULT '0' COMMENT '手动调账的单号id',
  `coin_id` int(11) DEFAULT '0' COMMENT '币种ID',
  `addr_type` int(11) DEFAULT '0' COMMENT '地址类型 101-e20 102 t20',
  `coin_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '币种名字',
  `addr_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '地址名字',
  `money` decimal(20,4) DEFAULT '0.0000' COMMENT '提币金额',
  `price` decimal(20,4) DEFAULT '0.0000' COMMENT '汇率',
  `nickename` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT 'from用户昵称',
  `to_channel` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT 'to渠道号',
  `to_userid` int(11) DEFAULT '0' COMMENT 'to用户编号',
  `to_nickname` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT 'to用户昵称',
  `platform_id` int(11) DEFAULT '0' COMMENT '平台编号',
  `opt_id` int(11) DEFAULT '0' COMMENT '操作用户ID',
  `opt_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '操作用户昵称',
  `wallet_type` int(14) DEFAULT '0' COMMENT '钱包类型 0 基础钱包 1 币付钱包',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=40719 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='区块链交易记录';

-- ----------------------------
-- Table structure for dy_channel_info
-- ----------------------------
DROP TABLE IF EXISTS `dy_channel_info`;
CREATE TABLE `dy_channel_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account` varchar(145) COLLATE utf8_bin DEFAULT '' COMMENT '账号',
  `password` varchar(145) COLLATE utf8_bin DEFAULT '' COMMENT '密码',
  `web_account` varchar(255) COLLATE utf8_bin DEFAULT '' COMMENT '渠道前端账号',
  `web_password` varchar(255) COLLATE utf8_bin DEFAULT '' COMMENT '渠道前端密码',
  `channel` varchar(145) COLLATE utf8_bin DEFAULT '' COMMENT '渠道标识',
  `phonenum` varchar(145) COLLATE utf8_bin DEFAULT '' COMMENT '电话号码',
  `description` varchar(145) COLLATE utf8_bin DEFAULT '' COMMENT '描述',
  `coin_agent_num` int(11) DEFAULT '0' COMMENT '币商代理的数量',
  `shop_agent_num` int(11) DEFAULT '0' COMMENT '商户代理的数量',
  `coin_merchant_num` int(11) DEFAULT '0' COMMENT '币商的数量',
  `shop_merchant_num` int(11) DEFAULT '0' COMMENT '商户的数量',
  `paytypelist` varchar(245) COLLATE utf8_bin DEFAULT '',
  `payidlist` varchar(245) COLLATE utf8_bin DEFAULT '',
  `is_lock` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否禁用用户 0解锁  1锁定禁用',
  `userid` int(11) DEFAULT '0',
  `create_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `update_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_close` tinyint(4) DEFAULT '0' COMMENT '是否关闭此通道，1=关闭',
  `office_url` varchar(245) COLLATE utf8_bin DEFAULT '' COMMENT '渠道独立的域名',
  `server_ip` varchar(45) COLLATE utf8_bin DEFAULT '' COMMENT '服务器IP',
  `comm_type` tinyint(4) DEFAULT '0' COMMENT '返佣规则：101 = 按级差返佣，102=按保底返佣',
  `channel_deal` tinyint(4) DEFAULT '0' COMMENT '交易类型：0：公开，1:私有',
  `register_type` varchar(245) COLLATE utf8_bin DEFAULT '' COMMENT 'json格式字符串：{"phone":1,"mail":1,"name":1}  ：三个里面有至少有一个',
  `currency_type` varchar(255) COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '渠道币种 usdt,cnyb,eth,btc',
  `buy_comm_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '平台买币收益费率',
  `add_buy_comm_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '平台买币加收的手续费',
  `sell_comm_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '平台卖币收益费率',
  `add_sell_comm_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '平台卖币加收收益费率',
  `pay_json` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '支付通道及费率json',
  `channel_type` int(11) DEFAULT '100' COMMENT '100  --平台渠道  200 --币商渠道 300 --商户渠道 400-码商',
  `orders_limit` decimal(10,4) DEFAULT '0.0000' COMMENT '接单限额 人民币',
  `deal_limit` decimal(10,4) DEFAULT '0.0000' COMMENT '交易限制 人民币',
  `channle_name` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '渠道名字',
  `is_add_buy_comm` int(11) DEFAULT '0' COMMENT '是否收取手续费 0 不收，1 收',
  `fund_scheduling` int(11) DEFAULT '1' COMMENT '允许资金调度 0-允许 1 不允许',
  `is_currency_buy_rate` int(11) DEFAULT '0' COMMENT '是否收取币商手续费 0 不收，1 收',
  `currency_add_buy_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '币商买币加收收益费率',
  `is_external` int(11) DEFAULT '0' COMMENT '是否外部渠道 0-不是 1-是',
  `is_transfer_review` int(4) DEFAULT '0' COMMENT '转账是否需要审核0 需要 1 不需要',
  `platform_id` int(11) DEFAULT '0' COMMENT '平台编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=141 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='渠道列表，在创建渠道的时候，不需要生成地址，渠道拥有渠道单独的后台';

-- ----------------------------
-- Table structure for dy_chat_data
-- ----------------------------
DROP TABLE IF EXISTS `dy_chat_data`;
CREATE TABLE `dy_chat_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `otcorderid` varchar(45) COLLATE utf8_bin DEFAULT '' COMMENT 'otc服务器的订单ID',
  `pcorderid` varchar(45) COLLATE utf8_bin DEFAULT '' COMMENT '三方平台的订单ID',
  `vendor_userid` int(11) DEFAULT '0' COMMENT '卖家ID，如果有的话',
  `customer_user_id` int(11) DEFAULT '0' COMMENT '买家ID，如果有的话',
  `deal_type` int(11) DEFAULT '0' COMMENT '交易类型',
  `channel` varchar(45) COLLATE utf8_bin DEFAULT '',
  `timesec` int(11) DEFAULT '0',
  `cratetime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `chattype` int(11) DEFAULT '0' COMMENT '聊天类型：g_chatDefine.chat_type  100：系统发送的支付消息，200：系统发送的消息；300：客服发送；400：客户发给币商，500：币商发给客户',
  `msgtype` int(11) DEFAULT '0' COMMENT '消息类型：0：默认定义的消息，根据ID进行查找，1：自定义的消息。一般是客服发送。',
  `msgid` int(11) DEFAULT '0' COMMENT '定义好的msgid。分别对应不同的消息内容',
  `msg` varchar(1024) COLLATE utf8_bin DEFAULT '' COMMENT '消息内容',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11053 DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for dy_coin_info
-- ----------------------------
DROP TABLE IF EXISTS `dy_coin_info`;
CREATE TABLE `dy_coin_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `coin_id` int(11) DEFAULT '0' COMMENT '币种ID',
  `coin_name` varchar(45) COLLATE utf8_bin DEFAULT '' COMMENT '币种名字',
  `coin_type` varchar(45) COLLATE utf8_bin DEFAULT '' COMMENT '币种类型',
  `description` varchar(245) COLLATE utf8_bin DEFAULT '' COMMENT '描述',
  `buy_price` decimal(20,4) DEFAULT '0.0000' COMMENT '系统购买的价格 buy_price=free_price + add_buy_prece',
  `sell_price` decimal(20,4) DEFAULT '0.0000' COMMENT '系统出售的价格,sell_price=free_sell_price + minus_sell_prece',
  `free_price` decimal(20,4) DEFAULT '0.0000' COMMENT '从其他交易市场获取回来的价格',
  `channel` varchar(45) COLLATE utf8_bin DEFAULT '',
  `add_buy_price` decimal(20,4) DEFAULT '0.0000' COMMENT '买的汇率差，可以是正负数，buy_price=free_price + add_buy_prece',
  `minus_sell_price` decimal(20,4) DEFAULT '0.0000' COMMENT '卖的汇率差，可以是正负数，sell_price=free_sell_price + minus_sell_prece',
  `is_change` tinyint(4) DEFAULT '0' COMMENT '价格是否跟随市场价格变化，0：不变，1：改变',
  `free_sell_price` decimal(20,4) DEFAULT '0.0000' COMMENT '从火币取回来的卖的价格',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `extract_currency_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '提币手续费',
  `min_extract_currency_count` int(11) DEFAULT '0' COMMENT '最小提币数额',
  `max_extract_currency_count` int(11) DEFAULT '0' COMMENT '最大提币数额',
  `transfer_curreny_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '转币手续费',
  `min_transfer_curreny_count` int(10) DEFAULT '0' COMMENT '最小转币数额',
  `max_transfer_curreny_count` int(11) DEFAULT '0' COMMENT '最大转币数额',
  `is_fixed_buy_rate` int(4) DEFAULT '0' COMMENT '是否固定购买汇率 0 否 1是',
  `fixed_buy_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '固定购买汇率',
  `is_fixed_sell_rate` int(4) DEFAULT '0' COMMENT '是否固定出售汇率 0 否 ， 1是',
  `fixed_sell_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '固定出售汇率 ',
  `status` int(4) DEFAULT '0' COMMENT '0-启用 1 禁止',
  `minus_coin_pay_price` decimal(20,4) DEFAULT '0.0000' COMMENT '币支付的汇率差，可以是正负数，free_price+ minus_coin_pay_price',
  `is_fixed_coin_pay_rate` int(4) DEFAULT '0' COMMENT '是否固定币支付汇率 0 否 ， 1是',
  `fixed_coin_pay_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '固定币支付汇率 ',
  `extract_currency_rate_trc` decimal(10,4) DEFAULT '0.0000' COMMENT '提币手续费',
  `withdraw_coin_price_diff` decimal(10,4) DEFAULT '0.0000' COMMENT '提币价格差',
  `is_withdraw_coin_pay_rate` int(4) DEFAULT '0' COMMENT '是否固定提币汇率 0 否 ， 1是',
  `withdraw_coin_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '固定提币汇率 ',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=181 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='币种描述';

-- ----------------------------
-- Table structure for dy_customer_order
-- ----------------------------
DROP TABLE IF EXISTS `dy_customer_order`;
CREATE TABLE `dy_customer_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `type` int(1) NOT NULL COMMENT '顾客买卖类型 0-买,1-卖',
  `vendor_order_id` bigint(20) NOT NULL COMMENT '所关联的币商挂单id',
  `customer_user_id` int(11) NOT NULL COMMENT '顾客user_id',
  `vendor_user_id` int(11) NOT NULL COMMENT '币商user_id',
  `price` decimal(20,4) NOT NULL COMMENT '成交单价',
  `amount` decimal(20,10) NOT NULL COMMENT '成交数量',
  `money` decimal(20,4) NOT NULL COMMENT '成交金额',
  `status` int(2) NOT NULL DEFAULT '1' COMMENT '状态 1=未付款，2=超时取消，3=已付款，4=付款超时， 5=冻结，6=超时完成，7=已完成， 8=已取消， 9=申诉中 10=回复申诉中， 11=申诉完成，12=冻结失败， 13=审核中 14=匹配中，15=审核不通过 16申诉取消， 17-未匹配',
  `merchant_order_id` varchar(255) CHARACTER SET utf8mb4 DEFAULT '0' COMMENT '所关联的商户相关订单id',
  `fee_rate` decimal(10,5) DEFAULT '0.00000' COMMENT '买方手续费比例',
  `fee` decimal(20,10) DEFAULT '0.**********' COMMENT '手续费',
  `proof_url` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '付款凭证',
  `pay_type` varchar(1024) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '这里的支付方式应该是一个列表，提供多个支付方式',
  `pay_id` varchar(1024) COLLATE utf8mb4_bin DEFAULT '' COMMENT '对应支付id，能找到支付类型',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `pay_time` datetime DEFAULT '2020-01-01 00:00:00' COMMENT '支付时间',
  `pass_time` varchar(45) COLLATE utf8mb4_bin DEFAULT '' COMMENT '放行时间',
  `channel` varchar(45) COLLATE utf8mb4_bin DEFAULT '' COMMENT '渠道标识',
  `publiceprice` decimal(10,4) DEFAULT '0.0000' COMMENT '公共的币的价格',
  `from_type` int(1) DEFAULT '0' COMMENT '订单类型：1：普通订单，2：三方订单：3:：提现订单',
  `notify_url` varchar(512) COLLATE utf8mb4_bin DEFAULT '' COMMENT '商户客户下的订单的时候，传入的通知回调',
  `body` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '商户客户的订单，给商户返回的参数',
  `sell_fee_rate` decimal(10,5) DEFAULT '0.00000' COMMENT '卖方费率，有需要，在商户卖币提现的时候，需要收取卖方费率',
  `sell_fee` decimal(20,10) DEFAULT '0.**********' COMMENT '费率总额',
  `cancel_time` datetime DEFAULT '2001-01-01 00:00:01' COMMENT '取消时间',
  `buy_fee_rate` decimal(10,5) DEFAULT '0.00000' COMMENT '买方费率，',
  `buy_fee` decimal(20,8) DEFAULT '0.00000000' COMMENT '买方需要支付的手续费总额',
  `paypal_order_id` varchar(45) COLLATE utf8mb4_bin DEFAULT '' COMMENT '支付平台的订单ID。例如支付宝，或者微信产生的订单ID。如果没有，则为空',
  `user_pay_id` int(11) DEFAULT '0' COMMENT '用户用的具体的支付ID',
  `deal_type` int(3) DEFAULT '0' COMMENT '顾客买卖类型 ，100=币商交易，200=商户用户，300=商户自己',
  `after_money` decimal(20,4) DEFAULT '0.0000' COMMENT '到账人民币的金额',
  `customer_ip` varchar(45) COLLATE utf8mb4_bin DEFAULT '' COMMENT '顾客进行交易的IP',
  `coin_id` int(11) DEFAULT '0' COMMENT '对应币种的ID',
  `refuse_mark` varchar(245) COLLATE utf8mb4_bin DEFAULT '' COMMENT '拒绝放币的理由',
  `payee_account` varchar(145) COLLATE utf8mb4_bin DEFAULT '' COMMENT '收款人账号',
  `payee_name` varchar(145) COLLATE utf8mb4_bin DEFAULT '' COMMENT '收款人名字',
  `payee_bank` varchar(145) COLLATE utf8mb4_bin DEFAULT '' COMMENT '收款银行',
  `payee_band_addr` varchar(145) COLLATE utf8mb4_bin DEFAULT '' COMMENT '收款银行地址',
  `notify_amount` int(11) DEFAULT '0' COMMENT '0是没有回调。非0是回调次数',
  `get_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '到账币的数量',
  `add_fee_num` decimal(10,4) DEFAULT '0.0000' COMMENT '追加手续费',
  `income` decimal(20,10) DEFAULT '0.**********' COMMENT '收益',
  `only_mark` varchar(245) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '暂时做生成订单试的唯一标识， 订单号改由代码生成后就可以弃用',
  `vendor_channel` varchar(245) CHARACTER SET utf8mb4 DEFAULT '' COMMENT 'customer_user_id 的渠道',
  `withdraw_type` int(11) DEFAULT '0' COMMENT '提现类型， 0 otc 1 代付',
  `coin_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '币种名字',
  `is_external` int(255) DEFAULT '0' COMMENT '是否外部渠道 0-不是 1-是',
  `fee_money` decimal(20,4) DEFAULT '0.0000' COMMENT '手续费法币',
  `trade_id` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '交易所订单ID',
  `history_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '历史币数量',
  `history_money` decimal(20,4) DEFAULT '0.0000' COMMENT '历史法币数量',
  `replenishment_remarks` varchar(1024) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '补单备注',
  `platform_id` int(11) DEFAULT '0' COMMENT '平台编号',
  `vendor_platform_id` int(11) DEFAULT '0' COMMENT '平台编号',
  `chain_type` int(11) DEFAULT '0' COMMENT '链类型',
  `chain_name` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '链名称',
  `chain_addr` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '链地址',
  `coin_pay_txid` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '补单或比配成功是的TXID',
  `is_wait` int(4) DEFAULT '0' COMMENT '提现单等待释放 0-已释放 1-等待释放',
  `owership` int(4) DEFAULT '0' COMMENT '0-系统地址 1-商户自备地址',
  `wallet_type` int(14) DEFAULT '0' COMMENT '钱包类型 0 基础钱包 1 币付钱包',
  `is_face_to_face` int(4) DEFAULT '0' COMMENT '是否面对面交易 0 否 1 是',
  `deal_addr` varchar(512) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '交易地址',
  `check_code` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '校验码',
  `verify_password` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT '校验口令',
  `check_status` int(4) DEFAULT '0' COMMENT '校验状态 0未校验 1 已校验',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=673672 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='用户买卖币记录';

-- ----------------------------
-- Table structure for dy_customer_permission
-- ----------------------------
DROP TABLE IF EXISTS `dy_customer_permission`;
CREATE TABLE `dy_customer_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parent_id` int(11) NOT NULL DEFAULT '0' COMMENT '父节点id',
  `name` varchar(255) COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '菜单名称',
  `keyword` varchar(500) COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '菜单关键字',
  `keyword_web` varchar(500) COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '前端关键字',
  `menuUrl` varchar(500) COLLATE utf8_bin DEFAULT NULL COMMENT '前端链接',
  `request_url` varchar(500) COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '请求链接',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '权限类型 1普通权限  2特殊权限',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `user_type` int(4) DEFAULT '0' COMMENT '用户类型这里跟user_info 的类型一致',
  `level` tinyint(1) DEFAULT NULL COMMENT '等级',
  PRIMARY KEY (`id`),
  KEY `keyword` (`keyword`) USING BTREE,
  KEY `keyword_web` (`keyword_web`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=168 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='客服后台权限表';

-- ----------------------------
-- Table structure for dy_group_info
-- ----------------------------
DROP TABLE IF EXISTS `dy_group_info`;
CREATE TABLE `dy_group_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '小组名称',
  `channel` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '渠道',
  `group_num` int(11) DEFAULT '0' COMMENT '小组人数',
  `group_recharge_switch` int(4) DEFAULT '1' COMMENT '团队充值开关 0-关闭 1-开启',
  `group_withdraw_switch` int(4) DEFAULT '1' COMMENT '团队提现开关 0-关闭 1-开启',
  `group_behalf_switch` int(4) DEFAULT '1' COMMENT '团队代付开关 0-关闭 1-开启',
  `platform_id` int(11) DEFAULT '0' COMMENT '平台编号',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

-- ----------------------------
-- Table structure for dy_group_member
-- ----------------------------
DROP TABLE IF EXISTS `dy_group_member`;
CREATE TABLE `dy_group_member` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_id` int(11) DEFAULT '0' COMMENT '小组ID',
  `group_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '小组名称',
  `group_user_id` int(11) DEFAULT '0' COMMENT '小组成员的ID',
  `group_user_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '小组成员的昵称',
  `status` int(11) DEFAULT '0' COMMENT '成员状态 1-正常 2-删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `group_recharge_switch` int(4) DEFAULT '1' COMMENT '团队充值开关 0-关闭 1-开启',
  `group_withdraw_switch` int(4) DEFAULT '1' COMMENT '团队提现开关 0-关闭 1-开启',
  `group_behalf_switch` int(4) DEFAULT '1' COMMENT '团队代付开关 0-关闭 1-开启',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

-- ----------------------------
-- Table structure for dy_mer_access
-- ----------------------------
DROP TABLE IF EXISTS `dy_mer_access`;
CREATE TABLE `dy_mer_access` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '权限id',
  `request_url` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT 'API请求url',
  `create_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `type` int(11) DEFAULT '0' COMMENT '类型，100=导航一级，200=导航二级，300=页面按钮',
  `name` varchar(45) CHARACTER SET utf8 DEFAULT '' COMMENT '接口名称',
  `parent_id` int(11) DEFAULT '0' COMMENT '父id',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `op_code` varchar(45) CHARACTER SET utf8 DEFAULT '' COMMENT '操作码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

-- ----------------------------
-- Table structure for dy_message_notice
-- ----------------------------
DROP TABLE IF EXISTS `dy_message_notice`;
CREATE TABLE `dy_message_notice` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '消息通知id',
  `type` int(2) DEFAULT '0' COMMENT '类型(0.无)',
  `title` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '标题',
  `content` text COLLATE utf8mb4_bin NOT NULL COMMENT '内容',
  `user_id` int(11) DEFAULT NULL COMMENT '消息发送的对象',
  `enable_status` int(1) DEFAULT '0' COMMENT '启用状态 启用-1、禁用-0',
  `remark` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `is_read` int(1) DEFAULT '0' COMMENT '是否已读(0未读，1已读)',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `channel` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '渠道号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;

-- ----------------------------
-- Table structure for dy_money_flow
-- ----------------------------
DROP TABLE IF EXISTS `dy_money_flow`;
CREATE TABLE `dy_money_flow` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) DEFAULT '0',
  `nickname` varchar(45) COLLATE utf8_bin DEFAULT '',
  `channel` varchar(45) COLLATE utf8_bin DEFAULT NULL,
  `coin_type` int(11) DEFAULT '0' COMMENT '币的类型',
  `coin_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '变化的金额',
  `money_amount` decimal(10,2) DEFAULT '0.00' COMMENT '资金流水类型：',
  `price` decimal(10,5) DEFAULT NULL COMMENT '汇率',
  `user_coin` decimal(20,10) DEFAULT NULL COMMENT '结算后持币量',
  `user_money` decimal(10,2) DEFAULT NULL COMMENT '结算后，持法币量',
  `orderid` varchar(45) COLLATE utf8_bin DEFAULT NULL COMMENT '订单ID',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='资金流水';

-- ----------------------------
-- Table structure for dy_notice_content
-- ----------------------------
DROP TABLE IF EXISTS `dy_notice_content`;
CREATE TABLE `dy_notice_content` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '公告id',
  `title` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '标题',
  `content` text COLLATE utf8mb4_bin COMMENT '内容',
  `type` int(1) DEFAULT '0' COMMENT '类型(0-无)',
  `expiration_time` datetime DEFAULT NULL COMMENT '公告过期时间',
  `weight` int(4) DEFAULT '0' COMMENT '排序权重',
  `enable_status` int(1) DEFAULT NULL COMMENT '是否有效(0-无效,1-有效)',
  `remark` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `channel` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '渠道号',
  `userid` int(11) DEFAULT NULL COMMENT '发布者id',
  `nickname` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发布者名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;

-- ----------------------------
-- Table structure for dy_order_appeal
-- ----------------------------
DROP TABLE IF EXISTS `dy_order_appeal`;
CREATE TABLE `dy_order_appeal` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `deal_orderid` bigint(20) DEFAULT '0' COMMENT '交易的订单ID',
  `from_userid` int(11) DEFAULT '0' COMMENT '申诉人ID',
  `to_userid` int(11) DEFAULT '0' COMMENT '被申诉人ID',
  `reason` varchar(255) COLLATE utf8_bin DEFAULT '' COMMENT '申诉原因',
  `detail` varchar(245) COLLATE utf8_bin DEFAULT '' COMMENT '申诉详情',
  `proof_url` varchar(1024) COLLATE utf8_bin DEFAULT '' COMMENT '证据图片的url',
  `reply_reason` varchar(245) COLLATE utf8_bin DEFAULT '' COMMENT '回复 原因',
  `reply_detail` varchar(245) COLLATE utf8_bin DEFAULT '' COMMENT '回复详情',
  `reply_url` varchar(245) COLLATE utf8_bin DEFAULT '' COMMENT '回复的证据url',
  `status` int(11) DEFAULT '1' COMMENT '1=申诉中，4=完结 5=用户放行，申诉取消， 6-取消申诉',
  `sys_remark` varchar(145) COLLATE utf8_bin DEFAULT '' COMMENT '客服备注',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `reply_time` varchar(245) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '回复时间',
  `ruling_result` int(11) DEFAULT '0' COMMENT '裁决结果 0-未裁决 1-买放胜 2-卖方胜',
  `buy_punishment` int(11) DEFAULT '0' COMMENT '处理买方 0-未裁决 1-不惩罚 2-账户冻结24小时 3-永久冻结账户',
  `sell_punishment` int(11) DEFAULT '0' COMMENT '处理卖方 0-未裁决 1-不惩罚 2-账户冻结24小时 3-永久冻结账户',
  `deal_type` int(3) DEFAULT '0' COMMENT '顾客买卖类型 ，100=币商交易(站内)，200=商户用户(充值)，300=商户自己(提现)',
  `platform_id` int(11) DEFAULT '0' COMMENT '平台编号',
  `appeal_order_type` int(4) DEFAULT '0' COMMENT '申诉的订单类型：1-otc订单 2-兑换店订单',
  `order_status` int(4) DEFAULT '0' COMMENT '申诉前订单的状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=360 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='申诉表';

-- ----------------------------
-- Table structure for dy_pay_info
-- ----------------------------
DROP TABLE IF EXISTS `dy_pay_info`;
CREATE TABLE `dy_pay_info` (
  `pay_id` int(11) NOT NULL COMMENT '这是唯一的ID，在支付的类型中，表示识别码。由添加新的通道的时候确认。',
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `payment` varchar(45) DEFAULT '' COMMENT '支付类型识别：zfb',
  `channelname` varchar(128) DEFAULT '',
  `description` varchar(255) DEFAULT '',
  `mark` varchar(125) DEFAULT '',
  `state` int(11) NOT NULL DEFAULT '1' COMMENT '1=开始，0=关闭',
  `deal_time` int(11) DEFAULT '0' COMMENT '交易笔数',
  `merchant_min_rate` double(11,4) NOT NULL DEFAULT '0.0000' COMMENT '商户最低费率',
  `channel_min_rate` double(11,4) NOT NULL DEFAULT '0.0000' COMMENT '渠道最低费率',
  `channel_max_rate` double(11,4) NOT NULL DEFAULT '0.0000' COMMENT '渠道最高费率',
  `merchant_num` int(11) NOT NULL DEFAULT '0' COMMENT '对接商户数',
  `deal_num` int(11) NOT NULL DEFAULT '0' COMMENT '交易笔数',
  `is_lock` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启  0开启 1锁定',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8422 DEFAULT CHARSET=utf8 COMMENT='支付类型 8101,8201,8301';

-- ----------------------------
-- Table structure for dy_role
-- ----------------------------
DROP TABLE IF EXISTS `dy_role`;
CREATE TABLE `dy_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '角色id',
  `role_name` varchar(45) CHARACTER SET utf8 DEFAULT NULL COMMENT '角色名',
  `create_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `access_list` varchar(1024) CHARACTER SET utf8 DEFAULT '' COMMENT '角色对应权限，英文逗号隔开',
  `role_type` int(11) DEFAULT '0' COMMENT '角色平台类型 100=客服后台，200=商户后台',
  `userid` int(11) DEFAULT '0' COMMENT '此字段用于商户后台，定义哪位商户的角色',
  `is_lock` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启 0开启 1锁定',
  `describe` varchar(500) COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '描述',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=44 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

-- ----------------------------
-- Table structure for dy_sms_monitor
-- ----------------------------
DROP TABLE IF EXISTS `dy_sms_monitor`;
CREATE TABLE `dy_sms_monitor` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` int(11) DEFAULT '0' COMMENT '用户ID',
  `nick_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '用户昵称',
  `pay_name` varchar(255) DEFAULT '' COMMENT '收款方式， 支付宝， 中国银行之类的',
  `from_telephone` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '发送电话',
  `sms_content` varchar(255) DEFAULT '' COMMENT '短信内容',
  `receive_time` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '短信收取时间',
  `order_id` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '订单ID',
  `status` int(11) DEFAULT '0' COMMENT '0-未匹配 1-已匹配',
  `remarks` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '异常原因',
  `pay_id` int(11) DEFAULT '0' COMMENT '支付方式 8101 之类的',
  `channel` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '渠道标识',
  `trans_amount` decimal(20,2) DEFAULT '0.00' COMMENT '交易金额',
  `bank_blance` decimal(20,2) DEFAULT '0.00' COMMENT '银行余额',
  `cardnum` varchar(45) DEFAULT '' COMMENT '银行卡号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=287 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for dy_sub_user_info
-- ----------------------------
DROP TABLE IF EXISTS `dy_sub_user_info`;
CREATE TABLE `dy_sub_user_info` (
  `userid` int(11) NOT NULL AUTO_INCREMENT COMMENT '子账号id',
  `account` varchar(100) CHARACTER SET utf8 NOT NULL COMMENT '账号，不对唯一性做检查',
  `password` varchar(45) CHARACTER SET utf8 NOT NULL COMMENT '登陆密码',
  `regdate` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `fundpassword` varchar(45) CHARACTER SET utf8 DEFAULT '' COMMENT '资金密码',
  `user_type` int(11) DEFAULT '0' COMMENT '子账号类型 100=客服后台，200=商户后台',
  `prohibit_login` int(4) DEFAULT '0' COMMENT '禁止登陆 0 允许登陆 1 禁止登陆',
  `username` varchar(128) CHARACTER SET utf8 DEFAULT '' COMMENT '用户名称',
  `agent` int(11) DEFAULT '0' COMMENT '直属上级userid',
  `roleid` int(11) DEFAULT '0' COMMENT '角色id',
  `updatedate` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `lasttime` int(11) DEFAULT '0' COMMENT '最后一次登陆时间，时间戳',
  `ip` varchar(45) CHARACTER SET utf8 DEFAULT '' COMMENT '最后一次登陆ip',
  `is_lock` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启 0开启 1锁定',
  `google_auth_secret` varchar(45) CHARACTER SET utf8 DEFAULT '' COMMENT '谷歌秘钥',
  `is_verified_google` tinyint(1) NOT NULL DEFAULT '2' COMMENT '是否验证谷歌秘钥  1=验证 2=不验证',
  `platform_id` int(11) DEFAULT '0' COMMENT '0-官方平台， 大于0子平台ID',
  PRIMARY KEY (`userid`)
) ENGINE=InnoDB AUTO_INCREMENT=69 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

-- ----------------------------
-- Table structure for dy_subsystem_info
-- ----------------------------
DROP TABLE IF EXISTS `dy_subsystem_info`;
CREATE TABLE `dy_subsystem_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `platform_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '平台名称',
  `accout` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '账号',
  `recharge_rate_list` varchar(512) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '充值费率列表json格式',
  `withdraw_rate_list` varchar(512) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '提现费率列表json格式',
  `behalf_rate_list` varchar(512) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '代付费率列表json格式',
  `related_shop_channel_list` varchar(512) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '关联的商户代理列表json',
  `related_coin_channel_list` varchar(512) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '关联的币商渠道列表json',
  `contact_etails` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '联系方式',
  `close_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '归拢服务费率',
  `take_coin_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '提币服务费率',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for dy_temp_order
-- ----------------------------
DROP TABLE IF EXISTS `dy_temp_order`;
CREATE TABLE `dy_temp_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) DEFAULT '0' COMMENT '用户ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `payee_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '收款人名字',
  `payee_account` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '收款人银行卡号',
  `payee_bank` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '收款银行/提币地址',
  `money` decimal(10,4) DEFAULT '0.0000' COMMENT '金额',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `type` int(11) NOT NULL DEFAULT '0' COMMENT '导入数据类型，100=提现，200=提币',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3081 DEFAULT CHARSET=utf8 COMMENT='批量导入表';

-- ----------------------------
-- Table structure for dy_transfer_energy_order
-- ----------------------------
DROP TABLE IF EXISTS `dy_transfer_energy_order`;
CREATE TABLE `dy_transfer_energy_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `from_user_id` int(11) DEFAULT '0' COMMENT '来源用户id',
  `from_nickename` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '来源用户昵称',
  `from_channel` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '来源用户渠道',
  `from_platform_id` int(11) DEFAULT '0' COMMENT '来源用户渠道',
  `energy_value` int(20) DEFAULT '0' COMMENT '能量值',
  `to_user_id` int(11) DEFAULT '0' COMMENT '目标用户id',
  `to_nickename` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '目标用户昵称',
  `to_channel` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '目标用户渠道',
  `to_platform_id` int(11) DEFAULT '0' COMMENT '目标用户渠道',
  `remarks` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '备注',
  `opt_type` int(4) DEFAULT '3' COMMENT '1-增加 2-减少 3-转让',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='转让能量订单表';

-- ----------------------------
-- Table structure for dy_trusteeship_address_pre
-- ----------------------------
DROP TABLE IF EXISTS `dy_trusteeship_address_pre`;
CREATE TABLE `dy_trusteeship_address_pre` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增唯一id',
  `coin_addr` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT 'USDT币地址',
  `password` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT 'plaintext',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` int(11) DEFAULT '0' COMMENT '0 启用 1 关闭 2 删除',
  `addr_type` int(11) DEFAULT '0' COMMENT '地址类型 101-e20 102 t20',
  `addr_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '地址名字',
  `channel` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '渠道',
  `type` int(4) DEFAULT '0' COMMENT '1-冷钱包充值地址  2-归拢地址',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1130 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for dy_trusteeship_wallet_order
-- ----------------------------
DROP TABLE IF EXISTS `dy_trusteeship_wallet_order`;
CREATE TABLE `dy_trusteeship_wallet_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_id` varchar(45) COLLATE utf8mb4_bin DEFAULT '' COMMENT '订单号',
  `channel` varchar(45) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '渠道号',
  `userid` int(11) DEFAULT '0' COMMENT '用户id',
  `nickname` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '用户昵称',
  `currency_type` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '币种',
  `amount` decimal(20,10) DEFAULT '0.**********' COMMENT '币数量',
  `actual_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '实际数量',
  `trusteeship` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '冷钱包地址',
  `user_txid` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '用户上传TXID',
  `create` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `remarks` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '备注',
  `status` int(11) DEFAULT '0' COMMENT '0-未到账 1-已到账 2-已取消',
  `coin_id` int(11) DEFAULT '0' COMMENT '币种ID',
  `addr_type` int(11) DEFAULT '0' COMMENT '地址类型 101-e20 102 t20',
  `coin_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT 'dy_block_chain_trans',
  `addr_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '地址名字',
  `platform_id` int(11) DEFAULT '0' COMMENT '平台编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=129 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT COMMENT='托管钱包表';

-- ----------------------------
-- Table structure for dy_user_address_pre
-- ----------------------------
DROP TABLE IF EXISTS `dy_user_address_pre`;
CREATE TABLE `dy_user_address_pre` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增唯一id',
  `user_id` int(11) DEFAULT '0' COMMENT '所属用户id',
  `coin_addr` varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT 'USDT币地址',
  `password` varchar(255) COLLATE utf8mb4_bin DEFAULT '' COMMENT 'plaintext',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `channel` varchar(45) COLLATE utf8mb4_bin DEFAULT '' COMMENT '对应的channel',
  `status` int(11) DEFAULT '0' COMMENT '状态 0=可用，1=无效',
  `addr_type` int(11) DEFAULT '0' COMMENT '地址类型 101-e20 102 t20',
  `addr_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '地址名字',
  `use_type` int(4) DEFAULT '0' COMMENT '0-充币地址， 1-币支付地址',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `coin_addr` (`coin_addr`) USING BTREE COMMENT '地址唯一'
) ENGINE=InnoDB AUTO_INCREMENT=177618 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for dy_user_api
-- ----------------------------
DROP TABLE IF EXISTS `dy_user_api`;
CREATE TABLE `dy_user_api` (
  `userid` int(11) NOT NULL COMMENT 'id',
  `pay_secret` varchar(45) CHARACTER SET utf8 DEFAULT '' COMMENT '接入秘钥',
  `return_secret` varchar(45) CHARACTER SET utf8 DEFAULT '' COMMENT '回调秘钥',
  `return_url` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '各支付通道回调地址',
  `create_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `default_return_url` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '默认回调地址',
  `white_ip` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '白名单',
  `api_url` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT 'API接口地址',
  `update_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `sell_return_url` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '提现回调地址',
  PRIMARY KEY (`userid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

-- ----------------------------
-- Table structure for dy_user_coin
-- ----------------------------
DROP TABLE IF EXISTS `dy_user_coin`;
CREATE TABLE `dy_user_coin` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) DEFAULT '0',
  `nickname` varchar(45) COLLATE utf8_bin DEFAULT '',
  `coin_id` int(11) DEFAULT '0' COMMENT '对应dy_coin_info的coin_id',
  `coin_name` varchar(45) COLLATE utf8_bin DEFAULT '' COMMENT '对应dy_coin_info的名字',
  `user_address` varchar(45) COLLATE utf8_bin DEFAULT '' COMMENT '用户在改分链上的地址',
  `coin_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '用户持币数量',
  `coin_lock_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '被锁定数量',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '每次更新时间',
  `channel` varchar(45) COLLATE utf8_bin DEFAULT '',
  `remark` varchar(45) COLLATE utf8_bin DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=782 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='用户币的信息表';

-- ----------------------------
-- Table structure for dy_user_conf
-- ----------------------------
DROP TABLE IF EXISTS `dy_user_conf`;
CREATE TABLE `dy_user_conf` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) DEFAULT '0',
  `bind_userid` int(11) DEFAULT '0' COMMENT '币商ID，如果币商ID为0.则表示全部公开的都可以接单，如果这个币商的ID是渠道的ID，那么说明',
  `channel` varchar(45) COLLATE utf8_bin DEFAULT '' COMMENT '如果绑定类型是针对渠道的，那么就是渠道标识',
  `channel_name` varchar(45) COLLATE utf8_bin DEFAULT '',
  `bind_type` int(11) DEFAULT '0' COMMENT '绑定类型: 101是针对渠道，102=针对当个币商，103=针对团队，104=针对小组',
  `is_exist` tinyint(4) DEFAULT '1' COMMENT '是否生效 1生效(开启)  0不生效(关闭)',
  `deal_type` tinyint(4) DEFAULT '0' COMMENT '交易类型：0=买，1=卖，2提现代付',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=467 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='商户和币商的关联配置\r\n接单配置表';

-- ----------------------------
-- Table structure for dy_user_info
-- ----------------------------
DROP TABLE IF EXISTS `dy_user_info`;
CREATE TABLE `dy_user_info` (
  `userid` int(11) NOT NULL AUTO_INCREMENT,
  `cid` varchar(45) NOT NULL COMMENT '设备唯一的标识',
  `account` varchar(45) NOT NULL COMMENT '账号，不对唯一性做检查',
  `password` varchar(45) NOT NULL,
  `nickname` varchar(45) DEFAULT '' COMMENT '昵称',
  `eth_address` varchar(45) NOT NULL DEFAULT '' COMMENT 'eth地址',
  `face_1` varchar(45) DEFAULT '',
  `regdate` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `sex` int(11) DEFAULT '0',
  `age` int(11) DEFAULT '0',
  `email` varchar(45) DEFAULT '',
  `phonenum` varchar(45) DEFAULT '',
  `eth_amount` decimal(20,10) DEFAULT '0.**********' COMMENT 'eth的数量',
  `erc_usdt_amount` decimal(20,10) DEFAULT '0.**********' COMMENT 'erc20的usdt的数量',
  `erc_usdt_lock_amount` decimal(20,10) DEFAULT '0.**********',
  `channel` varchar(45) DEFAULT '' COMMENT '渠道包',
  `invite_code` varchar(45) DEFAULT '' COMMENT '自己的邀请码',
  `bind_code` varchar(45) DEFAULT '' COMMENT '绑定的上级邀请码',
  `imei` varchar(45) DEFAULT '',
  `devname` varchar(45) DEFAULT '',
  `macname` varchar(45) DEFAULT '',
  `mobiletype` int(11) DEFAULT '0',
  `lasttime` int(11) DEFAULT '0',
  `penulttime` int(11) DEFAULT '0',
  `isban` int(11) DEFAULT '0',
  `description` varchar(129) DEFAULT '',
  `blacklist` int(11) DEFAULT '0' COMMENT '列入黑名单',
  `ip` varchar(45) DEFAULT '',
  `province` varchar(45) DEFAULT '' COMMENT '省份',
  `city` varchar(45) DEFAULT '' COMMENT '城市',
  `payidlist` varchar(255) DEFAULT '' COMMENT '支付的支付类型，固定到每个人去配置',
  `bindtype` int(11) DEFAULT '0' COMMENT '注册类型 1 手机注册',
  `fundpassword` varchar(45) DEFAULT '' COMMENT '资金密码',
  `user_type` int(11) DEFAULT '0' COMMENT '用户类型 100=平台账号，用于资金归集；200=渠道代理(码商)；201=码商； 202=币商;300=商户代理，301=商户',
  `admin_account` varchar(45) DEFAULT '' COMMENT '部分商户，会使用这个账号，登入后台查看',
  `admin_password` varchar(45) DEFAULT '',
  `status` int(2) NOT NULL DEFAULT '1' COMMENT '0全部 1正常 2待审核 3审核不通过 4已锁定 5已删除',
  `remark` varchar(255) DEFAULT '' COMMENT '描述',
  `is_lock` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否锁定 0解锁 1锁定',
  `team_user_num` int(11) DEFAULT '0' COMMENT '团队人数',
  `dir_user_num` int(11) DEFAULT '0' COMMENT '直属人数',
  `agent` int(11) DEFAULT '0' COMMENT '直属上级',
  `is_hang_buy` int(2) NOT NULL DEFAULT '1' COMMENT '是否允许挂买单，1=允许 2不允许',
  `is_hang_sell` int(2) NOT NULL DEFAULT '1' COMMENT '是否允许挂卖单，1=允许 2不允许',
  `min_buy` decimal(20,2) DEFAULT '0.00' COMMENT '最小购买',
  `max_buy` decimal(20,2) DEFAULT '0.00' COMMENT '最大购买',
  `min_sell` decimal(20,2) DEFAULT '0.00' COMMENT '最小出售',
  `max_sell` decimal(20,2) DEFAULT '0.00' COMMENT '最大出售',
  `comm_type` int(4) DEFAULT '0' COMMENT '收益类型：101=按级差进行收益，后期添加其他',
  `prohibit_login` int(4) DEFAULT '0' COMMENT '禁止登陆 0 允许登陆 1 禁止登陆',
  `username` varchar(45) DEFAULT '' COMMENT '用户名称',
  `is_accept_order` int(4) DEFAULT '0' COMMENT '是否允许接单 0 允许1 禁止',
  `deal_last_tiime` datetime DEFAULT '2020-01-01 00:00:00' COMMENT '最后一次交易时间 针对挂单交易',
  `is_invited` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否允许开设代理 0允许 1禁止',
  `back_mobile` varchar(45) DEFAULT '' COMMENT '联系方式，仅后台展示',
  `ban_time` varchar(255) CHARACTER SET utf8mb4 DEFAULT '2020-01-01 00:00:00' COMMENT '封禁时间',
  `is_allow_loginback` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否允许登陆渠道管理后台 0 允许1 禁止',
  `google_auth_secret` varchar(45) DEFAULT '' COMMENT '谷歌秘钥',
  `is_verified_google` tinyint(1) NOT NULL DEFAULT '2' COMMENT '是否验证谷歌秘钥  1=验证 2=不验证',
  `is_up_payername` int(4) DEFAULT '0' COMMENT '0 不用上传付款人 1 要上传付款人',
  `is_up_payvoucher` int(4) DEFAULT '0' COMMENT '0 不用上传支付凭证 1 要上传支付凭证',
  `is_otc` int(4) DEFAULT '0' COMMENT '是否开启OTC交易 0 开启 1 不开',
  `is_behalf_pay` int(4) DEFAULT '0' COMMENT '是否开启代付交易 0 开启 1 不开',
  `erc_fc_amount` decimal(20,4) DEFAULT '0.0000' COMMENT '法定货币数量',
  `erc_fc_lock_amount` decimal(20,4) DEFAULT '0.0000' COMMENT '法定货币冻结数量',
  `deal_coin_type` int(4) NOT NULL DEFAULT '0' COMMENT '成交货币类型 0 数字货币 1 法币',
  `energy_value` decimal(20,4) DEFAULT '0.0000' COMMENT '能量值',
  `weights_value` decimal(20,4) DEFAULT '1.0000' COMMENT '权重值',
  `team_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '团队名称',
  `platform_id` int(11) DEFAULT '0' COMMENT '平台编号',
  `is_team_accept_order` int(11) DEFAULT '0' COMMENT '是否禁止团队接单 0 否 1 是',
  `pre_authorization` int(20) DEFAULT '0' COMMENT '预售提现额',
  `coin_pay_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '币支付费率',
  `coin_pay_erc` int(4) DEFAULT '0' COMMENT '是否开启erc充币 0-关闭 1-开启',
  `coin_pay_trc` int(4) DEFAULT '0' COMMENT '是否开启trc充币 0-关闭 1-开启',
  `allow_sys_addr` int(4) DEFAULT '0' COMMENT '是否允许使用系统地址 0-否 1-是',
  `sys_erc_min` decimal(20,10) DEFAULT '0.**********' COMMENT '使用系统erc地址最小值',
  `coin_pay_usdt_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '币付钱包的USDT数量',
  `coin_pay_usdt_lock_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '币付冻结钱包的usdt数量',
  `is_check` int(4) DEFAULT '0' COMMENT '重复订单检查 0 检查 1 不检查',
  `extract_currency_rate_erc` decimal(10,4) DEFAULT '0.0000' COMMENT 'erc的提币手续费',
  `extract_currency_rate_trc` decimal(10,4) DEFAULT '0.0000' COMMENT 'trc的提币手续费',
  PRIMARY KEY (`userid`)
) ENGINE=InnoDB AUTO_INCREMENT=614054 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for dy_user_pay
-- ----------------------------
DROP TABLE IF EXISTS `dy_user_pay`;
CREATE TABLE `dy_user_pay` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pay_id` int(11) DEFAULT '0' COMMENT '支付ID，和st_pay_info中的pay_id一一对应',
  `description` varchar(255) DEFAULT '',
  `name` varchar(45) DEFAULT '' COMMENT '名字',
  `account` varchar(255) DEFAULT '' COMMENT '账号',
  `bank_name` varchar(128) DEFAULT '' COMMENT '银行名称',
  `bank_addr` varchar(128) DEFAULT '' COMMENT '银行地址',
  `qr_code` varchar(255) DEFAULT '' COMMENT '二维码信息',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `single_limit` decimal(20,2) DEFAULT '0.00' COMMENT '单笔限额',
  `max_limit` decimal(20,2) DEFAULT '0.00' COMMENT '最大限额',
  `day_limit` decimal(20,2) DEFAULT '0.00' COMMENT '每日限额',
  `userid` int(11) DEFAULT '0' COMMENT '用户ID',
  `status` int(11) DEFAULT '0' COMMENT '0 失效 1正常 2 已删除 3-待审核 4-审核不通过',
  `channel` varchar(255) DEFAULT '' COMMENT '渠道号',
  `buy_fee_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '利用这个支付方式 ，买币需要支付的手续费',
  `sell_fee_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '利用这个通道，卖币的时候，需要支付的手续费',
  `buy_comm_rate` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '用户当币商，利用这个通道，买币进来，赚取佣金的手续费',
  `sell_comm_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '用户当币商，利用这个通道，卖币能够赚取的手续费',
  `qrcode_url` varchar(255) DEFAULT '' COMMENT '如果是扫码支付，那么，这个二维码地址',
  `nickname` varchar(45) DEFAULT '' COMMENT '币商名称',
  `day_deal_money` decimal(10,4) DEFAULT '0.0000' COMMENT '日交易量，人民币',
  `fourthpartyId` int(10) DEFAULT '0' COMMENT '四方ID',
  `deal_last_time` datetime DEFAULT '2020-01-01 00:00:00' COMMENT '最后一次交易时间',
  `platform_id` int(11) DEFAULT '0' COMMENT '平台编号',
  `owership` int(4) DEFAULT '0' COMMENT '0-系统地址 1-商户自备地址',
  `adds` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '地址',
  `addr_type` int(11) DEFAULT '0' COMMENT '地址类型 101-erc20 102-trc20',
  `addr_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '地址名称',
  `opt_id` int(11) DEFAULT '0' COMMENT '操作者ID',
  `opt_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '操作者名字',
  `remarks` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '备注',
  `coin_pay_deal_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '币支付交易总数量',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=216 DEFAULT CHARSET=utf8 COMMENT='在这个里面加入用户的支付类型以及支付信息';

-- ----------------------------
-- Table structure for dy_user_rate
-- ----------------------------
DROP TABLE IF EXISTS `dy_user_rate`;
CREATE TABLE `dy_user_rate` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) DEFAULT '0',
  `payid` int(11) DEFAULT '0',
  `paytype` varchar(45) DEFAULT '',
  `buy_fee_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '利用这个支付方式 ，买币需要支付的手续费',
  `sell_fee_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '利用这个通道，卖币的时候，需要支付的手续费',
  `buy_comm_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '用户当币商，利用这个通道，买币进来，赚取佣金的手续费',
  `sell_comm_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '用户当币商，利用这个通道，卖币能够赚取的手续费',
  `is_used` tinyint(4) DEFAULT '1' COMMENT '是否在使用，默认是打开使用。在后台可以配置关掉',
  `last_time` datetime DEFAULT '2020-01-01 00:00:00' COMMENT '最后一次使用时间',
  `channel` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '渠道号',
  `behalf_sell_fee_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '利用这个通道，代付卖币的时候，需要支付的手续费',
  `behalf_buy_comm_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '用户当币商，利用这个通道，代付买币能够赚取的手续费',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4938 DEFAULT CHARSET=utf8 COMMENT='用户各种支付方式的手续费表';

-- ----------------------------
-- Table structure for dy_user_shop
-- ----------------------------
DROP TABLE IF EXISTS `dy_user_shop`;
CREATE TABLE `dy_user_shop` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) DEFAULT '0',
  `callback_url` varchar(245) COLLATE utf8_bin DEFAULT '' COMMENT '回调参数',
  `encryption_key` varchar(245) COLLATE utf8_bin DEFAULT '' COMMENT '加密秘钥',
  PRIMARY KEY (`id`),
  UNIQUE KEY `userid_UNIQUE` (`userid`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='该表记录用户作为商户的时候的信息，包括回调，包括加密秘钥。';

-- ----------------------------
-- Table structure for dy_vendor_order
-- ----------------------------
DROP TABLE IF EXISTS `dy_vendor_order`;
CREATE TABLE `dy_vendor_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) DEFAULT '0' COMMENT '用户ID',
  `type` int(1) DEFAULT '0' COMMENT '类型：0是买单，1是卖单',
  `price` decimal(10,2) DEFAULT '0.00',
  `amount` decimal(20,10) DEFAULT '0.**********' COMMENT '交易的数量',
  `price_type` int(1) DEFAULT '0' COMMENT '价格类型：0是普通价格，1是根据货币USDT价格差额',
  `min_money` decimal(20,4) DEFAULT '0.0000',
  `max_money` decimal(20,4) DEFAULT '0.0000',
  `enable_status` int(1) DEFAULT '1' COMMENT '状态：0：失效，1：可用  2=审核中，3=审核不通过 4 撤销挂单，5=等待客服审核， 6=客服审核不通过',
  `auto_switch` int(1) DEFAULT '0' COMMENT '自动开关：0：关闭，1：打开：2：临时关闭',
  `message` varchar(255) DEFAULT '' COMMENT '留言',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `check_type` int(1) DEFAULT '0' COMMENT '检索的类型：0：是普通订单，1是针对特定群体才能看到的订单',
  `subject` varchar(255) DEFAULT '' COMMENT '当针对特定群体的时候，在这里附上对应的信息',
  `max_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '交易的总数量',
  `channel` varchar(45) DEFAULT '' COMMENT '渠道标识。在派单的时候，需要对渠道进行识别',
  `unsold_order_num` int(11) DEFAULT '0' COMMENT '未成交订单数量',
  `deal_order_num` int(11) DEFAULT '0' COMMENT '成交订单数量',
  `unsold_order_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '未成交订单币数',
  `deal_order_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '成交订单币数',
  `cancel_order_num` int(11) DEFAULT '0' COMMENT '取消订单数量',
  `user_type` int(11) DEFAULT '0' COMMENT '100=平台用户，200=币商，300=商户',
  `payee_account` varchar(145) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '收款人账号',
  `payee_name` varchar(145) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '收款人账号',
  `payee_bank` varchar(145) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '收款银行',
  `payee_band_addr` varchar(145) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '收款银行地址',
  `deal_order_free` decimal(20,10) DEFAULT '0.**********' COMMENT '已付的手续费',
  `free` decimal(20,10) DEFAULT '0.**********' COMMENT '应付总手续费',
  `channel_deal` int(11) DEFAULT '0' COMMENT '交易类型：0：公开，1:私有',
  `fee_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '手续费率',
  `add_fee_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '追加手续费率',
  `fail_remark` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '不通过原因',
  `predict_money` decimal(20,4) DEFAULT '0.0000' COMMENT '预计到账',
  `deal_mode` int(11) DEFAULT '0' COMMENT '交易模式 0 可多次交易 1 一次交易完成',
  `rest_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '一笔吃挂单取消后需要一个休息时间，才可以重新上架',
  `deal_monery` decimal(20,4) DEFAULT '0.0000' COMMENT '挂自动购买时 购买人民币数量',
  `deal_count` decimal(20,4) DEFAULT '0.0000' COMMENT '挂自动购买时， 已派的订单数量',
  `withdraw_type` int(11) DEFAULT '0' COMMENT '提现类型， 0 otc 1 代付',
  `coin_id` int(11) DEFAULT '0' COMMENT '币种ID',
  `coin_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '币种名字',
  `notify_info` varchar(1000) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '回调信息',
  `is_external` int(255) DEFAULT '0' COMMENT '是否外部渠道 0-不是 1-是',
  `unsold_order_money` decimal(20,4) DEFAULT '0.0000' COMMENT '未成交订单法币数量',
  `deal_order_money` decimal(20,4) DEFAULT '0.0000' COMMENT '成交订单法币数量',
  `external_order_userid` int(4) DEFAULT '0' COMMENT '外部订单派给指定用户',
  `is_assigned` int(4) DEFAULT '0' COMMENT '是否需要重新匹配订单',
  `is_take` int(4) DEFAULT '0' COMMENT '是否接单 0 接单 1 不接单',
  `platform_id` int(11) DEFAULT '0' COMMENT '平台编号',
  `merchant_order_id` varchar(255) CHARACTER SET utf8mb4 DEFAULT '0' COMMENT '所关联的商户相关订单id',
  `wallet_type` int(14) DEFAULT '0' COMMENT '钱包类型 0 基础钱包 1 币付钱包',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1905 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for dy_web_conf
-- ----------------------------
DROP TABLE IF EXISTS `dy_web_conf`;
CREATE TABLE `dy_web_conf` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `channel` varchar(45) COLLATE utf8_bin DEFAULT '' COMMENT '渠道号',
  `channel_name` varchar(245) COLLATE utf8_bin DEFAULT '' COMMENT '渠道名',
  `login_url` varchar(245) COLLATE utf8_bin DEFAULT '' COMMENT '出事入的url',
  `authcode_url` varchar(245) COLLATE utf8_bin DEFAULT '' COMMENT '获取验证码的url',
  `modifycode_url` varchar(245) COLLATE utf8_bin DEFAULT '' COMMENT '修改密码发送的验证码',
  `chat_pic_url` varchar(245) COLLATE utf8_bin DEFAULT '' COMMENT '聊天图片上传的url',
  `chat_h5_pic_url` varchar(245) COLLATE utf8_bin DEFAULT '' COMMENT 'H5聊天上传的url',
  `chat_mgr_pic_url` varchar(245) COLLATE utf8_bin DEFAULT '' COMMENT '管理后台聊天上传的url',
  `alipay_qrcode_url` varchar(245) COLLATE utf8_bin DEFAULT '' COMMENT '支付宝收款码上传url',
  `wechat_qrcode_url` varchar(245) COLLATE utf8_bin DEFAULT '' COMMENT '微信收款码上传二维码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='短信验证码，图片上传，登入IP的配置';

-- ----------------------------
-- Table structure for log_amount_details
-- ----------------------------
DROP TABLE IF EXISTS `log_amount_details`;
CREATE TABLE `log_amount_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) DEFAULT '0' COMMENT '用户',
  `currency_type` int(11) DEFAULT '0' COMMENT '币类型',
  `is_lock` int(11) DEFAULT '0' COMMENT '是否是冻结 0 不是 1 是',
  `amount` decimal(20,10) DEFAULT '0.**********' COMMENT '币数量',
  `use_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '变化后的币数量',
  `order_id` varchar(255) DEFAULT '0' COMMENT '订单ID',
  `order_type` int(11) DEFAULT '0' COMMENT '订单类型 0 买 1卖 2充 3提 4系统补款 5系统扣款',
  `remarks` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '备注',
  `channel` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '渠道号',
  `createdate` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `coin_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '币种名字',
  `wallet_type` int(14) DEFAULT '0' COMMENT '钱包类型 0 基础钱包 1 币付钱包',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16840 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Table structure for log_commission_details
-- ----------------------------
DROP TABLE IF EXISTS `log_commission_details`;
CREATE TABLE `log_commission_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) DEFAULT '0' COMMENT '订单ID',
  `order_type` int(11) DEFAULT '0' COMMENT '订单类型 0充值 1提现 2转账  3提币 4站内交易 5充币（私人钱包）',
  `currency_type` int(11) DEFAULT '0' COMMENT '币类型',
  `source_userid` int(11) DEFAULT '0' COMMENT '出手续费用户',
  `source_nick` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '出手续费用户昵称',
  `target_userid` int(11) DEFAULT '0' COMMENT '得到分佣的用户',
  `target_nick` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '得到分佣的用户',
  `amount` decimal(20,10) DEFAULT '0.**********' COMMENT '币数量',
  `dea_price` decimal(10,4) DEFAULT '0.0000' COMMENT '成交价格',
  `money_count` decimal(10,2) DEFAULT '0.00' COMMENT '金额',
  `free_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '费率',
  `free_count` decimal(20,10) DEFAULT '0.**********' COMMENT '总手续',
  `commission_rate` decimal(10,4) DEFAULT '0.0000' COMMENT '佣金比例',
  `commission_count` decimal(20,10) DEFAULT '0.**********' COMMENT '得到手续费',
  `createdate` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `channel` varchar(255) DEFAULT '' COMMENT '渠道号',
  `coin_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '币种名字',
  `platform_id` int(11) DEFAULT '0' COMMENT '平台编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4075 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Table structure for log_fund_details
-- ----------------------------
DROP TABLE IF EXISTS `log_fund_details`;
CREATE TABLE `log_fund_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) DEFAULT '0' COMMENT '用户',
  `currency_type` int(11) DEFAULT '0' COMMENT '币类型',
  `amount` decimal(20,10) DEFAULT '0.**********' COMMENT '订单币数量',
  `before_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '用户变化前的币数量',
  `after_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '用户变化后的币数量',
  `order_id` varchar(255) CHARACTER SET utf8mb4 DEFAULT '0' COMMENT '订单ID',
  `order_type` int(11) DEFAULT '0' COMMENT '订单类型 1 购买 2出售 3充值 4 提现 5提币 6充币 7转入 8转出 9收益 10 手续费 11系统补款 12系统扣款 13 币支付 14 划转 15 兑换 16 转账(兑汇的个人兑个人)',
  `remarks` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '备注',
  `channel` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '渠道号',
  `createdate` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `user_type` int(4) DEFAULT '0' COMMENT '用户类型',
  `coin_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '币种名字',
  `money` decimal(20,4) DEFAULT '0.0000' COMMENT '法币数量',
  `after_money` decimal(20,4) DEFAULT '0.0000' COMMENT '用户变化后的法币数量',
  `platform_id` int(11) DEFAULT '0' COMMENT '平台编号',
  `wallet_type` int(14) DEFAULT '0' COMMENT '钱包类型 0 基础钱包 1 币付钱包',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=487410 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='资金流水';

-- ----------------------------
-- Table structure for log_operation
-- ----------------------------
DROP TABLE IF EXISTS `log_operation`;
CREATE TABLE `log_operation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(5) DEFAULT NULL COMMENT '日志类型， 1手动调账，2审核',
  `role_id` int(11) NOT NULL DEFAULT '0' COMMENT '角色id',
  `content` text COLLATE utf8_bin NOT NULL COMMENT '操作内容',
  `before_json` text COLLATE utf8_bin COMMENT '操作之前',
  `after_json` text COLLATE utf8_bin COMMENT '操作之后',
  `url` varchar(500) COLLATE utf8_bin DEFAULT '' COMMENT '请求链接',
  `role_name` varchar(255) COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '角色名',
  `userid` int(11) NOT NULL COMMENT '用户id',
  `username` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '用户名',
  `ip` varchar(255) COLLATE utf8_bin NOT NULL DEFAULT '',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=222 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='后台操作日志表';

-- ----------------------------
-- Table structure for log_paytype
-- ----------------------------
DROP TABLE IF EXISTS `log_paytype`;
CREATE TABLE `log_paytype` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) NOT NULL DEFAULT '0',
  `nickname` varchar(45) COLLATE utf8mb4_bin DEFAULT '',
  `channel` varchar(32) COLLATE utf8mb4_bin DEFAULT '',
  `regdate` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `paytype` int(11) DEFAULT '0' COMMENT '支付通道',
  `tem_deal_count` decimal(20,10) DEFAULT '0.**********' COMMENT '支付通道团队出售量',
  `agen_fee_count` decimal(20,10) DEFAULT '0.**********' COMMENT '给上级贡献的收益',
  `fee_count` decimal(20,10) DEFAULT '0.**********' COMMENT '自己的收益',
  `buy_count` decimal(20,10) DEFAULT '0.**********' COMMENT '支付通道团队出售量',
  `coin_id` int(11) DEFAULT '0' COMMENT '币种ID',
  PRIMARY KEY (`id`),
  KEY `id` (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=121 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;

-- ----------------------------
-- Table structure for log_paytype_daily
-- ----------------------------
DROP TABLE IF EXISTS `log_paytype_daily`;
CREATE TABLE `log_paytype_daily` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) NOT NULL DEFAULT '0',
  `dateid` varchar(45) COLLATE utf8mb4_bin DEFAULT '' COMMENT '时间id',
  `nickname` varchar(45) COLLATE utf8mb4_bin DEFAULT '',
  `channel` varchar(32) COLLATE utf8mb4_bin DEFAULT '',
  `regdate` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `paytype` int(11) DEFAULT '0' COMMENT '支付通道',
  `tem_deal_count` decimal(20,10) DEFAULT '0.**********' COMMENT '支付通道团队交易量',
  `agen_fee_count` decimal(20,10) DEFAULT '0.**********' COMMENT '给上级贡献的收益',
  `fee_count` decimal(20,10) DEFAULT '0.**********' COMMENT '自己的收益',
  `buy_count` decimal(20,10) DEFAULT '0.**********' COMMENT '支付通道团队出售量',
  `coin_id` int(11) DEFAULT '0' COMMENT '币种ID',
  PRIMARY KEY (`id`),
  KEY `id` (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=712 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;

-- ----------------------------
-- Table structure for log_sub
-- ----------------------------
DROP TABLE IF EXISTS `log_sub`;
CREATE TABLE `log_sub` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `platform_id` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '平台ID',
  `platform_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '平台名字',
  `usdt_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '数量',
  `money_amount` decimal(20,4) DEFAULT '0.0000' COMMENT '法币数量',
  `hosting_coin_num` int(11) DEFAULT '0' COMMENT '托管钱包充币笔数',
  `hosting_coin_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '托管钱包充币币数',
  `recharge_coin_num` int(11) DEFAULT '0' COMMENT '私人钱包充币笔数',
  `recharge_coin_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '私人钱包充币币数',
  `extract_coin_num` int(11) DEFAULT '0' COMMENT '提币笔数',
  `extract_coin_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '提币币数',
  `recharge_deal_num` int(11) DEFAULT '0' COMMENT '充值笔数',
  `recharge_deal_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '充值笔数',
  `withdraw_deal_num` int(11) DEFAULT '0' COMMENT '提现笔数',
  `withdraw_deal_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '提现币数',
  `sys_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '系统收益',
  `sys_add` decimal(20,10) DEFAULT '0.**********' COMMENT '系统补款',
  `sys_dec` decimal(20,10) DEFAULT '0.**********' COMMENT '系统扣款',
  `money_usdt` decimal(20,10) DEFAULT '0.**********' COMMENT '法币用户的U数量',
  `sys_user_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '系统用户的U数量',
  `r_coin_pay_sys` decimal(20,10) DEFAULT '0.**********' COMMENT '币支付充值数量-系统地址',
  `r_coin_pay_shop` decimal(20,10) DEFAULT '0.**********' COMMENT '币支付充值数量-商户地址',
  `r_coin_pay_deal_num` int(11) DEFAULT '0' COMMENT '币付笔数',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Table structure for log_sub_daily
-- ----------------------------
DROP TABLE IF EXISTS `log_sub_daily`;
CREATE TABLE `log_sub_daily` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `platform_id` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '平台ID',
  `platform_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '平台名字',
  `dateid` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '日期',
  `usdt_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '数量',
  `money_amount` decimal(20,4) DEFAULT '0.0000' COMMENT '法币数量',
  `hosting_coin_num` int(11) DEFAULT '0' COMMENT '托管钱包充币笔数',
  `hosting_coin_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '托管钱包充币币数',
  `recharge_coin_num` int(11) DEFAULT '0' COMMENT '私人钱包充币笔数',
  `recharge_coin_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '私人钱包充币币数',
  `extract_coin_num` int(11) DEFAULT '0' COMMENT '提币笔数',
  `extract_coin_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '提币币数',
  `recharge_deal_num` int(11) DEFAULT '0' COMMENT '充值笔数',
  `recharge_deal_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '充值笔数',
  `withdraw_deal_num` int(11) DEFAULT '0' COMMENT '提现笔数',
  `withdraw_deal_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '提现币数',
  `sys_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '系统收益',
  `sys_add` decimal(20,10) DEFAULT '0.**********' COMMENT '系统补款',
  `sys_dec` decimal(20,10) DEFAULT '0.**********' COMMENT '系统扣款',
  `money_usdt` decimal(20,10) DEFAULT '0.**********' COMMENT '法币用户的U数量',
  `sys_user_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '系统用户的U数量',
  `r_coin_pay_sys` decimal(20,10) DEFAULT '0.**********' COMMENT '币支付充值数量-系统地址',
  `r_coin_pay_shop` decimal(20,10) DEFAULT '0.**********' COMMENT '币支付充值数量-商户地址',
  `r_coin_pay_deal_num` int(11) DEFAULT '0' COMMENT '币付笔数',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1076 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Table structure for log_sub_week
-- ----------------------------
DROP TABLE IF EXISTS `log_sub_week`;
CREATE TABLE `log_sub_week` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `platform_id` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '平台ID',
  `platform_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '平台名字',
  `dateid` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '一年中的第几周',
  `dateid_start` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '本周开始日期',
  `dateid_end` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '本周结束日期',
  `usdt_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '数量',
  `money_amount` decimal(20,4) DEFAULT '0.0000' COMMENT '法币数量',
  `hosting_coin_num` int(11) DEFAULT '0' COMMENT '托管钱包充币笔数',
  `hosting_coin_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '托管钱包充币币数',
  `recharge_coin_num` int(11) DEFAULT '0' COMMENT '私人钱包充币笔数',
  `recharge_coin_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '私人钱包充币币数',
  `extract_coin_num` int(11) DEFAULT '0' COMMENT '提币笔数',
  `extract_coin_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '提币币数',
  `recharge_deal_num` int(11) DEFAULT '0' COMMENT '充值笔数',
  `recharge_deal_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '充值笔数',
  `withdraw_deal_num` int(11) DEFAULT '0' COMMENT '提现笔数',
  `withdraw_deal_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '提现币数',
  `sys_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '系统收益',
  `sys_add` decimal(20,10) DEFAULT '0.**********' COMMENT '系统补款',
  `sys_dec` decimal(20,10) DEFAULT '0.**********' COMMENT '系统扣款',
  `money_usdt` decimal(20,10) DEFAULT '0.**********' COMMENT '法币用户的U数量',
  `sys_user_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '系统用户的U数量',
  `r_coin_pay_sys` decimal(20,10) DEFAULT '0.**********' COMMENT '币支付充值数量-系统地址',
  `r_coin_pay_shop` decimal(20,10) DEFAULT '0.**********' COMMENT '币支付充值数量-商户地址',
  `r_coin_pay_deal_num` int(11) DEFAULT '0' COMMENT '币付笔数',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=303 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Table structure for log_subsystem_daily
-- ----------------------------
DROP TABLE IF EXISTS `log_subsystem_daily`;
CREATE TABLE `log_subsystem_daily` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `dateid` varchar(45) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '日期',
  `platform_id` int(11) DEFAULT '0' COMMENT '平台编号',
  `platform_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '平台名称',
  `deal_type` int(11) DEFAULT '0' COMMENT '1-提现OTC 2-提现代付 3-充值 4-提币 5 归拢手续费',
  `payid` int(11) DEFAULT '0' COMMENT '支付通道ID如8101、8201',
  `deal_count` decimal(20,10) DEFAULT '0.**********' COMMENT '交易量数字货币',
  `deal_money` decimal(20,4) DEFAULT '0.0000' COMMENT '交易量法币',
  `fee` decimal(20,10) DEFAULT '0.**********' COMMENT '服务费',
  `status` int(4) DEFAULT '0' COMMENT '0-未缴纳  1-已缴纳',
  `remarks` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '备注',
  `optid` int(11) DEFAULT '0' COMMENT '操作人',
  `opt_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '操作人名称',
  `fee_rate` decimal(20,4) DEFAULT '0.0000' COMMENT '服务费率',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=85 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Table structure for log_sys
-- ----------------------------
DROP TABLE IF EXISTS `log_sys`;
CREATE TABLE `log_sys` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `channel` varchar(45) DEFAULT '',
  `regdate` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dealer_dealer_num` int(11) DEFAULT '0' COMMENT '币商数量',
  `merchant_num` int(11) DEFAULT '0' COMMENT '商户数量',
  `erc_usdt_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT 'erc20的usdt的数量',
  `erc_usdt_lock_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT 'erc20锁定的usdt的数量',
  `total_count_buy` int(11) NOT NULL DEFAULT '0' COMMENT '总订单数(买)',
  `total_count_sell` int(11) NOT NULL DEFAULT '0' COMMENT '总订单数(卖)',
  `finish_count_buy` int(11) NOT NULL DEFAULT '0' COMMENT '完成数(买)',
  `finish_count_sell` int(11) NOT NULL DEFAULT '0' COMMENT '完成数(卖)',
  `cancel_count_buy` int(11) NOT NULL DEFAULT '0' COMMENT '取消订单数(买)',
  `cancel_count_sell` int(11) NOT NULL DEFAULT '0' COMMENT '取消订单数(卖)',
  `appeal_count` int(11) NOT NULL DEFAULT '0' COMMENT '申诉次数',
  `buy_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总购入，钱包内买币',
  `sell_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总出售，钱包内卖币',
  `recharge_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总充入，外面的币到钱包',
  `extract_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总提币，钱包的币到外面',
  `transfer_in_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总转入，钱包到钱包',
  `transfer_out_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总转出，钱包到钱包',
  `fee_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总手续费',
  `commission_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '总佣金',
  `sys_commission_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '系统吃掉总佣金',
  `coin_id` int(11) DEFAULT '0' COMMENT '币种ID',
  `exchange_recharge_order_count` int(11) DEFAULT '0' COMMENT '交易所充值订单笔数',
  `exchange_recharge_notmatched_count` int(11) DEFAULT '0' COMMENT '交易所充值未匹配笔数',
  `exchange_recharge_complete_count` int(11) DEFAULT '0' COMMENT '交易所充值完成笔数',
  `exchange_recharge_cancel_count` int(11) DEFAULT '0' COMMENT '交易所充值取消笔数',
  `exchange_withdraw_order_count` int(11) DEFAULT '0' COMMENT '交易所提现订单笔数',
  `exchange_withdraw_notmatched_count` int(11) DEFAULT '0' COMMENT '交易所提现未匹配笔数',
  `exchange_withdraw_complete_count` int(11) DEFAULT '0' COMMENT '交易所提现完成笔数',
  `exchange_withdraw_cancel_count` int(11) DEFAULT '0' COMMENT '交易所提现取消笔数',
  `platform_id` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '平台ID',
  `platform_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '平台名字',
  `usdt_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '数量',
  `money_amount` decimal(20,4) DEFAULT '0.0000' COMMENT '法币数量',
  `hosting_coin_num` int(11) DEFAULT '0' COMMENT '托管钱包充币笔数',
  `hosting_coin_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '托管钱包充币币数',
  `recharge_coin_num` int(11) DEFAULT '0' COMMENT '私人钱包充币笔数',
  `recharge_coin_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '私人钱包充币币数',
  `extract_coin_num` int(11) DEFAULT '0' COMMENT '提币笔数',
  `extract_coin_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '提币币数',
  `recharge_deal_num` int(11) DEFAULT '0' COMMENT '充值笔数',
  `recharge_deal_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '充值笔数',
  `withdraw_deal_num` int(11) DEFAULT '0' COMMENT '提现笔数',
  `withdraw_deal_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '提现币数',
  `sys_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '系统收益',
  `sys_add` decimal(20,10) DEFAULT '0.**********' COMMENT '系统补款',
  `sys_dec` decimal(20,10) DEFAULT '0.**********' COMMENT '系统扣款',
  `money_usdt` decimal(20,10) DEFAULT '0.**********' COMMENT '法币用户的U数量',
  `sys_user_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '系统用户的U数量',
  `r_coin_pay_sys` decimal(20,10) DEFAULT '0.**********' COMMENT '币支付充值数量-系统地址',
  `r_coin_pay_shop` decimal(20,10) DEFAULT '0.**********' COMMENT '币支付充值数量-商户地址',
  `r_coin_pay_deal_num` int(11) DEFAULT '0' COMMENT '币付笔数',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=125 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Table structure for log_sys_daily
-- ----------------------------
DROP TABLE IF EXISTS `log_sys_daily`;
CREATE TABLE `log_sys_daily` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `dateid` varchar(45) DEFAULT '',
  `channel` varchar(45) DEFAULT '',
  `dealer_dealer_num` int(11) DEFAULT '0' COMMENT '新增币商数量',
  `merchant_num` int(11) DEFAULT '0' COMMENT '新增商户数量',
  `erc_usdt_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT 'erc20的usdt的数量',
  `erc_usdt_lock_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT 'erc20锁定的usdt的数量',
  `total_count_buy` int(11) NOT NULL DEFAULT '0' COMMENT '总订单数(买)',
  `total_count_sell` int(11) NOT NULL DEFAULT '0' COMMENT '总订单数(卖)',
  `finish_count_buy` int(11) NOT NULL DEFAULT '0' COMMENT '完成数(买)',
  `finish_count_sell` int(11) NOT NULL DEFAULT '0' COMMENT '完成数(卖)',
  `cancel_count_buy` int(11) NOT NULL DEFAULT '0' COMMENT '取消订单数(买)',
  `cancel_count_sell` int(11) NOT NULL DEFAULT '0' COMMENT '取消订单数(卖)',
  `appeal_count` int(11) NOT NULL DEFAULT '0' COMMENT '申诉次数',
  `buy_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总购入，钱包内买币',
  `sell_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总出售，钱包内卖币',
  `recharge_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总充入，外面的币到钱包',
  `extract_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总提币，钱包的币到外面',
  `transfer_in_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总转入，钱包到钱包',
  `transfer_out_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总转出，钱包到钱包',
  `fee_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总手续费',
  `commission_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总佣金 ',
  `sys_commission_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '系统吃掉总佣金',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态 1未缴纳 2已缴纳',
  `check_userid` int(11) DEFAULT NULL COMMENT '备注人id',
  `check_username` varchar(255) DEFAULT NULL COMMENT '备注人名称',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `coin_id` int(11) DEFAULT '0' COMMENT '币种ID',
  `exchange_recharge_order_count` int(11) DEFAULT '0' COMMENT '交易所充值订单笔数',
  `exchange_recharge_notmatched_count` int(11) DEFAULT '0' COMMENT '交易所充值未匹配笔数',
  `exchange_recharge_complete_count` int(11) DEFAULT '0' COMMENT '交易所充值完成笔数',
  `exchange_recharge_cancel_count` int(11) DEFAULT '0' COMMENT '交易所充值取消笔数',
  `exchange_withdraw_order_count` int(11) DEFAULT '0' COMMENT '交易所提现订单笔数',
  `exchange_withdraw_notmatched_count` int(11) DEFAULT '0' COMMENT '交易所提现未匹配笔数',
  `exchange_withdraw_complete_count` int(11) DEFAULT '0' COMMENT '交易所提现完成笔数',
  `exchange_withdraw_cancel_count` int(11) DEFAULT '0' COMMENT '交易所提现取消笔数',
  `platform_id` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '平台ID',
  `platform_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '平台名字',
  `usdt_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '数量',
  `money_amount` decimal(20,4) DEFAULT '0.0000' COMMENT '法币数量',
  `hosting_coin_num` int(11) DEFAULT '0' COMMENT '托管钱包充币笔数',
  `hosting_coin_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '托管钱包充币币数',
  `recharge_coin_num` int(11) DEFAULT '0' COMMENT '私人钱包充币笔数',
  `recharge_coin_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '私人钱包充币币数',
  `extract_coin_num` int(11) DEFAULT '0' COMMENT '提币笔数',
  `extract_coin_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '提币币数',
  `recharge_deal_num` int(11) DEFAULT '0' COMMENT '充值笔数',
  `recharge_deal_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '充值笔数',
  `withdraw_deal_num` int(11) DEFAULT '0' COMMENT '提现笔数',
  `withdraw_deal_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '提现币数',
  `sys_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '系统收益',
  `sys_add` decimal(20,10) DEFAULT '0.**********' COMMENT '系统补款',
  `sys_dec` decimal(20,10) DEFAULT '0.**********' COMMENT '系统扣款',
  `money_usdt` decimal(20,10) DEFAULT '0.**********' COMMENT '法币用户的U数量',
  `sys_user_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '系统用户的U数量',
  `r_coin_pay_sys` decimal(20,10) DEFAULT '0.**********' COMMENT '币支付充值数量-系统地址',
  `r_coin_pay_shop` decimal(20,10) DEFAULT '0.**********' COMMENT '币支付充值数量-商户地址',
  `r_coin_pay_deal_num` int(11) DEFAULT '0' COMMENT '币付笔数',
  PRIMARY KEY (`id`),
  KEY `index2` (`dateid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4081 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Table structure for log_sys_week
-- ----------------------------
DROP TABLE IF EXISTS `log_sys_week`;
CREATE TABLE `log_sys_week` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `dateid` varchar(45) DEFAULT '',
  `channel` varchar(45) DEFAULT '',
  `dealer_dealer_num` int(11) DEFAULT '0' COMMENT '新增币商数量',
  `merchant_num` int(11) DEFAULT '0' COMMENT '新增商户数量',
  `erc_usdt_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT 'erc20的usdt的数量',
  `erc_usdt_lock_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT 'erc20锁定的usdt的数量',
  `total_count_buy` int(11) NOT NULL DEFAULT '0' COMMENT '总订单数(买)',
  `total_count_sell` int(11) NOT NULL DEFAULT '0' COMMENT '总订单数(卖)',
  `finish_count_buy` int(11) NOT NULL DEFAULT '0' COMMENT '完成数(买)',
  `finish_count_sell` int(11) NOT NULL DEFAULT '0' COMMENT '完成数(卖)',
  `cancel_count_buy` int(11) NOT NULL DEFAULT '0' COMMENT '取消订单数(买)',
  `cancel_count_sell` int(11) NOT NULL DEFAULT '0' COMMENT '取消订单数(卖)',
  `appeal_count` int(11) NOT NULL DEFAULT '0' COMMENT '申诉次数',
  `buy_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总购入，钱包内买币',
  `sell_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总出售，钱包内卖币',
  `recharge_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总充入，外面的币到钱包',
  `extract_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总提币，钱包的币到外面',
  `transfer_in_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总转入，钱包到钱包',
  `transfer_out_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总转出，钱包到钱包',
  `fee_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总手续费',
  `commission_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总佣金 ',
  `sys_commission_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '系统吃掉总佣金',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态 1未缴纳 2已缴纳',
  `check_userid` int(11) DEFAULT NULL COMMENT '备注人id',
  `check_username` varchar(255) DEFAULT NULL COMMENT '备注人名称',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `coin_id` int(11) DEFAULT '0' COMMENT '币种ID',
  `exchange_recharge_order_count` int(11) DEFAULT '0' COMMENT '交易所充值订单笔数',
  `exchange_recharge_notmatched_count` int(11) DEFAULT '0' COMMENT '交易所充值未匹配笔数',
  `exchange_recharge_complete_count` int(11) DEFAULT '0' COMMENT '交易所充值完成笔数',
  `exchange_recharge_cancel_count` int(11) DEFAULT '0' COMMENT '交易所充值取消笔数',
  `exchange_withdraw_order_count` int(11) DEFAULT '0' COMMENT '交易所提现订单笔数',
  `exchange_withdraw_notmatched_count` int(11) DEFAULT '0' COMMENT '交易所提现未匹配笔数',
  `exchange_withdraw_complete_count` int(11) DEFAULT '0' COMMENT '交易所提现完成笔数',
  `exchange_withdraw_cancel_count` int(11) DEFAULT '0' COMMENT '交易所提现取消笔数',
  `platform_id` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '平台ID',
  `platform_name` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '平台名字',
  `dateid_start` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '本周开始日期',
  `dateid_end` varchar(255) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '本周结束日期',
  `usdt_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '数量',
  `money_amount` decimal(20,4) DEFAULT '0.0000' COMMENT '法币数量',
  `hosting_coin_num` int(11) DEFAULT '0' COMMENT '托管钱包充币笔数',
  `hosting_coin_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '托管钱包充币币数',
  `recharge_coin_num` int(11) DEFAULT '0' COMMENT '私人钱包充币笔数',
  `recharge_coin_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '私人钱包充币币数',
  `extract_coin_num` int(11) DEFAULT '0' COMMENT '提币笔数',
  `extract_coin_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '提币币数',
  `recharge_deal_num` int(11) DEFAULT '0' COMMENT '充值笔数',
  `recharge_deal_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '充值笔数',
  `withdraw_deal_num` int(11) DEFAULT '0' COMMENT '提现笔数',
  `withdraw_deal_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '提现币数',
  `sys_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '系统收益',
  `sys_add` decimal(20,10) DEFAULT '0.**********' COMMENT '系统补款',
  `sys_dec` decimal(20,10) DEFAULT '0.**********' COMMENT '系统扣款',
  `money_usdt` decimal(20,10) DEFAULT '0.**********' COMMENT '法币用户的U数量',
  `sys_user_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '系统用户的U数量',
  `r_coin_pay_sys` decimal(20,10) DEFAULT '0.**********' COMMENT '币支付充值数量-系统地址',
  `r_coin_pay_shop` decimal(20,10) DEFAULT '0.**********' COMMENT '币支付充值数量-商户地址',
  `r_coin_pay_deal_num` int(11) DEFAULT '0' COMMENT '币付笔数',
  PRIMARY KEY (`id`),
  KEY `index2` (`dateid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1257 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Table structure for log_user
-- ----------------------------
DROP TABLE IF EXISTS `log_user`;
CREATE TABLE `log_user` (
  `userid` int(11) NOT NULL,
  `nickname` varchar(45) DEFAULT '',
  `channel` varchar(45) DEFAULT '',
  `regdate` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `total_count_buy` int(11) NOT NULL DEFAULT '0' COMMENT '总订单数(买)',
  `total_count_sell` int(11) NOT NULL DEFAULT '0' COMMENT '总订单数(卖)',
  `finish_count_buy` int(11) NOT NULL DEFAULT '0' COMMENT '完成数(买)',
  `finish_count_sell` int(11) NOT NULL DEFAULT '0' COMMENT '完成数(卖)',
  `cancel_count_buy` int(11) NOT NULL DEFAULT '0' COMMENT '取消订单数(买)',
  `cancel_count_sell` int(11) NOT NULL DEFAULT '0' COMMENT '取消订单数(卖)',
  `appeal_count` int(11) NOT NULL DEFAULT '0' COMMENT '申诉次数',
  `buy_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总购入，钱包内买币',
  `sell_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总出售，钱包内卖币',
  `recharge_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总充入，外面的币到钱包',
  `extract_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总提币，钱包的币到外面',
  `transfer_in_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总转入，钱包到钱包',
  `transfer_out_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总转出，钱包到钱包',
  `reward_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '奖励',
  `fee_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '手续费',
  `teamuser_num` int(11) DEFAULT '0' COMMENT '团队的人数',
  `team_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '团队佣金',
  `confirm_time` int(11) DEFAULT '0' COMMENT '确认时间(秒)',
  `buy_money` decimal(11,4) DEFAULT '0.0000' COMMENT '购买币时成交的人民币数量累加',
  `sell_money` decimal(11,4) DEFAULT '0.0000' COMMENT '出售币时成交的人民币数量累加',
  `coin_id` int(11) DEFAULT '0' COMMENT '币种ID',
  `team_amount_buy` decimal(20,10) DEFAULT '0.**********' COMMENT '团队购买币数量',
  `team_money_buy` decimal(20,4) DEFAULT '0.0000' COMMENT '团队购买法币数量',
  `personal_income_buy` decimal(20,10) DEFAULT '0.**********' COMMENT '个人的购买收益币数量',
  `team_count_buy` int(11) DEFAULT '0' COMMENT '团队购买订单总数量',
  `team_complete_count_buy` int(11) DEFAULT '0' COMMENT '团队购买完成订单数量',
  `team_amount_sell` decimal(20,10) DEFAULT '0.**********' COMMENT '团队出售币数量',
  `team_money_sell` decimal(20,4) DEFAULT '0.0000' COMMENT '团队出售法币数量',
  `personal_income_sell` decimal(20,10) DEFAULT '0.**********' COMMENT '个人的出售收益币数量',
  `team_count_sell` int(11) DEFAULT '0' COMMENT '团队出售订单总数量',
  `team_complete_count_sell` int(11) DEFAULT '0' COMMENT '团队出售完成订单数量',
  `team_balance_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '团队余额币数量',
  `team_balance_money` decimal(20,4) DEFAULT '0.0000' COMMENT '团队余额法币数量',
  `team_recharge_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '团队充值数字货币数量',
  `team_recharge_money` decimal(20,4) DEFAULT '0.0000' COMMENT '团队充值法币数量',
  `team_withdraw_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '团队提现数字货币数量',
  `team_withdraw_money` decimal(20,4) DEFAULT '0.0000' COMMENT '团队提现法币数量',
  `team_income_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '团队收益',
  `r_coin_pay_sys` decimal(20,10) DEFAULT '0.**********' COMMENT '币支付充值数量-系统地址',
  `r_coin_pay_shop` decimal(20,10) DEFAULT '0.**********' COMMENT '币支付充值数量-商户地址',
  `r_coin_pay_deal_num` int(11) DEFAULT '0' COMMENT '币付笔数',
  PRIMARY KEY (`userid`),
  KEY `userid` (`userid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

-- ----------------------------
-- Table structure for log_user_daily
-- ----------------------------
DROP TABLE IF EXISTS `log_user_daily`;
CREATE TABLE `log_user_daily` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) NOT NULL DEFAULT '0',
  `dateid` varchar(45) COLLATE utf8mb4_bin DEFAULT '' COMMENT '时间id',
  `nickname` varchar(45) COLLATE utf8mb4_bin DEFAULT '',
  `channel` varchar(32) COLLATE utf8mb4_bin DEFAULT '',
  `regdate` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `total_count_buy` int(11) NOT NULL DEFAULT '0' COMMENT '总订单数(买)',
  `total_count_sell` int(11) NOT NULL DEFAULT '0' COMMENT '总订单数(卖)',
  `finish_count_buy` int(11) NOT NULL DEFAULT '0' COMMENT '完成数(买)',
  `finish_count_sell` int(11) NOT NULL DEFAULT '0' COMMENT '完成数(卖)',
  `cancel_count_buy` int(11) NOT NULL DEFAULT '0' COMMENT '取消订单数(买)',
  `cancel_count_sell` int(11) NOT NULL DEFAULT '0' COMMENT '取消订单数(卖)',
  `appeal_count` int(11) NOT NULL DEFAULT '0' COMMENT '申诉次数',
  `buy_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总购入，钱包内买币',
  `sell_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总出售，钱包内卖币',
  `recharge_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总充入，外面的币到钱包',
  `extract_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总提币，钱包的币到外面',
  `transfer_in_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总转入，钱包到钱包',
  `transfer_out_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总转出，钱包到钱包',
  `reward_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总奖励',
  `fee_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '总手续费',
  `teamuser_num` int(11) DEFAULT '0' COMMENT '团队的人数',
  `team_amount` decimal(20,10) NOT NULL DEFAULT '0.**********' COMMENT '团队佣金',
  `confirm_time` int(11) DEFAULT '0' COMMENT '确认时间(秒)',
  `buy_money` decimal(11,4) DEFAULT '0.0000' COMMENT '购买币时成交的人民币数量累加',
  `sell_money` decimal(11,4) DEFAULT '0.0000' COMMENT '出售币时成交的人民币数量累加',
  `coin_id` int(11) DEFAULT '0' COMMENT '币种ID',
  `team_amount_buy` decimal(20,10) DEFAULT '0.**********' COMMENT '团队购买币数量',
  `team_money_buy` decimal(20,4) DEFAULT '0.0000' COMMENT '团队购买法币数量',
  `personal_income_buy` decimal(20,10) DEFAULT '0.**********' COMMENT '个人的购买收益币数量',
  `team_count_buy` int(11) DEFAULT '0' COMMENT '团队购买订单总数量',
  `team_complete_count_buy` int(11) DEFAULT '0' COMMENT '团队购买完成订单数量',
  `team_amount_sell` decimal(20,10) DEFAULT '0.**********' COMMENT '团队出售币数量',
  `team_money_sell` decimal(20,4) DEFAULT '0.0000' COMMENT '团队出售法币数量',
  `personal_income_sell` decimal(20,10) DEFAULT '0.**********' COMMENT '个人的出售收益币数量',
  `team_count_sell` int(11) DEFAULT '0' COMMENT '团队出售订单总数量',
  `team_complete_count_sell` int(11) DEFAULT '0' COMMENT '团队出售完成订单数量',
  `team_recharge_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '团队充值数字货币数量',
  `team_recharge_money` decimal(20,4) DEFAULT '0.0000' COMMENT '团队充值法币数量',
  `team_withdraw_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '团队提现数字货币数量',
  `team_withdraw_money` decimal(20,4) DEFAULT '0.0000' COMMENT '团队提现法币数量',
  `team_income_amount` decimal(20,10) DEFAULT '0.**********' COMMENT '团队收益',
  `r_coin_pay_sys` decimal(20,10) DEFAULT '0.**********' COMMENT '币支付充值数量-系统地址',
  `r_coin_pay_shop` decimal(20,10) DEFAULT '0.**********' COMMENT '币支付充值数量-商户地址',
  `r_coin_pay_deal_num` int(11) DEFAULT '0' COMMENT '币付笔数',
  `exchange_money_limit_unnamed` decimal(20,4) DEFAULT '0.0000' COMMENT '未实名单日兑汇限额默认人民币(其他换算成人民币)',
  `exchange_amount_limit_unnamed` int(11) DEFAULT '0' COMMENT '未实名单日兑汇笔数',
  PRIMARY KEY (`id`),
  KEY `id` (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2639 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=COMPACT;

-- ----------------------------
-- Table structure for log_user_login
-- ----------------------------
DROP TABLE IF EXISTS `log_user_login`;
CREATE TABLE `log_user_login` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `userid` int(11) DEFAULT NULL COMMENT '用户归属id，商户赋值自己，子账户赋值上级',
  `create_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
  `login_ip` varchar(45) CHARACTER SET utf8 DEFAULT NULL COMMENT '登陆ip地址',
  `isadmin` int(11) DEFAULT NULL COMMENT '是否超管账号，0不是，1是',
  `username` varchar(45) CHARACTER SET utf8 DEFAULT NULL COMMENT '用户名称',
  `loginid` int(11) NOT NULL DEFAULT '0' COMMENT '登陆用户id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1265 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

-- ----------------------------
-- Table structure for test
-- ----------------------------
DROP TABLE IF EXISTS `test`;
CREATE TABLE `test` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `card` varchar(255) COLLATE utf8_bin DEFAULT NULL,
  `num` int(11) DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=89 DEFAULT CHARSET=utf8 COLLATE=utf8_bin;
