from quart import Blueprint, request
from app.shared.response import Response
from app.shared.decorators import validator
from app.modules.http.services.exchange_service import ExchangeService
from app.modules.http.views.exchange_views import UpdateOrderInfoView, UpdateOrderStatusView

exchange_bp = Blueprint('exchange', __name__)
exchange_service = ExchangeService()


@exchange_bp.route('/updateOrderInfo', methods=['POST'])
@validator(required=UpdateOrderInfoView)
async def update_order_info(body: UpdateOrderInfoView):
    """
    更新订单信息
    
    参数:
    - orderID: 订单号
    - payeeAccount: 收款人账号
    - payeeName: 收款人名字
    - payeeBank: 收款银行
    - tradeId: 交易所订单号
    - sign: 签证
    """
    result = await exchange_service.update_order_info(body)
    return Response.success(result), 200


@exchange_bp.route('/updateOrderStatus', methods=['POST'])
@validator(required=UpdateOrderStatusView)
async def update_order_status(body: UpdateOrderStatusView):
    """
    更新订单状态
    
    参数:
    - orderID: 订单号
    - status: 状态 1-成功, 2-取消, 3-待放行, 4-激活
    - proofurl: 支付凭证
    - tradeId: 交易所订单号
    - sign: 签证
    """
    result = await exchange_service.update_order_status(body)
    return Response.success(result), 200
