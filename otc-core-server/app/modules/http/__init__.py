from quart import Blueprint

# 创建主Http蓝图
http = Blueprint('http', __name__)

# 延迟导入避免循环依赖
def register_http_blueprints():
    from app.modules.http.controllers.exchange_controller import exchange_bp
    from app.modules.http.controllers.group_controller import group_bp
    from app.modules.http.controllers.pay_center_controller import pay_center_bp
    from app.modules.http.controllers.pay_info_controller import pay_info_bp
    from app.modules.http.controllers.platform_controller import platform_bp
    from app.modules.http.controllers.user_controller import user_bp

    # 注册子蓝图
    http.register_blueprint(exchange_bp, url_prefix='/exchange')
    http.register_blueprint(group_bp, url_prefix='/group')
    http.register_blueprint(pay_center_bp, url_prefix='/paycenter')
    http.register_blueprint(pay_info_bp, url_prefix='/payinfo')
    http.register_blueprint(platform_bp, url_prefix='/platform')
    http.register_blueprint(user_bp, url_prefix='/user')

# 注册蓝图
register_http_blueprints()
