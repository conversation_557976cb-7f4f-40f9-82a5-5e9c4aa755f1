from typing import Dict, Any
from app.shared.response import ResponseError, ResponseCode
from app.modules.http.views.pay_info_views import SetPayInfoSwitchView
from app.core.data_source.mysql import db
from sqlalchemy import text
from app.modules.http.utils.crypto_utils import verify_md5_signature


class PayInfoService:
    """支付信息管理服务类"""

    async def set_pay_info_switch(self, data: SetPayInfoSwitchView) -> Dict[str, Any]:
        """
        设置收款账号开关
        对应原Lua函数: setPayInfoSwitch
        """
        # 验证签名
        if not verify_md5_signature(data.dict(exclude={'sign'}), data.sign):
            raise ResponseError(ResponseCode.CUSTOM, "签名验证失败")

        async with db.get_session() as session:
            # 查询支付账号信息
            result = await session.execute(
                text("SELECT * FROM dy_user_pay WHERE id = :pay_id"),
                {"pay_id": data.payId}
            )
            pay_row = result.fetchone()
            if not pay_row:
                raise ResponseError(ResponseCode.CUSTOM, "支付账号不存在")

            # 检查账号状态
            user_id = pay_row[13]  # userid字段位置
            pay_type = pay_row[1]  # paytype字段位置
            status = pay_row[14]   # status字段位置

            if status == 2:
                raise ResponseError(ResponseCode.CUSTOM, "支付账号不存在")

            # 更新支付信息状态
            await session.execute(
                text("UPDATE dy_user_pay SET status = :switch WHERE id = :pay_id"),
                {"switch": data.switch, "pay_id": data.payId}
            )

            # 这里可以添加更新缓存的逻辑
            # 对应原Lua中的UserInfoModel.SetPayInfo(payInfo)

        return {"code": 200, "msg": "success"}
