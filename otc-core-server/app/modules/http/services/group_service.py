from typing import Dict, Any
from app.shared.response import ResponseError, ResponseCode
from app.modules.http.views.group_views import (
    CreateGroupView, SetGroupMemberView, SetGroupOrdersView, DelGroupView
)
from app.core.data_source.mysql import db
from app.shared.schemas.dy_user_info import UserInfo
from sqlalchemy import text
from app.modules.http.utils.crypto_utils import verify_md5_signature


class GroupService:
    """分组管理服务类"""

    async def create_group(self, data: CreateGroupView) -> Dict[str, Any]:
        """
        创建分组
        对应原Lua函数: creategroup
        """
        # 验证签名
        if not verify_md5_signature(data.dict(exclude={'sign'}), data.sign):
            raise ResponseError(ResponseCode.CUSTOM, "签名验证失败")

        async with db.get_session() as session:
            # 检查组名是否存在
            result = await session.execute(
                text("SELECT * FROM dy_group_info WHERE group_name = :group_name"),
                {"group_name": data.groupName}
            )
            if result.fetchone():
                raise ResponseError(ResponseCode.CUSTOM, "组名已经存在！")

            # 检查渠道是否存在
            result = await session.execute(
                text("SELECT channel_type FROM dy_channel_info WHERE channel = :channel"),
                {"channel": data.channel}
            )
            channel_row = result.fetchone()
            if not channel_row:
                raise ResponseError(ResponseCode.CUSTOM, "渠道号不存在")

            channel_type = channel_row[0]
            if channel_type not in [200, 400]:
                raise ResponseError(ResponseCode.CUSTOM, "不是币商渠道或码商渠道创建失败")

            # 创建分组
            await session.execute(
                text("""
                    INSERT INTO dy_group_info(group_name, channel, platform_id) 
                    VALUES(:group_name, :channel, :platform_id)
                """),
                {
                    "group_name": data.groupName,
                    "channel": data.channel,
                    "platform_id": data.platformID
                }
            )

        return {"code": 200, "msg": "success"}

    async def set_group_member(self, data: SetGroupMemberView) -> Dict[str, Any]:
        """
        设置小组成员
        对应原Lua函数: setgroupmember
        """
        # 验证签名
        if not verify_md5_signature(data.dict(exclude={'sign'}), data.sign):
            raise ResponseError(ResponseCode.CUSTOM, "签名验证失败")

        async with db.get_session() as session:
            # 获取小组信息
            result = await session.execute(
                text("SELECT group_name, channel FROM dy_group_info WHERE id = :group_id"),
                {"group_id": data.groupID}
            )
            group_row = result.fetchone()
            if not group_row:
                raise ResponseError(ResponseCode.CUSTOM, "小组不存在")

            group_name, channel = group_row

            # 验证操作类型
            if data.optType not in [1, 2]:
                raise ResponseError(ResponseCode.CUSTOM, "操作类型错误")

            # 获取用户信息
            user = await UserInfo.get_user_info_by_id(data.userid)
            if not user:
                raise ResponseError(ResponseCode.CUSTOM, "用户不存在")

            # 检查用户当前状态
            result = await session.execute(
                text("""
                    SELECT status FROM dy_group_member 
                    WHERE group_user_id = :user_id AND group_id = :group_id 
                    ORDER BY id DESC LIMIT 1
                """),
                {"user_id": data.userid, "group_id": data.groupID}
            )
            status_row = result.fetchone()
            current_status = status_row[0] if status_row else 0

            if data.optType == 1:  # 添加
                if current_status == 1:
                    raise ResponseError(ResponseCode.CUSTOM, "用户已存在")

                if user.channel != channel:
                    raise ResponseError(ResponseCode.CUSTOM, "用户不属于小组渠道，添加失败")

                # 添加成员
                await session.execute(
                    text("""
                        INSERT INTO dy_group_member(group_id, group_name, group_user_id, group_user_name, status) 
                        VALUES(:group_id, :group_name, :user_id, :user_name, 1)
                    """),
                    {
                        "group_id": data.groupID,
                        "group_name": group_name,
                        "user_id": user.userid,
                        "user_name": user.nickname
                    }
                )

                # 更新组成员数量
                await session.execute(
                    text("UPDATE dy_group_info SET group_num = group_num + 1 WHERE id = :group_id"),
                    {"group_id": data.groupID}
                )

            elif data.optType == 2:  # 删除
                if current_status != 1:
                    raise ResponseError(ResponseCode.CUSTOM, "用户已经被删除或者已经存在")

                # 删除成员
                await session.execute(
                    text("""
                        UPDATE dy_group_member SET status = 2 
                        WHERE group_id = :group_id AND group_user_id = :user_id
                    """),
                    {"group_id": data.groupID, "user_id": user.userid}
                )

                # 更新组成员数量
                await session.execute(
                    text("UPDATE dy_group_info SET group_num = group_num - 1 WHERE id = :group_id"),
                    {"group_id": data.groupID}
                )

        return {"code": 200, "msg": "success"}

    async def set_group_orders(self, data: SetGroupOrdersView) -> Dict[str, Any]:
        """
        设置小组接单
        对应原Lua函数: setgrouporders
        """
        # 验证签名
        if not verify_md5_signature(data.dict(exclude={'sign'}), data.sign):
            raise ResponseError(ResponseCode.CUSTOM, "签名验证失败")

        async with db.get_session() as session:
            # 检查小组是否存在
            result = await session.execute(
                text("SELECT * FROM dy_group_info WHERE id = :group_id"),
                {"group_id": data.groupID}
            )
            if not result.fetchone():
                raise ResponseError(ResponseCode.CUSTOM, "小组不存在")

            # 验证参数
            if data.optID not in [1, 2, 3]:
                raise ResponseError(ResponseCode.CUSTOM, "参数错误1")

            if data.status not in [0, 1]:
                raise ResponseError(ResponseCode.CUSTOM, "参数错误2")

            # 确定要更新的字段
            field_map = {
                1: "group_recharge_switch",
                2: "group_withdraw_switch",
                3: "group_behalf_switch"
            }
            field_name = field_map[data.optID]

            # 更新小组开关
            await session.execute(
                text(f"UPDATE dy_group_info SET {field_name} = :status WHERE id = :group_id"),
                {"status": data.status, "group_id": data.groupID}
            )

            # 更新成员开关
            await session.execute(
                text(f"UPDATE dy_group_member SET {field_name} = :status WHERE group_id = :group_id"),
                {"status": data.status, "group_id": data.groupID}
            )

        return {"code": 200, "msg": "success"}

    async def del_group(self, data: DelGroupView) -> Dict[str, Any]:
        """
        删除分组
        对应原Lua函数: delgrouporders
        """
        # 验证签名
        if not verify_md5_signature(data.dict(exclude={'sign'}), data.sign):
            raise ResponseError(ResponseCode.CUSTOM, "签名验证失败")

        async with db.get_session() as session:
            # 检查小组是否存在
            result = await session.execute(
                text("SELECT * FROM dy_group_info WHERE id = :group_id"),
                {"group_id": data.groupID}
            )
            if not result.fetchone():
                return {"code": 200, "msg": "success"}

            # 删除分组信息
            await session.execute(
                text("DELETE FROM dy_group_info WHERE id = :group_id"),
                {"group_id": data.groupID}
            )

            # 删除分组成员
            await session.execute(
                text("DELETE FROM dy_group_member WHERE group_id = :group_id"),
                {"group_id": data.groupID}
            )

            # 删除相关配置
            await session.execute(
                text("DELETE FROM dy_user_conf WHERE bind_type = 104 AND bind_userid = :group_id"),
                {"group_id": data.groupID}
            )

        return {"code": 200, "msg": "success"}
