from typing import Dict, Any
from app.shared.response import ResponseError, ResponseCode
from app.modules.http.views.pay_center_views import BuyView, SellView
from app.core.data_source.mysql import db
from app.shared.schemas.dy_user_info import UserInfo
from sqlalchemy import text
from app.modules.http.utils.crypto_utils import verify_md5_signature
import json


class PayCenterService:
    """支付中心服务类"""

    async def buy(self, data: BuyView) -> Dict[str, Any]:
        """
        购买下单（买币）
        对应原Lua函数: buy
        """
        # 验证签名
        if not verify_md5_signature(data.dict(exclude={'sign'}), data.sign):
            raise ResponseError(ResponseCode.CUSTOM, "签名验证失败")

        async with db.get_session() as session:
            # 获取商户信息
            user = await UserInfo.get_user_info_by_id(data.merchantUserId)
            if not user:
                raise ResponseError(ResponseCode.CUSTOM, "商户不存在")

            # 检查商户状态
            if user.ischeck == 0:
                raise ResponseError(ResponseCode.CUSTOM, "商户未审核")

            # 检查是否有未完成的订单
            result = await session.execute(
                text("""
                    SELECT COUNT(*) FROM dy_customer_order 
                    WHERE customer_user_id = :user_id AND status IN (3, 4, 5, 6, 9, 10, 12, 13, 14, 15)
                """),
                {"user_id": data.merchantUserId}
            )
            pending_count = result.scalar()
            if pending_count > 0:
                raise ResponseError(ResponseCode.CUSTOM, "存在未完成的订单")

            # 验证金额
            if data.money <= 0:
                raise ResponseError(ResponseCode.CUSTOM, "金额必须大于0")

            # 这里需要实现具体的买币逻辑
            # 包括：
            # 1. 查找合适的币商挂单
            # 2. 创建客户订单
            # 3. 冻结资金
            # 4. 返回订单信息

            order_info = await self._create_buy_order(session, user, data)

        return {
            "code": 200,
            "msg": "success",
            "data": order_info
        }

    async def sell(self, data: SellView) -> Dict[str, Any]:
        """
        出售下单（卖币）
        对应原Lua函数: sell
        """
        # 验证签名
        if not verify_md5_signature(data.dict(exclude={'sign'}), data.sign):
            raise ResponseError(ResponseCode.CUSTOM, "签名验证失败")

        async with db.get_session() as session:
            # 获取商户信息
            user = await UserInfo.get_user_info_by_id(data.merchantUserId)
            if not user:
                raise ResponseError(ResponseCode.CUSTOM, "商户不存在")

            # 检查商户状态
            if user.ischeck == 0:
                raise ResponseError(ResponseCode.CUSTOM, "商户未审核")

            # 检查是否有未完成的订单
            result = await session.execute(
                text("""
                    SELECT COUNT(*) FROM dy_customer_order 
                    WHERE customer_user_id = :user_id AND status IN (3, 4, 5, 6, 9, 10, 12, 13, 14, 15)
                """),
                {"user_id": data.merchantUserId}
            )
            pending_count = result.scalar()
            if pending_count > 0:
                raise ResponseError(ResponseCode.CUSTOM, "存在未完成的订单")

            # 验证币数量
            if data.amount <= 0:
                raise ResponseError(ResponseCode.CUSTOM, "币数量必须大于0")

            # 检查用户余额
            if user.erc_usdt_amount < data.amount:
                raise ResponseError(ResponseCode.CUSTOM, "余额不足")

            # 这里需要实现具体的卖币逻辑
            # 包括：
            # 1. 查找合适的币商挂单
            # 2. 创建客户订单
            # 3. 冻结币
            # 4. 返回订单信息

            order_info = await self._create_sell_order(session, user, data)

        return {
            "code": 200,
            "msg": "success",
            "data": order_info
        }

    async def _create_buy_order(self, session, user, data: BuyView) -> Dict[str, Any]:
        """创建买币订单"""
        # 这里需要实现具体的买币订单创建逻辑
        # 包括匹配币商、计算手续费、创建订单等
        
        # 查找合适的卖币挂单
        result = await session.execute(
            text("""
                SELECT * FROM dy_vendor_order 
                WHERE type = 1 AND status = 1 AND channel = :channel
                ORDER BY price ASC LIMIT 1
            """),
            {"channel": user.channel}
        )
        vendor_order = result.fetchone()
        
        if not vendor_order:
            raise ResponseError(ResponseCode.CUSTOM, "暂无合适的挂单")

        # 创建客户订单
        order_id = await self._insert_customer_order(session, user, data, vendor_order, "buy")
        
        return {
            "orderId": order_id,
            "amount": data.money / vendor_order[4],  # money / price
            "price": vendor_order[4],
            "payInfo": {}  # 支付信息
        }

    async def _create_sell_order(self, session, user, data: SellView) -> Dict[str, Any]:
        """创建卖币订单"""
        # 这里需要实现具体的卖币订单创建逻辑
        
        # 查找合适的买币挂单
        result = await session.execute(
            text("""
                SELECT * FROM dy_vendor_order 
                WHERE type = 0 AND status = 1 AND channel = :channel
                ORDER BY price DESC LIMIT 1
            """),
            {"channel": user.channel}
        )
        vendor_order = result.fetchone()
        
        if not vendor_order:
            raise ResponseError(ResponseCode.CUSTOM, "暂无合适的挂单")

        # 创建客户订单
        order_id = await self._insert_customer_order(session, user, data, vendor_order, "sell")
        
        return {
            "orderId": order_id,
            "amount": data.amount,
            "money": data.amount * vendor_order[4],  # amount * price
            "price": vendor_order[4]
        }

    async def _insert_customer_order(self, session, user, data, vendor_order, order_type: str) -> int:
        """插入客户订单"""
        # 这里需要实现具体的订单插入逻辑
        # 包括计算手续费、设置订单状态等
        
        if order_type == "buy":
            amount = data.money / vendor_order[4]
            money = data.money
        else:
            amount = data.amount
            money = data.amount * vendor_order[4]

        # 插入订单
        result = await session.execute(
            text("""
                INSERT INTO dy_customer_order (
                    vendor_order_id, customer_user_id, vendor_user_id,
                    type, amount, money, status, create_time,
                    notify_url, body, customer_ip, from_type
                ) VALUES (
                    :vendor_order_id, :customer_user_id, :vendor_user_id,
                    :type, :amount, :money, :status, NOW(),
                    :notify_url, :body, :customer_ip, :from_type
                )
            """),
            {
                "vendor_order_id": vendor_order[0],
                "customer_user_id": user.userid,
                "vendor_user_id": vendor_order[2],
                "type": 0 if order_type == "buy" else 1,
                "amount": amount,
                "money": money,
                "status": 17,  # 未匹配状态
                "notify_url": getattr(data, 'notifyUrl', ''),
                "body": getattr(data, 'body', ''),
                "customer_ip": data.IP,
                "from_type": 2  # 三方订单
            }
        )
        
        return result.lastrowid
