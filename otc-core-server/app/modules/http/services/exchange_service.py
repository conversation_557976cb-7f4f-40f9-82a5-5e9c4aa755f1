import json
from typing import Dict, Any
from app.shared.response import ResponseError, ResponseCode
from app.modules.http.views.exchange_views import UpdateOrderInfoView, UpdateOrderStatusView
from app.core.data_source.mysql import db
from app.shared.schemas.dy_customer_order import CustomerOrder
from app.shared.schemas.dy_user_info import UserInfo
from sqlalchemy import text
from app.modules.http.utils.crypto_utils import verify_md5_signature


class ExchangeService:
    """交易所服务类"""

    async def update_order_info(self, data: UpdateOrderInfoView) -> Dict[str, Any]:
        """
        更新订单信息
        对应原Lua函数: updateOrderInfo
        """
        # 验证签名
        if not verify_md5_signature(data.dict(exclude={'sign'}), data.sign):
            raise ResponseError(ResponseCode.CUSTOM, "签名验证失败")

        async with db.get_session() as session:
            # 查询订单信息
            order = await CustomerOrder.get_by_id(session, data.orderID)
            if not order:
                raise ResponseError(ResponseCode.CUSTOM, "订单不存在")

            # 构建支付信息
            pay_info = {
                "id": 0,
                "paytype": 1,  # g_humanDefine.user_payList.bank
                "account": data.payeeAccount or "",
                "payee": data.payeeName or "",
                "qrcode": "",
                "bankname": data.payeeBank or "",
                "bankaddr": "",
                "singlelimit": "0",
                "daylimit": "0",
                "fourthpartyid": 0,
                "deallasttime": "2020-01-01 00:00:00"
            }

            # 更新订单支付信息
            order.pay_type = json.dumps(pay_info)
            order.payee_account = pay_info["account"]
            order.payee_name = pay_info["payee"]
            order.payee_bank = pay_info["bankname"]
            if data.tradeId:
                order.trade_id = data.tradeId

            await session.commit()

        return {"code": 200, "msg": "success"}

    async def update_order_status(self, data: UpdateOrderStatusView) -> Dict[str, Any]:
        """
        更新订单状态
        对应原Lua函数: updateOrderStatus
        """
        # 验证签名
        if not verify_md5_signature(data.dict(exclude={'sign'}), data.sign):
            raise ResponseError(ResponseCode.CUSTOM, "签名验证失败")

        async with db.get_session() as session:
            # 通过交易所订单号查找订单ID
            if not data.orderID:
                result = await session.execute(
                    text("SELECT id FROM dy_customer_order WHERE trade_id = :trade_id"),
                    {"trade_id": data.tradeId}
                )
                order_row = result.fetchone()
                if not order_row:
                    raise ResponseError(ResponseCode.CUSTOM, "订单不存在")
                data.orderID = order_row[0]

            # 获取订单信息
            order = await CustomerOrder.get_by_id(session, data.orderID)
            if not order:
                raise ResponseError(ResponseCode.CUSTOM, "订单不存在")

            # 验证订单类型
            if order.dealtype not in [200, 300]:
                raise ResponseError(ResponseCode.CUSTOM, "订单类型错误")

            # 确定操作用户
            if order.dealtype == 200:
                user_id = order.vendoruserid
            else:
                user_id = order.customeruserid

            user = await UserInfo.get_user_info_by_id(user_id)
            if not user:
                raise ResponseError(ResponseCode.CUSTOM, "用户不存在")

            # 根据状态执行相应操作
            await self._handle_order_status_change(session, order, user, data)

        return {"code": 200, "msg": "success"}

    async def _handle_order_status_change(self, session, order, user, data: UpdateOrderStatusView):
        """处理订单状态变更"""
        status = data.status

        if status == 1:  # 放行
            await self._pass_order(session, order, user)
        elif status == 2:  # 取消
            await self._cancel_order(session, order, user)
        elif status == 3:  # 付款
            await self._paid_order(session, order, user, data.proofurl)
        elif status == 4:  # 激活
            await self._activate_order(session, order, user)

    async def _pass_order(self, session, order, user):
        """放行订单"""
        # 检查订单状态是否允许放行
        if order.status not in [3, 4, 5]:  # 对应g_marketDefine.deal_status_pay等状态
            raise ResponseError(ResponseCode.CUSTOM, "订单状态不允许放行")

        # 更新订单状态为已完成
        await session.execute(
            text("UPDATE dy_customer_order SET status = 6 WHERE id = :order_id"),
            {"order_id": order.id}
        )

        # 解冻并转移资金
        # 这里需要根据具体业务逻辑实现资金转移

    async def _cancel_order(self, session, order, user):
        """取消订单"""
        # 更新订单状态为已取消
        await session.execute(
            text("UPDATE dy_customer_order SET status = 2 WHERE id = :order_id"),
            {"order_id": order.id}
        )

        # 解冻资金
        # 这里需要根据具体业务逻辑实现资金解冻

    async def _paid_order(self, session, order, user, proof_url: str):
        """付款订单"""
        # 更新订单状态为已付款
        await session.execute(
            text("UPDATE dy_customer_order SET status = 4, proof_url = :proof_url WHERE id = :order_id"),
            {"proof_url": proof_url, "order_id": order.id}
        )

    async def _activate_order(self, session, order, user):
        """激活订单"""
        # 检查订单状态
        if order.status not in [1, 2, 8]:  # 对应超时、取消、申诉取消等状态
            raise ResponseError(ResponseCode.CUSTOM, "该状态下，不能激活订单")

        # 检查收款人资金
        payee_info = None
        payer_info = None

        if order.type == 0:  # 买币
            payee_info = await UserInfo.get_user_info_by_id(order.vendor_user_id)
            payer_info = await UserInfo.get_user_info_by_id(order.customer_user_id)
        else:  # 卖币
            payee_info = await UserInfo.get_user_info_by_id(order.customer_user_id)
            payer_info = await UserInfo.get_user_info_by_id(order.vendor_user_id)

        if not payee_info or not payer_info:
            raise ResponseError(ResponseCode.CUSTOM, "用户信息不存在")

        # 检查余额
        currency_count = float(order.amount)
        if payee_info.erc_usdt_amount < currency_count:
            raise ResponseError(ResponseCode.CUSTOM, "收款人余额不足！")

        # 冻结资金
        await session.execute(
            text("UPDATE dy_user_info SET erc_usdt_lock_amount = erc_usdt_lock_amount + :amount WHERE userid = :user_id"),
            {"amount": currency_count, "user_id": payee_info.userid}
        )

        # 更新订单状态为待付款
        await session.execute(
            text("UPDATE dy_customer_order SET status = 3 WHERE id = :order_id"),
            {"order_id": order.id}
        )
