from typing import Dict, Any
from app.shared.response import ResponseError, ResponseCode
from app.modules.http.views.user_views import CreateChannelAgentView
from app.core.data_source.mysql import db
from sqlalchemy import text
from app.modules.http.utils.crypto_utils import verify_md5_signature
import hashlib
import json


class UserService:
    """用户管理服务类"""

    async def create_channel_agent(self, data: CreateChannelAgentView) -> Dict[str, Any]:
        """
        创建渠道商
        对应原Lua函数: createChannelAgent
        """
        # 验证签名
        if not verify_md5_signature(data.dict(exclude={'sign'}), data.sign):
            raise ResponseError(ResponseCode.CUSTOM, "签名验证失败")

        async with db.get_session() as session:
            # 检查渠道是否存在
            result = await session.execute(
                text("SELECT * FROM dy_channel_info WHERE channel = :channel"),
                {"channel": data.channel}
            )
            if result.fetchone():
                raise ResponseError(ResponseCode.CUSTOM, "渠道已存在")

            # 检查前台账号是否存在 (对应Lua中的web_account检查)
            result = await session.execute(
                text("SELECT * FROM dy_user_info WHERE account = :account OR phonenum = :phonenum OR email = :email"),
                {"account": data.web_account, "phonenum": data.web_account, "email": data.web_account}
            )
            if result.fetchone():
                raise ResponseError(ResponseCode.CUSTOM, "前端账号已存在")

            # 创建渠道信息
            channel_result = await session.execute(
                text("""
                    INSERT INTO dy_channel_info (
                        channel, phonenum, account, password, web_account, web_password,
                        channel_name, channel_deal, fund_scheduling, is_external,
                        channel_type, create_time
                    ) VALUES (
                        :channel, :phonenum, '', '', '', '',
                        :channel_name, :channel_deal, :fund_scheduling, :is_external,
                        :channel_type, NOW()
                    )
                """),
                {
                    "channel": data.channel,
                    "phonenum": data.phonenum,
                    "channel_name": data.channel_name,
                    "channel_deal": data.channel_deal,
                    "fund_scheduling": data.fund_scheduling,
                    "is_external": data.is_external,
                    "channel_type": getattr(data, 'channel_type', 200)
                }
            )
            channel_id = channel_result.lastrowid

            # 生成随机昵称 (对应Lua中的随机名称生成)
            import random
            import string
            chars = list(string.ascii_uppercase) + ['q']
            random_name = ''.join(random.choices(chars, k=7))

            # 获取所有可用支付方式
            pay_result = await session.execute(
                text("SELECT pay_id FROM dy_pay_info WHERE is_lock = 0")
            )
            pay_list = []
            for row in pay_result.fetchall():
                pay_list.append({
                    "payType": row[0],
                    "buy_free_rate": 0,
                    "sell_free_rate": 0,
                    "buy_common_rate": 0,
                    "sell_common_rate": 0,
                    "behalf_sell_fee_rate": 0,
                    "behalf_buy_comm_rate": 0,
                    "state": 1
                })

            # 创建渠道APP用户
            web_password_md5 = hashlib.md5(data.web_password.encode()).hexdigest()

            # 生成邀请码 (对应Lua中的invite_code生成)
            import string
            invite_chars = list(string.ascii_uppercase) + list(string.digits)
            invite_chars.remove('0')  # 移除0避免混淆
            invite_code = ''.join(random.choices(invite_chars, k=8))

            # 生成设备唯一标识 (对应Lua中的cid)
            cid_chars = list(string.ascii_uppercase) + list(string.digits)
            cid = ''.join(random.choices(cid_chars, k=16))

            # 生成随机face_1 (对应Lua中的GetInitFaceID)
            face_1 = str(random.randint(1, 10))

            # 生成payidlist JSON字符串
            pay_id_list = [item["payType"] for item in pay_list]
            payidlist_json = json.dumps(pay_id_list)

            user_result = await session.execute(
                text("""
                    INSERT INTO dy_user_info (
                        cid, account, password, nickname, face_1, phonenum, email,
                        channel, bind_code, bindtype, invite_code, payidlist,
                        erc_usdt_amount, agent, user_type, comm_type, back_mobile,
                        username, is_invited, is_allow_loginback, deal_coin_type,
                        team_name, platform_id, is_team_accept_order, create_time
                    ) VALUES (
                        :cid, :account, :password, :nickname, :face_1, :phonenum, '',
                        :channel, '', 1, :invite_code, :payidlist,
                        '0.**********', 0, 200, 101, :back_mobile,
                        :username, 0, 0, 0,
                        '', 0, 0, NOW()
                    )
                """),
                {
                    "cid": cid,
                    "account": data.web_account,
                    "password": web_password_md5,
                    "nickname": random_name,
                    "face_1": face_1,
                    "phonenum": data.web_account,
                    "channel": data.channel,
                    "invite_code": invite_code,
                    "payidlist": payidlist_json,
                    "back_mobile": data.phonenum,
                    "username": data.channel
                }
            )
            user_id = user_result.lastrowid

            # 插入用户费率表 (对应Lua中的dy_user_rate插入)
            for pay_item in pay_list:
                await session.execute(
                    text("""
                        INSERT INTO dy_user_rate (
                            userid, paytype, buy_fee_rate, sell_fee_rate,
                            buy_comm_rate, sell_comm_rate, channel, is_used,
                            behalf_sell_fee_rate, behalf_buy_comm_rate
                        ) VALUES (
                            :userid, :paytype, :buy_fee_rate, :sell_fee_rate,
                            :buy_comm_rate, :sell_comm_rate, :channel, :is_used,
                            :behalf_sell_fee_rate, :behalf_buy_comm_rate
                        )
                    """),
                    {
                        "userid": user_id,
                        "paytype": pay_item["payType"],
                        "buy_fee_rate": pay_item["buy_free_rate"],
                        "sell_fee_rate": pay_item["sell_free_rate"],
                        "buy_comm_rate": pay_item["buy_common_rate"],
                        "sell_comm_rate": pay_item["sell_common_rate"],
                        "channel": data.channel,
                        "is_used": pay_item["state"],
                        "behalf_sell_fee_rate": pay_item["behalf_sell_fee_rate"],
                        "behalf_buy_comm_rate": pay_item["behalf_buy_comm_rate"]
                    }
                )

            # 创建给Userid的自增值加20到50 (对应Lua中的AUTO_INCREMENT调整)
            add_num = random.randint(20, 50)
            await session.execute(
                text(f"ALTER TABLE dy_user_info AUTO_INCREMENT = {user_id + add_num}")
            )

            # 插入log用户表 (对应Lua中的log_user插入)
            await session.execute(
                text("INSERT INTO log_user (userid, channel) VALUES (:userid, :channel)"),
                {"userid": user_id, "channel": data.channel}
            )

            # 更新渠道对应的用户ID
            await session.execute(
                text("UPDATE dy_channel_info SET userid = :userid WHERE id = :channel_id"),
                {"userid": user_id, "channel_id": channel_id}
            )

            # 如果是外部渠道，默认开启自动接单
            if data.is_external == 1:
                from datetime import datetime
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                # 创建买单挂单
                await session.execute(
                    text("""
                        INSERT INTO dy_vendor_order (
                            userid, type, price_type, min_money, max_money, auto_switch,
                            create_time, channel, enable_status, user_type, channel_deal,
                            rest_time, is_external, platform_id
                        ) VALUES (
                            :userid, 1, 1, 1, 99999999, 1, :create_time,
                            :channel, 1, 200, :channel_deal, :rest_time, 1, 0
                        )
                    """),
                    {
                        "userid": user_id,
                        "create_time": current_time,
                        "channel": data.channel,
                        "channel_deal": data.channel_deal,
                        "rest_time": current_time
                    }
                )
                
                # 创建卖单挂单
                await session.execute(
                    text("""
                        INSERT INTO dy_vendor_order (
                            userid, type, price_type, min_money, max_money, auto_switch,
                            create_time, channel, enable_status, user_type, channel_deal,
                            rest_time, is_external, platform_id
                        ) VALUES (
                            :userid, 0, 1, 1, 99999999, 1, :create_time,
                            :channel, 1, 200, :channel_deal, :rest_time, 1, 0
                        )
                    """),
                    {
                        "userid": user_id,
                        "create_time": current_time,
                        "channel": data.channel,
                        "channel_deal": data.channel_deal,
                        "rest_time": current_time
                    }
                )

            await session.commit()

        return {"code": 200, "msg": "success"}
