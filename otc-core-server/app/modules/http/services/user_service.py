from typing import Dict, Any
from app.shared.response import ResponseError, ResponseCode
from app.modules.http.views.user_views import CreateChannelAgentView
from app.core.data_source.mysql import db
from sqlalchemy import text
from app.modules.http.utils.crypto_utils import verify_md5_signature
import hashlib
import json


class UserService:
    """用户管理服务类"""

    async def create_channel_agent(self, data: CreateChannelAgentView) -> Dict[str, Any]:
        """
        创建渠道商
        对应原Lua函数: createChannelAgent
        """
        # 验证签名
        if not verify_md5_signature(data.dict(exclude={'sign'}), data.sign):
            raise ResponseError(ResponseCode.CUSTOM, "签名验证失败")

        async with db.get_session() as session:
            # 检查渠道是否存在
            result = await session.execute(
                text("SELECT * FROM dy_channel_info WHERE channel = :channel"),
                {"channel": data.channel}
            )
            if result.fetchone():
                raise ResponseError(ResponseCode.CUSTOM, "渠道已存在")

            # 检查前台账号是否存在 (对应Lua中的web_account检查)
            result = await session.execute(
                text("SELECT * FROM dy_user_info WHERE account = :account OR phonenum = :phonenum OR email = :email"),
                {"account": data.web_account, "phonenum": data.web_account, "email": data.web_account}
            )
            if result.fetchone():
                raise ResponseError(ResponseCode.CUSTOM, "前端账号已存在")

            # 创建渠道信息 (对应Lua中的UserInfoModel.CreateChanel)
            channel_result = await session.execute(
                text("""
                    INSERT INTO dy_channel_info (
                        account, password, web_account, web_password, channel, phonenum,
                        channel_type, channle_name, channel_deal, fund_scheduling, is_external
                    ) VALUES (
                        '', '', '', '', :channel, :phonenum,
                        :channel_type, :channel_name, :channel_deal, :fund_scheduling, :is_external
                    )
                """),
                {
                    "channel": data.channel,
                    "phonenum": data.phonenum,
                    "channel_name": data.channel_name,
                    "channel_deal": data.channel_deal,
                    "fund_scheduling": data.fund_scheduling,
                    "is_external": data.is_external,
                    "channel_type": getattr(data, 'channel_type', 200)
                }
            )
            channel_id = channel_result.lastrowid

            if channel_id is None or channel_id == 0:
                raise ResponseError(ResponseCode.CUSTOM, "创建渠道失败")

            # 设置汇率 - 复制ALL渠道的币种信息 (对应Lua中的dy_coin_info插入)
            coin_result = await session.execute(
                text("SELECT coin_id, coin_name, coin_type, description, buy_price, sell_price, free_price FROM dy_coin_info WHERE channel = 'ALL' AND coin_id = 2003")
            )
            coin_info = coin_result.fetchone()
            if coin_info:
                await session.execute(
                    text("""
                        INSERT INTO dy_coin_info (
                            coin_id, coin_name, coin_type, description, buy_price, sell_price, free_price, channel
                        ) VALUES (
                            :coin_id, :coin_name, :coin_type, :description, :buy_price, :sell_price, :free_price, :channel
                        )
                    """),
                    {
                        "coin_id": coin_info[0],
                        "coin_name": coin_info[1],
                        "coin_type": coin_info[2],
                        "description": coin_info[3],
                        "buy_price": coin_info[4],
                        "sell_price": coin_info[5],
                        "free_price": coin_info[6],
                        "channel": data.channel
                    }
                )

            # 如果是特定类型的渠道，创建web配置 (对应Lua中的dy_web_conf插入)
            channel_type = getattr(data, 'channel_type', 200)
            if channel_type == 200 or channel_type == 400:
                await session.execute(
                    text("INSERT INTO dy_web_conf (channel, channel_name) VALUES (:channel, :channel_name)"),
                    {"channel": data.channel, "channel_name": data.channel_name}
                )

            # 导入必要的模块
            import random
            import string

            # 获取所有可用支付方式
            pay_result = await session.execute(
                text("SELECT pay_id FROM dy_pay_info WHERE is_lock = 0")
            )
            pay_list = []
            for row in pay_result.fetchall():
                pay_list.append({
                    "payType": row[0],
                    "buy_free_rate": 0,
                    "sell_free_rate": 0,
                    "buy_common_rate": 0,
                    "sell_common_rate": 0,
                    "behalf_sell_fee_rate": 0,
                    "behalf_buy_comm_rate": 0,
                    "state": 1
                })

            # 创建渠道APP用户 (对应Lua中的UserInfoModel.CreateUser)
            web_password_md5 = hashlib.md5(data.web_password.encode()).hexdigest()

            # 生成随机昵称 (完全对应Lua中的逻辑)
            # local tmp = {"A","B","C","D","E","F","G","H","I","J","K","L","N","M","O","P","q","R","S","T","U","V","W","X","Z"}
            name_chars = ["A","B","C","D","E","F","G","H","I","J","K","L","N","M","O","P","q","R","S","T","U","V","W","X","Z"]
            random_name = ''.join(random.choices(name_chars, k=7))

            # 生成邀请码 (对应Lua中的invite_code生成)
            invite_chars = ['A','B','C','D','E','F','G','H','I','J','K','L','N','M','O','P','Q','R','S','T','U','V','W','X','Y','Z',
                           '1','2','3','4','6','7','7','8','9']
            invite_code = ''.join(random.choices(invite_chars, k=8))

            # 生成设备唯一标识 (对应Lua中的GetRandomCID)
            cid_chars = list(string.ascii_uppercase) + list(string.digits)
            cid = ''.join(random.choices(cid_chars, k=16))

            # 生成随机face_1 (对应Lua中的GetInitFaceID)
            face_1 = str(random.randint(1, 10))

            # 生成payidlist JSON字符串 (只包含state=1的支付方式)
            pay_id_list = [item["payType"] for item in pay_list if item["state"] == 1]
            payidlist_json = json.dumps(pay_id_list)

            # 计算is_allow_loginback (对应Lua中的逻辑)
            # local is_allow_loginback = (usertype==200 or usertype ==400) and 0 or 1
            user_type = 200  # g_humanDefine.usertype_currency_agent
            is_allow_loginback = 0 if (user_type == 200 or user_type == 400) else 1

            user_result = await session.execute(
                text("""
                    INSERT INTO dy_user_info (
                        cid, account, password, nickname, face_1, phonenum, channel, bind_code,
                        bindtype, invite_code, payidlist, erc_usdt_amount, agent, user_type,
                        comm_type, back_mobile, username, email, is_invited, is_allow_loginback,
                        deal_coin_type, team_name, platform_id, is_team_accept_order
                    ) VALUES (
                        :cid, :account, :password, :nickname, :face_1, :phonenum, :channel, '',
                        :bindtype, :invite_code, :payidlist, '0.**********', 0, :user_type,
                        101, :back_mobile, :username, '', 0, :is_allow_loginback,
                        0, '', 0, 0
                    )
                """),
                {
                    "cid": cid,
                    "account": data.web_account,
                    "password": web_password_md5,
                    "nickname": random_name,
                    "face_1": face_1,
                    "phonenum": data.web_account,
                    "channel": data.channel,
                    "bindtype": 1,  # g_humanDefine.bindType_phone
                    "invite_code": invite_code,
                    "payidlist": payidlist_json,
                    "back_mobile": data.phonenum,
                    "username": data.channel,
                    "user_type": user_type,
                    "is_allow_loginback": is_allow_loginback
                }
            )
            user_id = user_result.lastrowid

            if user_id is None or user_id == 0:
                raise ResponseError(ResponseCode.CUSTOM, "创建用户失败")

            # 插入用户费率表 (对应Lua中的dy_user_rate插入)
            for pay_item in pay_list:
                await session.execute(
                    text("""
                        INSERT INTO dy_user_rate (
                            userid, paytype, buy_fee_rate, sell_fee_rate,
                            buy_comm_rate, sell_comm_rate, channel, is_used,
                            behalf_sell_fee_rate, behalf_buy_comm_rate
                        ) VALUES (
                            :userid, :paytype, :buy_fee_rate, :sell_fee_rate,
                            :buy_comm_rate, :sell_comm_rate, :channel, :is_used,
                            :behalf_sell_fee_rate, :behalf_buy_comm_rate
                        )
                    """),
                    {
                        "userid": user_id,
                        "paytype": pay_item["payType"],
                        "buy_fee_rate": pay_item["buy_free_rate"],
                        "sell_fee_rate": pay_item["sell_free_rate"],
                        "buy_comm_rate": pay_item["buy_common_rate"],
                        "sell_comm_rate": pay_item["sell_common_rate"],
                        "channel": data.channel,
                        "is_used": pay_item["state"],
                        "behalf_sell_fee_rate": pay_item["behalf_sell_fee_rate"],
                        "behalf_buy_comm_rate": pay_item["behalf_buy_comm_rate"]
                    }
                )

            # 创建给Userid的自增值加20到50 (对应Lua中的AUTO_INCREMENT调整)
            add_num = random.randint(20, 50)
            await session.execute(
                text(f"ALTER TABLE dy_user_info AUTO_INCREMENT = {user_id + add_num}")
            )

            # 插入log用户表 (对应Lua中的log_user插入)
            await session.execute(
                text("INSERT INTO log_user (userid, channel) VALUES (:userid, :channel)"),
                {"userid": user_id, "channel": data.channel}
            )

            # 更新渠道对应的用户ID
            await session.execute(
                text("UPDATE dy_channel_info SET userid = :userid WHERE id = :channel_id"),
                {"userid": user_id, "channel_id": channel_id}
            )

            # 如果是外部渠道，默认开启自动接单 (对应Lua中的外部渠道逻辑)
            if data.is_external == 1:
                from datetime import datetime
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # 获取用户的platform_id (对应Lua中的uInfo.platformid)
                platform_id = 0  # 默认值，实际应该从用户信息中获取

                # 创建买单挂单 (type=1表示买单)
                await session.execute(
                    text("""
                        INSERT INTO dy_vendor_order (
                            userid, type, price_type, min_money, max_money, auto_switch,
                            create_time, channel, enable_status, user_type, channel_deal,
                            rest_time, is_external, platform_id
                        ) VALUES (
                            :userid, 1, 1, 1, 99999999, 1, :create_time,
                            :channel, 1, 200, :channel_deal, :rest_time, 1, :platform_id
                        )
                    """),
                    {
                        "userid": user_id,
                        "create_time": current_time,
                        "channel": data.channel,
                        "channel_deal": data.channel_deal,
                        "rest_time": current_time,
                        "platform_id": platform_id
                    }
                )

                # 创建卖单挂单 (type=0表示卖单)
                await session.execute(
                    text("""
                        INSERT INTO dy_vendor_order (
                            userid, type, price_type, min_money, max_money, auto_switch,
                            create_time, channel, enable_status, user_type, channel_deal,
                            rest_time, is_external, platform_id
                        ) VALUES (
                            :userid, 0, 1, 1, 99999999, 1, :create_time,
                            :channel, 1, 200, :channel_deal, :rest_time, 1, :platform_id
                        )
                    """),
                    {
                        "userid": user_id,
                        "create_time": current_time,
                        "channel": data.channel,
                        "channel_deal": data.channel_deal,
                        "rest_time": current_time,
                        "platform_id": platform_id
                    }
                )

            await session.commit()

        return {"code": 200, "msg": "success"}
