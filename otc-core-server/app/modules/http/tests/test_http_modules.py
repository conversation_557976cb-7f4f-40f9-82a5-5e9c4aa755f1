"""
Http模块测试文件
用于验证重构后的Http模块功能是否正常
"""
import pytest
import json
from app.modules.http.utils.crypto_utils import generate_md5_signature, verify_md5_signature


class TestCryptoUtils:
    """测试加密工具类"""
    
    def test_md5_signature_generation(self):
        """测试MD5签名生成"""
        data = {
            "orderID": 12345,
            "payeeAccount": "****************",
            "payeeName": "张三",
            "payeeBank": "中国银行"
        }
        signature = generate_md5_signature(data)
        assert signature is not None
        assert len(signature) == 32  # MD5长度为32位
    
    def test_md5_signature_verification(self):
        """测试MD5签名验证"""
        data = {
            "orderID": 12345,
            "payeeAccount": "****************",
            "payeeName": "张三",
            "payeeBank": "中国银行"
        }
        signature = generate_md5_signature(data)
        assert verify_md5_signature(data, signature) is True
        
        # 测试错误签名
        assert verify_md5_signature(data, "wrong_signature") is False


class TestHttpModuleStructure:
    """测试Http模块结构"""
    
    def test_module_imports(self):
        """测试模块导入是否正常"""
        try:
            from app.modules.http.controllers.exchange_controller import exchange_bp
            from app.modules.http.controllers.group_controller import group_bp
            from app.modules.http.controllers.pay_center_controller import pay_center_bp
            from app.modules.http.controllers.pay_info_controller import pay_info_bp
            from app.modules.http.controllers.platform_controller import platform_bp
            from app.modules.http.controllers.user_controller import user_bp
            
            from app.modules.http.services.exchange_service import ExchangeService
            from app.modules.http.services.group_service import GroupService
            from app.modules.http.services.pay_center_service import PayCenterService
            from app.modules.http.services.pay_info_service import PayInfoService
            from app.modules.http.services.platform_service import PlatformService
            from app.modules.http.services.user_service import UserService
            
            assert True  # 如果能成功导入，测试通过
        except ImportError as e:
            pytest.fail(f"模块导入失败: {e}")
    
    def test_view_models(self):
        """测试视图模型"""
        try:
            from app.modules.http.views.exchange_views import UpdateOrderInfoView, UpdateOrderStatusView
            from app.modules.http.views.group_views import CreateGroupView, SetGroupMemberView
            from app.modules.http.views.pay_center_views import BuyView, SellView
            from app.modules.http.views.pay_info_views import SetPayInfoSwitchView
            from app.modules.http.views.user_views import CreateChannelAgentView
            
            # 测试视图模型实例化
            update_order_data = {
                "orderID": 12345,
                "payeeAccount": "****************",
                "payeeName": "张三",
                "payeeBank": "中国银行",
                "tradeId": "T123456789",
                "sign": "test_signature"
            }
            view = UpdateOrderInfoView.validate(update_order_data)
            assert view.orderID == 12345
            assert view.payeeName == "张三"
            
        except Exception as e:
            pytest.fail(f"视图模型测试失败: {e}")


def test_signature_example():
    """测试签名示例"""
    # 模拟一个完整的请求数据
    request_data = {
        "orderID": 12345,
        "payeeAccount": "****************",
        "payeeName": "张三",
        "payeeBank": "中国银行",
        "tradeId": "T123456789"
    }
    
    # 生成签名
    signature = generate_md5_signature(request_data)
    
    # 添加签名到请求数据
    request_data["sign"] = signature
    
    # 验证签名（模拟服务端验证过程）
    sign_to_verify = request_data.pop("sign")
    is_valid = verify_md5_signature(request_data, sign_to_verify)
    
    assert is_valid is True
    print(f"请求数据: {json.dumps(request_data, ensure_ascii=False)}")
    print(f"生成的签名: {signature}")


if __name__ == "__main__":
    # 运行测试
    test_signature_example()
    print("Http模块重构测试完成！")
