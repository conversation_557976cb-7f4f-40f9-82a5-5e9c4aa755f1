from app.modules.pay_center.views import OrderReqView
from app.shared.response import ResponseError, ResponseCode
from app.shared.schemas.dy_user_info import UserInfo
from enums import PayType


class PayCenterService:

    async def create_order(self, data: OrderReqView):
        # if payType == g_payCenterDefine.pay_id_list['bitpay'] then
        if data.payType == PayType.BIT_PAY:
            # TODO: 币支付
            pass
        else:
            # 其他支付
            # 检查币商是否有挂单
            coin_user = await UserInfo.get_user_info_by_id(data.coindealerID)
            if coin_user.user_type not in [200, 201, 202]:
                raise ResponseError(ResponseCode.CUSTOM, '币商不存在')

            merchant_user = await UserInfo.get_user_info_by_id(data.merchantID)
            if merchant_user.user_type not in [300, 301]:
                raise ResponseError(ResponseCode.CUSTOM, '商户不存在')
