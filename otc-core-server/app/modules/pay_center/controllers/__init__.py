from quart import Blueprint
from app.modules.pay_center.views import OrderReqView
from app.modules.pay_center.services import PayCenterService
from app.shared.response import Response
from app.shared.decorators import validator

pay_center = Blueprint('pay_center', __name__)
pay_center_service = PayCenterService()

@pay_center.route('/paycenter/createorder')
@validator(required=OrderReqView)
async def create_order(body: OrderReqView):
    """
    创建订单
    :param body:

    --ordertype 				订单类型 200 充值 300 提现
    --withdrawType				提现类型 0 OTC  1 代付
    --merchantID				商户ID
    --coindealerID				币商ID
    --amount					金额
    --replenishment_remarks		备注
    --payType 					支付类型
    --sign						签证

    :return:
    """
    await pay_center_service.create_order(body)
    return Response.success(), 200
