from app.shared.libs.base_view import BaseView
from enums import OrderType, WithdrawType, PayType


class OrderReqView(BaseView):
    """
    --ordertype 				订单类型 200 充值 300 提现
    --withdrawType				提现类型 0 OTC  1 代付
    --merchantID				商户ID
    --coindealerID				币商ID
    --amount					金额
    --replenishment_remarks		备注
    --payType 					支付类型
    --sign						签证
    """
    ordertype: OrderType
    withdrawType: WithdrawType | None
    merchantID: str
    coindealerID: str
    amount: str
    replenishment_remarks: str
    payType: PayType
    sign: str | None