import logging
import os
from app.shared.response import Response, ResponseCode, ResponseError


def register_blueprints(app):
    api_prefix = os.getenv('API_PREFIX')

    """注册所有控制器蓝图"""
    from app.modules.pay_center import pay_center
    from app.modules.ucoin import ucoin
    from app.modules.http import http

    app.register_blueprint(pay_center, url_prefix=api_prefix)
    app.register_blueprint(ucoin, url_prefix=api_prefix)
    app.register_blueprint(http, url_prefix=api_prefix)

    # Interceptor
    @app.before_request
    async def interceptor_before():
        pass

    @app.errorhandler(ResponseError)
    async def framework_error(error):
        if isinstance(error, ResponseError):
            return Response.fail(error)
        else:
            return error

    @app.errorhandler(404)
    async def not_found(res):
        logging.info(res)
        error = ResponseError(ResponseCode.NOT_FOUND)
        return Response.fail(error)
