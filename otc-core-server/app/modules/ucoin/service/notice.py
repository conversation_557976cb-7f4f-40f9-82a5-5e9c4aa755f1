import json
from app.shared import *
from app.shared.libs.base_service import BaseService
from app.shared.libs.redis_key import RDS_NOTICE_LIST, RDS_NOTICE_MESSAGE_LIST
from app.shared.schemas.dy_message_notice import MessageNotice
from app.shared.schemas.dy_notice_list import NoticeContent
from enums.event import ResponseEventCode


class NoticeService(BaseService):

    def __init__(self):
        super().__init__()

    async def message_list(self, code: ResponseEventCode, pb_data: bytes):
        from app.modules.ucoin.py_protobufs import notice_pb2
        # 解析 protobuf 消息
        message = notice_pb2.MessageListRequest()
        message.ParseFromString(pb_data)

        user_id: int = message.userid
        page: int = message.pagenum
        page_size: int = message.pagesize

        offset = (page - 1) * page_size + 1

        resp_pb = notice_pb2.MessageListResponse()

        await self.check_user_exists(
            user_id,
            code,
            resp_pb
        )

        messages = await self.get_message_from_redis(user_id)

        need_to_add = []
        for index, message in enumerate(messages):
            if index >= offset:
                message_pb = notice_pb2.MessageInfo()
                message_pb = convert_to_proto(message_pb, message)
                message_pb.isread = message.is_read
                # --
                need_to_add.append(message_pb)
            if index > offset + page_size:
                break

        # 合并
        resp_pb.result = 0
        resp_pb.messagelist.extend(need_to_add)
        str_data = resp_pb.SerializeToString()
        return response_binary_data(code.value, str_data)

    async def get_message_from_redis(self, user_id: int):
        key = RDS_NOTICE_MESSAGE_LIST % user_id
        cache = await self.redis.hgetall(key)
        if cache:
            messages = []
            for k, message in cache:
                data = json.loads(message)
                schema = MessageNotice()
                schema.from_dict(data)
                messages.append(schema)
            return messages
        else:
            return await self.load_messages(user_id)

    async def load_messages(self, user_id: int):
        messages = await MessageNotice.find_all(user_id)
        for message in messages:
            data = message.to_dict()
            await self.redis.hset(
                RDS_NOTICE_MESSAGE_LIST % user_id,
                message.id,
                json.dumps(data)
            )
        return messages

    async def notice_list(self, code: ResponseEventCode, pb_data: bytes):
        from app.modules.ucoin.py_protobufs import notice_pb2
        # 解析 protobuf 消息
        message = notice_pb2.NoticeListRequest()
        message.ParseFromString(pb_data)

        user_id: int = message.userid
        page: int = message.pagenum
        page_size: int = message.pagesize

        resp_pb = notice_pb2.NoticeListResponse()

        user = await self.check_user_exists(
            user_id,
            code,
            resp_pb
        )

        # --
        start = page - 1 * page_size
        end = start + page_size - 1

        key = RDS_NOTICE_LIST % user.channel
        if not await self.redis.exists(key):
            notices = await NoticeContent.get_notices(user.channel)
            for notice in notices:
                await self.redis.lpush(key, json.dumps(notice.to_dict()))
        notice_list = await self.redis.lrange(key, start, end)
        notice_list = [json.loads(item) for item in notice_list]

        # 先导出需要返回的实际 notice
        notices_to_add = []
        for notice_item in notice_list:
            notice_info = notice_pb2.NoticeInfo()
            notice_info = convert_to_proto(notice_info, notice_item)
            notice_info.noticetype = notice_item['type']
            notice_info.expirationtime = int(get_timestamp_from_string(notice_item['expiration_time']))
            notices_to_add.append(notice_info)

        resp_pb.result = 0
        # 合并
        resp_pb.noticelist.extend(notices_to_add)
        str_data = resp_pb.SerializeToString()
        return response_binary_data(code.value, str_data)
