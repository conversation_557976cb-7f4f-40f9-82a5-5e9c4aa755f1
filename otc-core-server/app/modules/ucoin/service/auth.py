import json
from time import time

from app.modules.ucoin.service import UserService
from app.shared import *
from app.shared.libs.base_service import BaseService
from app.shared.libs.redis_key import *
from app.shared.response.error import ResponseCode, WSResponseError
from app.shared.schemas.dy_channel_info import ChannelInfo
from app.shared.schemas.dy_user_info import UserInfo
from enums.event import ResponseEventCode
from enums.user import AccountBindType


class AuthService(BaseService):

    def __init__(self):
        super().__init__()

    async def login(self, code: ResponseEventCode, pb_data: bytes):
        from app.modules.ucoin.py_protobufs import auth_pb2
        from app.modules.ucoin.service.user import UserService

        # 解析 protobuf 消息
        message = auth_pb2.LoginRequest()
        message.ParseFromString(pb_data)

        # 提取字段
        phonenum: str = message.phonenum
        password: str = message.password
        channel: str = message.channel
        bindtype: int = message.bindtype

        # --
        resp_pb = auth_pb2.LoginResponse()

        if AccountBindType(bindtype) == AccountBindType.PHONE:
            # 手机登陆
            # 根据手机号码和渠道号查找用户 ID
            user = await UserInfo.get_user_by_phone(
                phonenum,
                channel,
            )
            if user:
                await self.redis.hset(
                    RDS_USER_PHONE_LIST,
                    f'{phonenum}_{channel}',
                    json.dumps(user.to_dict()),
                )
            else:
                user = await UserInfo.get_user_by_account(phonenum, channel)
                # --
                if user:
                    await self.redis.hset(
                        RDS_USER_ACCOUNT_LIST,
                        f'{phonenum}_{channel}',
                        json.dumps(user.to_dict()),
                    )
                else:
                    user = await UserInfo.get_user_by_email(phonenum, channel)
                    # --
                    if user:
                        await self.redis.hset(
                            RDS_USER_EMAIL_LIST,
                            f'{phonenum}_{channel}',
                            json.dumps(user.to_dict()),
                        )
                    else:
                        raise WSResponseError(
                            ResponseCode.WS_PHONE_NOT_EXIST,
                            code,
                            resp_pb
                        )

            if user.password != md5_hash(password):
                raise WSResponseError(
                    ResponseCode.WS_PASS_ERR,
                    code,
                    resp_pb
                )
        else:
            # 其他登陆方式
            raise WSResponseError(
                ResponseCode.WS_NOT_SUPPORT,
                code,
                resp_pb
            )

        user_service = UserService()
        user = await user_service.get_user_from_redis(user.userid)

        # 检查有没有被封号
        if user.ban_time == '-1':
            raise WSResponseError(
                ResponseCode.WS_USER_BANNED_FOREVER,
                code,
                resp_pb
            )

        ban_time = get_timestamp_from_string(user.ban_time)
        now = int(time())
        if ban_time > now:
            raise WSResponseError(
                ResponseCode.WS_USER_BANNED_LIMITED,
                code,
                resp_pb,
                specify_msg=f'{user.ban_time}'
            )

        # 检查是否被禁止登陆
        if user.prohibit_login == 1:
            raise WSResponseError(
                ResponseCode.WS_USER_PROHIBIT_LOGIN,
                code,
                resp_pb
            )

        # 检查该渠道是否有被关闭
        channel_info = await ChannelInfo.check_channel(channel)
        if not channel_info or channel_info.is_lock:
            raise WSResponseError(
                ResponseCode.WS_CHANNEL_CLOSED,
                code,
                resp_pb
            )

        # 检查是否在线，在就踢掉原来的
        is_online = await self.redis.hexists(RDS_ONLINE_USERS, str(user.userid))
        if not is_online:
            # TODO: 踢下线
            pass

        # TODO: 看看有没有地址

        # 登录
        user.penulttime = user.lasttime
        user.lasttime = int(time())
        await user.update()

        user_pb = UserService.convert_to_pb(user)
        if user_pb:
            resp_pb.uinfo.CopyFrom(user_pb)
        else:
            raise WSResponseError(
                ResponseCode.FAIL,
                code,
                resp_pb
            )

        await self.redis.hset(RDS_ONLINE_USERS, str(user.userid), '0')

        resp_pb.result = 0
        resp_pb.systime = int(time())
        str_data = resp_pb.SerializeToString()
        return response_binary_data(code.value, str_data)

    @staticmethod
    async def heartbeat(code: ResponseEventCode):
        from app.modules.ucoin.py_protobufs import auth_pb2
        resp_pb = auth_pb2.HeartbeatResponse()
        resp_pb.result = 0
        str_data = resp_pb.SerializeToString()
        return response_binary_data(code.value, str_data)

    async def reconnect(self, code: ResponseEventCode, pb_data: bytes):
        from app.modules.ucoin.py_protobufs import auth_pb2
        from app.modules.ucoin.py_protobufs import user_pb2

        # 解析 protobuf 消息
        message = auth_pb2.ReconnectRequest()
        message.ParseFromString(pb_data)

        user_id: int = message.userid

        resp_pb = auth_pb2.ReconnectResponse()

        user = await self.check_user_exists(
            user_id,
            code,
            resp_pb
        )
        await self.redis.hset(RDS_ONLINE_USERS, str(user.userid), '0')

        user_pb = UserService.convert_to_pb(user)
        resp_pb.result = 0
        resp_pb.uinfo.CopyFrom(user_pb)
        resp_pb.systime = int(time())
        str_data = resp_pb.SerializeToString()
        return response_binary_data(code.value, str_data)
