from decimal import Decimal

from app.modules.ucoin.service.coin import CoinService
from app.shared import response_binary_data, convert_to_proto, ResponseCode
from app.shared.libs.base_service import BaseService
from app.shared.response.error import WSResponseError
from app.shared.schemas.dy_coin_info import CoinInfo
from app.shared.schemas.dy_trusteeship_wallet_order import TrusteeshipWalletOrder
from app.shared.schemas.dy_user_address_pre import UserAddressPre
from app.shared.schemas.dy_user_info import UserInfo
from enums import HangOrderType, CurrencyType
from enums.event import ResponseEventCode


class WalletService(BaseService):

    def __init__(self):
        super().__init__()

    async def withdraw(self, code: ResponseEventCode, pb_data: bytes):
        from app.modules.ucoin.py_protobufs import order_pb2
        # 解析 protobuf 消息
        message = order_pb2.WithdrawRequest()
        message.ParseFromString(pb_data)

        # 提取字段
        user_id: int = message.userid
        order_type: int = message.ordertype
        to_user_id: int = message.touserid
        fund_password: str = message.fundpwd

        resp_pb = order_pb2.WithdrawResponse()

        user = await self.check_user_exists(
            user_id,
            code,
            resp_pb
        )

        # NOTES: 傻逼逻辑先不用管
        # if order_type == 2:
        #     to_user = await self.check_user_exists(
        #         to_user_id,
        #         code,
        #         resp_pb
        #     )

        # 检查用户是否锁定
        if user.is_lock == 1:
            raise WSResponseError(
                ResponseCode.WS_ACCOUNT_LOCKED,
                code,
                resp_pb
            )

        # 检查资金密码
        if not self.verify_fund_password(user, fund_password):
            raise WSResponseError(
                ResponseCode.WS_FUND_PASS_ERR,
                code,
                resp_pb
            )

        coin_service = CoinService()

        if order_type == 1:
            sys_price = await coin_service.withdraw_coin_price(
                CurrencyType.USDT,
                True,
                user_id=user_id,
                channel=user.channel
            )
            specify_type = 2
        else:
            sys_price = await coin_service.ERC20_USDT_price(
                HangOrderType.SELL,
                CurrencyType.USDT,
                True,
                user_id=user_id,
                channel=user.channel
            )
            specify_type = 4

        withdraw_payload = {
            'to_address': message.toaddress,
            'currency_type': CurrencyType.USDT.value,
            'currency_amount': message.currencyamount,
            'subject': message.subject,
            'version': message.version,
            'notifyurl': message.notifyurl,
            'body': message.body,
            'out_trade_no': message.outtradeno,
            'specify_type': specify_type,
            'addr_type': message.addrtype,
            'price': sys_price,
            'money': float(message.currencyamount) * sys_price,
            'wallet_type': 0
        }

        print(withdraw_payload)

        """
        转账提现的逻辑
        """

        resp_pb.result = 0
        str_data = resp_pb.SerializeToString()
        return response_binary_data(code.value, str_data)

    @staticmethod
    async def check_wallet_address(code: ResponseEventCode, pb_data: bytes):
        from app.modules.ucoin.py_protobufs import order_pb2
        # 解析 protobuf 消息
        message = order_pb2.CheckWalletAddressRequest()
        message.ParseFromString(pb_data)

        # 提取字段
        adds: str = message.adds

        resp_pb = order_pb2.CheckWalletAddressResponse()

        is_valid = await UserAddressPre.check_address(adds)
        if is_valid:
            resp_pb.ret = 1
        else:
            resp_pb.ret = 0

        resp_pb.result = 0
        resp_pb.adds = adds
        str_data = resp_pb.SerializeToString()
        return response_binary_data(code.value, str_data)

    async def withdraw_fee(self, code: ResponseEventCode, pb_data: bytes):
        from app.modules.ucoin.py_protobufs import order_pb2
        # 解析 protobuf 消息
        message = order_pb2.WithdrawFeeRequest()
        message.ParseFromString(pb_data)

        # 提取字段
        user_id: int = message.userid
        chaintype: int = message.chaintype

        resp_pb = order_pb2.WithdrawFeeResponse()
        resp_pb.withdrawalfee = "0"
        resp_pb.minwithdrawalcount = "0"
        resp_pb.maxwithdrawalcount = "0"
        resp_pb.transferfee = "0"
        resp_pb.mintransfercount = "0"
        resp_pb.maxtransfercount = "0"

        user = await self.check_user_exists(
            user_id,
            code,
            resp_pb
        )
        # USDT - 2003
        coin = await CoinInfo.get_coin(2003, channel=user.channel)
        if coin:
            if chaintype == 101:
                resp_pb.withdrawalfee = str(coin.extract_currency_rate)
            else:
                resp_pb.withdrawalfee = str(coin.extract_currency_rate_trc)
            resp_pb.minwithdrawalcount = str(coin.min_extract_currency_count)
            resp_pb.maxwithdrawalcount = str(coin.max_extract_currency_count)
            resp_pb.transferfee = str(coin.transfer_curreny_rate)
            resp_pb.mintransfercount = str(coin.min_transfer_curreny_count)
            resp_pb.maxtransfercount = str(coin.max_transfer_curreny_count)

        resp_pb.result = 0
        str_data = resp_pb.SerializeToString()
        return response_binary_data(code.value, str_data)

    async def wallet_transfer_in_records(self, code: ResponseEventCode, pb_data: bytes):
        from app.modules.ucoin.py_protobufs import order_pb2
        # 解析 protobuf 消息
        message = order_pb2.WalletTransInRequest()
        message.ParseFromString(pb_data)

        # 提取字段
        user_id: int = message.userid
        page: int = message.pagenum
        page_size: int = message.pagesize
        order_type: int = message.ordertype

        resp_pb = order_pb2.WalletTransInResponse()

        await self.check_user_exists(
            user_id,
            code,
            resp_pb
        )

        offset = (page - 1) * page_size

        wallet_order = await TrusteeshipWalletOrder.get_orders(
            user_id,
            page_size,
            offset,
        )

        need_to_add = []
        for order in wallet_order:
            order_pb = order_pb2.WalletOrder()
            order_pb = convert_to_proto(order_pb, order)
            order_pb.actualamount = order.actual_amount
            order_pb.usertxid = order.user_txid
            order_pb.create = order.create.strftime('%Y-%m-%d %H:%M:%S')
            order_pb.coinid = order.coin_id
            order_pb.addrtype = order.addr_type
            order_pb.coinname = order.coin_name
            order_pb.addrname = order.addr_name
            need_to_add.append(order_pb)

        # 合并
        resp_pb.result = 0
        resp_pb.infolist.extend(need_to_add)
        resp_pb.pagenum = page
        resp_pb.pagesize = page_size
        resp_pb.ordertype = order_type
        str_data = resp_pb.SerializeToString()
        return response_binary_data(code.value, str_data)

    async def withdraw_foundation(
            self,
            user: UserInfo,
            payload: dict,

    ):
        pass
