from enums.event import RequestEventCode, ResponseEventCode
from app.modules.ucoin.service.user import UserService
from app.modules.ucoin.service.wallet import WalletService
from app.modules.ucoin.service.auth import AuthService
from app.modules.ucoin.service.notice import NoticeService
from app.modules.ucoin.service.trade import TradeService

auth_service = AuthService()
notice_service = NoticeService()
trade_service = TradeService()
user_service = UserService()
wallet_service = WalletService()


async def foundation(code: RequestEventCode, pb_data: bytes):
    if code == RequestEventCode.LOGIN:
        """h5 app 登录"""
        return await auth_service.login(ResponseEventCode.LOGIN, pb_data)
    elif code == RequestEventCode.ADD_PAYMENT:
        """添加用户支付方式"""
        return await user_service.add_payment(ResponseEventCode.ADD_PAYMENT, pb_data)
    elif code == RequestEventCode.PAYMENTS:
        """用户支付方式"""
        return await user_service.payments(ResponseEventCode.PAYMENTS, pb_data)
    elif code == RequestEventCode.HEARTBEAT:
        """心跳"""
        return await auth_service.heartbeat(ResponseEventCode.HEARTBEAT)
    elif code == RequestEventCode.RECONNECT:
        """登录重连"""
        return await auth_service.reconnect(ResponseEventCode.RECONNECT, pb_data)
    elif code == RequestEventCode.NOTICES:
        """公告列表"""
        return await notice_service.notice_list(ResponseEventCode.NOTICES, pb_data)
    elif code == RequestEventCode.MESSAGE_LIST:
        """公告列表"""
        return await notice_service.message_list(ResponseEventCode.MESSAGE_LIST, pb_data)
    elif code == RequestEventCode.HANG_ORDER:
        """挂单"""
        return await trade_service.hang_order(ResponseEventCode.HANG_ORDER, pb_data)
    elif code == RequestEventCode.MY_HANG_ORDER:
        """我的挂单"""
        return await trade_service.my_hang_order(ResponseEventCode.MY_HANG_ORDER, pb_data)
    elif code == RequestEventCode.DEAL:
        """买币"""
        return await trade_service.deal(ResponseEventCode.DEAL, pb_data)
    elif code == RequestEventCode.TODAY_FLOW:
        """今日流水"""
        return await trade_service.today_flow(ResponseEventCode.TODAY_FLOW, pb_data)
    elif code == RequestEventCode.TRADE_RECORDS:
        """交易记录"""
        return await trade_service.trade_records(ResponseEventCode.TRADE_RECORDS, pb_data)
    elif code == RequestEventCode.PENDING_ORDERS:
        """待处理订单"""
        return await trade_service.pending_orders(ResponseEventCode.PENDING_ORDERS, pb_data)
    elif code == RequestEventCode.TRADING_ORDER_LIST:
        """活跃订单列表"""
        return await trade_service.trading_order_list(ResponseEventCode.TRADING_ORDER_LIST, pb_data)
    elif code == RequestEventCode.WITHDRAW:
        """提现"""
        return await wallet_service.withdraw(ResponseEventCode.WITHDRAW, pb_data)
    elif code == RequestEventCode.WITHDRAW_RECORDS:
        """提现订单列表"""
        return await trade_service.withdraw_records(ResponseEventCode.WITHDRAW_RECORDS, pb_data)
    elif code == RequestEventCode.EXCHANGE_RATE:
        """查询汇率"""
        return await trade_service.exchange_rate(ResponseEventCode.EXCHANGE_RATE, pb_data)
    elif code == RequestEventCode.WALLET_TRANS_IN_RECORDS:
        """托管钱包充值记录"""
        return await wallet_service.wallet_transfer_in_records(
            ResponseEventCode.WALLET_TRANS_IN_RECORDS,
            pb_data
        )
    elif code == RequestEventCode.CHECK_ADDRESS:
        """提币转账手续"""
        return await wallet_service.check_wallet_address(
            ResponseEventCode.CHECK_ADDRESS,
            pb_data
        )
    elif code == RequestEventCode.WITHDRAW_FEE:
        """提币转账手续"""
        return await wallet_service.withdraw_fee(
            ResponseEventCode.WITHDRAW_FEE,
            pb_data
        )
    elif code == RequestEventCode.INCOME_AND_EXPENDITURE:
        """收支明细"""
        return await user_service.income_and_expenditure(
            ResponseEventCode.INCOME_AND_EXPENDITURE,
            pb_data
        )
