import logging
from app.shared import ResponseError, ResponseCode
from app.shared.libs.base_service import BaseService
from app.shared.schemas.dy_coin_info import CoinInfo
from enums import HangOrderType, CurrencyType, TradeType


class CoinService(BaseService):

    def __init__(self):
        super().__init__()

    @staticmethod
    def trade_type_conversion(
            trade_type: TradeType,
            customer_user_id: int,
            user_id: int,
    ):
        if user_id == customer_user_id:
            # 用户是顾客
            if trade_type == TradeType.BUY:
                # 买
                return TradeType.BUY
            else:
                # 卖
                return TradeType.SELL
        else:
            # 用户是币商
            if trade_type == TradeType.BUY:
                # 卖
                return TradeType.SELL
            else:
                # 买
                return TradeType.BUY

    @staticmethod
    def calc_price(coin: CoinInfo, deal_type: HangOrderType):
        if deal_type == HangOrderType.BUY:
            # 买价
            # 是否固定购买汇率 0 否 1是
            if coin.is_fixed_buy_rate == 1:
                return float(coin.fixed_buy_rate)
            else:
                return float(coin.buy_price) + float(coin.add_buy_price)
        elif deal_type == HangOrderType.SELL:
            # 卖价
            # 是否固定出售汇率 0 否 ， 1是
            if coin.is_fixed_sell_rate == 1:
                return float(coin.fixed_sell_rate)
            else:
                return float(coin.sell_price) + float(coin.minus_sell_price)
        else:
            # 币支付
            # 是否固定币支付汇率 0 否 ， 1是
            if coin.is_fixed_coin_pay_rate == 1:
                return float(coin.fixed_coin_pay_rate)
            else:
                return float(coin.free_price) + float(coin.minus_coin_pay_price)

    @staticmethod
    async def withdraw_coin_price(
            currency_type: CurrencyType,
            is_user_price: bool,
            user_id: int,
            channel: str,
    ):
        if is_user_price:
            coin = await CoinInfo.get_coin(
                currency_type.value,
                user_id=user_id
            )
            if coin:
                if coin.is_withdraw_coin_pay_rate == 1:
                    return coin.withdraw_coin_rate
                else:
                    return coin.buy_price + coin.withdraw_coin_price_diff
            else:
                logging.error('未设置币价')

        coin = await CoinInfo.get_coin(
            currency_type.value,
            channel=channel
        )
        if coin:
            if coin.is_withdraw_coin_pay_rate == 1:
                return coin.withdraw_coin_rate
            else:
                return coin.buy_price + coin.withdraw_coin_price_diff
        else:
            logging.error('未设置币价')

    async def ERC20_USDT_price(
            self,
            deal_type: HangOrderType,
            currency_type: CurrencyType,
            is_user_price: bool,
            user_id: int,
            channel: str,
    ):
        """
        获取erc20 中usdt对比人民币的汇价
        :param user_id:
        :param channel:
        :param deal_type:
        :param currency_type:
        :param is_user_price:
        :return:
        """
        if is_user_price:
            coin = await CoinInfo.get_coin(
                currency_type.value,
                user_id=user_id
            )
            if coin:
                return self.calc_price(coin, deal_type)
        # 如果没有特别设置的用户汇率，则使用渠道提供的汇率
        coin = await CoinInfo.get_coin(
            currency_type.value,
            channel=channel
        )
        if coin:
            return self.calc_price(coin, deal_type)

        # 如果都为空，数据表没有该用户适用的汇率
        logging.error(ResponseError(ResponseCode.UCOIN_H5_EXCHANGE_RATE_NOT_SETTING))
