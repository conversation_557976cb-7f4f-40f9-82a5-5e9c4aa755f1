import json
import logging
from datetime import datetime, timedelta
from decimal import Decimal

from app.shared.response.error import WSResponseError
from app.shared.schemas.dy_user_rate import UserRate
from app.shared.schemas.log_fund_details import LogFundDetails
from enums import FeeRateMode
from enums.event import ResponseEventCode
from app.shared import convert_to_proto, response_binary_data, ResponseCode, decimal_to_str
from app.shared.libs.base_service import BaseService
from app.shared.libs.redis_key import *
from app.shared.schemas.dy_user_info import UserInfo
from app.shared.schemas.dy_user_pay import UserPay
from quart import websocket


class UserService(BaseService):

    def __init__(self):
        super().__init__()

    async def income_and_expenditure(self, code: ResponseEventCode, pb_data: bytes):
        from app.modules.ucoin.py_protobufs import user_pb2
        # 解析 protobuf 消息
        message = user_pb2.IncomeExpenditureRequest()
        message.ParseFromString(pb_data)

        # 提取字段
        user_id: int = message.userid
        detail_type: int = message.detailtype
        page: int = message.pagenum
        page_size: int = message.pagesize

        resp_pb = user_pb2.IncomeExpenditureResponse()

        await self.check_user_exists(
            user_id,
            code,
            resp_pb
        )

        # 近 14 天
        # 获取当前日期时间
        today = datetime.today()
        # 计算 14天 前的日期时间
        start_time = (today - timedelta(days=14))
        offset = (page - 1) * page_size

        if detail_type == 0:
            # 获取近 14 日统计
            order_1_amount = await LogFundDetails.sum_order_type(
                user_id,
                order_type=1,
                start_time=start_time,
                end_time=today,
            )
            if order_1_amount:
                resp_pb.parameter1 = decimal_to_str(order_1_amount)
            # --
            order_2_amount = await LogFundDetails.sum_order_type(
                user_id,
                order_type=2,
                start_time=start_time,
                end_time=today,
            )
            if order_2_amount:
                resp_pb.parameter2 = decimal_to_str(order_2_amount)

            # 获取近 14 日明细
            stats = await LogFundDetails.stats_date(
                user_id,
                start_time,
                today,
                page_size,
                offset
            )
        else:
            order_amount = await LogFundDetails.sum_order_type(
                user_id,
                order_type=detail_type,
                start_time=start_time,
                end_time=today,
            )
            if order_amount:
                resp_pb.parameter1 = decimal_to_str(order_amount)

            # 获取近 14 日明细
            stats = await LogFundDetails.stats_date(
                user_id,
                start_time,
                today,
                page_size,
                offset,
                order_type=detail_type
            )

        need_to_add = []
        for stats_item in stats:
            stats_pb = user_pb2.IncomeExpenditure()
            stats_pb.detailtype = stats_item.order_type
            stats_pb.amount = decimal_to_str(stats_item.amount)
            stats_pb.afteramount = decimal_to_str(stats_item.after_amount)
            stats_pb.createtime = stats_item.createdate.strftime('%Y-%m-%d %H:%M:%S')
            # --
            need_to_add.append(stats_pb)

        # 合并
        resp_pb.result = 0
        resp_pb.detaillist.extend(need_to_add)
        resp_pb.detailtype = message.detailtype
        resp_pb.pagenum = message.pagenum
        resp_pb.pagesize = message.pagesize
        str_data = resp_pb.SerializeToString()
        return response_binary_data(code.value, str_data)

    async def add_payment(self, code: ResponseEventCode, pb_data: bytes):
        from app.modules.ucoin.py_protobufs import user_pb2
        # 解析 protobuf 消息
        message = user_pb2.AddPaymentRequest()
        message.ParseFromString(pb_data)

        # 提取字段
        user_id: int = message.userid
        payee: str = message.payee
        account: str = message.account
        bank_name: str = message.bankname
        bank_addr: str = message.bankaddr
        qr_code: str = message.qrcode
        single_limit: float = message.singlelimit
        day_limit: float = message.daylimit
        pay_type: int = message.paytype
        # -- 源代码那沙雕写了个 newpwd 的字段，客户端没有传，proto 也没有定义
        # -- 暂时不用管这傻逼代码

        resp_pb = user_pb2.AddPaymentResponse()

        user = await self.check_user_exists(
            user_id,
            code,
            resp_pb
        )

        # 检查玩家是否存在该种类型的支付方式
        if pay_type not in user.paytypelist:
            raise WSResponseError(
                ResponseCode.WS_USER_NOT_EXIST,
                code,
                resp_pb
            )
        # 查看是否已经绑定该信息
        if await UserPay.check_exist(
                account,
                user_id,
                pay_type,
        ):
            raise WSResponseError(
                ResponseCode.WS_PAYINFO_EXIST,
                code,
                resp_pb
            )

        new_payment = UserPay()
        new_payment.pay_id = pay_type
        new_payment.name = payee
        new_payment.account = account
        new_payment.bank_name = bank_name
        new_payment.bank_addr = bank_addr
        new_payment.qr_code = qr_code
        new_payment.single_limit = single_limit
        new_payment.day_limit = day_limit
        new_payment.userid = user_id
        new_payment.status = 1
        new_payment.channel = user.channel
        new_payment.platform_id = user.platform_id
        try:
            await new_payment.insert()
        except BaseException as err:
            logging.error(err)
            raise WSResponseError(
                ResponseCode.WS_ADD_ERR_1,
                code,
                resp_pb
            )
        await self.add_user_payment(new_payment)

        resp_pb.result = 0
        str_data = resp_pb.SerializeToString()
        return response_binary_data(code.value, str_data)

    async def payments(self, code: ResponseEventCode, pb_data: bytes):
        from app.modules.ucoin.py_protobufs import user_pb2
        # 解析 protobuf 消息
        message = user_pb2.PaymentsRequest()
        message.ParseFromString(pb_data)

        # 提取字段
        user_id: int = message.userid

        resp_pb = user_pb2.PaymentsResponse()

        user = await self.check_user_exists(
            user_id,
            code,
            resp_pb
        )

        # 先导出需要返回
        need_to_add = []
        for payment_id in user.paytypelist:
            payment_list = await self.get_user_payment(user.userid, payment_id)
            for _, v in payment_list.items():
                data = json.loads(v)
                payment_pb = user_pb2.Payment()
                payment_pb = convert_to_proto(payment_pb, data)
                payment_pb.paytype = data['pay_id']
                payment_pb.payee = data['name']
                payment_pb.bankname = data['bank_name']
                payment_pb.bankaddr = data['bank_addr']
                payment_pb.qrcode = data['qr_code']
                payment_pb.singlelimit = str(data['single_limit'])
                payment_pb.daylimit = str(data['day_limit'])
                payment_pb.fourthpartyid = int(data['fourthpartyId']) or 0
                payment_pb.deallasttime = data['deal_last_time'] or datetime.now().strftime(
                    '%Y-%m-%d %H:%M:%S'
                )
                payment_pb.todaymoney = str(await self.get_day_limit(payment_id))
                need_to_add.append(payment_pb)

        resp_pb.result = 0
        # 合并
        resp_pb.paylist.extend(need_to_add)
        str_data = resp_pb.SerializeToString()
        return response_binary_data(code.value, str_data)

    async def check_payment_available(self, user: UserInfo):
        """
        检查是否存在可用的支付方式
        :param user:
        :return:
        """
        for pay_type in user.paytypelist:
            payment = await self.get_user_payment(user.userid, pay_type)
            if payment:
                for _, v in payment.items():
                    data = json.loads(v)
                    if data['status'] == 1:
                        return True
        return False

    async def get_user_payment(self, user_id: int, pay_type: int):
        key = RDS_USER_PAYMENT % (user_id, pay_type)
        if await self.redis.exists(key):
            return await self.redis.hgetall(key)

        payments = await UserPay.load(user_id, pay_type)
        for payment in payments:
            await self.add_user_payment(payment)

        return await self.redis.hgetall(key)

    async def add_user_payment(self, payment: UserPay):
        key = RDS_USER_PAYMENT % (payment.userid, payment.pay_id)
        await self.redis.hset(
            key,
            str(payment.pay_id),
            json.dumps(payment.to_dict())
        )

    async def get_day_limit(self, pay_id: int):
        today = datetime.today().strftime('%Y-%m-%d')
        user_day_limit = await self.redis.hget(RDS_USER_DAY_LIMIT, pay_id)
        # --
        day_limit = 0
        if user_day_limit:
            data = json.loads(user_day_limit)
            if data['date'] == today:
                day_limit = data['num'] or 0

        return day_limit

    async def set_user(self, user: UserInfo):
        await self.redis.set(
            RDS_USER % user.userid,
            json.dumps(user.to_dict())
        )

    @staticmethod
    async def send_auto_sell(user: UserInfo):
        from app.modules.ucoin.py_protobufs import user_pb2
        update_pb = user_pb2.UpdateUserInfoResponse()
        update_pb.typelist = ["autosell"]
        update_pb.valuelist = [str(user.autosell)]
        str_data = update_pb.SerializeToString()
        resp_pb = response_binary_data(ResponseEventCode.UPDATE_USER_INFO.value, str_data)
        await websocket.send(resp_pb)

    async def check_online_user(self, user_id: int):
        return await self.redis.hexists(RDS_ONLINE_USERS, str(user_id))

    async def get_user_fee_rate(
            self,
            user_id: int,
            pay_id: int,
            rate_type: FeeRateMode
    ):
        key = f'{user_id}_{pay_id}_{rate_type.value}'
        if await self.redis.hexists(
                RDS_USER_FEE_RATE,
                key
        ):
            rate = await self.redis.hget(
                RDS_USER_FEE_RATE,
                key
            )
            if rate:
                return float(rate)
            else:
                return 0

        return await self.load_user_fee_rate(
            user_id, pay_id, rate_type
        )

    async def load_user_fee_rate(
            self,
            user_id: int,
            pay_id: int,
            rate_type: FeeRateMode
    ):
        user_rate = await UserRate.find_mode(
            user_id,
            str(pay_id),
        )
        rate = 0.
        if user_rate:
            if rate_type == FeeRateMode.BUY_FEE_RATE:
                rate = user_rate.buy_fee_rate
            elif rate_type == FeeRateMode.SELL_FEE_RATE:
                rate = user_rate.sell_fee_rate
            elif rate_type == FeeRateMode.BUY_COMM_RATE:
                rate = user_rate.buy_comm_rate
            elif rate_type == FeeRateMode.SELL_COMM_RATE:
                rate = user_rate.sell_comm_rate
            elif rate_type == FeeRateMode.BEHALF_SELL_FEE_RATE:
                rate = user_rate.behalf_sell_fee_rate
            else:
                rate = user_rate.behalf_buy_comm_rate

        await self.set_user_fee_rate(
            user_id, pay_id, rate_type, rate
        )
        return rate

    async def set_user_fee_rate(
            self,
            user_id: int,
            pay_id: int,
            rate_type: FeeRateMode,
            rate: float
    ):
        key = f'{user_id}_{pay_id}_{rate_type.value}'
        await self.redis.hset(
            RDS_USER_FEE_RATE,
            key,
            rate
        )

    @staticmethod
    def convert_to_pb(user: UserInfo):
        from app.modules.ucoin.py_protobufs import user_pb2
        user_pb = user_pb2.UserInfo()
        user_pb = convert_to_proto(user_pb, user)
        # --
        user_pb.password = ''
        # -- 转义字段
        user_pb.ethaddress = user.eth_address
        user_pb.face1 = user.face_1
        user_pb.ethamount = decimal_to_str(user.eth_amount)
        user_pb.ercusdtamount = decimal_to_str(user.erc_usdt_amount)
        user_pb.ercusdtlockamount = decimal_to_str(user.erc_usdt_lock_amount)
        user_pb.invitecode = user.invite_code
        user_pb.isfundpassword = len(user.fundpassword) > 0
        user_pb.paytypelist.extend(user.paytypelist)
        user_pb.usertype = user.user_type
        user_pb.autosell = int(user.autosell)
        user_pb.minbuy = decimal_to_str(user.min_buy)
        user_pb.maxbuy = decimal_to_str(user.max_buy)
        user_pb.minsell = decimal_to_str(user.min_sell)
        user_pb.maxsell = decimal_to_str(user.max_sell)
        user_pb.islock = user.is_lock
        user_pb.ishangbuy = user.is_hang_buy
        user_pb.ishangsell = user.is_hang_sell
        user_pb.commtype = user.comm_type
        user_pb.prohibitlogin = user.prohibit_login
        user_pb.isacceptorder = user.is_accept_order
        if isinstance(user.deal_last_tiime, str):
            user_pb.deallasttime = user.deal_last_tiime
        elif isinstance(user.deal_last_tiime, datetime):
            user_pb.deallasttime = user.deal_last_tiime.strftime("%Y-%m-%d %H:%M:%S")
        user_pb.bantime = user.ban_time
        user_pb.isinvited = user.is_invited
        user_pb.isotc = user.is_otc
        user_pb.isbehalfpay = user.is_behalf_pay
        user_pb.ercfcamount = decimal_to_str(user.erc_fc_amount)
        user_pb.ercfclockamount = decimal_to_str(user.erc_fc_lock_amount)
        user_pb.dealcointype = user.deal_coin_type
        user_pb.energyvalue = decimal_to_str(user.energy_value)
        user_pb.weightsvalue = decimal_to_str(user.weights_value)
        user_pb.teamname = user.team_name
        user_pb.platformid = user.platform_id
        user_pb.isteamacceptorder = user.is_team_accept_order
        user_pb.preauthorization = str(user.pre_authorization)
        user_pb.coinpayrate = decimal_to_str(user.coin_pay_rate)
        user_pb.coinpayerc = user.coin_pay_erc
        user_pb.coinpaytrc = user.coin_pay_trc
        user_pb.allowsysaddr = user.allow_sys_addr
        user_pb.sysercmin = decimal_to_str(user.sys_erc_min)
        user_pb.coinpayusdtamount = decimal_to_str(user.coin_pay_usdt_amount)
        user_pb.coinpayusdtlockamount = decimal_to_str(user.coin_pay_usdt_lock_amount)
        user_pb.ischeck = user.is_check
        user_pb.extractcurrencyrateerc = decimal_to_str(user.extract_currency_rate_erc)
        user_pb.extractcurrencyratetrc = decimal_to_str(user.extract_currency_rate_trc)

        return user_pb

    async def get_user_from_redis(self, user_id: int):
        key = RDS_USER % user_id
        data_str = await self.redis.get(key)
        if data_str:
            data = json.loads(data_str)
            user = UserInfo()
            user.from_dict(data)
            # --
            user.erc_usdt_amount = Decimal(
                await self.redis.hget(
                    RDS_USER_USDT_AMOUNT,
                    str(user_id)
                )
            )
            user.erc_usdt_lock_amount = Decimal(
                await self.redis.hget(
                    RDS_USER_USDT_LOCK_AMOUNT,
                    str(user_id)
                )
            )
            user.erc_fc_amount = Decimal(
                await self.redis.hget(
                    RDS_USER_FC_AMOUNT,
                    str(user_id)
                )
            )
            user.erc_fc_lock_amount = Decimal(
                await self.redis.hget(
                    RDS_USER_FC_LOCK_AMOUNT,
                    str(user_id)
                )
            )
            user.coin_pay_usdt_amount = Decimal(
                await self.redis.hget(
                    RDS_USER_COIN_PAY_USDT_AMOUNT,
                    str(user_id)
                )
            )
            user.coin_pay_usdt_lock_amount = Decimal(
                await self.redis.hget(
                    RDS_USER_COIN_PAY_USDT_LOCK_AMOUNT,
                    str(user_id)
                )
            )
        else:
            user = await UserInfo.get_user_info_by_id(user_id)

        return user
