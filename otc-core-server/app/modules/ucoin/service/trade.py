import json
import logging
from datetime import datetime
from decimal import Decimal, ROUND_FLOOR, ROUND_HALF_UP
from quart import websocket
from app.shared.schemas.dy_block_chain_trans import BlockChainTrans
from app.shared.schemas.dy_coin_info import CoinInfo
from app.shared.schemas.dy_user_conf import UserConf
from app.shared.schemas.log_user import LogUser
from enums import *
from enums.event import ResponseEventCode
from app.modules.ucoin.service import UserService
from app.modules.ucoin.service.coin import CoinService
from app.shared import response_binary_data, convert_to_proto, resolve_user_trade_type_within_order, ResponseCode
from app.shared.libs.redis_key import *
from app.shared.response.error import WSResponseError
from app.shared.schemas.dy_channel_info import ChannelInfo
from app.shared.schemas.dy_customer_order import CustomerOrder
from app.shared.schemas.dy_user_info import UserInfo
from app.shared.schemas.dy_vendor_order import VendorOrder
from app.shared.schemas.log_user_daily import LogUserDaily
from app.shared.libs.base_service import BaseService


class TradeService(BaseService):

    def __init__(self):
        super().__init__()

    async def deal(self, code: ResponseEventCode, pb_data: bytes):
        from app.modules.ucoin.py_protobufs import order_pb2
        from app.modules.ucoin.py_protobufs import trade_pb2
        # 解析 protobuf 消息
        message = trade_pb2.DealRequest()
        message.ParseFromString(pb_data)

        # 提取字段
        user_id: int = message.userid
        hang_order_id: int = message.hangid
        fund_password: str = message.fundpwd
        # 交易类型，0是买，1是卖
        deal_type: int = message.dealtype
        # 交易数量
        amount = Decimal(message.amount)
        # 钱的类型 0-币 1-人民币
        money_type = CurrencyType(message.moneytype)
        # 支付方式
        pay_type: int = message.paytype
        # 币种ID
        coin_id: int = message.cionid

        resp_pb = trade_pb2.DealResponse()

        user = await self.check_user_exists(
            user_id,
            code,
            resp_pb
        )

        # 检查资金密码
        if not self.verify_fund_password(user, fund_password):
            raise WSResponseError(
                ResponseCode.WS_FUND_PASS_ERR,
                code,
                resp_pb
            )
        # 检查用户是否锁定
        if user.is_lock == 1:
            raise WSResponseError(
                ResponseCode.WS_ACCOUNT_LOCKED,
                code,
                resp_pb
            )

        # 查询还有多少订单正在进行中
        if not await CustomerOrder.check_processing(user_id):
            raise WSResponseError(
                ResponseCode.WS_NO_PAY_ORDER_LIMIT,
                code,
                resp_pb
            )

        await self.lock_order(hang_order_id)
        vendor_order = await self.get_vendor_order_by_id(hang_order_id)
        if not vendor_order:
            await self.unlock_order(hang_order_id)
            raise WSResponseError(
                ResponseCode.WS_HANG_ORDER_NOT_EXISTS,
                code,
                resp_pb
            )

        if vendor_order.is_take == 1:
            await self.unlock_order(hang_order_id)
            raise WSResponseError(
                ResponseCode.WS_HANG_ORDER_EXPIRED,
                code,
                resp_pb
            )

        # 检查渠道是否公开
        channel_info = await ChannelInfo.find(user.channel)
        if channel_info:
            channel_deal = channel_info.channel_deal
        else:
            channel_deal = 1

        if channel_deal == 0:
            # 公开
            # 检查这个挂单是公开还是私有的
            if vendor_order.channel_deal == 1:
                await self.unlock_order(hang_order_id)
                raise WSResponseError(
                    ResponseCode.WS_HANG_ORDER_PRIVATE,
                    code,
                    resp_pb
                )
        else:
            is_ok = False
            if vendor_order.user_type == 300:
                is_ok = await UserConf.check_user_belong(
                    user_id,
                    user.channel,
                    vendor_order.withdraw_type
                )
            if vendor_order.channel != user.channel and not is_ok:
                await self.unlock_order(hang_order_id)
                raise WSResponseError(
                    ResponseCode.WS_HANG_ORDER_NOT_BELONG_YOUR_CHANNEL,
                    code,
                    resp_pb
                )

        # 该订单还不能购买
        if vendor_order.rest_time > datetime.now():
            await self.unlock_order(hang_order_id)
            raise WSResponseError(
                ResponseCode.WS_HANG_ORDER_TRADE_NOT_YET,
                code,
                resp_pb
            )

        user_service = UserService()
        coin_service = CoinService()

        order_user = await user_service.get_user_from_redis(vendor_order.userid)
        if not order_user:
            raise WSResponseError(
                ResponseCode.CUSTOM,
                code,
                resp_pb,
                specify_msg=f'挂单的卖家不存在'
            )
        # 检查订单价格
        if vendor_order.price_type == 0:
            price = vendor_order.price
        else:
            price = await coin_service.ERC20_USDT_price(
                HangOrderType(vendor_order.type),
                CurrencyType.USDT,
                True,
                user_id=order_user.userid,
                channel=order_user.channel
            )
            price = Decimal(price)

        if money_type == FiatCurrencyType.RMB:
            currency_count = (amount * price * 10000000000).to_integral_value(rounding=ROUND_FLOOR)
            currency_count = currency_count / 10000000000
            money_count = amount
        else:
            currency_count = amount
            money_count = amount * price
            if vendor_order.deal_mode == 1:
                currency_count = vendor_order.amount
                money_count = vendor_order.predict_money

        # 商户提现的下单
        if vendor_order.type == 1 and vendor_order.user_type == 300 and vendor_order.deal_mode == 0:
            remain_money = vendor_order.predict_money - vendor_order.unsold_order_money - vendor_order.deal_order_money
            remain_money = remain_money.quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
            money_count = money_count.quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
            if remain_money < money_count:
                await self.unlock_order(hang_order_id)
                raise WSResponseError(
                    ResponseCode.WS_HANG_ORDER_MONEY_NOT_ENOUGH,
                    code,
                    resp_pb
                )

            diff_money = remain_money - money_count
            diff_money = diff_money.quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
            if 0 < diff_money < vendor_order.min_money:
                await self.unlock_order(hang_order_id)
                raise WSResponseError(
                    ResponseCode.CUSTOM,
                    code,
                    resp_pb,
                    specify_msg=f'下单金额需为 {remain_money}'
                )
        # -- 下单
        await self.deal_order(
            user,
            order_user,
            vendor_order,
            deal_type,
            currency_count,
            money_count,
            price,
            pay_type,
            code,
            resp_pb
        )

        await self.unlock_order(hang_order_id)
        # 给前端转化，前端只认对于这个这个用户说是买还是卖

    async def deal_order(
            self,
            user: UserInfo,
            hang_user: UserInfo,
            vendor_order: VendorOrder,
            deal_type: int,
            currency_count: Decimal,
            money_count: Decimal,
            price: Decimal,
            pay_type: int,
            # --
            code: ResponseEventCode,
            resp_pb,
    ):
        # 自己的单
        if user.userid == vendor_order.userid:
            raise WSResponseError(
                ResponseCode.CUSTOM,
                code,
                resp_pb,
                specify_msg=f'不能购买或出售自己的订单'
            )
        # 检查建议类型匹不匹配
        if deal_type != (vendor_order.type + 1) % 2:
            raise WSResponseError(
                ResponseCode.CUSTOM,
                code,
                resp_pb,
                specify_msg=f'交易类型不一致，请选择正确的交易类型'
            )
        # 挂单的状态不对
        if vendor_order.enable_status != 1:
            raise WSResponseError(
                ResponseCode.CUSTOM,
                code,
                resp_pb,
                specify_msg=f'挂单已经失效，请选择其他挂单'
            )
        # 检查是否在限额之内
        if money_count < vendor_order.min_money or money_count > vendor_order.max_money:
            raise WSResponseError(
                ResponseCode.CUSTOM,
                code,
                resp_pb,
                specify_msg=f'输入数量请在限额之内'
            )

        channels = await ChannelInfo.find_all()
        channel_map = {}
        for channel_info in channels:
            channel_map[channel_info.channel] = {
                'isExternal': channel_info.is_external
            }

        """
        TODO 好多逻辑待补充
        """

    async def quick_deal(self, code: ResponseEventCode, pb_data: bytes):
        from app.modules.ucoin.py_protobufs import order_pb2
        # 解析 protobuf 消息
        message = order_pb2.QuickDealRequest()
        message.ParseFromString(pb_data)

        # 提取字段
        user_id: int = message.userid
        fund_password: str = message.ordertype
        page: int = message.pagenum
        page_size: int = message.pagesize

        offset = (page - 1) * page_size

        resp_pb = order_pb2.QuickDealResponse()

        user = await self.check_user_exists(
            user_id,
            code,
            resp_pb
        )
        user_service = UserService()

        # 检查资金密码
        if not self.verify_fund_password(user, fund_password):
            raise WSResponseError(
                ResponseCode.WS_FUND_PASS_ERR,
                code,
                resp_pb
            )

        # 检查用户是否锁定
        if user.is_lock == 1:
            raise WSResponseError(
                ResponseCode.WS_ACCOUNT_LOCKED,
                code,
                resp_pb
            )

        # TODO 明天做

        # 合并
        resp_pb.result = 0
        str_data = resp_pb.SerializeToString()
        return response_binary_data(code.value, str_data)

    async def withdraw_records(self, code: ResponseEventCode, pb_data: bytes):
        from app.modules.ucoin.py_protobufs import order_pb2
        # 解析 protobuf 消息
        message = order_pb2.WithdrawRecordsRequest()
        message.ParseFromString(pb_data)

        # 提取字段
        user_id: int = message.userid
        order_type = CoinOrderType(message.ordertype)
        page: int = message.pagenum
        page_size: int = message.pagesize

        offset = (page - 1) * page_size

        resp_pb = order_pb2.WithdrawRecordsResponse()

        await self.check_user_exists(
            user_id,
            code,
            resp_pb
        )

        records = await BlockChainTrans.find_all(
            user_id, order_type, page_size, offset
        )

        need_to_add = []
        for record in records:
            record_pb = order_pb2.CryptoRecord()
            record_pb = convert_to_proto(record_pb, record)
            # --
            record_pb.txid = record.tx_id
            record_pb.txtype = record.tx_type
            record_pb.chainid = record.chain_id
            record_pb.txdata = record.tx_data
            record_pb.fromaddr = record.from_addr
            record_pb.toaddr = record.to_addr
            record_pb.txfee = record.tx_fee
            record_pb.txstatus = record.tx_status
            record_pb.txtime = record.tx_time
            record_pb.blockhash = record.block_hash
            record_pb.recdstatus = record.recd_status
            record_pb.createtime = record.create_time.strftime("%Y-%m-%d %H:%M:%S")
            record_pb.specifictype = record.specific_type
            record_pb.coinid = record.coin_id
            record_pb.addrtype = record.addr_type
            record_pb.coinname = record.coin_name
            record_pb.addrname = record.addr_name
            # --
            need_to_add.append(record_pb)

        # 合并
        resp_pb.result = 0
        resp_pb.recordinfo.extend(need_to_add)
        resp_pb.ordertype = message.ordertype
        resp_pb.pagenum = message.pagenum
        resp_pb.pagesize = message.pagesize
        str_data = resp_pb.SerializeToString()
        return response_binary_data(code.value, str_data)

    async def trading_order_list(self, code: ResponseEventCode, pb_data: bytes):
        from app.modules.ucoin.py_protobufs import order_pb2
        # 解析 protobuf 消息
        message = order_pb2.CustomerOrdersRequest()
        message.ParseFromString(pb_data)

        # 提取字段
        user_id: int = message.userid
        page: int = message.pagenum
        page_size: int = message.pagesize
        # 用户购买就是查卖的单
        if TradeType(message.ordertype) == TradeType.BUY:
            trade_type = TradeType.SELL
        else:
            trade_type = TradeType.BUY
        # trade_type: TradeType = TradeType(message.ordertype)

        resp_pb = order_pb2.CustomerOrdersResponse()

        user = await self.check_user_exists(
            user_id,
            code,
            resp_pb
        )
        # 先把所有的渠道跟价格查出来
        price_within_channels = await CoinInfo.find_price_within_channels()
        price_map = {}
        for row in price_within_channels:
            price_map[row.channel] = {}
            if row.is_fixed_buy_rate == 1:
                price_map[row.channel][HangOrderType.BUY] = float(row.fixed_buy_rate)
            else:
                price_map[row.channel][HangOrderType.BUY] = float(row.buy_price) + float(row.add_buy_price)
            if row.is_fixed_sell_rate == 1:
                price_map[row.channel][HangOrderType.SELL] = float(row.fixed_sell_rate)
            else:
                price_map[row.channel][HangOrderType.SELL] = float(row.free_price) + float(row.minus_sell_price)
        # --
        rate_within_channels = await ChannelInfo.find_rate_within_channels()
        rate_map = {}
        for row in rate_within_channels:
            rate_map[row.channel] = {
                'userid': row.userid,
                'rate': float(row.add_sell_comm_rate),
                'channelDeal': row.channel_deal,
                'isAddbBuyComm': row.is_add_buy_comm,
                'isCurrencyBuyRate': row.is_currency_buy_rate,
                'currencyAddBuyRate': float(row.currency_add_buy_rate),
            }
        # --
        start = (page - 1) * page_size
        end = start + page_size
        # 检查用户的渠道是公有还是私有
        if rate_map[user.channel]['channelDeal'] == 0:
            orders = await VendorOrder.find_public(
                user_id,
                trade_type,
                user.channel,
            )
        else:
            orders = await VendorOrder.find_private(
                user_id,
                trade_type,
                user.channel,
            )

        need_to_add = []
        for order in orders:
            if PriceType(order.price_type) == PriceType.NORMAL:
                price = order.price
            else:
                if price_map[f'{order.userid}']:
                    price = price_map[f'{order.userid}'][HangOrderType(order.type)]
                else:
                    price = price_map[f'{order.channel}']

            order.deal_order_amount = order.deal_order_amount or Decimal('0')
            order.unsold_order_amount = order.deal_order_amount or Decimal('0')
            money = (order.amount - order.deal_order_amount + order.unsold_order_amount) * price

            order_user = await UserInfo.get_user_info_by_id(order.userid)
            if order_user and order.deal_mode == 1:
                money = order.predict_money - ((order.deal_order_amount + order.unsold_order_amount) * price)
            if money >= order.min_money:
                vendor_order_pb = order_pb2.VendorOrder()
                vendor_order_pb = convert_to_proto(vendor_order_pb, order)
                vendor_order_pb.hangid = order.id
                vendor_order_pb.pricetype = order.price_type
                vendor_order_pb.minmoney = str(order.min_money)
                vendor_order_pb.maxmoney = str(order.max_money)
                vendor_order_pb.autoswitch = order.auto_switch
                vendor_order_pb.maxamount = str(order.max_amount)
                vendor_order_pb.enablestatus = order.enable_status
                vendor_order_pb.unsoldordernum = order.unsold_order_num or 0
                vendor_order_pb.dealordernum = order.deal_order_num or 0
                vendor_order_pb.unsoldorderamount = str(order.unsold_order_amount)
                vendor_order_pb.dealorderamount = str(order.deal_order_amount)
                vendor_order_pb.cancelnum = order.cancel_order_num or 0
                vendor_order_pb.usertype = order.user_type
                vendor_order_pb.payeeaccount = order.payee_account or ''
                vendor_order_pb.payeename = order.payee_name or ''
                vendor_order_pb.payeebank = order.payee_bank or ''
                vendor_order_pb.payeebandaddr = order.payee_band_addr or ''
                vendor_order_pb.deal_order_free = str(order.deal_order_free)
                vendor_order_pb.addfeerate = str(order.add_fee_rate)
                vendor_order_pb.createtime = str(order.create_time.strftime('%Y-%m-%d %H:%M:%S'))
                vendor_order_pb.channeldeal = order.channel_deal
                vendor_order_pb.dealmodel = order.deal_mode or 0
                vendor_order_pb.predictmoney = str(order.predict_money)
                vendor_order_pb.resttime = str(order.rest_time.strftime('%Y-%m-%d %H:%M:%S'))
                vendor_order_pb.dealmonery = str(order.deal_monery)
                vendor_order_pb.dealcount = str(order.deal_count)
                vendor_order_pb.withdrawtype = order.withdraw_type or 0
                vendor_order_pb.unsoldordermoney = str(order.unsold_order_money)
                vendor_order_pb.dealordermoney = str(order.deal_order_money)
                # --
                need_to_add.append(vendor_order_pb)

        # TODO: lua 代码里面有一大堆的傻逼排序逻辑，暂时不用管

        # 合并
        resp_pb.result = 0
        resp_pb.volist.extend(need_to_add)
        resp_pb.ordertype = trade_type.value
        resp_pb.pagenum = page
        resp_pb.pagesize = page_size
        str_data = resp_pb.SerializeToString()
        return response_binary_data(code.value, str_data)

    async def my_hang_order(self, code: ResponseEventCode, pb_data: bytes):
        from app.modules.ucoin.py_protobufs import order_pb2
        # 解析 protobuf 消息
        message = order_pb2.MyOrderListRequest()
        message.ParseFromString(pb_data)

        # 提取字段
        user_id: int = message.userid
        trade_type = HangOrderType(message.ordertype)
        page: int = message.pagenum
        page_size: int = message.pagesize

        offset = (page - 1) * page_size

        resp_pb = order_pb2.MyOrderListResponse()

        user = await self.check_user_exists(
            user_id,
            code,
            resp_pb
        )

        orders = await VendorOrder.find_orders_pagination(
            user_id,
            trade_type,
            page_size,
            offset
        )
        need_to_add = []
        for order in orders:
            order_pb = order_pb2.VendorOrder()
            order_pb = convert_to_proto(order_pb, order)
            order_pb.userid = user.userid
            order_pb.hangid = order.id
            order_pb.pricetype = order.price_type
            order_pb.minmoney = str(order.min_money)
            order_pb.maxmoney = str(order.max_money)
            order_pb.autoswitch = order.auto_switch
            order_pb.maxamount = str(order.max_amount)
            order_pb.enablestatus = order.enable_status
            order_pb.nickname = user.nickname
            order_pb.unsoldordernum = order.unsold_order_num
            order_pb.dealordernum = order.deal_order_num
            order_pb.unsoldorderamount = str(order.unsold_order_amount)
            order_pb.dealorderamount = str(order.deal_order_amount)
            order_pb.cancelnum = order.cancel_order_num
            order_pb.createtime = order.create_time.strftime('%Y-%m-%d %H:%M:%S')
            order_pb.unsoldordermoney = str(order.unsold_order_money)
            order_pb.dealordermoney = str(order.deal_order_money)
            # --
            need_to_add.append(order_pb)
            # --
            resp_pb.dealnum.extend([order.unsold_order_num])

        # 合并
        resp_pb.result = 0
        resp_pb.volist.extend(need_to_add)
        resp_pb.ordertype = trade_type.value
        resp_pb.pagenum = page
        resp_pb.pagesize = page_size
        str_data = resp_pb.SerializeToString()
        return response_binary_data(code.value, str_data)

    async def hang_order(self, code: ResponseEventCode, pb_data: bytes):
        from app.modules.ucoin.py_protobufs import order_pb2
        from app.modules.ucoin.py_protobufs import trade_pb2
        # 解析 protobuf 消息
        message = trade_pb2.HangOrderRequest()
        message.ParseFromString(pb_data)

        # 提取字段
        user_id: int = message.userid
        deal_type = HangOrderType(message.type)
        price_type = PriceType(message.pricetype)
        amount: float = float(message.amount)
        price: float = float(message.price)
        min_money: float = float(message.minmoney)
        max_money: float = float(message.maxmoney)
        auto_switch = HangOrderAutoType(message.autoswitch)
        fund_password: str = message.fundpwd
        note: str = message.message

        resp_pb = trade_pb2.HangOrderResponse()

        if amount <= 0:
            WSResponseError(
                ResponseCode.WS_AMOUNT_MUST_MORE_THEN_ZERO,
                code,
                resp_pb
            )
        if min_money <= 0 or min_money > max_money:
            raise WSResponseError(
                ResponseCode.WS_AMOUNT_LIMIT_ERR,
                code,
                resp_pb
            )

        user = await self.check_user_exists(
            user_id,
            code,
            resp_pb
        )
        # 检查用户是否锁定
        if user.is_lock == 1:
            raise WSResponseError(
                ResponseCode.WS_ACCOUNT_LOCKED,
                code,
                resp_pb
            )
        # 检查资金密码
        if not self.verify_fund_password(user, fund_password):
            raise WSResponseError(
                ResponseCode.WS_FUND_PASS_ERR,
                code,
                resp_pb
            )

        deal_money = 0
        deal_amount = 0
        if auto_switch == HangOrderAutoType.MANUAL:
            if price_type != 0:
                coin_service = CoinService()
                price = await coin_service.ERC20_USDT_price(
                    deal_type,
                    CurrencyType.USDT,
                    True,
                    user_id=user_id,
                    channel=user.channel
                )
            if amount * price < min_money:
                raise WSResponseError(
                    ResponseCode.WS_AMOUNT_CANNOT_LESS_THAN_LIMIT,
                    code,
                    resp_pb
                )
            deal_amount = amount
        else:
            if deal_type == HangOrderType.BUY:
                deal_money = amount

        # 只能卖单， 买单， 自动售卖分别只能有一个
        vendor_list = await VendorOrder.get_vendor_id_of_orders(
            user_id,
            deal_type
        )
        for vendor in vendor_list:
            vendor_order = await self.get_vendor_order_by_id(vendor.id)
            if vendor_order:
                if vendor_order.type == deal_type.value and vendor_order.auto_switch == auto_switch.value:
                    raise WSResponseError(
                        ResponseCode.WS_AMOUNT_CANNOT_LESS_THAN_LIMIT,
                        code,
                        resp_pb
                    )

        # 检查接单限制跟交易限制
        channel = await ChannelInfo.find(user.channel)
        if not channel:
            raise WSResponseError(
                ResponseCode.WS_CHANNEL_NOT_EXISTS,
                code,
                resp_pb
            )

        rate = 0.
        if user.user_type == 200 or user.user_type == 201:
            if channel.is_add_buy_comm == 1:
                rate = channel.add_sell_comm_rate
        elif user.user_type == 202:
            if channel.is_currency_buy_rate == 1:
                rate = channel.currency_add_buy_rate

        coin_service = CoinService()
        user_service = UserService()

        sys_price = await coin_service.ERC20_USDT_price(
            deal_type,
            CurrencyType.USDT,
            True,
            user_id=user_id,
            channel=user.channel
        )
        if auto_switch == HangOrderAutoType.AUTO:
            # 接单限制
            if deal_type == TradeType.SELL:
                price = round(sys_price, 4)
                if channel.orders_limit > (user.erc_usdt_amount - user.erc_usdt_lock_amount) * price:
                    raise WSResponseError(
                        ResponseCode.WS_BALANCE_NOT_ENOUGH,
                        code,
                        resp_pb
                    )
            if user.is_accept_order == 1:
                raise WSResponseError(
                    ResponseCode.WS_FORBID_ACCEPT_ORDER,
                    code,
                    resp_pb
                )

            if user.is_team_accept_order == 1:
                raise WSResponseError(
                    ResponseCode.WS_TEAM_FORBID_ACCEPT_ORDER,
                    code,
                    resp_pb
                )

        # 交易限制
        if channel.deal_limit > min_money:
            raise WSResponseError(
                ResponseCode.WS_MIN_LIMIT_SMALLER_THAN_SETTING,
                code,
                resp_pb
            )
        # 检查是不是系统价格， 价格必须大于0
        if price_type == PriceType.NORMAL and price <= 0:
            raise WSResponseError(
                ResponseCode.WS_INPUT_PRICE,
                code,
                resp_pb
            )
        # 挂卖单的时候
        if deal_type == HangOrderType.SELL:
            # 检查是否允许挂卖
            if user.is_hang_sell == 2:
                raise WSResponseError(
                    ResponseCode.WS_FORBID_HANG_SELL_ORDER,
                    code,
                    resp_pb
                )

            # 检查收款方式
            if not await user_service.check_payment_available(user):
                raise WSResponseError(
                    ResponseCode.WS_NO_AVAILABLE_PAYMENT,
                    code,
                    resp_pb
                )

            # 挂单总额为0时就是，挂身上可用余额不检查
            # 挂单总额不为0时就是只挂这么多， 检查可用余额
            if auto_switch == HangOrderAutoType.MANUAL:
                if amount > (user.erc_usdt_amount - user.erc_usdt_lock_amount):
                    raise WSResponseError(
                        ResponseCode.WS_BALANCE_NOT_ENOUGH,
                        code,
                        resp_pb
                    )
        else:
            # 检查是否允许挂买
            if user.is_hang_buy == 2:
                raise WSResponseError(
                    ResponseCode.WS_FORBID_HANG_SELL_ORDER,
                    code,
                    resp_pb
                )

        # 插入到数据库中
        new_vendor_order = VendorOrder()
        new_vendor_order.userid = user_id
        new_vendor_order.type = deal_type.value
        new_vendor_order.price = price
        new_vendor_order.amount = deal_amount
        new_vendor_order.price_type = price_type.value
        new_vendor_order.min_money = min_money
        new_vendor_order.max_money = max_money
        new_vendor_order.auto_switch = auto_switch.value
        new_vendor_order.message = note
        new_vendor_order.max_amount = amount
        new_vendor_order.channel = user.channel
        new_vendor_order.enable_status = 1
        new_vendor_order.user_type = 200
        new_vendor_order.channel_deal = channel.channel_deal
        new_vendor_order.add_fee_rate = rate
        new_vendor_order.deal_monery = deal_money
        new_vendor_order.platform_id = user.platform_id
        try:
            await new_vendor_order.insert()
        except BaseException as err:
            logging.error(err)
            raise WSResponseError(
                ResponseCode.WS_FORBID_HANG_SELL_ORDER,
                code,
                resp_pb
            )
        # TODO Lua 这里有一段线程锁的逻辑，不知道有什么卵用

        vendor_order_pb = order_pb2.VendorOrder()
        vendor_order_pb = convert_to_proto(vendor_order_pb, new_vendor_order)
        vendor_order_pb.hangid = new_vendor_order.id
        vendor_order_pb.pricetype = new_vendor_order.price_type
        vendor_order_pb.minmoney = str(new_vendor_order.min_money)
        vendor_order_pb.maxmoney = str(new_vendor_order.max_money)
        vendor_order_pb.autoswitch = new_vendor_order.auto_switch
        vendor_order_pb.maxamount = str(new_vendor_order.max_amount)
        vendor_order_pb.enablestatus = 1
        vendor_order_pb.nickname = user.nickname
        vendor_order_pb.unsoldordernum = 0
        vendor_order_pb.dealordernum = 0
        vendor_order_pb.unsoldorderamount = '0'
        vendor_order_pb.dealorderamount = '0'
        vendor_order_pb.cancelnum = 0
        vendor_order_pb.usertype = 200
        vendor_order_pb.addfeerate = str(rate)
        vendor_order_pb.createtime = new_vendor_order.create_time.strftime('%Y-%m-%d %H:%M:%S')
        vendor_order_pb.channeldeal = new_vendor_order.channel_deal
        vendor_order_pb.dealmodel = 0
        vendor_order_pb.resttime = new_vendor_order.rest_time.strftime('%Y-%m-%d %H:%M:%S')
        vendor_order_pb.dealmonery = str(deal_money)
        vendor_order_pb.dealcount = "0"
        vendor_order_pb.withdrawtype = 0
        vendor_order_pb.unsoldordermoney = '0'
        vendor_order_pb.dealordermoney = '0'

        # 加到用户自己的挂单列表中区
        await self.add_user_vendor_order(user_id, deal_type, new_vendor_order.id)
        # 保存到缓存中
        await self.set_vendor_order(new_vendor_order)

        if new_vendor_order.auto_switch == 1:
            if new_vendor_order.type == 1:
                user.autosell = new_vendor_order.id
                await user_service.set_user(user)
                await user_service.send_auto_sell(user)
        else:
            await self.add_vendor_order(new_vendor_order.id)
            # 找出所有需要更新的用户
            user_list = []
            if channel.channel_deal == 0:
                # 私有的只发给自己渠道的
                channels = await ChannelInfo.find_self_channels()
                channel_names = []
                for channel_item in channels:
                    channel_names.append(channel_item.channel)
                if len(channel_names) > 0:
                    channel_users = await UserInfo.find_by_channels(channel_names)
                    for channel_user in channel_users:
                        is_online = await user_service.check_online_user(channel_user.userid)
                        if is_online and channel_user.userid != user_id:
                            user_list.append(channel_user.userid)
            else:
                channel_users = await UserInfo.find_by_channels([user.channel])
                for channel_user in channel_users:
                    is_online = await user_service.check_online_user(channel_user.userid)
                    if is_online and channel_user.userid != user_id:
                        user_list.append(channel_user.userid)

            for pay_type in user.paytypelist:
                temp_rate = 0.
                if channel.is_add_buy_comm and (user.user_type == 200 or user.user_type == 201):
                    fee_rate = await user_service.get_user_fee_rate(
                        channel.userid,
                        pay_type,
                        FeeRateMode.SELL_COMM_RATE
                    )
                    temp_rate = fee_rate + rate
                pay_list = await user_service.get_user_payment(user_id, pay_type)
                for _, v in pay_list.items():
                    data = json.loads(v)
                    if data['status'] == 1:
                        vendor_order_pb.paylist.extend([pay_type])
                        vendor_order_pb.feeRate.extend(str(temp_rate))

            if vendor_order_pb.pricetype == 1:
                vendor_order_pb.price = str(sys_price)
            vendor_order_pb.nickname = user.nickname

            # --
            new_order_pb = order_pb2.UpdateVendorOrder()
            new_order_pb.result = 0
            new_order_pb.volist.extend([vendor_order_pb])
            str_data = new_order_pb.SerializeToString()
            new_order_resp_pb = response_binary_data(
                ResponseEventCode.UPDATE_NEW_ORDER.value,
                str_data
            )
            await websocket.send(new_order_resp_pb)

        resp_pb.result = 0
        str_data = resp_pb.SerializeToString()
        return response_binary_data(code.value, str_data)

    async def today_flow(self, code: ResponseEventCode, pb_data: bytes):
        from app.modules.ucoin.py_protobufs import trade_pb2
        # 解析 protobuf 消息
        message = trade_pb2.TodayFlowRequest()
        message.ParseFromString(pb_data)

        # 提取字段
        user_id: int = message.userid

        resp_pb = trade_pb2.TodayFlowResponse()

        user = await self.check_user_exists(
            user_id,
            code,
            resp_pb
        )

        today_flow_data = await LogUserDaily.user_today_flow(user_id)
        if today_flow_data:
            pass
        else:
            resp_pb.todaySell = '0'
            resp_pb.todaybuy = "0"
            resp_pb.averagetime = 0
            resp_pb.successrate = "0%"

        from app.modules.ucoin.service.coin import CoinService
        coin_service = CoinService()
        rate = await coin_service.ERC20_USDT_price(
            HangOrderType.BUY,
            CurrencyType.USDT,
            True,
            user_id=user_id,
            channel=user.channel
        )
        resp_pb.exchangerate = str(rate or 0)

        resp_pb.result = 0
        str_data = resp_pb.SerializeToString()
        return response_binary_data(code.value, str_data)

    async def trade_records(self, code: ResponseEventCode, pb_data: bytes):
        from app.modules.ucoin.py_protobufs import order_pb2
        from app.modules.ucoin.py_protobufs import trade_pb2
        # 解析 protobuf 消息
        message = trade_pb2.TradeRecordRequest()
        message.ParseFromString(pb_data)

        # 提取字段
        user_id: int = message.userid
        page: int = message.pagenum
        page_size: int = message.pagesize
        trade_type = TradeType(message.type)
        order_status = OrderProcessStatusType(message.status)

        resp_pb = trade_pb2.TradeRecordResponse()

        await self.check_user_exists(
            user_id,
            code,
            resp_pb
        )

        # --
        offset = (page - 1) * page_size
        if trade_type == TradeType.ALL:
            if order_status != OrderProcessStatusType.READY:
                orders = await CustomerOrder.user_trade_orders(
                    user_id,
                    page_size,
                    offset,
                    order_status=order_status,
                )
            else:
                orders = await CustomerOrder.user_trade_orders(
                    user_id,
                    page_size,
                    offset,
                )
        else:
            if order_status != OrderProcessStatusType.READY:
                orders = await CustomerOrder.user_trade_orders(
                    user_id,
                    page_size,
                    offset,
                    trade_type=trade_type,
                    order_status=order_status,
                )
            else:
                orders = await CustomerOrder.user_trade_orders(
                    user_id,
                    page_size,
                    offset,
                    trade_type=trade_type,
                )

        user_service = UserService()

        need_to_add = []
        for order in orders:
            order_pb = order_pb2.CustomerOrderInfo()
            order_pb = convert_to_proto(order_pb, order)
            order_pb.dealid = order.id
            # 给前端转化，前端只认对于这个这个用户说是买还是卖
            order_pb.type = CoinService.trade_type_conversion(
                TradeType(order.type),
                order.customer_user_id,
                user_id
            ).value
            order_pb.vendororderid = order.vendor_order_id
            order_pb.customeruserid = order.customer_user_id
            order_pb.vendoruserid = order.vendor_user_id
            order_pb.merchantorderid = order.merchant_order_id
            order_pb.feerate = str(order.fee_rate)
            if order.proof_url:
                if len(order.proof_url) > 0:
                    # 这他妈是一个用,分割的字符串
                    proof_url_list = order.proof_url.split(',')
                    order_pb.proofurl.extend(proof_url_list)
            order_pb.paytypelist = order.pay_type
            order_pb.payidlist = order.pay_id
            order_pb.createtime = order.create_time.strftime('%Y-%m-%d %H:%M:%S')
            order_pb.paytime = order.pay_time.strftime('%Y-%m-%d %H:%M:%S')
            order_pb.passtime = order.pass_time
            order_pb.publicprice = order.publiceprice
            order_pb.fromtype = order.from_type
            order_pb.notifyurl = order.notify_url
            order_pb.sellfeerate = str(order.sell_fee_rate)
            order_pb.sellfee = str(order.sell_fee)
            order_pb.canceltime = order.cancel_time.strftime('%Y-%m-%d %H:%M:%S')
            order_pb.updatetime = order.update_time.strftime('%Y-%m-%d %H:%M:%S')
            order_pb.getamount = str(order.get_amount)
            order_pb.dealtype = order.deal_type
            order_pb.withdrawtype = order.withdraw_type
            order_pb.aftermoney = str(order.after_money)
            order_pb.feemoney = str(order.fee_money)
            # 填充用户昵称
            customer_user = await user_service.get_user_from_redis(order.customer_user_id)
            order_pb.customerusernickname = customer_user.nickname
            vendor_user = await user_service.get_user_from_redis(order.vendor_user_id)
            order_pb.vendorusernickname = vendor_user.nickname
            # --
            need_to_add.append(order_pb)

        log_user = await LogUser.find_one(user_id)
        if log_user:
            resp_pb.ordercount = log_user.total_count_buy + log_user.total_count_sell
            resp_pb.finishordercount = log_user.finish_count_buy + log_user.finish_count_sell

        resp_pb.infolist.extend(need_to_add)
        resp_pb.type = message.type
        resp_pb.paytype = message.paytype
        resp_pb.pagenum = message.pagenum
        resp_pb.pagesize = message.pagesize
        str_data = resp_pb.SerializeToString()
        return response_binary_data(code.value, str_data)

    async def pending_orders(self, code: ResponseEventCode, pb_data: bytes):
        from app.modules.ucoin.py_protobufs import trade_pb2
        from app.modules.ucoin.py_protobufs import order_pb2
        # 解析 protobuf 消息
        message = trade_pb2.PendingOrdersRequest()
        message.ParseFromString(pb_data)

        # 提取字段
        user_id: int = message.userid

        resp_pb = trade_pb2.PendingOrdersResponse()

        user = await self.check_user_exists(
            user_id,
            code,
            resp_pb
        )
        user_orders = await CustomerOrder.user_pending_orders(user.userid)

        orders_to_add = []
        for order in user_orders:
            order_info = order_pb2.CustomerOrderInfo()
            order_info = convert_to_proto(order_info, order)
            # -- 补充表不一致的字段
            order_info.dealid = order.id
            order.type = resolve_user_trade_type_within_order(
                user.userid,
                TradeType(order.type),
                order.customer_user_id,
                order.vendor_user_id
            )
            order_info.vendororderid = order.vendor_order_id
            order_info.customeruserid = order.customer_user_id
            order_info.vendoruserid = order.vendor_user_id
            order_info.merchantorderid = order.merchant_order_id
            order_info.feerate = order.fee_rate
            if not order.proof_url or len(order.proof_url) > 0:
                proof_url_list = order.proof_url.split(',')
                order_info.proofurl.extend(proof_url_list)
            order_info.paytypelist = order.pay_type
            order_info.payidlist = order.pay_id
            order_info.createtime = order.create_time
            order_info.paytime = order.pay_time
            order_info.passtime = order.pass_time
            order_info.fromtype = order.from_type
            # -- 查找买家昵称
            customer = await UserInfo.get_user_info_by_id(order.customer_user_id)
            order_info.customerusernickname = customer.nickname or '匿名用户'
            # -- 查找卖家昵称
            vendor = await UserInfo.get_user_info_by_id(order.vendor_user_id)
            order_info.vendorusernickname = vendor.nickname or '匿名商家'
            # --
            order_info.updatetime = order.update_time
            order_info.getamount = order.get_amount
            order_info.dealtype = order.deal_type
            order_info.withdrawtype = order.withdraw_type
            order_info.aftermoney = order.after_money
            order_info.feemoney = order.fee_money

            orders_to_add.append(order)

        resp_pb.infolist.extend(orders_to_add)
        str_data = resp_pb.SerializeToString()
        return response_binary_data(code.value, str_data)

    async def exchange_rate(self, code: ResponseEventCode, pb_data: bytes):
        from app.modules.ucoin.py_protobufs import trade_pb2
        # 解析 protobuf 消息
        message = trade_pb2.ExchangeRateRequest()
        message.ParseFromString(pb_data)

        # 提取字段
        user_id: int = message.userid

        resp_pb = trade_pb2.ExchangeRateResponse()

        user = await self.check_user_exists(
            user_id,
            code,
            resp_pb
        )

        coin_service = CoinService()

        buy_rate = await coin_service.ERC20_USDT_price(
            HangOrderType.BUY,
            CurrencyType.USDT,
            True,
            user_id=user_id,
            channel=user.channel
        )
        sell_rate = await coin_service.ERC20_USDT_price(
            HangOrderType.SELL,
            CurrencyType.USDT,
            True,
            user_id=user_id,
            channel=user.channel
        )

        # --
        resp_pb.buyrate = str(buy_rate)
        resp_pb.sellrate = str(sell_rate)
        str_data = resp_pb.SerializeToString()
        return response_binary_data(code.value, str_data)

    async def get_vendor_order_by_id(self, vendor_id: int):
        data_str = await self.redis.hget(
            RDS_VENDOR_ORDER,
            vendor_id
        )
        if data_str:
            data = json.loads(data_str)
            order = VendorOrder()
            order.from_dict(data)
        else:
            order = await VendorOrder.find(vendor_id)
        return order

    async def add_user_vendor_order(self, user_id: int, hang_type: HangOrderType, order_id: int):
        await self.redis.lpush(
            RDS_USER_VENDOR_ORDER % (user_id, hang_type.value),
            order_id
        )

    async def set_vendor_order(self, vendor_order: VendorOrder):
        await self.redis.hset(
            RDS_VENDOR_ORDER,
            vendor_order.id,
            json.dumps(vendor_order.to_dict()),
        )

    async def add_vendor_order(self, order_id: int):
        await self.redis.lpush(
            RDS_VENDOR_ORDER_LIST,
            order_id
        )

    async def lock_order(self, hang_order_id: int):
        key = RDS_LOCK_HANG_ORDER % hang_order_id
        # 使用 setnx 尝试获取锁
        acquired = await self.redis.setnx(key, "1")
        if acquired:
            # 设置过期时间
            await self.redis.expire(key, 30)

    async def unlock_order(self, hang_order_id: int):
        key = RDS_LOCK_HANG_ORDER % hang_order_id
        await self.redis.delete(key)
