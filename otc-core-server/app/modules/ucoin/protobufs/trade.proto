syntax = "proto3";

package trade;

import "order.proto";

//今天的现金流（今日售出/买入）
message TodayFlowRequest {
  int32 userid = 1;
  int32 coinid = 2;
}
message TodayFlowResponse {
  int32 result = 1;
  string msg = 2;
  string todaySell = 3;          //进入卖出
  string todaybuy = 4;          //进入买人
  int32 averagetime = 5;          //平均确定时长
  string successrate = 6;        //交易成功率
  string exchangerate = 7;        //汇率
  int32 coinid = 8;
}

// 交易记录
message TradeRecordRequest {
  int32 userid = 1;
  int32 type = 2;                  //类型 0-买 1-卖 2-全部
  int32 status = 3;                  //订单状态 0-全部 1-未付款 3-已付款 7-已完成, 8-已取消, 9-申诉中
  int32 paytype = 4;                  //交易类型  8101-支付宝,8201-微信,8001-银行卡
  int32 pagenum = 5;                  //页码
  int32 pagesize = 6;                //笔数
}
message TradeRecordResponse {
  int32 result = 1;
  string msg = 2;
  int32 type = 3;                  //类型 0-买 1-卖 2-全部
  int32 status = 4;                  //订单状态 0-全部 1-未付款 3-已付款 7-已完成, 8-已取消, 9-申诉中
  int32 paytype = 5;                  //交易类型 8101-支付宝,8201-微信,8001-银行卡
  int32 pagenum = 6;                  //页码
  int32 pagesize = 7;                //笔数
  repeated order.CustomerOrderInfo infolist = 8;          //信息列表
  int32 ordercount = 9;                //法币订单数
  int32 finishordercount = 10;            //成功法币订单数
}

// 待处理的订单
message PendingOrdersRequest {
  int32 userid = 1;
}
message PendingOrdersResponse {
  int32 result = 1;
  string msg = 2;
  repeated order.CustomerOrderInfo infolist = 3;          //信息列表
}

// 查询汇率
message ExchangeRateRequest {
  int32 userid = 1;
}
message ExchangeRateResponse {
  int32 result = 1;
  string msg = 2;
  string buyrate = 3;                  //买汇率
  string sellrate = 4;                  //卖汇率
}

// 挂单
message HangOrderRequest {
  int32 userid = 1;
  int32 type = 2;          //挂单类型：0是买单：1是卖单
  int32 pricetype = 3;     //价格类型：0是普通价格，1是根据USDT市场价格来定
  string price = 4;         //价格类型
  string amount = 5;        //数量
  string minmoney = 6;      //最小的金额
  string maxmoney = 7;      //最大的金额
  int32 autoswitch = 8;    //0是非自动挂单，1是自动挂单
  string message = 9;       //挂单遗留的消息
  string fundpwd = 10;       //资金密码
  int32 coinid = 11;      //币种ID
}
message HangOrderResponse {//注意，人物自己的信息和获得的物品通过其他的协议通知玩家,这里只返回购买的结果
  int32 result = 1;    //返回的通知
  string msg = 2;
  order.VendorOrder voInfo = 3;
}

// 交易，也就是买卖
message DealRequest {
  int32 userid = 1;
  int32 hangid = 2;        //对应的挂单ID
  int32 dealtype = 3;        //交易类型，0是买，1是卖
  string amount = 4;        //交易数量
  string fundPwd = 5;        //交易密码
  string price = 6;        //价格
  int32 moneytype = 7;      //钱的类型 0-币 1-人民币
  int32 paytype = 8;        //支付方式
}
message DealResponse {
  int32 result = 1;        //产生的一个订单的类型
  string msg = 2;
  order.CustomerOrderInfo coinfo = 3;   //返回一个对应的结构体
}

// 快捷买卖币
message QuickDealRequest {
  int32 userid = 1;
  int32 hangid = 2;        //对应的挂单ID
  int32 dealtype = 3;        //交易类型，0是买，1是卖
  string amount = 4;        //交易数量
  string fundPwd = 5;        //交易密码
  int32 moneytype = 6;        //钱的类型 0-币 1-人民币
  int32 paytype = 7;        //支付方式
  int32 cionid = 8;        //币种ID
}
message QuickDealResponse {
  int32 result = 1;
  string msg = 2;
  order.CustomerOrderInfo coinfo = 3;  //返回一个对应的结构体
}