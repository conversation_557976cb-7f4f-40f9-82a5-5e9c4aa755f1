syntax = "proto3";

package user;

message UserInfo {
  int32 userid = 1;          //用户标识ID
  string cid = 2;                  //用作判断手机标识的唯一字符串
  string account = 3;        //账号
  string password = 4;        //密码
  string nickname = 5;        //昵称
  string ethaddress = 6;        //eth地址
  string face1 = 7;          //头像
  string regdate = 8;        //注册时间
  int32 sex = 9;            //性别
  int32 age = 10;          //年龄
  string email = 11;          //邮箱
  string phonenum = 12;        //电话
  string ethamount = 13;      //eth的数量
  string ercusdtamount = 14;    //erc20的usdt的数量
  string ercusdtlockamount = 15;  //erc20的usdt的锁定数量
  string channel = 16;        //渠道号
  string invitecode = 17;      //自己的邀请码
  string bindcode = 18;        //绑定的上级邀请码
  string imei = 19;          //手机IMEI码
  string devname = 20;              //设备号
  string macname = 21;              //物理地址
  int32 mobiletype = 22;            //运营商
  int32 lasttime = 23;              //上次登录时间
  int32 penulttime = 24;            //倒数第二次登录
  int32 isban = 25;                  //是否禁号，列入黑名单
  string description = 26;        //个性签名
  int32 blacklist = 27;
  string ip = 28;                //IP地址
  string province = 29;          //省份
  string city = 30;              //城市
  int32 bindtype = 31;            //注册类型 1 手机注册
  int32 isfundpassword = 32;         //是否有设定资金密码
  repeated int32 paytypelist = 33;      //支付通道列表
  int32 agent = 34;          //直属上级
  int32 usertype = 35;        //用户类型 100=平台账号，用于资金归集；200=币商代理；201=码商；202=币商；300=商户代理，301=商户
  int32 autosell = 36;        //自动售卖 0-否  大于0 自动售卖的订单
  string minbuy = 37;        //最小购买
  string maxbuy = 38;        //最大购买
  string minsell = 39;        //最小出售
  string maxsell = 40;        //最大出售
  int32 islock = 41;          //是否锁定 0解锁 1锁定
  int32 ishangbuy = 42;        //是否允许挂买单，1=允许 2不允许
  int32 ishangsell = 43;        //是否允许挂卖单，1=允许 2不允许
  int32 commtype = 44;        //收益类型：101=按级差进行收益，后期添加其他
  int32 prohibitlogin = 45;      //是否禁止登陆
  int32 isacceptorder = 46;      //是否允许接单 0 允许1 禁止
  string deallasttime = 47;      //上次交易时间
  string bantime = 48;        //封禁时间
  int32 isinvited = 49;        //是否允许开设代理 0允许 1禁止
  int32 isotc = 50;          //是否开启OTC交易 0 开启 1 不开
  int32 isbehalfpay = 51;      //是否开启代付交易 0 开启 1 不开
  string ercfcamount = 52;      //法定货币数量
  string ercfclockamount = 53;    //法定货币冻结数量
  int32 dealcointype = 54;      //成交货币类型 0 数字货币 1 法币
  string energyvalue = 55;      //能量值
  string weightsvalue = 56;      //权重值
  string teamname = 57;        //团队名称
  int32 platformid = 58;        //平台编号
  int32 isteamacceptorder = 59;    //是否禁止团队接单 0 否 1 是
  string preauthorization = 60;    //提现预售额度
  string coinpayrate = 61;      //币支付费率
  int32 coinpayerc = 62;        //是否开启erc充币 0-关闭 1-开启
  int32 coinpaytrc = 63;        //是否开启trc充币 0-关闭 1-开启
  int32 allowsysaddr = 64;      //是否允许使用系统地址 0-否 1-是
  string sysercmin = 65;        //使用系统erc地址最小值
  string coinpayusdtamount = 66;    //币付钱包
  string coinpayusdtlockamount = 67;  //币付冻结钱包
  int32 ischeck = 68;        //重复订单检查 0 检查 1 不检查
  string extractcurrencyrateerc = 69;//erc的提币手续费
  string extractcurrencyratetrc = 70;//trc的提币手续费
}

message coininfo {
  int32 coinid = 1;          //币种ID
  string coinname = 2;          //币种昵称
  string coinamount = 3;          //币总数量
  string coinlockamount = 4;        //冻结币总数量
  string coinfreeamount = 5;        //可用币的数量
  int32 status = 6;            //币状态 0-可用 1-不可用
  string buyrate = 7;          //购买汇率
  string sellrate = 8;          //购买汇率
}

message coinaddr {
  int32 addrtype = 1;          //链类型
  string addrname = 2;          //链名称
  string address = 3;          //链地址
}

message Payment {
  int32 paytype = 1;          //支付类型
  string account = 2;        //账号
  string payee = 3;          //收款人
  string qrcode = 4;          //收款码
  string bankname = 5;        //开户行
  string bankaddr = 6;        //开户地址
  string singlelimit = 7;      //单笔限额
  string daylimit = 8;        //单日限额
  int32 id = 9;            //编号
  int32 status = 10;          //0 失效 1 生效
  int32 userid = 11;          //所属玩家
  string todaymoney = 12;      //今日收款
  int32 fourthpartyid = 13;      //四方ID
  string deallasttime = 14;      //最后一次交易的时间
}

message subinfo
{
  int32 userid = 1;          //用户ID
  string nickname = 2;        //用户名字
  string teamperformance = 3;    //出售量
  string myperformance = 4;      //给上级贡献的收益
  int32 isacceptorder = 5;      //是否允许接单 0 允许1 禁止
  int32 prohibitlogin = 6;      //禁止登陆 0 允许登陆 1 禁止登陆
  repeated string paytypelist = 7;      //支付方式
  repeated string payratelist = 8;      //出售费率
  string buycount = 9;        //购买量
  repeated string buyratelist = 10;      //购买费率
  repeated string behalfbuyratelist = 11;    //代付费率
}

message twalletorderinfo {
  string order_id = 1;          //订单号
  string channel = 2;          //渠道号
  int32 userid = 3;            //用户id
  string nickname = 4;          //用户昵称
  string currency_type = 5;        //币种
  string amount = 6;            //币数量
  string actualamount = 7;        //实际数量
  string trusteeship = 8;        //冷钱包地址
  string usertxid = 9;          //用户上传TXID
  string create = 10;          //创建时间
  string remarks = 11;          //备注
  int32 status = 12;            //0-未到账 1-已到账
  string coinid = 13;          //币种ID
  string coinname = 14;          //币种名字
  int32 addrtype = 15;          //地址类型
  string addrname = 16;          //地址名字
}

//我的收款方式
message PaymentsRequest {
  int32 userid = 1;          //玩家ID
}
message PaymentsResponse {
  int32 result = 1;
  string msg = 2;
  repeated Payment paylist = 3;        //支付信息列表
}

//更新用户信息
message UpdateUserInfoResponse {
  int32 result = 1;
  string msg = 2;
  repeated string typelist = 3;
  repeated string valuelist = 4;
}

// 添加收款方式
message AddPaymentRequest {
  int32 userid = 1;          //玩家ID
  string account = 2;        //账号
  string payee = 3;          //收款人
  string qrcode = 4;          //收款码
  string bankname = 5;        //银行名字
  string bankaddr = 6;        //银行地址
  string singlelimit = 7;      //单笔限额
  string daylimit = 8;        //单日限额
  int32 paytype = 9;          //支付类型
}
message AddPaymentResponse {
  int32 result = 1;
  string msg = 2;
}

// 收支明细
message IncomeExpenditure {
  int32 detailtype = 1;      //0 全部 1 购买 2出售 3充值 4 提现 5提币 6充币 7转入 8转出 9收益 10 手续费 11系统补款 12系统扣款
  string amount = 2;      //交易数量
  string afteramount = 3;    //余额
  string createtime = 4;      //创建时间
}
message IncomeExpenditureRequest {
  int32 userid = 1;
  int32 detailtype = 2;        //0 全部 1 购买 2出售 3充值 4 提现 5提币 6充币 7转入 8转出 9收益 10 手续费 11系统补款 12系统扣款
  int32 pagenum = 3;          //页数。每页显示10个。
  int32 pagesize = 4;        //一页多少条
  int32 coinid = 5;          //币种ID
}
message IncomeExpenditureResponse {
  int32 result = 1;
  string msg = 2;
  int32 detailtype = 3;            //0 全部 1 购买 2出售 3充值 4 提现 5提币 6充币 7转入 8转出 9收益 10 手续费 11系统补款 12系统扣款
  int32 pagenum = 4;              //页数。每页显示10个。
  int32 pagesize = 5;            //一页多少条
  string parameter1 = 6;            //参数1 页面只有一个参数是就用这一个
  string parameter2 = 7;            //参数1 页面有二个参数是第二个就用这
  repeated IncomeExpenditure detaillist = 8;  //参数1 页面有二个参数是第二个就用这
  int32 coinid = 9;              //币种ID
}