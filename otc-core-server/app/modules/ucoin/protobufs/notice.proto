syntax = "proto3";

package notice;

message NoticeInfo {
  int32 id = 1;            //公告ID
  string title = 2;          //标题
  string content = 3;        //内容
  int32 noticetype = 4;        //类型 暂无全是0
  int32 expirationtime = 5;      //公告过期时间
  string remark = 6;          //备注
}

message MessageInfo {
  int32 id = 1;            //消息通知id
  int32 messagetype = 2;        //类型 暂无全是0
  string title = 3;          //标题
  string content = 4;        //内容
  string remark = 5;          //备注
  int32 isread = 6;          //是否已读(0 未读 1已读)
}

//公告列表
message NoticeListRequest  {
  int32 userid = 1;        //玩家ID
  int32 pagenum = 2;      //页码
  int32 pagesize = 3;      //一页多少条
}
message NoticeListResponse {
  int32 result = 1;
  string msg = 2;        //当result 不等0时 这里赋值有提示语
  int32 pagenum = 3;      //页码
  int32 pagesize = 4;      //一页多少条
  repeated NoticeInfo noticelist = 5;    //广播列表
}

//公告详情
message NoticeDetailRequest {
  int32 userid = 1;        //玩家ID
  int32 id = 2;          //公告ID
}
message NoticeDetailResponse {
  int32 result = 1;
  string msg = 2;        //当result 不等0时 这里赋值有提示语
}

// 消息通知列表
message MessageListRequest {
  int32 userid = 1;        //玩家ID
  int32 pagenum = 2;      //页码
  int32 pagesize = 3;      //一页多少条
}
message MessageListResponse {
  int32 result = 1;
  string msg = 2;        //当result 不等0时 这里赋值有提示语
  int32 pagenum = 3;      //页码
  int32 pagesize = 4;      //一页多少条
  repeated MessageInfo messagelist = 5;    //广播列表
}
//
////阅读消息
//message cgnoticemessageread {
//  int32 userid = 1;        //玩家ID
//  int32 id = 2;          //消息ID
//}
//message gcnoticemessageread {
//  int32 result = 1;
//  string msg = 2;        //当result 不等0时 这里赋值有提示语
//}
//
////删除消息
//message cgnoticemessagedelete {
//  int32 userid = 1;        //玩家ID
//  int32 id = 2;          //消息ID
//}
//message gcnoticemessagedelete {
//  int32 result = 1;
//  string msg = 2;        //当result 不等0时 这里赋值有提示语
//}
