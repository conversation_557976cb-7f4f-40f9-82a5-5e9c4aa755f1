syntax = "proto3";

package auth;

import "user.proto";

message LoginRequest {
  string phonenum = 1;
  string password = 2;
  string channel = 4;
  int32 bindtype = 5;
}

message LoginResponse {
  int32 result = 1;
  string msg = 2;          //当result 不等0时 这里赋值有提示语
  user.UserInfo uinfo = 3;
  int32 systime = 4;          //服务器时间戳，用于客户端校准
  repeated user.coininfo coinlist = 5;        //币数量
  repeated user.coinaddr addrlist = 6;        //链数组
  repeated user.coinaddr trusteeshipaddresslist = 7;//冷钱包地址
}

// 心跳包
message HeartbeatRequest {
  int32 userid = 1;
}
message HeartbeatResponse
{
  int32 result = 1;
  string msg = 2;
}

// 重连
message ReconnectRequest {
  int32 userid = 1;
}
message ReconnectResponse {
  int32 result = 1;
  string msg = 2;
  user.UserInfo uinfo = 3;
  int32 systime = 4;          //服务器时间戳，用于客户端校准
}