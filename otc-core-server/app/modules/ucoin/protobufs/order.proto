syntax = "proto3";

package order;

// 绑定后属于我的代理的信息
message VendorOrder {
  int32 userid = 1;
  int32 hangid = 2;            //挂单ID
  int32 type = 3;              //挂单类型：0是买单：1是卖单
  int32 pricetype = 4;          //价格类型：0是普通价格，1是根据USDT市场价格来定
  string price = 5;              //价格类型
  string amount = 6;              //数量
  string minmoney = 7;            //最小的金额
  string maxmoney = 8;            //最大的金额
  int32 autoswitch = 9;          //0是非自动挂单，1是自动挂单
  string message = 10;          //挂单遗留的消息
  string maxamount = 11;        //交易的总数量
  int32 enablestatus = 12;      //状态：0，失效，1是可用
  string channel = 13;          //渠道标识
  string nickname = 14;          //昵称
  int32 unsoldordernum = 15;      //未成交订单数量
  int32 dealordernum = 16;      //成交订单数量
  string unsoldorderamount = 18;    //未成交订单币数
  string dealorderamount = 19;    //成交订单币数
  repeated int32 paylist = 20;        //支持的支付类型列表
  int32 cancelnum = 21;        //取消订单数量
  int32 usertype = 22;        //100=平台用户，200=币商，300=商户
  string payeeaccount = 23;      //收款人账号
  string payeename = 24;        //收款人账号
  string payeebank = 25;        //收款银行
  string payeebandaddr = 26;      //收款银行地址
  string deal_order_free = 27;    //已付的手续费
  string free = 28;          //应付总手续费
  repeated string feeRate = 29;        //手续费率
  string addfeerate = 30;      //追加手续费
  string createtime = 31;      //创建时间
  int32 channeldeal = 32;      //交易类型：0：公开，1:私有
  int32 dealmodel = 33;        //交易模式 0 可多次交易 1 一次交易完成
  string predictmoney = 34;      //预计到账
  string resttime = 35;        //一笔吃挂单取消后需要一个休息时间，才可以重新上架
  string dealmonery = 36;      //挂自动购买时 购买人民币数量
  string dealcount = 37;        //挂自动购买时， 已派的订单数量
  int32 withdrawtype = 38;      //提现类型 0 otc 1 代付
  int32 coinid = 39;          //币种ID
  string coinname = 40;        //币种名字
  int32 isexternal = 41;        //是否外部渠道 0-不是 1-是
  string unsoldordermoney = 42;    //未成交订单法币数量
  string dealordermoney = 43;    //成交订单法币数量
  int32 externalorderuserid = 44;  //外部订单派给拿给用户
  int32 isassigned = 46;        //是否已重交易所拿到订单
  int32 istake = 47;          //是否接单 0 接单 1 不接单
  int32 wallettype = 48;        //钱包类型 0 基础钱包 1 币付钱包
}

// 每产生一笔交易，在订单表中，就会有一个记录
message CustomerOrderInfo {
  int32 dealid = 1;   //交易ID
  int32 type = 2;    //交易类型
  int32 vendororderid = 3;    //币商挂单ID
  int32 customeruserid = 4;    //顾客的userid
  int32 vendoruserid = 5;     //币商ID
  string price = 6;
  string amount = 7;
  string money = 8;  //成交金额
  int32 status = 9;   //状态
  string merchantorderid = 10;    //所关联的商户的定单ID
  string feerate = 11;    //费率
  string fee = 12;   //手续费
  repeated string proofurl = 13;   //付款证明
  string paytypelist = 14;   //支付方式,在钱包以及钱包的交易中，如果卖家提供了两个支付方式，那么就应该把两个支付方式都显示出来
  string payidlist = 15;    //对应支付表中配置的ID
  string createtime = 16;   //创建时间
  string paytime = 17;   //支付时间
  string passtime = 18;   //放行时间
  string channel = 19;    //渠道号
  string publicprice = 20;    //交易所中提供的价格类型
  int32 fromtype = 21;     //订单的来源类型，1：普通订单，2：三方订单，3：商户提币订单
  string customerusernickname = 22;    //顾客的昵称
  string vendorusernickname = 23;     //币商的昵称
  string notifyurl = 24;     //商户客户下的订单的时候，传入的通知回调
  string body = 25;     //商户客户的订单，给商户返回的参数
  string sellfeerate = 26;     //卖方费率，有需要，在商户卖币提现的时候，需要收取卖方费率
  string sellfee = 27;      //费率总额
  string canceltime = 28;     //取消时间
  string buyfeerate = 29;     //买房费率
  string buyfee = 30;      //费率总额
  string updatetime = 31;      //更新时间
  int32 dealtype = 32;      //100=币商交易，200=商户用户，300=商户自己
  string getamount = 33;      //到账金额
  string income = 34;      //收益
  int32 withdrawtype = 35;    //提现类型 0 otc 1 代付
  int32 coinid = 36;          //币种ID
  string coinname = 37;        //币种名字
  int32 isexternal = 38;        //是否外部渠道 0-不是 1-是
  string aftermoney = 39;      //法币到账数量
  string feemoney = 40;        //法币手续费
  string tradeid = 41;        //交易所订单ID
  int32 iswait = 43;          //等待释放的订单
  int32 chaintype = 44;        //链类型
  string chainname = 45;        //链名称
  string chainaddr = 46;        //链地址
  int32 owership = 47;        //0-系统地址 1-商户自备地址
  int32 wallettype = 48;        //钱包类型 0 基础钱包 1 币付钱包
}

// 申诉的详情
message apppealinfo {
  int32 appealid = 1;        //申诉ID
  int32 fromuserid = 2;        //申诉人userId
  string fromusernickname = 3;   //申诉人昵称
  int32 touserid = 4;        //被申诉人userId
  string tousernickname = 5;      //被申诉人昵称
  int32 orderid = 6;          //相关联的订单id
  string reason = 7;        //申诉理由
  string description = 8;      //详细描述
  string proofurl = 9;      //相关凭证url
  string replyreason = 10;    //回复理由
  string replydescription = 11;  //回复详细描述
  string replyproofurl = 12;    //回复相关凭证
  string replytime = 13;      //回复时间
  string sysremark = 14;      //客服备注
  int32 status = 15;        //状态(0-新建,1-关闭,2-客服取消,3-客服放行)
  int32 needaudit = 16;        //需要审核 (0-不需要, 1-需要) 默认0
  string createtime = 17;      //创建时间
}

// 币币交易详情
message CryptoRecord {
  int32 id = 1;          //id
  string txid = 2;          //交易id
  int32 txtype = 3;        //交易类型(1-转入/2-转出)
  string chainid = 4;        //所属公链
  string txdata = 5;          //交易信息
  string fromaddr = 6;        //发起地址
  string toaddr = 7;        //目标地址
  string amount = 8;        //数量
  string txfee = 9;        //交易手续费
  int32 txstatus = 10;      //交易状态
  string txtime = 11;      //交易时间
  string blockhash = 12;      //区块hash
  int32 recdstatus = 13;      //记录状态 101-提币中 102-审核中 103-已驳回 104-出款中 105-成功
  string remark = 14;        //备注
  string createtime = 15;      //创建时间
  int32 specifictype = 16;    //详细类型 1-充币 2-提币 3-转入 4-转账
  int32 coinid = 17;        //币种ID
  string coinname = 18;      //币种名字
  int32 addrtype = 19;      //地址类型
  string addrname = 20;      //地址名字
}

message incomeexpendituredetail {
  int32 detailtype = 1;      //0 全部 1 购买 2出售 3充值 4 提现 5提币 6充币 7转入 8转出 9收益 10 手续费 11系统补款 12系统扣款
  string amount = 2;      //交易数量
  string afteramount = 3;    //余额
  string createtime = 4;      //创建时间
}

message WalletOrder {
  string order_id = 1;          //订单号
  string channel = 2;          //渠道号
  int32 userid = 3;            //用户id
  string nickname = 4;          //用户昵称
  string currency_type = 5;        //币种
  string amount = 6;            //币数量
  string actualamount = 7;        //实际数量
  string trusteeship = 8;        //冷钱包地址
  string usertxid = 9;          //用户上传TXID
  string create = 10;          //创建时间
  string remarks = 11;          //备注
  int32 status = 12;            //0-未到账 1-已到账
  string coinid = 13;          //币种ID
  string coinname = 14;          //币种名字
  int32 addrtype = 15;          //地址类型
  string addrname = 16;          //地址名字
}

// 托管钱包充值记录
message WalletTransInRequest {
  int32 userid = 1;
  int32 pagenum = 2;
  int32 pagesize = 3;
  int32 ordertype = 4;                  //订单类型 0 全部
}
message WalletTransInResponse {
  int32 result = 1;
  string msg = 2;
  int32 pagenum = 3;
  int32 pagesize = 4;
  int32 ordertype = 5;                  //订单类型 0 全部
  repeated WalletOrder infolist = 6;              //订单信息列表
}

// 提币转账手续
message WithdrawFeeRequest {
  int32 userid = 1;
  int32 coinid = 2;                    //币种ID
  int32 chaintype = 3;                  //链类型,区块类型 101-erc 102-trc
}
message WithdrawFeeResponse {
  int32 result = 1;
  string msg = 2;
  string withdrawalfee = 3;                //提币手续费
  string minwithdrawalcount = 4;              //最小提现
  string maxwithdrawalcount = 5;              //最大提现
  string transferfee = 6;                //转账手续费
  string mintransfercount = 7;              //最小转账
  string maxtransfercount = 8;              //最大转账
  int32 coinid = 9;                    //币种ID
  int32 chaintype = 10;                  //链类型,区块类型 101-erc 102-trc
}


// 查询是否站内地址
message CheckWalletAddressRequest {
  string adds = 1;
}
message CheckWalletAddressResponse {
  int32 result = 1;
  string msg = 2;
  string adds = 3;
  int32 ret = 4;                      // 0 站内地址 1 站外地址
}

// 通知客户端新增挂单
message UpdateVendorOrder {
  int32 result = 1;
  string msg = 2;
  repeated VendorOrder volist = 3;
}


// 顾客订单列表(查看币商的挂单)
message CustomerOrdersRequest {
  int32 userid = 1;
  int32 ordertype = 2;          //0 买单 1 卖单
  int32 pagenum = 3;          //页数。每页显示10个。
  int32 pagesize = 4;      //一页多少条
  int32 coinid = 5;          //币种ID
}
message CustomerOrdersResponse {
  int32 result = 1;
  string msg = 2;
  repeated VendorOrder volist = 3;    //挂单信息
  int32 ordertype = 4;          //0 买单 1 卖单
  int32 pagenum = 5;          //页数。每页显示10个。
  int32 pagesize = 6;        //一页多少条
  int32 coinid = 7;          //币种ID
}

// 查看自己的挂单
message MyOrderListRequest {
  int32 userid = 1;
  int32 ordertype = 2;      //0 挂买  1 挂卖
  int32 pagenum = 3;      //页码
  int32 pagesize = 4;      //一页多少条
}
message MyOrderListResponse {
  int32 result = 1;
  int32 msg = 2;
  repeated VendorOrder volist = 3;
  repeated int32 dealnum = 4;        //正在交易中的数量
  int32 ordertype = 5;      //0 挂买  1 挂卖
  int32 pagenum = 6;      //页码
  int32 pagesize = 7;      //一页多少条
}

// 币币交易记录
message WithdrawRecordsRequest {
  int32 userid = 1;
  int32 ordertype = 2;                //操作类型， 0-全部 1-充币 2-提币 3-转入 4-转账
  int32 pagenum = 3;                //页码
  int32 pagesize = 4;                //一页多少条
}
message WithdrawRecordsResponse {
  int32 result = 1;
  string msg = 2;
  int32 userid = 3;
  int32 ordertype = 4;                //操作类型， 0-全部 1-充币 2-提币 3-转入 4-转账
  int32 pagenum = 5;                //页码
  int32 pagesize = 6;                //一页多少条
  repeated CryptoRecord recordinfo = 7;        //币币交易详情列表
}

// 提币/转账
message WithdrawRequest {
  int32 userid = 1;
  int32 ordertype = 2;                //订单类型， 1-提币 2-转账
  string toaddress = 3;                //提币地址
  int32 currencytype = 4;              //货币类型  2003
  string currencyamount = 5;              //货币数量
  string subject = 6;                //暂时不知道是什么
  string version = 7;                //暂时不知道是什么
  string notifyurl = 8;                //暂时不知道是什么
  string body = 9;                  //暂时不知道是什么
  string outtradeno = 10;              //暂时不知道是什么
  string fundpwd = 11;                //资金密码
  int32 addrtype = 12;                //地址类型
  int32 touserid = 13;                //目标用户
}
message WithdrawResponse {
  int32 result = 1;
  string msg = 2;
}
