# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: user.proto
# Protobuf Python Version: 6.32.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    32,
    0,
    '',
    'user.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\nuser.proto\x12\x04user\"\xd8\n\n\x08UserInfo\x12\x0e\n\x06userid\x18\x01 \x01(\x05\x12\x0b\n\x03\x63id\x18\x02 \x01(\t\x12\x0f\n\x07\x61\x63\x63ount\x18\x03 \x01(\t\x12\x10\n\x08password\x18\x04 \x01(\t\x12\x10\n\x08nickname\x18\x05 \x01(\t\x12\x12\n\nethaddress\x18\x06 \x01(\t\x12\r\n\x05\x66\x61\x63\x65\x31\x18\x07 \x01(\t\x12\x0f\n\x07regdate\x18\x08 \x01(\t\x12\x0b\n\x03sex\x18\t \x01(\x05\x12\x0b\n\x03\x61ge\x18\n \x01(\x05\x12\r\n\x05\x65mail\x18\x0b \x01(\t\x12\x10\n\x08phonenum\x18\x0c \x01(\t\x12\x11\n\tethamount\x18\r \x01(\t\x12\x15\n\rercusdtamount\x18\x0e \x01(\t\x12\x19\n\x11\x65rcusdtlockamount\x18\x0f \x01(\t\x12\x0f\n\x07\x63hannel\x18\x10 \x01(\t\x12\x12\n\ninvitecode\x18\x11 \x01(\t\x12\x10\n\x08\x62indcode\x18\x12 \x01(\t\x12\x0c\n\x04imei\x18\x13 \x01(\t\x12\x0f\n\x07\x64\x65vname\x18\x14 \x01(\t\x12\x0f\n\x07macname\x18\x15 \x01(\t\x12\x12\n\nmobiletype\x18\x16 \x01(\x05\x12\x10\n\x08lasttime\x18\x17 \x01(\x05\x12\x12\n\npenulttime\x18\x18 \x01(\x05\x12\r\n\x05isban\x18\x19 \x01(\x05\x12\x13\n\x0b\x64\x65scription\x18\x1a \x01(\t\x12\x11\n\tblacklist\x18\x1b \x01(\x05\x12\n\n\x02ip\x18\x1c \x01(\t\x12\x10\n\x08province\x18\x1d \x01(\t\x12\x0c\n\x04\x63ity\x18\x1e \x01(\t\x12\x10\n\x08\x62indtype\x18\x1f \x01(\x05\x12\x16\n\x0eisfundpassword\x18  \x01(\x05\x12\x13\n\x0bpaytypelist\x18! \x03(\x05\x12\r\n\x05\x61gent\x18\" \x01(\x05\x12\x10\n\x08usertype\x18# \x01(\x05\x12\x10\n\x08\x61utosell\x18$ \x01(\x05\x12\x0e\n\x06minbuy\x18% \x01(\t\x12\x0e\n\x06maxbuy\x18& \x01(\t\x12\x0f\n\x07minsell\x18\' \x01(\t\x12\x0f\n\x07maxsell\x18( \x01(\t\x12\x0e\n\x06islock\x18) \x01(\x05\x12\x11\n\tishangbuy\x18* \x01(\x05\x12\x12\n\nishangsell\x18+ \x01(\x05\x12\x10\n\x08\x63ommtype\x18, \x01(\x05\x12\x15\n\rprohibitlogin\x18- \x01(\x05\x12\x15\n\risacceptorder\x18. \x01(\x05\x12\x14\n\x0c\x64\x65\x61llasttime\x18/ \x01(\t\x12\x0f\n\x07\x62\x61ntime\x18\x30 \x01(\t\x12\x11\n\tisinvited\x18\x31 \x01(\x05\x12\r\n\x05isotc\x18\x32 \x01(\x05\x12\x13\n\x0bisbehalfpay\x18\x33 \x01(\x05\x12\x13\n\x0b\x65rcfcamount\x18\x34 \x01(\t\x12\x17\n\x0f\x65rcfclockamount\x18\x35 \x01(\t\x12\x14\n\x0c\x64\x65\x61lcointype\x18\x36 \x01(\x05\x12\x13\n\x0b\x65nergyvalue\x18\x37 \x01(\t\x12\x14\n\x0cweightsvalue\x18\x38 \x01(\t\x12\x10\n\x08teamname\x18\x39 \x01(\t\x12\x12\n\nplatformid\x18: \x01(\x05\x12\x19\n\x11isteamacceptorder\x18; \x01(\x05\x12\x18\n\x10preauthorization\x18< \x01(\t\x12\x13\n\x0b\x63oinpayrate\x18= \x01(\t\x12\x12\n\ncoinpayerc\x18> \x01(\x05\x12\x12\n\ncoinpaytrc\x18? \x01(\x05\x12\x14\n\x0c\x61llowsysaddr\x18@ \x01(\x05\x12\x11\n\tsysercmin\x18\x41 \x01(\t\x12\x19\n\x11\x63oinpayusdtamount\x18\x42 \x01(\t\x12\x1d\n\x15\x63oinpayusdtlockamount\x18\x43 \x01(\t\x12\x0f\n\x07ischeck\x18\x44 \x01(\x05\x12\x1e\n\x16\x65xtractcurrencyrateerc\x18\x45 \x01(\t\x12\x1e\n\x16\x65xtractcurrencyratetrc\x18\x46 \x01(\t\"\xa3\x01\n\x08\x63oininfo\x12\x0e\n\x06\x63oinid\x18\x01 \x01(\x05\x12\x10\n\x08\x63oinname\x18\x02 \x01(\t\x12\x12\n\ncoinamount\x18\x03 \x01(\t\x12\x16\n\x0e\x63oinlockamount\x18\x04 \x01(\t\x12\x16\n\x0e\x63oinfreeamount\x18\x05 \x01(\t\x12\x0e\n\x06status\x18\x06 \x01(\x05\x12\x0f\n\x07\x62uyrate\x18\x07 \x01(\t\x12\x10\n\x08sellrate\x18\x08 \x01(\t\"?\n\x08\x63oinaddr\x12\x10\n\x08\x61\x64\x64rtype\x18\x01 \x01(\x05\x12\x10\n\x08\x61\x64\x64rname\x18\x02 \x01(\t\x12\x0f\n\x07\x61\x64\x64ress\x18\x03 \x01(\t\"\x82\x02\n\x07Payment\x12\x0f\n\x07paytype\x18\x01 \x01(\x05\x12\x0f\n\x07\x61\x63\x63ount\x18\x02 \x01(\t\x12\r\n\x05payee\x18\x03 \x01(\t\x12\x0e\n\x06qrcode\x18\x04 \x01(\t\x12\x10\n\x08\x62\x61nkname\x18\x05 \x01(\t\x12\x10\n\x08\x62\x61nkaddr\x18\x06 \x01(\t\x12\x13\n\x0bsinglelimit\x18\x07 \x01(\t\x12\x10\n\x08\x64\x61ylimit\x18\x08 \x01(\t\x12\n\n\x02id\x18\t \x01(\x05\x12\x0e\n\x06status\x18\n \x01(\x05\x12\x0e\n\x06userid\x18\x0b \x01(\x05\x12\x12\n\ntodaymoney\x18\x0c \x01(\t\x12\x15\n\rfourthpartyid\x18\r \x01(\x05\x12\x14\n\x0c\x64\x65\x61llasttime\x18\x0e \x01(\t\"\xf5\x01\n\x07subinfo\x12\x0e\n\x06userid\x18\x01 \x01(\x05\x12\x10\n\x08nickname\x18\x02 \x01(\t\x12\x17\n\x0fteamperformance\x18\x03 \x01(\t\x12\x15\n\rmyperformance\x18\x04 \x01(\t\x12\x15\n\risacceptorder\x18\x05 \x01(\x05\x12\x15\n\rprohibitlogin\x18\x06 \x01(\x05\x12\x13\n\x0bpaytypelist\x18\x07 \x03(\t\x12\x13\n\x0bpayratelist\x18\x08 \x03(\t\x12\x10\n\x08\x62uycount\x18\t \x01(\t\x12\x13\n\x0b\x62uyratelist\x18\n \x03(\t\x12\x19\n\x11\x62\x65halfbuyratelist\x18\x0b \x03(\t\"\xb2\x02\n\x10twalletorderinfo\x12\x10\n\x08order_id\x18\x01 \x01(\t\x12\x0f\n\x07\x63hannel\x18\x02 \x01(\t\x12\x0e\n\x06userid\x18\x03 \x01(\x05\x12\x10\n\x08nickname\x18\x04 \x01(\t\x12\x15\n\rcurrency_type\x18\x05 \x01(\t\x12\x0e\n\x06\x61mount\x18\x06 \x01(\t\x12\x14\n\x0c\x61\x63tualamount\x18\x07 \x01(\t\x12\x13\n\x0btrusteeship\x18\x08 \x01(\t\x12\x10\n\x08usertxid\x18\t \x01(\t\x12\x0e\n\x06\x63reate\x18\n \x01(\t\x12\x0f\n\x07remarks\x18\x0b \x01(\t\x12\x0e\n\x06status\x18\x0c \x01(\x05\x12\x0e\n\x06\x63oinid\x18\r \x01(\t\x12\x10\n\x08\x63oinname\x18\x0e \x01(\t\x12\x10\n\x08\x61\x64\x64rtype\x18\x0f \x01(\x05\x12\x10\n\x08\x61\x64\x64rname\x18\x10 \x01(\t\"!\n\x0fPaymentsRequest\x12\x0e\n\x06userid\x18\x01 \x01(\x05\"O\n\x10PaymentsResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12\x1e\n\x07paylist\x18\x03 \x03(\x0b\x32\r.user.Payment\"Z\n\x16UpdateUserInfoResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12\x10\n\x08typelist\x18\x03 \x03(\t\x12\x11\n\tvaluelist\x18\x04 \x03(\t\"\xaf\x01\n\x11\x41\x64\x64PaymentRequest\x12\x0e\n\x06userid\x18\x01 \x01(\x05\x12\x0f\n\x07\x61\x63\x63ount\x18\x02 \x01(\t\x12\r\n\x05payee\x18\x03 \x01(\t\x12\x0e\n\x06qrcode\x18\x04 \x01(\t\x12\x10\n\x08\x62\x61nkname\x18\x05 \x01(\t\x12\x10\n\x08\x62\x61nkaddr\x18\x06 \x01(\t\x12\x13\n\x0bsinglelimit\x18\x07 \x01(\t\x12\x10\n\x08\x64\x61ylimit\x18\x08 \x01(\t\x12\x0f\n\x07paytype\x18\t \x01(\x05\"1\n\x12\x41\x64\x64PaymentResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\"\xad\x01\n\x11IncomeExpenditure\x12\x17\n\ndetailtype\x18\x01 \x01(\x05H\x00\x88\x01\x01\x12\x13\n\x06\x61mount\x18\x02 \x01(\tH\x01\x88\x01\x01\x12\x18\n\x0b\x61\x66teramount\x18\x03 \x01(\tH\x02\x88\x01\x01\x12\x17\n\ncreatetime\x18\x04 \x01(\tH\x03\x88\x01\x01\x42\r\n\x0b_detailtypeB\t\n\x07_amountB\x0e\n\x0c_afteramountB\r\n\x0b_createtime\"\xc8\x01\n\x18IncomeExpenditureRequest\x12\x13\n\x06userid\x18\x01 \x01(\x05H\x00\x88\x01\x01\x12\x17\n\ndetailtype\x18\x02 \x01(\x05H\x01\x88\x01\x01\x12\x14\n\x07pagenum\x18\x03 \x01(\x05H\x02\x88\x01\x01\x12\x15\n\x08pagesize\x18\x04 \x01(\x05H\x03\x88\x01\x01\x12\x13\n\x06\x63oinid\x18\x05 \x01(\x05H\x04\x88\x01\x01\x42\t\n\x07_useridB\r\n\x0b_detailtypeB\n\n\x08_pagenumB\x0b\n\t_pagesizeB\t\n\x07_coinid\"\xe0\x02\n\x19IncomeExpenditureResponse\x12\x13\n\x06result\x18\x01 \x01(\x05H\x00\x88\x01\x01\x12\x10\n\x03msg\x18\x02 \x01(\tH\x01\x88\x01\x01\x12\x17\n\ndetailtype\x18\x03 \x01(\x05H\x02\x88\x01\x01\x12\x14\n\x07pagenum\x18\x04 \x01(\x05H\x03\x88\x01\x01\x12\x15\n\x08pagesize\x18\x05 \x01(\x05H\x04\x88\x01\x01\x12\x17\n\nparameter1\x18\x06 \x01(\tH\x05\x88\x01\x01\x12\x17\n\nparameter2\x18\x07 \x01(\tH\x06\x88\x01\x01\x12+\n\ndetaillist\x18\x08 \x03(\x0b\x32\x17.user.IncomeExpenditure\x12\x13\n\x06\x63oinid\x18\t \x01(\x05H\x07\x88\x01\x01\x42\t\n\x07_resultB\x06\n\x04_msgB\r\n\x0b_detailtypeB\n\n\x08_pagenumB\x0b\n\t_pagesizeB\r\n\x0b_parameter1B\r\n\x0b_parameter2B\t\n\x07_coinidb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'user_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_USERINFO']._serialized_start=21
  _globals['_USERINFO']._serialized_end=1389
  _globals['_COININFO']._serialized_start=1392
  _globals['_COININFO']._serialized_end=1555
  _globals['_COINADDR']._serialized_start=1557
  _globals['_COINADDR']._serialized_end=1620
  _globals['_PAYMENT']._serialized_start=1623
  _globals['_PAYMENT']._serialized_end=1881
  _globals['_SUBINFO']._serialized_start=1884
  _globals['_SUBINFO']._serialized_end=2129
  _globals['_TWALLETORDERINFO']._serialized_start=2132
  _globals['_TWALLETORDERINFO']._serialized_end=2438
  _globals['_PAYMENTSREQUEST']._serialized_start=2440
  _globals['_PAYMENTSREQUEST']._serialized_end=2473
  _globals['_PAYMENTSRESPONSE']._serialized_start=2475
  _globals['_PAYMENTSRESPONSE']._serialized_end=2554
  _globals['_UPDATEUSERINFORESPONSE']._serialized_start=2556
  _globals['_UPDATEUSERINFORESPONSE']._serialized_end=2646
  _globals['_ADDPAYMENTREQUEST']._serialized_start=2649
  _globals['_ADDPAYMENTREQUEST']._serialized_end=2824
  _globals['_ADDPAYMENTRESPONSE']._serialized_start=2826
  _globals['_ADDPAYMENTRESPONSE']._serialized_end=2875
  _globals['_INCOMEEXPENDITURE']._serialized_start=2878
  _globals['_INCOMEEXPENDITURE']._serialized_end=3051
  _globals['_INCOMEEXPENDITUREREQUEST']._serialized_start=3054
  _globals['_INCOMEEXPENDITUREREQUEST']._serialized_end=3254
  _globals['_INCOMEEXPENDITURERESPONSE']._serialized_start=3257
  _globals['_INCOMEEXPENDITURERESPONSE']._serialized_end=3609
# @@protoc_insertion_point(module_scope)
