# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: trade.proto
# Protobuf Python Version: 6.32.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    32,
    0,
    '',
    'trade.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()

import app.modules.ucoin.py_protobufs.order_pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\x0btrade.proto\x12\x05trade\x1a\x0border.proto\"2\n\x10TodayFlowRequest\x12\x0e\n\x06userid\x18\x01 \x01(\x05\x12\x0e\n\x06\x63oinid\x18\x02 \x01(\x05\"\xa5\x01\n\x11TodayFlowResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12\x11\n\ttodaySell\x18\x03 \x01(\t\x12\x10\n\x08todaybuy\x18\x04 \x01(\t\x12\x13\n\x0b\x61veragetime\x18\x05 \x01(\x05\x12\x13\n\x0bsuccessrate\x18\x06 \x01(\t\x12\x14\n\x0c\x65xchangerate\x18\x07 \x01(\t\x12\x0e\n\x06\x63oinid\x18\x08 \x01(\x05\"v\n\x12TradeRecordRequest\x12\x0e\n\x06userid\x18\x01 \x01(\x05\x12\x0c\n\x04type\x18\x02 \x01(\x05\x12\x0e\n\x06status\x18\x03 \x01(\x05\x12\x0f\n\x07paytype\x18\x04 \x01(\x05\x12\x0f\n\x07pagenum\x18\x05 \x01(\x05\x12\x10\n\x08pagesize\x18\x06 \x01(\x05\"\xde\x01\n\x13TradeRecordResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\x05\x12\x0e\n\x06status\x18\x04 \x01(\x05\x12\x0f\n\x07paytype\x18\x05 \x01(\x05\x12\x0f\n\x07pagenum\x18\x06 \x01(\x05\x12\x10\n\x08pagesize\x18\x07 \x01(\x05\x12*\n\x08infolist\x18\x08 \x03(\x0b\x32\x18.order.CustomerOrderInfo\x12\x12\n\nordercount\x18\t \x01(\x05\x12\x18\n\x10\x66inishordercount\x18\n \x01(\x05\"&\n\x14PendingOrdersRequest\x12\x0e\n\x06userid\x18\x01 \x01(\x05\"`\n\x15PendingOrdersResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12*\n\x08infolist\x18\x03 \x03(\x0b\x32\x18.order.CustomerOrderInfo\"%\n\x13\x45xchangeRateRequest\x12\x0e\n\x06userid\x18\x01 \x01(\x05\"V\n\x14\x45xchangeRateResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12\x0f\n\x07\x62uyrate\x18\x03 \x01(\t\x12\x10\n\x08sellrate\x18\x04 \x01(\t\"\xcc\x01\n\x10HangOrderRequest\x12\x0e\n\x06userid\x18\x01 \x01(\x05\x12\x0c\n\x04type\x18\x02 \x01(\x05\x12\x11\n\tpricetype\x18\x03 \x01(\x05\x12\r\n\x05price\x18\x04 \x01(\t\x12\x0e\n\x06\x61mount\x18\x05 \x01(\t\x12\x10\n\x08minmoney\x18\x06 \x01(\t\x12\x10\n\x08maxmoney\x18\x07 \x01(\t\x12\x12\n\nautoswitch\x18\x08 \x01(\x05\x12\x0f\n\x07message\x18\t \x01(\t\x12\x0f\n\x07\x66undpwd\x18\n \x01(\t\x12\x0e\n\x06\x63oinid\x18\x0b \x01(\x05\"T\n\x11HangOrderResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12\"\n\x06voInfo\x18\x03 \x01(\x0b\x32\x12.order.VendorOrder\"\x93\x01\n\x0b\x44\x65\x61lRequest\x12\x0e\n\x06userid\x18\x01 \x01(\x05\x12\x0e\n\x06hangid\x18\x02 \x01(\x05\x12\x10\n\x08\x64\x65\x61ltype\x18\x03 \x01(\x05\x12\x0e\n\x06\x61mount\x18\x04 \x01(\t\x12\x0f\n\x07\x66undPwd\x18\x05 \x01(\t\x12\r\n\x05price\x18\x06 \x01(\t\x12\x11\n\tmoneytype\x18\x07 \x01(\x05\x12\x0f\n\x07paytype\x18\x08 \x01(\x05\"U\n\x0c\x44\x65\x61lResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12(\n\x06\x63oinfo\x18\x03 \x01(\x0b\x32\x18.order.CustomerOrderInfo\"\x99\x01\n\x10QuickDealRequest\x12\x0e\n\x06userid\x18\x01 \x01(\x05\x12\x0e\n\x06hangid\x18\x02 \x01(\x05\x12\x10\n\x08\x64\x65\x61ltype\x18\x03 \x01(\x05\x12\x0e\n\x06\x61mount\x18\x04 \x01(\t\x12\x0f\n\x07\x66undPwd\x18\x05 \x01(\t\x12\x11\n\tmoneytype\x18\x06 \x01(\x05\x12\x0f\n\x07paytype\x18\x07 \x01(\x05\x12\x0e\n\x06\x63ionid\x18\x08 \x01(\x05\"Z\n\x11QuickDealResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12(\n\x06\x63oinfo\x18\x03 \x01(\x0b\x32\x18.order.CustomerOrderInfob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'trade_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
    DESCRIPTOR._loaded_options = None
    _globals['_TODAYFLOWREQUEST']._serialized_start = 35
    _globals['_TODAYFLOWREQUEST']._serialized_end = 85
    _globals['_TODAYFLOWRESPONSE']._serialized_start = 88
    _globals['_TODAYFLOWRESPONSE']._serialized_end = 253
    _globals['_TRADERECORDREQUEST']._serialized_start = 255
    _globals['_TRADERECORDREQUEST']._serialized_end = 373
    _globals['_TRADERECORDRESPONSE']._serialized_start = 376
    _globals['_TRADERECORDRESPONSE']._serialized_end = 598
    _globals['_PENDINGORDERSREQUEST']._serialized_start = 600
    _globals['_PENDINGORDERSREQUEST']._serialized_end = 638
    _globals['_PENDINGORDERSRESPONSE']._serialized_start = 640
    _globals['_PENDINGORDERSRESPONSE']._serialized_end = 736
    _globals['_EXCHANGERATEREQUEST']._serialized_start = 738
    _globals['_EXCHANGERATEREQUEST']._serialized_end = 775
    _globals['_EXCHANGERATERESPONSE']._serialized_start = 777
    _globals['_EXCHANGERATERESPONSE']._serialized_end = 863
    _globals['_HANGORDERREQUEST']._serialized_start = 866
    _globals['_HANGORDERREQUEST']._serialized_end = 1070
    _globals['_HANGORDERRESPONSE']._serialized_start = 1072
    _globals['_HANGORDERRESPONSE']._serialized_end = 1156
    _globals['_DEALREQUEST']._serialized_start = 1159
    _globals['_DEALREQUEST']._serialized_end = 1306
    _globals['_DEALRESPONSE']._serialized_start = 1308
    _globals['_DEALRESPONSE']._serialized_end = 1393
    _globals['_QUICKDEALREQUEST']._serialized_start = 1396
    _globals['_QUICKDEALREQUEST']._serialized_end = 1549
    _globals['_QUICKDEALRESPONSE']._serialized_start = 1551
    _globals['_QUICKDEALRESPONSE']._serialized_end = 1641
# @@protoc_insertion_point(module_scope)
