# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: order.proto
# Protobuf Python Version: 6.32.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    32,
    0,
    '',
    'order.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0border.proto\x12\x05order\"\x97\x07\n\x0bVendorOrder\x12\x0e\n\x06userid\x18\x01 \x01(\x05\x12\x0e\n\x06hangid\x18\x02 \x01(\x05\x12\x0c\n\x04type\x18\x03 \x01(\x05\x12\x11\n\tpricetype\x18\x04 \x01(\x05\x12\r\n\x05price\x18\x05 \x01(\t\x12\x0e\n\x06\x61mount\x18\x06 \x01(\t\x12\x10\n\x08minmoney\x18\x07 \x01(\t\x12\x10\n\x08maxmoney\x18\x08 \x01(\t\x12\x12\n\nautoswitch\x18\t \x01(\x05\x12\x0f\n\x07message\x18\n \x01(\t\x12\x11\n\tmaxamount\x18\x0b \x01(\t\x12\x14\n\x0c\x65nablestatus\x18\x0c \x01(\x05\x12\x0f\n\x07\x63hannel\x18\r \x01(\t\x12\x10\n\x08nickname\x18\x0e \x01(\t\x12\x16\n\x0eunsoldordernum\x18\x0f \x01(\x05\x12\x14\n\x0c\x64\x65\x61lordernum\x18\x10 \x01(\x05\x12\x19\n\x11unsoldorderamount\x18\x12 \x01(\t\x12\x17\n\x0f\x64\x65\x61lorderamount\x18\x13 \x01(\t\x12\x0f\n\x07paylist\x18\x14 \x03(\x05\x12\x11\n\tcancelnum\x18\x15 \x01(\x05\x12\x10\n\x08usertype\x18\x16 \x01(\x05\x12\x14\n\x0cpayeeaccount\x18\x17 \x01(\t\x12\x11\n\tpayeename\x18\x18 \x01(\t\x12\x11\n\tpayeebank\x18\x19 \x01(\t\x12\x15\n\rpayeebandaddr\x18\x1a \x01(\t\x12\x17\n\x0f\x64\x65\x61l_order_free\x18\x1b \x01(\t\x12\x0c\n\x04\x66ree\x18\x1c \x01(\t\x12\x0f\n\x07\x66\x65\x65Rate\x18\x1d \x03(\t\x12\x12\n\naddfeerate\x18\x1e \x01(\t\x12\x12\n\ncreatetime\x18\x1f \x01(\t\x12\x13\n\x0b\x63hanneldeal\x18  \x01(\x05\x12\x11\n\tdealmodel\x18! \x01(\x05\x12\x14\n\x0cpredictmoney\x18\" \x01(\t\x12\x10\n\x08resttime\x18# \x01(\t\x12\x12\n\ndealmonery\x18$ \x01(\t\x12\x11\n\tdealcount\x18% \x01(\t\x12\x14\n\x0cwithdrawtype\x18& \x01(\x05\x12\x0e\n\x06\x63oinid\x18\' \x01(\x05\x12\x10\n\x08\x63oinname\x18( \x01(\t\x12\x12\n\nisexternal\x18) \x01(\x05\x12\x18\n\x10unsoldordermoney\x18* \x01(\t\x12\x16\n\x0e\x64\x65\x61lordermoney\x18+ \x01(\t\x12\x1b\n\x13\x65xternalorderuserid\x18, \x01(\x05\x12\x12\n\nisassigned\x18. \x01(\x05\x12\x0e\n\x06istake\x18/ \x01(\x05\x12\x12\n\nwallettype\x18\x30 \x01(\x05\"\x88\x07\n\x11\x43ustomerOrderInfo\x12\x0e\n\x06\x64\x65\x61lid\x18\x01 \x01(\x05\x12\x0c\n\x04type\x18\x02 \x01(\x05\x12\x15\n\rvendororderid\x18\x03 \x01(\x05\x12\x16\n\x0e\x63ustomeruserid\x18\x04 \x01(\x05\x12\x14\n\x0cvendoruserid\x18\x05 \x01(\x05\x12\r\n\x05price\x18\x06 \x01(\t\x12\x0e\n\x06\x61mount\x18\x07 \x01(\t\x12\r\n\x05money\x18\x08 \x01(\t\x12\x0e\n\x06status\x18\t \x01(\x05\x12\x17\n\x0fmerchantorderid\x18\n \x01(\t\x12\x0f\n\x07\x66\x65\x65rate\x18\x0b \x01(\t\x12\x0b\n\x03\x66\x65\x65\x18\x0c \x01(\t\x12\x10\n\x08proofurl\x18\r \x03(\t\x12\x13\n\x0bpaytypelist\x18\x0e \x01(\t\x12\x11\n\tpayidlist\x18\x0f \x01(\t\x12\x12\n\ncreatetime\x18\x10 \x01(\t\x12\x0f\n\x07paytime\x18\x11 \x01(\t\x12\x10\n\x08passtime\x18\x12 \x01(\t\x12\x0f\n\x07\x63hannel\x18\x13 \x01(\t\x12\x13\n\x0bpublicprice\x18\x14 \x01(\t\x12\x10\n\x08\x66romtype\x18\x15 \x01(\x05\x12\x1c\n\x14\x63ustomerusernickname\x18\x16 \x01(\t\x12\x1a\n\x12vendorusernickname\x18\x17 \x01(\t\x12\x11\n\tnotifyurl\x18\x18 \x01(\t\x12\x0c\n\x04\x62ody\x18\x19 \x01(\t\x12\x13\n\x0bsellfeerate\x18\x1a \x01(\t\x12\x0f\n\x07sellfee\x18\x1b \x01(\t\x12\x12\n\ncanceltime\x18\x1c \x01(\t\x12\x12\n\nbuyfeerate\x18\x1d \x01(\t\x12\x0e\n\x06\x62uyfee\x18\x1e \x01(\t\x12\x12\n\nupdatetime\x18\x1f \x01(\t\x12\x10\n\x08\x64\x65\x61ltype\x18  \x01(\x05\x12\x11\n\tgetamount\x18! \x01(\t\x12\x0e\n\x06income\x18\" \x01(\t\x12\x14\n\x0cwithdrawtype\x18# \x01(\x05\x12\x0e\n\x06\x63oinid\x18$ \x01(\x05\x12\x10\n\x08\x63oinname\x18% \x01(\t\x12\x12\n\nisexternal\x18& \x01(\x05\x12\x12\n\naftermoney\x18\' \x01(\t\x12\x10\n\x08\x66\x65\x65money\x18( \x01(\t\x12\x0f\n\x07tradeid\x18) \x01(\t\x12\x0e\n\x06iswait\x18+ \x01(\x05\x12\x11\n\tchaintype\x18, \x01(\x05\x12\x11\n\tchainname\x18- \x01(\t\x12\x11\n\tchainaddr\x18. \x01(\t\x12\x10\n\x08owership\x18/ \x01(\x05\x12\x12\n\nwallettype\x18\x30 \x01(\x05\"\xe2\x02\n\x0b\x61pppealinfo\x12\x10\n\x08\x61ppealid\x18\x01 \x01(\x05\x12\x12\n\nfromuserid\x18\x02 \x01(\x05\x12\x18\n\x10\x66romusernickname\x18\x03 \x01(\t\x12\x10\n\x08touserid\x18\x04 \x01(\x05\x12\x16\n\x0etousernickname\x18\x05 \x01(\t\x12\x0f\n\x07orderid\x18\x06 \x01(\x05\x12\x0e\n\x06reason\x18\x07 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x08 \x01(\t\x12\x10\n\x08proofurl\x18\t \x01(\t\x12\x13\n\x0breplyreason\x18\n \x01(\t\x12\x18\n\x10replydescription\x18\x0b \x01(\t\x12\x15\n\rreplyproofurl\x18\x0c \x01(\t\x12\x11\n\treplytime\x18\r \x01(\t\x12\x11\n\tsysremark\x18\x0e \x01(\t\x12\x0e\n\x06status\x18\x0f \x01(\x05\x12\x11\n\tneedaudit\x18\x10 \x01(\x05\x12\x12\n\ncreatetime\x18\x11 \x01(\t\"\xe3\x02\n\x0c\x43ryptoRecord\x12\n\n\x02id\x18\x01 \x01(\x05\x12\x0c\n\x04txid\x18\x02 \x01(\t\x12\x0e\n\x06txtype\x18\x03 \x01(\x05\x12\x0f\n\x07\x63hainid\x18\x04 \x01(\t\x12\x0e\n\x06txdata\x18\x05 \x01(\t\x12\x10\n\x08\x66romaddr\x18\x06 \x01(\t\x12\x0e\n\x06toaddr\x18\x07 \x01(\t\x12\x0e\n\x06\x61mount\x18\x08 \x01(\t\x12\r\n\x05txfee\x18\t \x01(\t\x12\x10\n\x08txstatus\x18\n \x01(\x05\x12\x0e\n\x06txtime\x18\x0b \x01(\t\x12\x11\n\tblockhash\x18\x0c \x01(\t\x12\x12\n\nrecdstatus\x18\r \x01(\x05\x12\x0e\n\x06remark\x18\x0e \x01(\t\x12\x12\n\ncreatetime\x18\x0f \x01(\t\x12\x14\n\x0cspecifictype\x18\x10 \x01(\x05\x12\x0e\n\x06\x63oinid\x18\x11 \x01(\x05\x12\x10\n\x08\x63oinname\x18\x12 \x01(\t\x12\x10\n\x08\x61\x64\x64rtype\x18\x13 \x01(\x05\x12\x10\n\x08\x61\x64\x64rname\x18\x14 \x01(\t\"f\n\x17incomeexpendituredetail\x12\x12\n\ndetailtype\x18\x01 \x01(\x05\x12\x0e\n\x06\x61mount\x18\x02 \x01(\t\x12\x13\n\x0b\x61\x66teramount\x18\x03 \x01(\t\x12\x12\n\ncreatetime\x18\x04 \x01(\t\"\xad\x02\n\x0bWalletOrder\x12\x10\n\x08order_id\x18\x01 \x01(\t\x12\x0f\n\x07\x63hannel\x18\x02 \x01(\t\x12\x0e\n\x06userid\x18\x03 \x01(\x05\x12\x10\n\x08nickname\x18\x04 \x01(\t\x12\x15\n\rcurrency_type\x18\x05 \x01(\t\x12\x0e\n\x06\x61mount\x18\x06 \x01(\t\x12\x14\n\x0c\x61\x63tualamount\x18\x07 \x01(\t\x12\x13\n\x0btrusteeship\x18\x08 \x01(\t\x12\x10\n\x08usertxid\x18\t \x01(\t\x12\x0e\n\x06\x63reate\x18\n \x01(\t\x12\x0f\n\x07remarks\x18\x0b \x01(\t\x12\x0e\n\x06status\x18\x0c \x01(\x05\x12\x0e\n\x06\x63oinid\x18\r \x01(\t\x12\x10\n\x08\x63oinname\x18\x0e \x01(\t\x12\x10\n\x08\x61\x64\x64rtype\x18\x0f \x01(\x05\x12\x10\n\x08\x61\x64\x64rname\x18\x10 \x01(\t\"\\\n\x14WalletTransInRequest\x12\x0e\n\x06userid\x18\x01 \x01(\x05\x12\x0f\n\x07pagenum\x18\x02 \x01(\x05\x12\x10\n\x08pagesize\x18\x03 \x01(\x05\x12\x11\n\tordertype\x18\x04 \x01(\x05\"\x90\x01\n\x15WalletTransInResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12\x0f\n\x07pagenum\x18\x03 \x01(\x05\x12\x10\n\x08pagesize\x18\x04 \x01(\x05\x12\x11\n\tordertype\x18\x05 \x01(\x05\x12$\n\x08infolist\x18\x06 \x03(\x0b\x32\x12.order.WalletOrder\"G\n\x12WithdrawFeeRequest\x12\x0e\n\x06userid\x18\x01 \x01(\x05\x12\x0e\n\x06\x63oinid\x18\x02 \x01(\x05\x12\x11\n\tchaintype\x18\x03 \x01(\x05\"\xed\x01\n\x13WithdrawFeeResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12\x15\n\rwithdrawalfee\x18\x03 \x01(\t\x12\x1a\n\x12minwithdrawalcount\x18\x04 \x01(\t\x12\x1a\n\x12maxwithdrawalcount\x18\x05 \x01(\t\x12\x13\n\x0btransferfee\x18\x06 \x01(\t\x12\x18\n\x10mintransfercount\x18\x07 \x01(\t\x12\x18\n\x10maxtransfercount\x18\x08 \x01(\t\x12\x0e\n\x06\x63oinid\x18\t \x01(\x05\x12\x11\n\tchaintype\x18\n \x01(\x05\")\n\x19\x43heckWalletAddressRequest\x12\x0c\n\x04\x61\x64\x64s\x18\x01 \x01(\t\"T\n\x1a\x43heckWalletAddressResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12\x0c\n\x04\x61\x64\x64s\x18\x03 \x01(\t\x12\x0b\n\x03ret\x18\x04 \x01(\x05\"T\n\x11UpdateVendorOrder\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12\"\n\x06volist\x18\x03 \x03(\x0b\x32\x12.order.VendorOrder\"m\n\x15\x43ustomerOrdersRequest\x12\x0e\n\x06userid\x18\x01 \x01(\x05\x12\x11\n\tordertype\x18\x02 \x01(\x05\x12\x0f\n\x07pagenum\x18\x03 \x01(\x05\x12\x10\n\x08pagesize\x18\x04 \x01(\x05\x12\x0e\n\x06\x63oinid\x18\x05 \x01(\x05\"\x9f\x01\n\x16\x43ustomerOrdersResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12\"\n\x06volist\x18\x03 \x03(\x0b\x32\x12.order.VendorOrder\x12\x11\n\tordertype\x18\x04 \x01(\x05\x12\x0f\n\x07pagenum\x18\x05 \x01(\x05\x12\x10\n\x08pagesize\x18\x06 \x01(\x05\x12\x0e\n\x06\x63oinid\x18\x07 \x01(\x05\"Z\n\x12MyOrderListRequest\x12\x0e\n\x06userid\x18\x01 \x01(\x05\x12\x11\n\tordertype\x18\x02 \x01(\x05\x12\x0f\n\x07pagenum\x18\x03 \x01(\x05\x12\x10\n\x08pagesize\x18\x04 \x01(\x05\"\x9d\x01\n\x13MyOrderListResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\x05\x12\"\n\x06volist\x18\x03 \x03(\x0b\x32\x12.order.VendorOrder\x12\x0f\n\x07\x64\x65\x61lnum\x18\x04 \x03(\x05\x12\x11\n\tordertype\x18\x05 \x01(\x05\x12\x0f\n\x07pagenum\x18\x06 \x01(\x05\x12\x10\n\x08pagesize\x18\x07 \x01(\x05\"^\n\x16WithdrawRecordsRequest\x12\x0e\n\x06userid\x18\x01 \x01(\x05\x12\x11\n\tordertype\x18\x02 \x01(\x05\x12\x0f\n\x07pagenum\x18\x03 \x01(\x05\x12\x10\n\x08pagesize\x18\x04 \x01(\x05\"\xa5\x01\n\x17WithdrawRecordsResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12\x0e\n\x06userid\x18\x03 \x01(\x05\x12\x11\n\tordertype\x18\x04 \x01(\x05\x12\x0f\n\x07pagenum\x18\x05 \x01(\x05\x12\x10\n\x08pagesize\x18\x06 \x01(\x05\x12\'\n\nrecordinfo\x18\x07 \x03(\x0b\x32\x13.order.CryptoRecord\"\x81\x02\n\x0fWithdrawRequest\x12\x0e\n\x06userid\x18\x01 \x01(\x05\x12\x11\n\tordertype\x18\x02 \x01(\x05\x12\x11\n\ttoaddress\x18\x03 \x01(\t\x12\x14\n\x0c\x63urrencytype\x18\x04 \x01(\x05\x12\x16\n\x0e\x63urrencyamount\x18\x05 \x01(\t\x12\x0f\n\x07subject\x18\x06 \x01(\t\x12\x0f\n\x07version\x18\x07 \x01(\t\x12\x11\n\tnotifyurl\x18\x08 \x01(\t\x12\x0c\n\x04\x62ody\x18\t \x01(\t\x12\x12\n\nouttradeno\x18\n \x01(\t\x12\x0f\n\x07\x66undpwd\x18\x0b \x01(\t\x12\x10\n\x08\x61\x64\x64rtype\x18\x0c \x01(\x05\x12\x10\n\x08touserid\x18\r \x01(\x05\"/\n\x10WithdrawResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\tb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'order_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_VENDORORDER']._serialized_start=23
  _globals['_VENDORORDER']._serialized_end=942
  _globals['_CUSTOMERORDERINFO']._serialized_start=945
  _globals['_CUSTOMERORDERINFO']._serialized_end=1849
  _globals['_APPPEALINFO']._serialized_start=1852
  _globals['_APPPEALINFO']._serialized_end=2206
  _globals['_CRYPTORECORD']._serialized_start=2209
  _globals['_CRYPTORECORD']._serialized_end=2564
  _globals['_INCOMEEXPENDITUREDETAIL']._serialized_start=2566
  _globals['_INCOMEEXPENDITUREDETAIL']._serialized_end=2668
  _globals['_WALLETORDER']._serialized_start=2671
  _globals['_WALLETORDER']._serialized_end=2972
  _globals['_WALLETTRANSINREQUEST']._serialized_start=2974
  _globals['_WALLETTRANSINREQUEST']._serialized_end=3066
  _globals['_WALLETTRANSINRESPONSE']._serialized_start=3069
  _globals['_WALLETTRANSINRESPONSE']._serialized_end=3213
  _globals['_WITHDRAWFEEREQUEST']._serialized_start=3215
  _globals['_WITHDRAWFEEREQUEST']._serialized_end=3286
  _globals['_WITHDRAWFEERESPONSE']._serialized_start=3289
  _globals['_WITHDRAWFEERESPONSE']._serialized_end=3526
  _globals['_CHECKWALLETADDRESSREQUEST']._serialized_start=3528
  _globals['_CHECKWALLETADDRESSREQUEST']._serialized_end=3569
  _globals['_CHECKWALLETADDRESSRESPONSE']._serialized_start=3571
  _globals['_CHECKWALLETADDRESSRESPONSE']._serialized_end=3655
  _globals['_UPDATEVENDORORDER']._serialized_start=3657
  _globals['_UPDATEVENDORORDER']._serialized_end=3741
  _globals['_CUSTOMERORDERSREQUEST']._serialized_start=3743
  _globals['_CUSTOMERORDERSREQUEST']._serialized_end=3852
  _globals['_CUSTOMERORDERSRESPONSE']._serialized_start=3855
  _globals['_CUSTOMERORDERSRESPONSE']._serialized_end=4014
  _globals['_MYORDERLISTREQUEST']._serialized_start=4016
  _globals['_MYORDERLISTREQUEST']._serialized_end=4106
  _globals['_MYORDERLISTRESPONSE']._serialized_start=4109
  _globals['_MYORDERLISTRESPONSE']._serialized_end=4266
  _globals['_WITHDRAWRECORDSREQUEST']._serialized_start=4268
  _globals['_WITHDRAWRECORDSREQUEST']._serialized_end=4362
  _globals['_WITHDRAWRECORDSRESPONSE']._serialized_start=4365
  _globals['_WITHDRAWRECORDSRESPONSE']._serialized_end=4530
  _globals['_WITHDRAWREQUEST']._serialized_start=4533
  _globals['_WITHDRAWREQUEST']._serialized_end=4790
  _globals['_WITHDRAWRESPONSE']._serialized_start=4792
  _globals['_WITHDRAWRESPONSE']._serialized_end=4839
# @@protoc_insertion_point(module_scope)
