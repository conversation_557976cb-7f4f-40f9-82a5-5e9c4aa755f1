# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: auth.proto
# Protobuf Python Version: 6.32.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder

_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    32,
    0,
    '',
    'auth.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()

import app.modules.ucoin.py_protobufs.user_pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n\nauth.proto\x12\x04\x61uth\x1a\nuser.proto\"U\n\x0cLoginRequest\x12\x10\n\x08phonenum\x18\x01 \x01(\t\x12\x10\n\x08password\x18\x02 \x01(\t\x12\x0f\n\x07\x63hannel\x18\x04 \x01(\t\x12\x10\n\x08\x62indtype\x18\x05 \x01(\x05\"\xd0\x01\n\rLoginResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12\x1d\n\x05uinfo\x18\x03 \x01(\x0b\x32\x0e.user.UserInfo\x12\x0f\n\x07systime\x18\x04 \x01(\x05\x12 \n\x08\x63oinlist\x18\x05 \x03(\x0b\x32\x0e.user.coininfo\x12 \n\x08\x61\x64\x64rlist\x18\x06 \x03(\x0b\x32\x0e.user.coinaddr\x12.\n\x16trusteeshipaddresslist\x18\x07 \x03(\x0b\x32\x0e.user.coinaddr\"\"\n\x10HeartbeatRequest\x12\x0e\n\x06userid\x18\x01 \x01(\x05\"0\n\x11HeartbeatResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\"\"\n\x10ReconnectRequest\x12\x0e\n\x06userid\x18\x01 \x01(\x05\"`\n\x11ReconnectResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12\x1d\n\x05uinfo\x18\x03 \x01(\x0b\x32\x0e.user.UserInfo\x12\x0f\n\x07systime\x18\x04 \x01(\x05\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'auth_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
    DESCRIPTOR._loaded_options = None
    _globals['_LOGINREQUEST']._serialized_start = 32
    _globals['_LOGINREQUEST']._serialized_end = 117
    _globals['_LOGINRESPONSE']._serialized_start = 120
    _globals['_LOGINRESPONSE']._serialized_end = 328
    _globals['_HEARTBEATREQUEST']._serialized_start = 330
    _globals['_HEARTBEATREQUEST']._serialized_end = 364
    _globals['_HEARTBEATRESPONSE']._serialized_start = 366
    _globals['_HEARTBEATRESPONSE']._serialized_end = 414
    _globals['_RECONNECTREQUEST']._serialized_start = 416
    _globals['_RECONNECTREQUEST']._serialized_end = 450
    _globals['_RECONNECTRESPONSE']._serialized_start = 452
    _globals['_RECONNECTRESPONSE']._serialized_end = 548
# @@protoc_insertion_point(module_scope)
