# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: notice.proto
# Protobuf Python Version: 6.32.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    32,
    0,
    '',
    'notice.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0cnotice.proto\x12\x06notice\"t\n\nNoticeInfo\x12\n\n\x02id\x18\x01 \x01(\x05\x12\r\n\x05title\x18\x02 \x01(\t\x12\x0f\n\x07\x63ontent\x18\x03 \x01(\t\x12\x12\n\nnoticetype\x18\x04 \x01(\x05\x12\x16\n\x0e\x65xpirationtime\x18\x05 \x01(\x05\x12\x0e\n\x06remark\x18\x06 \x01(\t\"n\n\x0bMessageInfo\x12\n\n\x02id\x18\x01 \x01(\x05\x12\x13\n\x0bmessagetype\x18\x02 \x01(\x05\x12\r\n\x05title\x18\x03 \x01(\t\x12\x0f\n\x07\x63ontent\x18\x04 \x01(\t\x12\x0e\n\x06remark\x18\x05 \x01(\t\x12\x0e\n\x06isread\x18\x06 \x01(\x05\"F\n\x11NoticeListRequest\x12\x0e\n\x06userid\x18\x01 \x01(\x05\x12\x0f\n\x07pagenum\x18\x02 \x01(\x05\x12\x10\n\x08pagesize\x18\x03 \x01(\x05\"|\n\x12NoticeListResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12\x0f\n\x07pagenum\x18\x03 \x01(\x05\x12\x10\n\x08pagesize\x18\x04 \x01(\x05\x12&\n\nnoticelist\x18\x05 \x03(\x0b\x32\x12.notice.NoticeInfo\"1\n\x13NoticeDetailRequest\x12\x0e\n\x06userid\x18\x01 \x01(\x05\x12\n\n\x02id\x18\x02 \x01(\x05\"3\n\x14NoticeDetailResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\"G\n\x12MessageListRequest\x12\x0e\n\x06userid\x18\x01 \x01(\x05\x12\x0f\n\x07pagenum\x18\x02 \x01(\x05\x12\x10\n\x08pagesize\x18\x03 \x01(\x05\"\x7f\n\x13MessageListResponse\x12\x0e\n\x06result\x18\x01 \x01(\x05\x12\x0b\n\x03msg\x18\x02 \x01(\t\x12\x0f\n\x07pagenum\x18\x03 \x01(\x05\x12\x10\n\x08pagesize\x18\x04 \x01(\x05\x12(\n\x0bmessagelist\x18\x05 \x03(\x0b\x32\x13.notice.MessageInfob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'notice_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_NOTICEINFO']._serialized_start=24
  _globals['_NOTICEINFO']._serialized_end=140
  _globals['_MESSAGEINFO']._serialized_start=142
  _globals['_MESSAGEINFO']._serialized_end=252
  _globals['_NOTICELISTREQUEST']._serialized_start=254
  _globals['_NOTICELISTREQUEST']._serialized_end=324
  _globals['_NOTICELISTRESPONSE']._serialized_start=326
  _globals['_NOTICELISTRESPONSE']._serialized_end=450
  _globals['_NOTICEDETAILREQUEST']._serialized_start=452
  _globals['_NOTICEDETAILREQUEST']._serialized_end=501
  _globals['_NOTICEDETAILRESPONSE']._serialized_start=503
  _globals['_NOTICEDETAILRESPONSE']._serialized_end=554
  _globals['_MESSAGELISTREQUEST']._serialized_start=556
  _globals['_MESSAGELISTREQUEST']._serialized_end=627
  _globals['_MESSAGELISTRESPONSE']._serialized_start=629
  _globals['_MESSAGELISTRESPONSE']._serialized_end=756
# @@protoc_insertion_point(module_scope)
