import asyncio
import json
from datetime import datetime
from quart import Blueprint, websocket
from app.shared import process_binary_data, ResponseError
from app.modules.ucoin.service import foundation
from app.shared.response.error import WSResponseError

ucoin = Blueprint('ucoin', __name__)

# 存储所有活跃的 WebSocket 连接
active_connections = set()


@ucoin.websocket('/ucoin')
async def ucoin_endpoint():
    """
    WebSocket 端点
    客户端可以通过 ws://127.0.0.1:6002/ucoin 连接
    """
    # 将新连接添加到集合中
    current_socket = websocket
    active_connections.add(current_socket)
    print(f"新的 WebSocket 连接建立。当连接数: {len(active_connections)}")

    try:
        # 保持连接开放，持续通信
        while True:
            # 等待接收客户端发来的消息
            data = await websocket.receive()
            code, pb_data = process_binary_data(data)
            # --
            res_pb = await foundation(code, pb_data)
            await websocket.send(res_pb)
    except asyncio.CancelledError:
        # 连接被正常关闭
        print("WebSocket 连接正常关闭")
    except WSResponseError as ws_error:
        await websocket.send(ws_error.data)
    except ResponseError as error:
        print(f"WebSocket 连接异常: {error.msg}")
        response = {
            'type': 'error',
            'message': f'{error.msg}',
            'timestamp': datetime.now().isoformat()
        }
        await websocket.send(json.dumps(response))
    finally:
        # 确保从集合中移除连接
        if current_socket in active_connections:
            active_connections.remove(current_socket)
        print(f"一个连接断开。当前连接数: {len(active_connections)}")
