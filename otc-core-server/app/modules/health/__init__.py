from quart import Blueprint, jsonify
import asyncio
import time
from datetime import datetime
from app.core.data_source.mysql import db
from app.core.data_source.redis import RedisManager

health_bp = Blueprint('health', __name__)

@health_bp.route('/health', methods=['GET'])
async def health_check():
    """
    健康检查端点
    返回应用的健康状态信息
    """
    health_status = {
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.0.0',
        'checks': {}
    }
    
    # 检查数据库连接
    try:
        start_time = time.time()
        # 简单的数据库查询测试
        async with db.get_session() as session:
            result = await session.execute("SELECT 1")
            result.scalar()
        
        db_response_time = (time.time() - start_time) * 1000
        health_status['checks']['database'] = {
            'status': 'healthy',
            'response_time_ms': round(db_response_time, 2)
        }
    except Exception as e:
        health_status['status'] = 'unhealthy'
        health_status['checks']['database'] = {
            'status': 'unhealthy',
            'error': str(e)
        }
    
    # 检查 Redis 连接
    try:
        start_time = time.time()
        # 这里需要根据实际的 Redis 管理器实现来调整
        # redis_client = RedisManager.get_client()
        # await redis_client.ping()
        
        redis_response_time = (time.time() - start_time) * 1000
        health_status['checks']['redis'] = {
            'status': 'healthy',
            'response_time_ms': round(redis_response_time, 2)
        }
    except Exception as e:
        health_status['status'] = 'unhealthy'
        health_status['checks']['redis'] = {
            'status': 'unhealthy',
            'error': str(e)
        }
    
    # 返回适当的 HTTP 状态码
    status_code = 200 if health_status['status'] == 'healthy' else 503
    
    return jsonify(health_status), status_code

@health_bp.route('/ready', methods=['GET'])
async def readiness_check():
    """
    就绪检查端点
    检查应用是否准备好接收流量
    """
    return jsonify({
        'status': 'ready',
        'timestamp': datetime.utcnow().isoformat()
    }), 200

@health_bp.route('/live', methods=['GET'])
async def liveness_check():
    """
    存活检查端点
    检查应用是否还在运行
    """
    return jsonify({
        'status': 'alive',
        'timestamp': datetime.utcnow().isoformat()
    }), 200
