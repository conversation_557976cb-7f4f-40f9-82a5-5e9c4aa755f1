import os
import logging
import time
from logging.handlers import RotatingFileHandler

from quart import request


def configure_logging(app):
    # 创建logs目录如果不存在
    os.makedirs('logs', exist_ok=True)

    # 1. 创建记录器
    access_log = logging.getLogger('quart.access')
    error_log = logging.getLogger('quart.error')

    # 2. 设置级别
    logging.root.setLevel(logging.INFO)
    access_log.setLevel(logging.INFO)
    error_log.setLevel(logging.ERROR)

    # 3. 创建处理器和格式
    # -- Runtime 日志
    runtime_handler = RotatingFileHandler(
        'logs/runtime.log', maxBytes=1024 * 1024 * 50, backupCount=5
    )
    runtime_format = logging.Formatter(
        '[%(asctime)s][%(levelname)s] - %(message)s'
    )
    runtime_handler.setFormatter(runtime_format)
    # runtime_handler.addFilter(LevelRangeFilter(logging.DEBUG, logging.INFO))
    logging.root.addHandler(runtime_handler)
    # 输出到控制台
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(runtime_format)
    logging.root.addHandler(console_handler)

    # --Access 日志
    access_handler = RotatingFileHandler(
        'logs/access.log', maxBytes=1024 * 1024 * 50, backupCount=3
    )
    access_format = logging.Formatter(
        '[%(asctime)s] - %(message)s'
    )
    access_handler.setFormatter(access_format)
    access_log.addHandler(access_handler)
    # Error 日志
    error_handler = RotatingFileHandler(
        'logs/error.log', maxBytes=1024 * 1024 * 50, backupCount=3
    )
    error_format = logging.Formatter(
        '[%(asctime)s] - %(message)s\n%(exc_info)s'
    )
    error_handler.setFormatter(error_format)
    error_log.addHandler(error_handler)

    @app.before_request
    async def before_request():
        request.start_time = time.time()

    @app.after_request
    async def log_access(response):
        duration = (time.time() - request.start_time) * 1000  # 毫秒
        access_log.info(
            f'{request.remote_addr} "{request.method} {request.path}" '
            f'- usage - {duration:.2f}ms '
        )

        return response

    @app.errorhandler(Exception)
    async def handle_exceptions(e):
        error_log.error(
            f"Unhandled exception: {str(e)}",
            exc_info=True
        )
        return {"error": "Internal Server Error"}, 500


class LevelRangeFilter(logging.Filter):
    """只允许特定级别范围内的日志通过"""

    def __init__(self, low, high):
        self.low = low
        self.high = high
        super().__init__()

    def filter(self, record):
        return self.low <= record.levelno <= self.high
