import os
from app.config import Config

# 直接设置环境变量，避免 .env 文件编码问题
os.environ.setdefault('QUART_ENV', 'development')
os.environ.setdefault('SECRET_KEY', 'HT_SK_8dCuzcs23_DEV')
os.environ.setdefault('API_PREFIX', '/')
os.environ.setdefault('DB_HOST', '127.0.0.1')
os.environ.setdefault('DB_PORT', '3306')
os.environ.setdefault('DB_USER', 'root')
os.environ.setdefault('DB_PASSWORD', '123456')
os.environ.setdefault('DB_URL', 'mysql+aiomysql://root:123456@localhost:3306/otc')
os.environ.setdefault('REDIS_HOST', '127.0.0.1')
os.environ.setdefault('REDIS_PORT', '6379')
os.environ.setdefault('REDIS_DB', '0')


class DevelopmentConfig(Config):
    """开发环境配置"""
    ENV = 'development'
    DEBUG = True
    # 数据库
    SQLALCHEMY_DATABASE_URI = os.getenv('DB_URL')
    SQLALCHEMY_COMMIT_ON_TEARDOWN = True
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ECHO = False  # 输出SQL日志

    REDIS_HOST = os.getenv('REDIS_HOST')
    REDIS_PORT = os.getenv('REDIS_PORT')
    REDIS_DB = os.getenv('REDIS_DB')
