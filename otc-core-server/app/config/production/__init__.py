import os
from app.config import Config
from dotenv import load_dotenv

load_dotenv()


class ProductionConfig(Config):
    """生产环境配置"""
    ENV = 'production'
    DEBUG = False
    # 数据库
    SQLALCHEMY_DATABASE_URI = os.getenv('DB_URL')  # 生产环境必须明确设置
    SQLALCHEMY_COMMIT_ON_TEARDOWN = True
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ECHO = False  # 输出SQL日志

    REDIS_HOST = os.getenv('REDIS_HOST')
    REDIS_PORT = os.getenv('REDIS_PORT')
    REDIS_DB = os.getenv('REDIS_DB')
