from quart import Quart
from app.core.data_source import register_mysql, register_redis
from app.logging import configure_logging
from app.modules import register_blueprints


def create_app(config_name='development'):
    app = Quart(__name__)
    app.config.from_object(f'app.config.{config_name}.{config_name.capitalize()}Config')
    app.config['JSON_AS_ASCII'] = False  # 确保JSON不转义非ASCII字符

    # 加载日志配置
    configure_logging(app)
    # 数据库相关
    register_mysql(app)
    # 注册 Redis
    register_redis(app)
    # 注册蓝图
    register_blueprints(app)

    if app.config['ENV'] == 'production':
        app.logger.info('Start with production')
    elif app.config['ENV'] == 'testing':
        app.logger.info('Start with testing')
    else:
        app.logger.info('Start with development')

    return app
