import logging
from contextlib import asynccontextmanager
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker, AsyncSession
from sqlalchemy.orm import declarative_base

from app.shared.response import ResponseError, ResponseCode

Base = declarative_base()


class MySQLManager:
    def __init__(self):
        self.engine = None
        self.session_factory = None

    async def init(self, db_url: str, echo=False):
        self.engine = create_async_engine(
            db_url,
            echo=echo,
            pool_pre_ping=True,
        )
        self.session_factory = async_sessionmaker(
            self.engine,
            autocommit=False,
            expire_on_commit=False,
        )

    @asynccontextmanager
    async def get_session(self):
        session: AsyncSession = self.session_factory()
        try:
            yield session
            await session.commit()
        except Exception as e:
            await session.rollback()
            logging.error(e)
            # --
            error = e.__dict__
            if error['code'] == 'gkpj':
                # -- 主键冲突
                raise ResponseError(ResponseCode.DUPLICATE_ENTRY)
        finally:
            await session.close()

    async def close(self):
        if self.engine is not None:
            await self.engine.dispose()


# 实例
db = MySQLManager()


def register_mysql(app):
    # 数据库初始化
    @app.before_serving
    async def init_db():
        await db.init(
            app.config['SQLALCHEMY_DATABASE_URI'],
            app.config['SQLALCHEMY_ECHO']
        )

        async with db.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
            result = await conn.execute(text("SELECT VERSION()"))
            version = result.scalar()
            app.logger.info(f'MySQL version {version}')

        app.logger.info('Connect to mysql successfully.')

    # 关闭连接
    @app.after_serving
    async def close_db():
        await db.close()
        app.logger.info('Disconnected mysql')
