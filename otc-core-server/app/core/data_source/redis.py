import logging
import asyncio
from redis import asyncio as aioredis


class RedisManager(object):
    __pool_dict: dict[tuple[str, int, int, str], aioredis.ConnectionPool] = {}

    def __init__(
            self,
            host='127.0.0.1',
            port=6379,
            db=0,
            password='',
            decode_responses=True,
            max_connections=10
    ):
        pool_key = (host, port, db, password)
        if pool_key not in self.__pool_dict:
            self.__pool_dict[pool_key] = aioredis.ConnectionPool(
                host=host,
                port=port,
                db=db,
                password=password,
                decode_responses=decode_responses,
                max_connections=max_connections
            )
        self._r = aioredis.Redis(
            connection_pool=self.__pool_dict[pool_key]
        )
        asyncio.create_task(self._ping_async())

    async def get_redis(self) -> aioredis.Redis:
        return self._r

    async def _ping_async(self):
        try:
            await self._r.ping()
        except Exception as e:
            raise e


def register_redis(app):
    logging.info(f'Initializing the Redis.')

    @app.before_serving
    async def init_redis():
        manager = RedisManager(
            host=app.config['REDIS_HOST'],
            port=app.config['REDIS_PORT'],
            db=app.config['REDIS_DB'],
        )
        app.redis = await manager.get_redis()
        logging.info(f'Redis initialized.')

    @app.after_serving
    async def close_redis():
        await app.redis.close()
