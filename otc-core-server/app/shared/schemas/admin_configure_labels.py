from sqlalchemy.orm import load_only

from app.shared.schemas import BaseSchema, db
from sqlalchemy.future import select
from sqlalchemy import Column, VARCHAR, INTEGER, BOOLEAN, UniqueConstraint, func


class AdminConfigureLabel(BaseSchema):
    __tablename__ = 'admin_configure_labels'
    __table_args__ = (
        UniqueConstraint('label', 'draws', name='uix_label_draws'),
    )

    label = Column(
        VARCHAR(50),
        nullable=False,
        comment='字符类别'
    )
    draws = Column(
        INTEGER,
        nullable=False,
        comment='笔画数'
    )
    base = Column(
        BOOLEAN,
        default=False,
        comment='是否初始字符，不能修改和删除'
    )

    @classmethod
    async def find_all(cls):
        stmt = select(cls).options(load_only(cls.label, cls.draws))
        result = await cls.select_execute(stmt)
        return result

    @classmethod
    async def check_completeness(cls, labels: list[str], draws: int) -> list['AdminConfigureLabel']:
        stmt = select(cls).filter(cls.label.in_(labels), cls.draws == draws)
        result = await cls.select_execute(stmt)
        return result

    @classmethod
    async def group_by_draws(cls) -> list[int]:
        stmt = select(cls.draws, func.count().label('count')).group_by(
            cls.draws
        )
        result = await cls.select_execute(stmt)
        return result

    @classmethod
    async def labels_within_draws(cls, draws: int) -> list['AdminConfigureLabel']:
        stmt = select(cls).where(cls.draws == draws).options(load_only(cls.label))
        result = await cls.select_execute(stmt)
        return result

    @classmethod
    async def check_exist_bulk(cls, draws: int, labels: list[str]) -> dict[str, bool]:
        # 查询存在的元素
        stmt = select(cls).where(
            cls.draws == draws,
            cls.label.in_(labels)
        ).options(load_only(cls.label))
        results: list[AdminConfigureLabel] = await cls.select_execute(stmt) or []
        existing_labels = [row.label for row in results]
        # 构建结果字典
        return {label: label in existing_labels for label in labels}
