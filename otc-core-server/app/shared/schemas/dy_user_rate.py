from sqlalchemy import Column, select
from sqlalchemy import VARCHAR, DECIMAL, INTEGER, DATETIME
from sqlalchemy.orm import load_only

from app.shared.schemas import BaseSchema


class UserRate(BaseSchema):
    __tablename__ = 'dy_user_rate'

    id = Column(
        INTEGER,
        primary_key=True,
        autoincrement=True,
    )
    userid = Column(
        INTEGER,
    )
    payid = Column(
        INTEGER,
    )
    paytype = Column(
        VARCHAR(45),
    )
    buy_fee_rate = Column(
        DECIMAL(10, 4),
        comment="，买币需要支付的手续费"
    )
    sell_fee_rate = Column(
        DECIMAL(10, 4),
        comment="利用这个通道，卖币的时候，需要支付的手续费"
    )
    buy_comm_rate = Column(
        DECIMAL(10, 4),
        comment="用户当币商，利用这个通道，买币进来，赚取佣金的手续费"
    )
    sell_comm_rate = Column(
        DECIMAL(10, 4),
        comment="用户当币商，利用这个通道，卖币能够赚取的手续费"
    )
    is_used = Column(
        INTEGER,
        comment="是否在使用，默认是打开使用。在后台可以配置关掉"
    )
    last_time = Column(
        DATETIME,
        comment="最后一次使用时间"
    )
    channel = Column(
        VARCHAR(255),
        comment="渠道号"
    )
    behalf_sell_fee_rate = Column(
        DECIMAL(10, 4),
        comment="利用这个通道，代付卖币的时候，需要支付的手续费"
    )
    behalf_buy_comm_rate = Column(
        DECIMAL(10, 4),
        comment="用户当币商，利用这个通道，代付买币能够赚取的手续费"
    )

    @classmethod
    async def find_mode(cls, user_id: int, pay_type: str) -> 'UserRate | None':
        """
        这傻逼 pay_type 就是 pay_id ，待测试
        :param user_id:
        :param pay_type:
        :return:
        """
        stmt = select(cls).where(
            cls.userid == user_id,
            cls.paytype == pay_type
        )
        results = await cls.select_execute(stmt) or []
        if len(results) > 0:
            return results[0]
        else:
            return None
