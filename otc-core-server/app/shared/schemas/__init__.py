from decimal import Decimal
from enum import Enum
from datetime import datetime
from app.core.data_source import Base, db
from sqlalchemy import update, insert
from sqlalchemy.dialects.mysql import insert as d_insert


class BaseSchema(Base):
    __abstract__ = True

    @property
    def attrs(self):
        attrs = {}
        for attr in vars(self):
            if attr == '_sa_instance_state':
                continue
            attrs[attr] = vars(self)[attr]

        return attrs

    def __repr__(self):
        attr_list = []
        for attr in vars(self):
            if attr == '_sa_instance_state':
                continue
            attr_list.append(f'{attr}=\'{vars(self)[attr]}\'')
        attrs = ' '.join(attr_list)
        return f'<{self.__class__.__name__} {attrs} {hex(id(self))}>'

    def from_dict(self, data: dict):
        for k, v in data.items():
            setattr(self, k, v)

    def to_dict(self):
        __data = {}
        for k, v in vars(self).items():
            if k == '_sa_instance_state':
                continue
            if isinstance(v, list):
                tmp = []
                for item in v:
                    if isinstance(item, BaseSchema):
                        tmp.append(item.to_dict())
                    else:
                        tmp.append(item)
                __data[k] = tmp
            else:
                if isinstance(v, datetime):
                    __data[k] = v.strftime("%Y-%m-%d %H:%M:%S")
                elif isinstance(v, Enum):
                    __data[k] = v.value
                elif isinstance(v, Decimal):
                    __data[k] = float(v)
                else:
                    if isinstance(v, BaseSchema):
                        __data[k] = v.to_dict()
                    else:
                        __data[k] = v
        return __data

    def serialization(self):
        view = {}
        for attr in vars(self):
            if attr == '_sa_instance_state':
                continue
            view[attr] = vars(self)[attr]
        return view

    async def insert(self, *args, **kwargs):
        """
        常规插入
        :return:
        """
        async with db.get_session() as session:
            session.add(self)
            await session.commit()
            await session.refresh(self)

    async def update(self):
        pass

    async def upsert(self, *args, **kwargs):
        attrs = {}
        for attr in vars(self):
            if attr == '_sa_instance_state':
                continue
            attrs[attr] = vars(self)[attr]
        stmt = d_insert(self.__class__).values(**attrs)
        # 构造更新部分
        update_dict = {col: getattr(stmt.inserted, col) for col in attrs}
        # 添加 ON DUPLICATE KEY UPDATE 子句
        stmt = stmt.on_duplicate_key_update(**update_dict)

        async with db.get_session() as session:
            await session.execute(stmt)
            await session.commit()

    @staticmethod
    async def select_execute(stmt):
        async with db.get_session() as session:
            results = await session.execute(stmt)
            records = results.scalars().all()
            return records

    # @staticmethod
    # async def paginate(stmt: Select, page: int = 1, per_page: int = 50):
    #     from app.shared.pagination import Pagination
    #     # 计算偏移量
    #     offset = (page - 1) * per_page
    #     async with db.get_session() as session:
    #         results = await session.execute(stmt)
    #         records = results.scalars().all()
    #         # 获取总数
    #         total = len(records)
    #         # 计算总页数
    #         pages = ceil(total / per_page) if total else 1
    #
    #         # 执行分页查询
    #         stmt = stmt.limit(per_page).offset(offset)
    #         results = await session.execute(stmt)
    #         records = results.scalars().all()
    #
    #         pagination = Pagination()
    #         pagination.items = records
    #         pagination.total = total
    #         pagination.pages = pages
    #         pagination.page = page
    #         pagination.per_page = per_page
    #
    #         return pagination

    @staticmethod
    async def select_execute_mappings(stmt):
        async with db.get_session() as session:
            results = await session.execute(stmt)
            # 转换为字典列表
            records = [dict(row) for row in results.mappings()]
            return records

    @classmethod
    async def mapping_insert(cls, data: list):
        async with db.get_session() as session:
            await session.execute(insert(cls), data)
            await session.commit()

    @classmethod
    async def bulk_update(cls, data: list[dict]):
        """
        data:   包含更新数据的字典列表
        """
        if not data:
            return
        async with db.get_session() as session:
            await session.execute(update(cls), data)
            await session.commit()
