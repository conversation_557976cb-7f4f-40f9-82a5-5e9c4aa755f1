from datetime import datetime
from sqlalchemy import Column, select
from sqlalchemy import VARCHAR, DECIMAL, INTEGER, DATETIME
from sqlalchemy import TIMESTAMP
from app.shared.schemas import BaseSchema


class UserPay(BaseSchema):
    __tablename__ = 'dy_user_pay'

    id = Column(
        INTEGER,
        primary_key=True,
        autoincrement=True,
    )
    pay_id = Column(
        INTEGER,
        comment="支付ID，和st_pay_info中的pay_id一一对应"
    )
    description = Column(
        VARCHAR(255),
    )
    name = Column(
        VARCHAR(45),
        comment="收款人名字"
    )
    account = Column(
        VARCHAR(255),
        comment="账号"
    )
    bank_name = Column(
        VARCHAR(128),
        comment="银行名称"
    )
    bank_addr = Column(
        VARCHAR(128),
        comment="银行地址"
    )
    qr_code = Column(
        VARCHAR(255),
        comment="二维码信息"
    )
    create_time = Column(
        DATETIME,
    )
    update_time = Column(
        TIMESTAMP,
        onupdate=datetime.now,
        default=datetime.now,
    )
    single_limit = Column(
        DECIMAL(20, 2),
        comment="单笔限额"
    )
    max_limit = Column(
        DECIMAL(20, 2),
        comment="最大限额"
    )
    day_limit = Column(
        DECIMAL(20, 2),
        comment="每日限额"
    )
    userid = Column(
        INTEGER,
        comment="用户ID"
    )
    status = Column(
        INTEGER,
        comment="4-审核不通过"
    )
    channel = Column(
        VARCHAR(255),
        comment="渠道号"
    )
    buy_fee_rate = Column(
        DECIMAL(10, 4),
        default=0.,
        comment="，买币需要支付的手续费"
    )
    sell_fee_rate = Column(
        DECIMAL(10, 4),
        default=0.,
        comment="利用这个通道，卖币的时候，需要支付的手续费"
    )
    buy_comm_rate = Column(
        DECIMAL(10, 4),
        default=0.,
        comment="用户当币商，利用这个通道，买币进来，赚取佣金的手续费"
    )
    sell_comm_rate = Column(
        DECIMAL(10, 4),
        default=0.,
        comment="用户当币商，利用这个通道，卖币能够赚取的手续费"
    )
    qrcode_url = Column(
        VARCHAR(255),
        comment="如果是扫码支付，那么，这个二维码地址"
    )
    nickname = Column(
        VARCHAR(45),
        comment="币商名称"
    )
    day_deal_money = Column(
        DECIMAL(10, 4),
        comment="日交易量，人民币"
    )
    fourthpartyId = Column(
        INTEGER,
        default=0,
        comment="四方ID"
    )
    deal_last_time = Column(
        DATETIME,
        comment="最后一次交易时间"
    )
    platform_id = Column(
        INTEGER,
        comment="平台编号"
    )
    owership = Column(
        INTEGER,
        comment="1-商户自备地址"
    )
    adds = Column(
        VARCHAR(255),
        comment="地址"
    )
    addr_type = Column(
        INTEGER,
        comment="102-trc20"
    )
    addr_name = Column(
        VARCHAR(255),
        comment="地址名称"
    )
    opt_id = Column(
        INTEGER,
        comment="操作者ID"
    )
    opt_name = Column(
        VARCHAR(255),
        comment="操作者名字"
    )
    remarks = Column(
        VARCHAR(255),
        comment="备注"
    )
    coin_pay_deal_amount = Column(
        DECIMAL(20, 10),
        comment="币支付交易总数量"
    )

    @classmethod
    async def load(cls, user_id: int, pay_type: int) -> 'list[UserPay]':
        stmt = select(cls).where(
            cls.userid == user_id,
            cls.pay_id == pay_type,
            cls.status != 2
        )
        results = await cls.select_execute(stmt) or []
        return results

    @classmethod
    async def check_exist(
            cls,
            account: str,
            user_id: int,
            pay_type: int
    ):
        stmt = select(cls).where(
            cls.account == account,
            cls.userid == user_id,
            cls.pay_id == pay_type,
            cls.status != 2
        )
        results = await cls.select_execute(stmt) or []
        return len(results) > 0
