from datetime import datetime
from sqlalchemy import Column, select
from sqlalchemy import DECIMA<PERSON>, INTEGER
from sqlalchemy import TIMESTAMP
from app.shared.schemas import BaseSchema


class AgRelation(BaseSchema):
    __tablename__ = 'ag_relation'

    id = Column(
        INTEGER,
        primary_key=True,
        autoincrement=True,
    )
    userid = Column(
        INTEGER,
        comment="userid"
    )
    bind_userid = Column(
        INTEGER,
        comment="绑定的用户的ID"
    )
    level = Column(
        INTEGER,
        comment="层级关系"
    )
    team_usdt_amount = Column(
        DECIMAL(20, 10),
        default=0.
    )
    team_money_amount = Column(
        DECIMAL(20, 2),
        default=0.
    )
    dir_usdt_amount = Column(
        DECIMAL(20, 10),
        default=0.
    )
    dir_money_amount = Column(
        DECIMAL(20, 2),
        default=0.
    )
    team_usdt_last_month = Column(
        DECIMAL(20, 10),
        default=0.
    )
    team_money_last_month = Column(
        DECIMAL(20, 2),
        default=0.
    )
    team_usdt_month = Column(
        DECIMAL(20, 10),
        default=0.
    )
    team_money_month = Column(
        DECIMAL(20, 2),
        default=0.
    )
    dir_usdt_last_month = Column(
        DECIMAL(20, 10),
        default=0.
    )
    dir_money_last_month = Column(
        DECIMAL(20, 2),
        default=0.
    )
    dir_usdt_month = Column(
        DECIMAL(20, 10),
        default=0.
    )
    dir_money_month = Column(
        DECIMAL(20, 2),
        default=0.
    )
    team_usdt_last_week = Column(
        DECIMAL(20, 10),
        default=0.
    )
    team_money_last_week = Column(
        DECIMAL(20, 2),
        default=0.
    )
    team_usdt_week = Column(
        DECIMAL(20, 10),
        default=0.
    )
    team_money_week = Column(
        DECIMAL(20, 2),
        default=0.
    )
    dir_usdt_last_week = Column(
        DECIMAL(20, 10),
        default=0.
    )
    dir_money_last_week = Column(
        DECIMAL(20, 2),
        default=0.
    )
    dir_usdt_week = Column(
        DECIMAL(20, 10),
        default=0.
    )
    dir_money_week = Column(
        DECIMAL(20, 2),
        default=0.
    )
    team_usdt_last_day = Column(
        DECIMAL(20, 10),
        default=0.
    )
    team_money_last_day = Column(
        DECIMAL(20, 2),
        default=0.
    )
    team_usdt_today = Column(
        DECIMAL(20, 10),
        default=0.
    )
    team_money_today = Column(
        DECIMAL(20, 2),
        default=0.
    )
    dir_usdt_last_day = Column(
        DECIMAL(20, 10),
        default=0.
    )
    dir_money_last_day = Column(
        DECIMAL(20, 2),
        default=0.
    )
    dir_usdt_today = Column(
        DECIMAL(20, 10),
        default=0.
    )
    dir_money_today = Column(
        DECIMAL(20, 2),
        default=0.
    )
    update_date = Column(
        TIMESTAMP,
        onupdate=datetime.now,
        default=datetime.now,
    )
