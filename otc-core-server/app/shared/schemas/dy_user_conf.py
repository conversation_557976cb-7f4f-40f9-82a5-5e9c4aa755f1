from datetime import datetime
from sqlalchemy import Column, select, or_, and_
from sqlalchemy import VARCHAR, INTEGER, DATETIME
from app.shared.schemas import BaseSchema


class UserConf(BaseSchema):
    __tablename__ = 'dy_user_conf'

    id = Column(
        INTEGER,
        primary_key=True,
        autoincrement=True,
    )
    userid = Column(
        INTEGER,
    )
    bind_userid = Column(
        INTEGER,
        comment="币商ID，如果币商ID为0.则表示全部公开的都可以接单，如果这个币商的ID是渠道的ID，那么说明"
    )
    channel = Column(
        VARCHAR(45),
        comment="如果绑定类型是针对渠道的，那么就是渠道标识"
    )
    channel_name = Column(
        VARCHAR(45),
    )
    bind_type = Column(
        INTEGER,
        comment="101是针对渠道，102=针对当个币商，103=针对团队，104=针对小组"
    )
    is_exist = Column(
        INTEGER,
        default=1,
        comment="1生效(开启)0不生效(关闭)"
    )
    deal_type = Column(
        INTEGER,
        default=0,
        comment="交易类型：0=买，1=卖，2提现代付"
    )
    create_time = Column(
        DATETIME,
        default=datetime.now
    )

    @classmethod
    async def check_user_belong(
            cls,
            user_id: int,
            channel: str,
            withdraw_type: int
    ):
        from app.shared.schemas.ag_relation import AgRelation
        from app.shared.schemas.dy_group_member import GroupMember

        if withdraw_type == 0:
            deal_type = cls.deal_type == 1
            switch_type = GroupMember.group_withdraw_switch == 1
        else:
            deal_type = cls.deal_type == 2
            switch_type = GroupMember.group_behalf_switch == 1

        sub_query_1 = select(AgRelation).where(
            AgRelation.bind_userid == cls.bind_userid,
            AgRelation.userid == user_id,
        ).exists()
        sub_query_2 = select(GroupMember).where(
            GroupMember.group_id == cls.bind_userid,
            GroupMember.group_user_id == user_id,
            GroupMember.status == 1,
            switch_type
        ).exists()

        stmt = select(cls).where(
            cls.userid == user_id,
            deal_type,
            or_(
                and_(
                    cls.bind_type == 101,
                    cls.channel_name == channel
                ),
                and_(
                    cls.bind_type == 102,
                    cls.bind_userid == user_id,
                ),
                and_(
                    cls.bind_type == 103,
                    or_(
                        cls.bind_userid == user_id,
                        sub_query_1,
                    )
                ),
                and_(
                    cls.bind_type == 104,
                    sub_query_2
                ),
            )
        )

        results = await cls.select_execute(stmt) or []
        return len(results) > 0
