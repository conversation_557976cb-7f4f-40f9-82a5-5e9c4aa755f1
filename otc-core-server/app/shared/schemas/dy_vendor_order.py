from datetime import datetime
from sqlalchemy import Column, select, or_, and_
from sqlalchemy import VARCHAR, DECIMAL, INTEGER, DATETIME
from sqlalchemy import TIMESTAMP
from sqlalchemy.orm import load_only

from app.shared.schemas import BaseSchema
from enums import HangOrderType, TradeType


class VendorOrder(BaseSchema):
    __tablename__ = 'dy_vendor_order'

    id = Column(
        INTEGER,
        primary_key=True,
        autoincrement=True,
    )
    userid = Column(
        INTEGER,
        comment="用户ID"
    )
    type = Column(
        INTEGER,
        comment="类型：0是买单，1是卖单"
    )
    price = Column(
        DECIMAL(10, 2),
    )
    amount = Column(
        DECIMAL(20, 10),
        comment="交易的数量"
    )
    price_type = Column(
        INTEGER,
        comment="价格类型：0是普通价格，1是根据货币USDT价格差额"
    )
    min_money = Column(
        DECIMAL(20, 4),
    )
    max_money = Column(
        DECIMAL(20, 4),
    )
    enable_status = Column(
        INTEGER,
        comment="6=客服审核不通过"
    )
    auto_switch = Column(
        INTEGER,
        comment="自动开关：0：关闭，1：打开：2：临时关闭"
    )
    message = Column(
        VARCHAR(255),
        comment="留言"
    )
    create_time = Column(
        DATETIME,
        default=datetime.now,
    )
    update_time = Column(
        TIMESTAMP,
        onupdate=datetime.now,
        default=datetime.now,
    )
    check_type = Column(
        INTEGER,
        comment="检索的类型：0：是普通订单，1是针对特定群体才能看到的订单"
    )
    subject = Column(
        VARCHAR(255),
        comment="当针对特定群体的时候，在这里附上对应的信息"
    )
    max_amount = Column(
        DECIMAL(20, 10),
        comment="交易的总数量"
    )
    channel = Column(
        VARCHAR(45),
        comment="渠道标识。在派单的时候，需要对渠道进行识别"
    )
    unsold_order_num = Column(
        INTEGER,
        comment="未成交订单数量"
    )
    deal_order_num = Column(
        INTEGER,
        comment="成交订单数量"
    )
    unsold_order_amount = Column(
        DECIMAL(20, 10),
        comment="未成交订单币数"
    )
    deal_order_amount = Column(
        DECIMAL(20, 10),
        comment="成交订单币数"
    )
    cancel_order_num = Column(
        INTEGER,
        comment="取消订单数量"
    )
    user_type = Column(
        INTEGER,
        comment="100=平台用户，200=币商，300=商户"
    )
    payee_account = Column(
        VARCHAR(145),
        comment="收款人账号"
    )
    payee_name = Column(
        VARCHAR(145),
        comment="收款人账号"
    )
    payee_bank = Column(
        VARCHAR(145),
        comment="收款银行"
    )
    payee_band_addr = Column(
        VARCHAR(145),
        comment="收款银行地址"
    )
    deal_order_free = Column(
        DECIMAL(20, 10),
        comment="已付的手续费"
    )
    free = Column(
        DECIMAL(20, 10),
        comment="应付总手续费"
    )
    channel_deal = Column(
        INTEGER,
        comment="交易类型：0：公开，1:私有"
    )
    fee_rate = Column(
        DECIMAL(10, 4),
        comment="手续费率"
    )
    add_fee_rate = Column(
        DECIMAL(10, 4),
        comment="追加手续费率"
    )
    fail_remark = Column(
        VARCHAR(255),
        comment="不通过原因"
    )
    predict_money = Column(
        DECIMAL(20, 4),
        comment="预计到账"
    )
    deal_mode = Column(
        INTEGER,
        comment="一次交易完成"
    )
    rest_time = Column(
        DATETIME,
        default=datetime.now,
        comment="一笔吃挂单取消后需要一个休息时间，才可以重新上架"
    )
    deal_monery = Column(
        DECIMAL(20, 4),
        comment="购买人民币数量"
    )
    deal_count = Column(
        DECIMAL(20, 4),
        comment="已派的订单数量"
    )
    withdraw_type = Column(
        INTEGER,
        comment="代付"
    )
    coin_id = Column(
        INTEGER,
        comment="币种ID"
    )
    coin_name = Column(
        VARCHAR(255),
        comment="币种名字"
    )
    notify_info = Column(
        VARCHAR(1000),
        comment="回调信息"
    )
    is_external = Column(
        INTEGER,
        comment="1-是"
    )
    unsold_order_money = Column(
        DECIMAL(20, 4),
        comment="未成交订单法币数量"
    )
    deal_order_money = Column(
        DECIMAL(20, 4),
        comment="成交订单法币数量"
    )
    external_order_userid = Column(
        INTEGER,
        comment="外部订单派给指定用户"
    )
    is_assigned = Column(
        INTEGER,
        comment="是否需要重新匹配订单"
    )
    is_take = Column(
        INTEGER,
        comment="不接单"
    )
    platform_id = Column(
        INTEGER,
        comment="平台编号"
    )
    merchant_order_id = Column(
        VARCHAR(255),
        comment="所关联的商户相关订单id"
    )
    wallet_type = Column(
        INTEGER,
        comment="币付钱包"
    )

    @classmethod
    async def find_orders_pagination(
            cls,
            user_id: int,
            trade_type: HangOrderType,
            page_size: int,
            offset: int
    ) -> 'list[VendorOrder]':
        stmt = select(cls).where(
            cls.userid == user_id,
            cls.type == trade_type.value,
        ).order_by(
            cls.id.desc()
        ).limit(
            page_size
        ).offset(
            offset
        )
        results: list[cls] = await cls.select_execute(stmt) or []
        return results

    @classmethod
    async def is_auto_sell(cls, user_id: int):
        stmt = select(cls).where(
            cls.userid == user_id,
            cls.type == 1,
            cls.auto_switch == 1,
            cls.enable_status == 1,
        )
        results: list[cls] = await cls.select_execute(stmt) or []
        if len(results):
            return results[0].id
        else:
            return 0

    @classmethod
    async def get_vendor_id_of_orders(
            cls,
            user_id: int,
            order_type: HangOrderType
    ) -> 'list[VendorOrder]':
        stmt = select(cls).where(
            cls.userid == user_id,
            cls.type == order_type.value,
            cls.enable_status == 1
        ).order_by(
            cls.id.desc()
        ).options(
            load_only(
                cls.id
            )
        )
        results = await cls.select_execute(stmt) or []
        return results

    @classmethod
    async def find(cls, vendor_id: int) -> 'VendorOrder | None':
        stmt = select(cls).where(
            cls.id == vendor_id,
        )
        results = await cls.select_execute(stmt) or []
        if len(results) > 0:
            return results[0]
        else:
            return None

    @classmethod
    async def find_public(
            cls,
            user_id: int,
            trade_type: TradeType,
            channel: str,
    ) -> 'list[VendorOrder]':
        from app.shared.schemas.dy_user_conf import UserConf
        from app.shared.schemas.ag_relation import AgRelation
        from app.shared.schemas.dy_group_member import GroupMember

        sub_query = select(UserConf).where(
            UserConf.userid == cls.userid,
            UserConf.is_exist == 1,
            and_(
                UserConf.deal_type == 2,
                or_(
                    and_(
                        UserConf.bind_type == 101,
                        UserConf.channel_name == channel
                    ),
                    and_(
                        UserConf.bind_type == 102,
                        UserConf.bind_userid == user_id
                    ),
                    and_(
                        UserConf.bind_type == 103,
                        or_(
                            UserConf.bind_userid == user_id,
                            select(AgRelation).where(
                                AgRelation.bind_userid == UserConf.bind_userid,
                                AgRelation.userid == user_id
                            ).exists()
                        )
                    ),
                    and_(
                        UserConf.bind_type == 104,
                        select(GroupMember).where(
                            GroupMember.group_id == UserConf.bind_userid,
                            GroupMember.group_user_id == user_id,
                            GroupMember.status == 1,
                            GroupMember.group_behalf_switch == 1,
                        ).exists()
                    ),
                )
            )
        ).exists()

        stmt = select(cls).where(
            cls.userid != user_id,
            cls.enable_status == 1,
            cls.auto_switch == 0,
            cls.is_external == 0,
            cls.type == trade_type.value,
            cls.rest_time < datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            cls.channel_deal == 0,
            or_(
                cls.user_type != 300,
                and_(
                    cls.user_type == 300,
                    or_(
                        and_(
                            cls.withdraw_type == 0,
                            sub_query,
                        ),
                        and_(
                            cls.withdraw_type == 1,
                            sub_query,
                        )
                    )
                )
            )
        )
        results = await cls.select_execute(stmt) or []
        return results

    @classmethod
    async def find_private(
            cls,
            user_id: int,
            trade_type: TradeType,
            channel: str,
    ) -> 'list[VendorOrder]':
        from app.shared.schemas.dy_user_conf import UserConf
        from app.shared.schemas.ag_relation import AgRelation
        from app.shared.schemas.dy_group_member import GroupMember

        sub_query = select(UserConf).where(
            UserConf.userid == cls.userid,
            UserConf.is_exist == 1,
            and_(
                UserConf.deal_type == 2,
                or_(
                    and_(
                        UserConf.bind_type == 101,
                        UserConf.channel_name == channel
                    ),
                    and_(
                        UserConf.bind_type == 102,
                        UserConf.bind_userid == user_id
                    ),
                    and_(
                        UserConf.bind_type == 103,
                        or_(
                            UserConf.bind_userid == user_id,
                            select(AgRelation).where(
                                AgRelation.bind_userid == UserConf.bind_userid,
                                AgRelation.userid == user_id
                            ).exists()
                        )
                    ),
                    and_(
                        UserConf.bind_type == 104,
                        select(GroupMember).where(
                            GroupMember.group_id == UserConf.bind_userid,
                            GroupMember.group_user_id == user_id,
                            GroupMember.status == 1,
                            GroupMember.group_behalf_switch == 1,
                        ).exists()
                    ),
                )
            )
        ).exists()

        stmt = select(cls).where(
            cls.userid == user_id,
            cls.enable_status == 1,
            cls.auto_switch == 0,
            cls.is_external == 0,
            cls.type == trade_type.value,
            cls.rest_time < datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            or_(
                cls.channel == channel,
                and_(
                    cls.user_type == 300,
                    or_(
                        and_(
                            cls.withdraw_type == 0,
                            sub_query,
                        ),
                        and_(
                            cls.withdraw_type == 1,
                            sub_query,
                        )
                    )
                )
            )
        )
        results = await cls.select_execute(stmt) or []
        return results
