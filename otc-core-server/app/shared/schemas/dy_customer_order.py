from datetime import datetime
from sqlalchemy import select, func, desc, and_, or_
from sqlalchemy import Column, VARCHAR, DECIMAL, INTEGER, BIGINT, DATETIME, TIMESTAMP
from sqlalchemy.orm import load_only
from app.shared.schemas import BaseSchema
from enums import OrderProcessStatusType, OrderStatusType, TradeType


class CustomerOrder(BaseSchema):
    __tablename__ = 'dy_customer_order'

    id = Column(
        BIGINT,
        primary_key=True,
        autoincrement=True,
    )
    type = Column(
        INTEGER,
        comment="0-买,1-卖"
    )
    vendor_order_id = Column(
        BIGINT,
        comment="所关联的币商挂单id"
    )
    customer_user_id = Column(
        INTEGER,
        comment="顾客user_id"
    )
    vendor_user_id = Column(
        INTEGER,
        comment="币商user_id"
    )
    price = Column(
        DECIMAL(20, 4),
        comment="成交单价"
    )
    amount = Column(
        DECIMAL(20, 10),
        comment="成交数量"
    )
    money = Column(
        DECIMAL(20, 4),
        comment="成交金额"
    )
    status = Column(
        INTEGER,
        comment="17-未匹配"
    )
    merchant_order_id = Column(
        VARCHAR(255),
        comment="所关联的商户相关订单id"
    )
    fee_rate = Column(
        DECIMAL(10, 5),
        comment="买方手续费比例"
    )
    fee = Column(
        DECIMAL(20, 10),
        comment="手续费"
    )
    proof_url = Column(
        VARCHAR(255),
        comment="付款凭证"
    )
    pay_type = Column(
        VARCHAR(1024),
        comment="这里的支付方式应该是一个列表，提供多个支付方式"
    )
    pay_id = Column(
        VARCHAR(1024),
        comment="对应支付id，能找到支付类型"
    )
    create_time = Column(
        DATETIME,
        comment="创建时间"
    )
    update_time = Column(
        TIMESTAMP,
        onupdate=datetime.now,
        default=datetime.now,
        comment="更新时间"
    )
    pay_time = Column(
        DATETIME,
        comment="支付时间"
    )
    pass_time = Column(
        VARCHAR(45),
        comment="放行时间"
    )
    channel = Column(
        VARCHAR(45),
        comment="渠道标识"
    )
    publiceprice = Column(
        DECIMAL(10, 4),
        comment="公共的币的价格"
    )
    from_type = Column(
        INTEGER,
        comment="订单类型：1：普通订单，2：三方订单：3:：提现订单"
    )
    notify_url = Column(
        VARCHAR(512),
        comment="商户客户下的订单的时候，传入的通知回调"
    )
    body = Column(
        VARCHAR(255),
        comment="商户客户的订单，给商户返回的参数"
    )
    sell_fee_rate = Column(
        DECIMAL(10, 5),
        comment="卖方费率，有需要，在商户卖币提现的时候，需要收取卖方费率"
    )
    sell_fee = Column(
        DECIMAL(20, 10),
        comment="费率总额"
    )
    cancel_time = Column(
        DATETIME,
        comment="取消时间"
    )
    buy_fee_rate = Column(
        DECIMAL(10, 5),
        comment="买方费率，"
    )
    buy_fee = Column(
        DECIMAL(20, 8),
        comment="买方需要支付的手续费总额"
    )
    paypal_order_id = Column(
        VARCHAR(45),
        comment="支付平台的订单ID。例如支付宝，或者微信产生的订单ID。如果没有，则为空"
    )
    user_pay_id = Column(
        INTEGER,
        comment="用户用的具体的支付ID"
    )
    deal_type = Column(
        INTEGER,
        comment="，100=币商交易，200=商户用户，300=商户自己"
    )
    after_money = Column(
        DECIMAL(20, 4),
        comment="到账人民币的金额"
    )
    customer_ip = Column(
        VARCHAR(45),
        comment="顾客进行交易的IP"
    )
    coin_id = Column(
        INTEGER,
        comment="对应币种的ID"
    )
    refuse_mark = Column(
        VARCHAR(245),
        comment="拒绝放币的理由"
    )
    payee_account = Column(
        VARCHAR(145),
        comment="收款人账号"
    )
    payee_name = Column(
        VARCHAR(145),
        comment="收款人名字"
    )
    payee_bank = Column(
        VARCHAR(145),
        comment="收款银行"
    )
    payee_band_addr = Column(
        VARCHAR(145),
        comment="收款银行地址"
    )
    notify_amount = Column(
        INTEGER,
        comment="0是没有回调。非0是回调次数"
    )
    get_amount = Column(
        DECIMAL(20, 10),
        comment="到账币的数量"
    )
    add_fee_num = Column(
        DECIMAL(10, 4),
        comment="追加手续费"
    )
    income = Column(
        DECIMAL(20, 10),
        comment="收益"
    )
    only_mark = Column(
        VARCHAR(245),
        comment="订单号改由代码生成后就可以弃用"
    )
    vendor_channel = Column(
        VARCHAR(245),
        comment="的渠道"
    )
    withdraw_type = Column(
        INTEGER,
        comment="代付"
    )
    coin_name = Column(
        VARCHAR(255),
        comment="币种名字"
    )
    is_external = Column(
        INTEGER,
        comment="1-是"
    )
    fee_money = Column(
        DECIMAL(20, 4),
        comment="手续费法币"
    )
    trade_id = Column(
        VARCHAR(255),
        comment="交易所订单ID"
    )
    history_amount = Column(
        DECIMAL(20, 10),
        comment="历史币数量"
    )
    history_money = Column(
        DECIMAL(20, 4),
        comment="历史法币数量"
    )
    replenishment_remarks = Column(
        VARCHAR(1024),
        comment="补单备注"
    )
    platform_id = Column(
        INTEGER,
        comment="平台编号"
    )
    vendor_platform_id = Column(
        INTEGER,
        comment="平台编号"
    )
    chain_type = Column(
        INTEGER,
        comment="链类型"
    )
    chain_name = Column(
        VARCHAR(255),
        comment="链名称"
    )
    chain_addr = Column(
        VARCHAR(255),
        comment="链地址"
    )
    coin_pay_txid = Column(
        VARCHAR(255),
        comment="补单或比配成功是的TXID"
    )
    is_wait = Column(
        INTEGER,
        comment="1-等待释放"
    )
    owership = Column(
        INTEGER,
        comment="1-商户自备地址"
    )
    wallet_type = Column(
        INTEGER,
        comment="币付钱包"
    )
    is_face_to_face = Column(
        INTEGER,
        comment="是"
    )
    deal_addr = Column(
        VARCHAR(512),
        comment="交易地址"
    )
    check_code = Column(
        VARCHAR(255),
        comment="校验码"
    )
    verify_password = Column(
        VARCHAR(255),
        comment="校验口令"
    )
    check_status = Column(
        INTEGER,
        comment="已校验"
    )

    @classmethod
    async def user_trade_orders(
            cls,
            user_id: int,
            page_size: int,
            offset: int,
            trade_type: TradeType | None = None,
            order_status: OrderProcessStatusType | None = None,
    ) -> 'list[CustomerOrder]':
        if trade_type:
            if trade_type == TradeType.BUY:
                user_or = or_(
                    and_(
                        cls.customer_user_id == user_id,
                        cls.type == TradeType.BUY.value,
                    ),
                    and_(
                        cls.vendor_user_id == user_id,
                        cls.type == TradeType.SELL.value,
                    ),
                )
            else:
                user_or = or_(
                    and_(
                        cls.customer_user_id == user_id,
                        cls.type == TradeType.SELL.value,
                    ),
                    and_(
                        cls.vendor_user_id == user_id,
                        cls.type == TradeType.BUY.value,
                    ),
                )
        else:
            user_or = or_(
                cls.customer_user_id == user_id,
                cls.vendor_user_id == user_id,
            )

        if order_status:
            if order_status == OrderProcessStatusType.CANCELED:
                status_or = or_(
                    cls.status.in_([2, 8, 16]),
                )
            elif order_status == OrderProcessStatusType.PENDING_TIMEOUT:
                status_or = or_(
                    cls.status.in_([1, 3]),
                )
            elif order_status == OrderProcessStatusType.FINISHED_WITH_MANUAL:
                status_or = or_(
                    cls.status.in_([7, 11]),
                )
            else:
                status_or = (cls.status == order_status.value)

            stmt = select(cls).where(
                user_or,
                status_or,
            )
        else:
            stmt = select(cls).where(
                user_or,
            )

        stmt = stmt.order_by(
            cls.create_time.desc()
        ).limit(
            page_size
        ).offset(
            offset
        )
        # --
        results = await cls.select_execute(stmt) or []
        return results

    @classmethod
    async def user_pending_orders(
            cls,
            user_id: int,
    ) -> 'list[CustomerOrder]':
        stmt = select(cls).where(
            or_(
                cls.customer_user_id == user_id,
                cls.vendor_user_id == user_id,
            ),
            or_(
                cls.status == OrderProcessStatusType.PENDING,
                cls.status == OrderProcessStatusType.BUYER_PAYED,
                cls.status == OrderProcessStatusType.APPEAL
            )
        ).order_by(
            cls.id.desc()
        )
        # --
        results = await cls.select_execute(stmt) or []
        return results

    @classmethod
    async def check_processing(cls, user_id: int):
        stmt = select(cls, func.count(cls.id).label('processing_count')).where(
            cls.customer_user_id == user_id,
            cls.status == 1,
        )
        # --
        results = await cls.select_execute(stmt)
        if results[0]['processing_count'] >= 5:
            return False
        else:
            return True
