from sqlalchemy.orm import load_only, relationship, selectinload
from sqlalchemy.future import select
from sqlalchemy import Column, VARCHAR, INTEGER, Enum, ForeignKey
from app.shared.schemas import BaseSchema


class AdminConfigureModel(BaseSchema):
    __tablename__ = 'admin_configure_models'

    shape_id = Column(
        INTEGER,
        ForeignKey('admin_configure_model_label_shapes.id'),
        nullable=False,
        comment='Shape(形状) id'
    )
    model_id = Column(
        INTEGER,
        comment='model id, 如果是自定义上传则为空'
    )
    # --
    shape = relationship(
        "AdminConfigureModelLabelShape",
        back_populates="model",
    )  # 定义关系

    @classmethod
    async def find_all(cls) -> list['AdminConfigureModel']:
        from app.shared.schemas.admin_configure_model_label_shapes import AdminConfigureModelLabelShape
        # 预加载
        stmt = select(cls).options(
            load_only(
                cls.model_name,
                cls.model_path,
                cls.created_time
            ),
            selectinload(cls.shape).load_only(
                AdminConfigureModelLabelShape.id,
                AdminConfigureModelLabelShape.shape_name,
                AdminConfigureModelLabelShape.draws,
            ),
        )
        result = await cls.select_execute(stmt)
        return result

    @classmethod
    async def find(cls, model_id: int) -> 'AdminConfigureModel':
        stmt = select(cls).filter(cls.id == model_id).options(
            load_only(
                cls.model_name,
                cls.model_path,
                cls.etag,
                cls.ocr_etag,
                cls.shape_id,
                cls.created_time,
            ),
        )

        result = await cls.select_execute(stmt)
        return result[0]
