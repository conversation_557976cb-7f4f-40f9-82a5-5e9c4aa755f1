from decimal import Decimal
from sqlalchemy import select, func, desc, and_
from sqlalchemy import Column, VARCHAR, DECIMAL, INTEGER, Enum, BOOLEAN
from sqlalchemy.orm import load_only
from app.shared.schemas import BaseSchema, db


class Example(BaseSchema):
    __tablename__ = 'example'

    example_int = Column(
        INTEGER,
        index=True,
        comment="TODO"
    )
    example_varchar = Column(
        VARCHAR(20),
        index=True,
        comment="TODO"
    )
    example_enum = Column(
        Enum(),
        index=True,
        comment="TODO"
    )
    example_decimal = Column(
        DECIMAL(12, 6),
        comment="TODO"
    )
    example_bool = Column(
        BOOLEAN,
        default=True,
        index=True,
        comment="TODO"
    )

    # others
    # --
    # 非表内字段
    idx = -1

    @classmethod
    async def paginate_example(
            cls,
            example_int: int,
            page=1,
            page_size=50,
            example_varchar: str | None = None,
            confidence: float | int | None = None,
    ):
        filters = [
            cls.example_int == example_int,
            cls.example_bool == True,
            # cls.created_time.between('2025-08-15 10:30:00', '2025-08-15 23:59:59')
        ]
        if example_varchar:
            filters.append(cls.example_varchar == example_varchar)

        stmt = select(cls).order_by(
            desc(cls.created_time)
        ).where(
            and_(*filters)
        ).options(
            load_only(
                cls.example_varchar,
            )
        )
        pagination = await cls.paginate(
            stmt,
            page=page,
            per_page=page_size,
        )
        return pagination

    # @classmethod
    # async def stats(cls, draws: int, labels: list[str]):
    #     stmt = select(cls.confirm, cls.set, func.count().label('total')).where(
    #         cls.draws == draws,
    #         cls.confirm.in_(labels),
    #         cls.set != TImageSet.UNMARK,
    #     ).group_by(
    #         cls.confirm,
    #         cls.set
    #     )
    #     async with db.get_session() as session:
    #         results = await session.execute(stmt)
    #         # 转换为字典列表
    #         records = [dict(row) for row in results.mappings()]
    #         return records
    #
    # @classmethod
    # async def stats_unmark_of_draws(cls, draws: list[int]):
    #     stmt = select(cls.draws, func.count().label('total')).where(
    #         cls.draws.in_(draws),
    #         cls.set == TImageSet.UNMARK,
    #         cls.enabled == True,
    #     ).group_by(
    #         cls.draws,
    #     )
    #     async with db.get_session() as session:
    #         results = await session.execute(stmt)
    #         # 转换为字典列表
    #         records = [dict(row) for row in results.mappings()]
    #         return records
    #
    # @classmethod
    # async def find_size(cls, draws: int, label: str, size: int = None):
    #     stmt = select(cls).where(
    #         cls.draws == draws,
    #         cls.confirm == label,
    #         cls.set != TImageSet.UNMARK,
    #     ).options(
    #         load_only(
    #             cls.file_name
    #         )
    #     ).limit(size)
    #     result = await cls.select_execute(stmt)
    #     return result

    # @classmethod
    # async def delete(cls, dataset_id: int):
    #     stmt = delete(cls).where(cls.dataset_id == dataset_id)
    #     async with db.get_session() as session:
    #         await session.execute(stmt)
    #         session.commit()
