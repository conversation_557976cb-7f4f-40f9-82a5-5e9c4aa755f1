import json
from datetime import datetime
from sqlalchemy import select, TIMESTAMP, DATETIME, update
from sqlalchemy import Column, VARCHAR, DECIMAL, INTEGER, BOOLEAN
from sqlalchemy.orm import load_only
from app.shared.libs.redis_key import *
from app.shared.schemas import BaseSchema, db
from app.shared.schemas.dy_vendor_order import VendorOrder


class UserInfo(BaseSchema):
    __tablename__ = 'dy_user_info'

    userid = Column(
        INTEGER,
        primary_key=True,
        autoincrement=True,
    )
    cid = Column(
        VARCHAR(45),
        comment="设备唯一的标识"
    )
    account = Column(
        VARCHAR(45),
        comment="账号，不对唯一性做检查"
    )
    password = Column(
        VARCHAR(45),
    )
    nickname = Column(
        VARCHAR(45),
        comment="昵称"
    )
    eth_address = Column(
        VARCHAR(45),
        comment="eth地址"
    )
    face_1 = Column(
        VARCHAR(45),
    )
    regdate = Column(
        TIMESTAMP,
        default=datetime.now,
    )
    sex = Column(
        INTEGER,
    )
    age = Column(
        INTEGER,
    )
    email = Column(
        VARCHAR(45),
    )
    phonenum = Column(
        VARCHAR(45),
    )
    eth_amount = Column(
        DECIMAL(20, 10),
        comment="eth的数量"
    )
    erc_usdt_amount = Column(
        DECIMAL(20, 10),
        comment="erc20的usdt的数量"
    )
    erc_usdt_lock_amount = Column(
        DECIMAL(20, 10),
    )
    channel = Column(
        VARCHAR(45),
        comment="渠道包"
    )
    invite_code = Column(
        VARCHAR(45),
        comment="自己的邀请码"
    )
    bind_code = Column(
        VARCHAR(45),
        comment="绑定的上级邀请码"
    )
    imei = Column(
        VARCHAR(45),
    )
    devname = Column(
        VARCHAR(45),
    )
    macname = Column(
        VARCHAR(45),
    )
    mobiletype = Column(
        INTEGER,
    )
    lasttime = Column(
        INTEGER,
    )
    penulttime = Column(
        INTEGER,
    )
    isban = Column(
        INTEGER,
    )
    description = Column(
        VARCHAR(129),
    )
    blacklist = Column(
        INTEGER,
    )
    ip = Column(
        VARCHAR(45),
    )
    province = Column(
        VARCHAR(45),
    )
    city = Column(
        VARCHAR(45),
    )
    payidlist = Column(
        VARCHAR(255),
    )
    bindtype = Column(
        INTEGER,
    )
    fundpassword = Column(
        VARCHAR(45),
    )
    user_type = Column(
        INTEGER,
    )
    admin_account = Column(
        VARCHAR(45),
    )
    admin_password = Column(
        VARCHAR(45),
    )
    status = Column(
        INTEGER,
    )
    remark = Column(
        VARCHAR(255),
    )
    is_lock = Column(
        BOOLEAN,
    )
    team_user_num = Column(
        INTEGER,
    )
    dir_user_num = Column(
        INTEGER,
    )
    agent = Column(
        INTEGER,
    )
    is_hang_buy = Column(
        INTEGER,
    )
    is_hang_sell = Column(
        INTEGER,
    )
    min_buy = Column(
        DECIMAL(20, 2),
    )
    max_buy = Column(
        DECIMAL(20, 2),
    )
    min_sell = Column(
        DECIMAL(20, 2),
    )
    max_sell = Column(
        DECIMAL(20, 2),
    )
    comm_type = Column(
        INTEGER,
    )
    prohibit_login = Column(
        INTEGER,
    )
    username = Column(
        VARCHAR(45),
    )
    is_accept_order = Column(
        INTEGER,
    )
    deal_last_tiime = Column(
        DATETIME,
        default=datetime.fromisoformat('2020-01-01 00:00:00')
    )
    is_invited = Column(
        BOOLEAN,
    )
    back_mobile = Column(
        VARCHAR(45),
    )
    ban_time = Column(
        VARCHAR(255),
    )
    is_allow_loginback = Column(
        BOOLEAN,
    )
    google_auth_secret = Column(
        VARCHAR(45),
    )
    is_verified_google = Column(
        BOOLEAN,
    )
    is_up_payername = Column(
        INTEGER,
    )
    is_up_payvoucher = Column(
        INTEGER,
    )
    is_otc = Column(
        INTEGER,
    )
    is_behalf_pay = Column(
        INTEGER,
    )
    erc_fc_amount = Column(
        DECIMAL(20, 2),
    )
    erc_fc_lock_amount = Column(
        DECIMAL(20, 2),
    )
    deal_coin_type = Column(
        INTEGER,
    )
    energy_value = Column(
        DECIMAL(20, 2),
    )
    weights_value = Column(
        DECIMAL(20, 2),
    )
    team_name = Column(
        VARCHAR(255),
    )
    platform_id = Column(
        INTEGER,
    )
    is_team_accept_order = Column(
        INTEGER,
    )
    pre_authorization = Column(
        INTEGER,
    )
    coin_pay_rate = Column(
        DECIMAL(10, 4),
    )
    coin_pay_erc = Column(
        INTEGER,
    )
    coin_pay_trc = Column(
        INTEGER,
    )
    allow_sys_addr = Column(
        INTEGER,
    )
    sys_erc_min = Column(
        DECIMAL(10, 4),
    )
    coin_pay_usdt_amount = Column(
        DECIMAL(10, 4),
    )
    coin_pay_usdt_lock_amount = Column(
        DECIMAL(10, 4),
    )
    is_check = Column(
        INTEGER,
    )
    extract_currency_rate_erc = Column(
        DECIMAL(10, 4),
    )
    extract_currency_rate_trc = Column(
        DECIMAL(10, 4),
    )

    # others
    # --
    # 非表内字段
    autosell = 0
    paytypelist: list[int] = []

    async def update(self):
        values = {}
        for k, v in self.attrs.items():
            if k in ['_sa_instance_state', 'autosell', 'paytypelist']:
                continue
            values[k] = v

        stmt = update(self.__class__).where(
            self.__class__.userid == self.userid
        ).values(values)
        async with db.get_session() as session:
            await session.execute(stmt)
            await session.commit()

    @classmethod
    async def get_user_by_phone(cls, phonenum: str, channel: str) -> 'UserInfo | None':
        """
        :param phonenum:
        :param channel:
        :return:
        """
        stmt = select(cls).where(
            cls.phonenum == phonenum,
            cls.channel == channel,
        ).options(
            load_only(
                cls.userid,
                cls.phonenum,
                cls.password,
                cls.ban_time,
                cls.prohibit_login,
                cls.lasttime,
            )
        )
        results: list[cls] = await cls.select_execute(stmt) or []
        if len(results) > 0:
            return results[0]
        else:
            return None

    @classmethod
    async def get_user_by_account(cls, account: str, channel: str) -> 'UserInfo | None':
        """
        :param account:
        :param channel:
        :return:
        """
        stmt = select(cls).where(
            cls.account == account,
            cls.channel == channel,
        ).options(
            load_only(
                cls.userid,
                cls.account,
                cls.password,
                cls.ban_time,
                cls.prohibit_login,
                cls.lasttime,
            )
        )
        results: list[cls] = await cls.select_execute(stmt) or []
        if len(results) > 0:
            return results[0]
        else:
            return None

    @classmethod
    async def get_user_by_email(cls, email: str, channel: str) -> 'UserInfo | None':
        """
        :param email:
        :param channel:
        :return:
        """
        stmt = select(cls).where(
            cls.email == email,
            cls.channel == channel,
        ).options(
            load_only(
                cls.userid,
                cls.email,
                cls.password,
                cls.ban_time,
                cls.prohibit_login,
                cls.lasttime,
            )
        )
        results: list[cls] = await cls.select_execute(stmt) or []
        if len(results) > 0:
            return results[0]
        else:
            return None

    @classmethod
    async def get_user_info_by_id(cls, user_id: int):
        stmt = select(
            cls
        ).where(
            cls.userid == user_id
        )
        results: list[cls] = await cls.select_execute(stmt)
        if len(results) > 0:
            user = results[0]

            if user.payidlist or len(user.payidlist) > 0:
                user.paytypelist = []
                # payidlist 会是一个 int 数组, 但数据库是一个字符串
                payment_list = json.loads(user.payidlist)
                for payment in payment_list:
                    user.paytypelist.append(payment)

            user.autosell = await VendorOrder.is_auto_sell(user.userid)

            # --
            from quart import current_app
            from redis import asyncio as aioredis
            if hasattr(current_app, "redis"):
                if isinstance(current_app.redis, aioredis.Redis):
                    await current_app.redis.hset(
                        RDS_USER_USDT_AMOUNT,
                        str(user.userid),
                        str(user.erc_usdt_amount),
                    )
                    await current_app.redis.hset(
                        RDS_USER_USDT_LOCK_AMOUNT,
                        str(user.userid),
                        str(user.erc_usdt_lock_amount),
                    )
                    # --
                    await current_app.redis.hset(
                        RDS_USER_FC_AMOUNT,
                        str(user.userid),
                        str(user.erc_fc_amount),
                    )
                    await current_app.redis.hset(
                        RDS_USER_FC_LOCK_AMOUNT,
                        str(user.userid),
                        str(user.erc_fc_lock_amount),
                    )
                    # --
                    await current_app.redis.hset(
                        RDS_USER_COIN_PAY_USDT_AMOUNT,
                        str(user.userid),
                        str(user.coin_pay_usdt_amount),
                    )
                    await current_app.redis.hset(
                        RDS_USER_COIN_PAY_USDT_LOCK_AMOUNT,
                        str(user.userid),
                        str(user.coin_pay_usdt_lock_amount),
                    )
                    # -- save user
                    await current_app.redis.set(
                        RDS_USER % user.userid,
                        json.dumps(user.to_dict())
                    )
            return user
        else:
            return None

    @classmethod
    async def find_by_channels(cls, channels: list[str]) -> 'list[UserInfo]':
        stmt = select(cls).where(
            cls.channel.in_(channels)
        ).options(
            load_only(
                cls.userid
            )
        )
        results = await cls.select_execute(stmt) or []
        return results

    # @classmethod
    # async def stats_unmark_of_draws(cls, draws: list[int]):
    #     stmt = select(cls.draws, func.count().label('total')).where(
    #         cls.draws.in_(draws),
    #         cls.set == TImageSet.UNMARK,
    #         cls.enabled == True,
    #     ).group_by(
    #         cls.draws,
    #     )
    #     async with db.get_session() as session:
    #         results = await session.execute(stmt)
    #         # 转换为字典列表
    #         records = [dict(row) for row in results.mappings()]
    #         return records
    #
    # @classmethod
    # async def find_size(cls, draws: int, label: str, size: int = None):
    #     stmt = select(cls).where(
    #         cls.draws == draws,
    #         cls.confirm == label,
    #         cls.set != TImageSet.UNMARK,
    #     ).options(
    #         load_only(
    #             cls.file_name
    #         )
    #     ).limit(size)
    #     result = await cls.select_execute(stmt)
    #     return result

    # @classmethod
    # async def delete(cls, dataset_id: int):
    #     stmt = delete(cls).where(cls.dataset_id == dataset_id)
    #     async with db.get_session() as session:
    #         await session.execute(stmt)
    #         session.commit()
