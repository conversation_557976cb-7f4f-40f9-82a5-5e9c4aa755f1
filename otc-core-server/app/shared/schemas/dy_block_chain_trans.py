from datetime import datetime
from sqlalchemy import Column, select, and_
from sqlalchemy import VARCHAR, DECIMAL, INTEGER, DATETIME
from app.shared.schemas import BaseSchema
from enums import CoinOrderType


class BlockChainTrans(BaseSchema):
    __tablename__ = 'dy_block_chain_trans'

    id = Column(
        INTEGER,
        primary_key=True,
        autoincrement=True,
    )
    tx_id = Column(
        VARCHAR(255),
        comment="交易id"
    )
    in_tx_id = Column(
        VARCHAR(255),
    )
    tx_type = Column(
        VARCHAR(50),
        comment="2出(提币-sell)"
    )
    chain_id = Column(
        VARCHAR(50),
        comment="所属公链"
    )
    tx_data = Column(
        VARCHAR(255),
        comment="交易信息"
    )
    from_addr = Column(
        VARCHAR(255),
        comment="发起地址"
    )
    to_addr = Column(
        VARCHAR(255),
        comment="目标地址"
    )
    amount = Column(
        DECIMAL(20, 10),
        comment="转账数量"
    )
    tx_fee = Column(
        DECIMAL(20, 10),
        comment="交易手续费"
    )
    tx_status = Column(
        INTEGER,
        comment="交易状态"
    )
    tx_time = Column(
        VARCHAR(255),
        comment="交易时间"
    )
    block_hash = Column(
        VARCHAR(255),
        comment="区块hash"
    )
    recd_status = Column(
        INTEGER,
        comment="107=扣币失败，已取消"
    )
    remark = Column(
        VARCHAR(255),
        comment="备注"
    )
    create_time = Column(
        DATETIME,
        default=datetime.now,
        comment="创建时间"
    )
    update_time = Column(
        VARCHAR(255),
        default=datetime.now,
        comment="更新时间"
    )
    put_status = Column(
        INTEGER,
        comment="归拢状态，0：不需要归拢，1：没归拢，2：已经归拢"
    )
    userid = Column(
        INTEGER,
        comment="对应的用户ID"
    )
    channel = Column(
        VARCHAR(45),
        comment="对应的渠道"
    )
    user_type = Column(
        INTEGER,
        comment="用户类型：100=币商，200=商户用户，300=商户"
    )
    put_address = Column(
        VARCHAR(45),
        comment="归拢地址"
    )
    put_txid = Column(
        VARCHAR(145),
        comment="归拢的txid"
    )
    put_time = Column(
        VARCHAR(145),
        comment="归拢时间"
    )
    get_amount = Column(
        DECIMAL(20, 10),
        comment="实际到账数量"
    )
    specific_type = Column(
        INTEGER,
        comment="5-币支付"
    )
    txid = Column(
        VARCHAR(255),
        comment="转账凭证"
    )
    coin_payer = Column(
        INTEGER,
        comment="打币人ID"
    )
    coin_payer_nickname = Column(
        VARCHAR(45),
        comment="打币人昵称"
    )
    coin_payer_remark = Column(
        VARCHAR(255),
        comment="打币人备注"
    )
    hosting_adds = Column(
        INTEGER,
        comment="币支付商户地址"
    )
    hosting_status = Column(
        INTEGER,
        comment="5补款被驳回"
    )
    related_order = Column(
        VARCHAR(45),
        comment="相关订单(手动添加的冷钱包id)"
    )
    bill_repair_id = Column(
        INTEGER,
        comment="手动调账的单号id"
    )
    coin_id = Column(
        INTEGER,
        comment="币种ID"
    )
    addr_type = Column(
        INTEGER,
        comment="t20"
    )
    coin_name = Column(
        VARCHAR(255),
        comment="币种名字"
    )
    addr_name = Column(
        VARCHAR(255),
        comment="地址名字"
    )
    money = Column(
        DECIMAL(20, 4),
        comment="提币金额"
    )
    price = Column(
        DECIMAL(20, 4),
        comment="汇率"
    )
    nickename = Column(
        VARCHAR(255),
        comment="from用户昵称"
    )
    to_channel = Column(
        VARCHAR(255),
        comment="to渠道号"
    )
    to_userid = Column(
        INTEGER,
        comment="to用户编号"
    )
    to_nickname = Column(
        VARCHAR(255),
        comment="to用户昵称"
    )
    platform_id = Column(
        INTEGER,
        comment="平台编号"
    )
    opt_id = Column(
        INTEGER,
        comment="操作用户ID"
    )
    opt_name = Column(
        VARCHAR(255),
        comment="操作用户昵称"
    )
    wallet_type = Column(
        INTEGER,
        comment="币付钱包"
    )

    @classmethod
    async def find_all(
            cls,
            user_id: int,
            order_type: CoinOrderType,
            page_size: int,
            offset: int,
    ) -> 'list[BlockChainTrans]':
        if order_type == CoinOrderType.ALL:
            where_filter = cls.userid == user_id
        else:
            where_filter = and_(
                cls.userid == user_id,
                cls.specific_type == order_type.value,
            )

        stmt = select(cls).where(
            where_filter
        ).order_by(
            cls.id.desc()
        ).limit(
            page_size
        ).offset(
            offset
        )
        # --
        results = await cls.select_execute(stmt) or []
        return results
