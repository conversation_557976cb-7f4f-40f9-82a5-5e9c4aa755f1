from datetime import datetime
from sqlalchemy import select, func, desc, and_, TIMESTAMP
from sqlalchemy import Column, VARCHAR, DECIMAL, INTEGER, BOOLEAN
from sqlalchemy.orm import load_only
from app.shared.schemas import BaseSchema, db


class UserAddressPre(BaseSchema):
    __tablename__ = 'dy_user_address_pre'

    id = Column(
        INTEGER,
        primary_key=True,
        autoincrement=True,
    )
    user_id = Column(
        INTEGER,
        comment="所属用户id"
    )
    coin_addr = Column(
        VARCHAR(64),
        comment="USDT币地址"
    )
    password = Column(
        VARCHAR(255),
        comment="plaintext"
    )
    create_time = Column(
        comment="创建时间"
    )
    update_time = Column(
        TIMESTAMP,
        onupdate=datetime.now,
        default=datetime.now,
        comment="更新时间"
    )
    channel = Column(
        VARCHAR(45),
        comment="对应的channel"
    )
    status = Column(
        INTEGER,
        comment="0=可用，1=无效"
    )
    addr_type = Column(
        INTEGER,
        comment="t20"
    )
    addr_name = Column(
        VARCHAR(255),
        comment="地址名字"
    )
    use_type = Column(
        INTEGER,
        comment="1-币支付地址"
    )

    @classmethod
    async def check_address(cls, address: str):
        stmt = select(cls).where(
            cls.user_id != 0,
            cls.coin_addr == address
        )
        results = await cls.select_execute(stmt) or []
        return len(results) > 0
