from sqlalchemy import Column, INTEGER, VARCHAR
from sqlalchemy.orm import selectinload, load_only
from sqlalchemy.orm import relationship
from sqlalchemy.future import select
from app.shared.schemas import BaseSchema


class AdminConfigureModelLabelShape(BaseSchema):
    __tablename__ = 'admin_configure_model_label_shapes'

    shape_name = Column(
        VARCHAR(100),
        comment='形状名称, 方便区分'
    )
    draws = Column(
        INTEGER,
        nullable=False,
        comment='笔画数'
    )
    labels = relationship(
        'AdminConfigureLabelWithinShape',
        back_populates='shape'
    )
    model = relationship(
        'AdminConfigureModel',
        back_populates='shape'
    )
    dataset = relationship(
        'Dataset',
        back_populates='shape'
    )

    @classmethod
    async def find_all(cls) -> list['AdminConfigureModelLabelShape']:
        from app.shared.schemas.admin_configure_label_within_shapes import AdminConfigureLabelWithinShape
        # 预加载
        stmt = select(cls).options(
            load_only(
                cls.shape_name,
                cls.draws,
                cls.created_time
            ),
            selectinload(cls.labels).load_only(
                AdminConfigureLabelWithinShape.idx,
                AdminConfigureLabelWithinShape.label
            ),
        )
        result = await cls.select_execute(stmt)
        return result

    @classmethod
    async def find(cls, shape_id: int) -> 'AdminConfigureModelLabelShape | None':
        from app.shared.schemas.admin_configure_label_within_shapes import AdminConfigureLabelWithinShape
        # 预加载
        stmt = select(cls).filter(
            cls.id == shape_id
        ).options(load_only(
            cls.shape_name,
            cls.draws,
        ), selectinload(
            cls.labels
        ).load_only(
            AdminConfigureLabelWithinShape.idx,
            AdminConfigureLabelWithinShape.label
        ))
        result = await cls.select_execute(stmt)
        if len(result) > 0:
            return result[0]
        else:
            return None

    @classmethod
    async def find_by_draws(cls, draws: int) -> list['AdminConfigureModelLabelShape']:
        from app.shared.schemas.admin_configure_label_within_shapes import AdminConfigureLabelWithinShape
        # 预加载
        stmt = select(cls).filter(
            cls.draws == draws
        ).options(load_only(
            cls.shape_name,
        ), selectinload(
            cls.labels
        ).load_only(
            AdminConfigureLabelWithinShape.idx,
            AdminConfigureLabelWithinShape.label
        ))
        result = await cls.select_execute(stmt)
        return result
