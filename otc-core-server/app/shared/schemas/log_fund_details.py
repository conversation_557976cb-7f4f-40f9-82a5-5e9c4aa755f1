from datetime import datetime
from decimal import Decimal

from sqlalchemy import Column, select, func, and_
from sqlalchemy import VARCHAR, DECIMAL, INTEGER, TIMESTAMP
from app.shared.schemas import BaseSchema


class LogFundDetails(BaseSchema):
    __tablename__ = 'log_fund_details'

    id = Column(
        INTEGER,
        primary_key=True,
        autoincrement=True,
    )
    userid = Column(
        INTEGER,
        comment="用户"
    )
    currency_type = Column(
        INTEGER,
        comment="币类型"
    )
    amount = Column(
        DECIMAL(20, 10),
        comment="订单币数量"
    )
    before_amount = Column(
        DECIMAL(20, 10),
        comment="用户变化前的币数量"
    )
    after_amount = Column(
        DECIMAL(20, 10),
        comment="用户变化后的币数量"
    )
    order_id = Column(
        VARCHAR(255),
        comment="订单ID"
    )
    order_type = Column(
        INTEGER,
        comment="转账(兑汇的个人兑个人)"
    )
    remarks = Column(
        VARCHAR(255),
        comment="备注"
    )
    channel = Column(
        VARCHAR(255),
        comment="渠道号"
    )
    createdate = Column(
        TIMESTAMP,
        default=datetime.now,
        comment="创建时间"
    )
    user_type = Column(
        INTEGER,
        comment="用户类型"
    )
    coin_name = Column(
        VARCHAR(255),
        comment="币种名字"
    )
    money = Column(
        DECIMAL(20, 4),
        comment="法币数量"
    )
    after_money = Column(
        DECIMAL(20, 4),
        comment="用户变化后的法币数量"
    )
    platform_id = Column(
        INTEGER,
        comment="平台编号"
    )
    wallet_type = Column(
        INTEGER,
        comment="币付钱包"
    )

    @classmethod
    async def sum_order_type(
            cls,
            user_id: int,
            order_type: int,
            start_time: datetime,
            end_time: datetime,
    ) -> 'Decimal | None':
        stmt = select(cls.order_type, func.sum(cls.amount)).where(
            cls.userid == user_id,
            cls.order_type == order_type,
            cls.createdate.between(start_time, end_time),
        ).group_by(
            cls.order_type
        )
        results = await cls.select_execute_mappings(stmt)
        if len(results) > 0:
            return results[0]['sum']
        else:
            return None

    @classmethod
    async def stats_date(
            cls,
            user_id: int,
            start_time: datetime,
            end_time: datetime,
            page_size: int,
            offset: int,
            order_type: int | None = None
    ) -> 'list[LogFundDetails]':
        if order_type:
            where_filter = and_(
                cls.amount != 0,
                cls.userid == user_id,
                cls.order_type == order_type,
                cls.createdate.between(start_time, end_time),
            )
        else:
            where_filter = and_(
                cls.amount != 0,
                cls.userid == user_id,
                cls.createdate.between(start_time, end_time),
            )

        stmt = select(cls).where(
            where_filter
        ).order_by(
            cls.id.desc()
        ).limit(
            page_size
        ).offset(
            offset
        )
        results = await cls.select_execute(stmt) or []
        return results
