from datetime import datetime
from sqlalchemy import select, func, desc, and_
from sqlalchemy import Column, VARCHAR, TIMESTAMP, INTEGER, DATETIME, TEXT
from sqlalchemy.orm import load_only
from app.shared.schemas import BaseSchema, db


class NoticeContent(BaseSchema):
    __tablename__ = 'dy_notice_content'

    id = Column(
        INTEGER,
        primary_key=True,
        autoincrement=True,
    )
    title = Column(
        VARCHAR(100),
        comment="标题"
    )
    content = Column(
        TEXT,
        comment="内容"
    )
    type = Column(
        INTEGER,
        comment="类型(0-无)"
    )
    expiration_time = Column(
        DATETIME,
        comment="公告过期时间"
    )
    weight = Column(
        INTEGER,
        comment="排序权重"
    )
    enable_status = Column(
        INTEGER,
        comment="是否有效(0-无效,1-有效)"
    )
    remark = Column(
        VARCHAR(255),
        comment="备注"
    )
    create_time = Column(
        DATETIME,
        comment="创建时间"
    )
    update_time = Column(
        TIMESTAMP,
        default=datetime.now,
        comment="更新时间"
    )
    channel = Column(
        VARCHAR(255),
        comment="渠道号"
    )
    userid = Column(
        INTEGER,
        comment="发布者id"
    )
    nickname = Column(
        VARCHAR(255),
        comment="发布者名称"
    )

    @classmethod
    async def get_notices(cls, channel: str) -> 'list[NoticeContent]':
        stmt = select(cls).where(
            cls.enable_status == 1,
            cls.channel == channel,
            cls.expiration_time > datetime.now()
        )
        results = await cls.select_execute(stmt) or []
        return results
