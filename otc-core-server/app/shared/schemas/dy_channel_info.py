from datetime import datetime
from sqlalchemy import select, or_
from sqlalchemy import Column, VARCHAR, DECIMAL, INTEGER, BOOLEAN, TIMESTAMP
from sqlalchemy.orm import load_only
from app.shared.schemas import BaseSchema


class ChannelInfo(BaseSchema):
    __tablename__ = 'dy_channel_info'

    id = Column(
        INTEGER,
        primary_key=True,
        autoincrement=True,
    )
    account = Column(
        VARCHAR(145),
        comment="账号"
    )
    password = Column(
        VARCHAR(145),
        comment="密码"
    )
    web_account = Column(
        VARCHAR(255),
        comment="渠道前端账号"
    )
    web_password = Column(
        VARCHAR(255),
        comment="渠道前端密码"
    )
    channel = Column(
        VARCHAR(145),
        comment="渠道标识"
    )
    phonenum = Column(
        VARCHAR(145),
        comment="电话号码"
    )
    description = Column(
        VARCHAR(145),
        comment="描述"
    )
    coin_agent_num = Column(
        INTEGER,
        comment="币商代理的数量"
    )
    shop_agent_num = Column(
        INTEGER,
        comment="商户代理的数量"
    )
    coin_merchant_num = Column(
        INTEGER,
        comment="币商的数量"
    )
    shop_merchant_num = Column(
        INTEGER,
        comment="商户的数量"
    )
    paytypelist = Column(
        VARCHAR(245),
    )
    payidlist = Column(
        VARCHAR(245),
    )
    is_lock = Column(
        BOOLEAN,
        comment="1锁定禁用"
    )
    userid = Column(
        INTEGER,
    )
    create_date = Column(
        TIMESTAMP,
        default=datetime.now,
    )
    update_date = Column(
        TIMESTAMP,
        onupdate=datetime.now,
        default=datetime.now,
    )
    is_close = Column(
        INTEGER,
        comment="是否关闭此通道，1=关闭"
    )
    office_url = Column(
        VARCHAR(245),
        comment="渠道独立的域名"
    )
    server_ip = Column(
        VARCHAR(45),
        comment="服务器IP"
    )
    comm_type = Column(
        INTEGER,
        comment="按级差返佣，102=按保底返佣"
    )
    channel_deal = Column(
        INTEGER,
        comment="交易类型：0：公开，1:私有"
    )
    register_type = Column(
        VARCHAR(245),
        comment="：三个里面有至少有一个"
    )
    currency_type = Column(
        VARCHAR(255),
        comment="usdt,cnyb,eth,btc"
    )
    buy_comm_rate = Column(
        DECIMAL(10, 4),
        comment="平台买币收益费率"
    )
    add_buy_comm_rate = Column(
        DECIMAL(10, 4),
        comment="平台买币加收的手续费"
    )
    sell_comm_rate = Column(
        DECIMAL(10, 4),
        comment="平台卖币收益费率"
    )
    add_sell_comm_rate = Column(
        DECIMAL(10, 4),
        comment="平台卖币加收收益费率"
    )
    pay_json = Column(
        VARCHAR(255),
        comment="支付通道及费率json"
    )
    channel_type = Column(
        INTEGER,
        comment="400-码商"
    )
    orders_limit = Column(
        DECIMAL(10, 4),
        comment="人民币"
    )
    deal_limit = Column(
        DECIMAL(10, 4),
        comment="人民币"
    )
    channle_name = Column(
        VARCHAR(255),
        comment="渠道名字"
    )
    is_add_buy_comm = Column(
        INTEGER,
        comment="收"
    )
    fund_scheduling = Column(
        INTEGER,
        comment="不允许"
    )
    is_currency_buy_rate = Column(
        INTEGER,
        comment="收"
    )
    currency_add_buy_rate = Column(
        DECIMAL(10, 4),
        comment="币商买币加收收益费率"
    )
    is_external = Column(
        INTEGER,
        comment="1-是"
    )
    is_transfer_review = Column(
        INTEGER,
        comment="不需要"
    )
    platform_id = Column(
        INTEGER,
        comment="平台编号"
    )

    @classmethod
    async def find_all(cls) -> 'list[ChannelInfo]':
        stmt = select(cls)
        results = await cls.select_execute(stmt) or []
        return results

    @classmethod
    async def check_channel(cls, channel: str) -> 'ChannelInfo | None':
        stmt = select(cls).where(
            cls.channel == channel
        ).options(
            load_only(
                cls.is_lock,
            )
        )
        results = await cls.select_execute(stmt)
        if len(results) > 0:
            return results[0]
        else:
            return None

    @classmethod
    async def find(cls, channel: str) -> 'ChannelInfo | None':
        stmt = select(cls).where(
            cls.channel == channel
        )
        results = await cls.select_execute(stmt) or []
        if len(results) > 0:
            return results[0]
        else:
            return None

    @classmethod
    async def find_self_channels(cls) -> 'list[ChannelInfo]':
        stmt = select(cls).where(
            cls.channel_deal == 0,
            or_(
                cls.channel_type == 200,
                cls.channel_type == 400
            )
        )
        results = await cls.select_execute(stmt) or []
        return results

    @classmethod
    async def find_rate_within_channels(cls) -> 'list[ChannelInfo]':
        stmt = select(cls)
        results = await cls.select_execute(stmt) or []
        return results
