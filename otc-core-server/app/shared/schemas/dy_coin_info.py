from datetime import datetime
from sqlalchemy import select, func, desc, and_
from sqlalchemy import Column, VARCHAR, DECIMAL, INTEGER, DATETIME
from sqlalchemy.orm import load_only
from app.shared.schemas import BaseSchema, db


class CoinInfo(BaseSchema):
    __tablename__ = 'dy_coin_info'

    id = Column(
        INTEGER,
        primary_key=True,
        autoincrement=True,
    )
    coin_id = Column(
        INTEGER,
        comment="币种ID"
    )
    coin_name = Column(
        VARCHAR(45),
        comment="币种名字"
    )
    coin_type = Column(
        VARCHAR(45),
        comment="币种类型"
    )
    description = Column(
        VARCHAR(245),
        comment="描述"
    )
    buy_price = Column(
        DECIMAL(20, 4),
        comment="add_buy_prece"
    )
    sell_price = Column(
        DECIMAL(20, 4),
        comment="minus_sell_prece"
    )
    free_price = Column(
        DECIMAL(20, 4),
        comment="从其他交易市场获取回来的价格"
    )
    channel = Column(
        VARCHAR(45),
    )
    add_buy_price = Column(
        DECIMAL(20, 4),
        comment="add_buy_prece"
    )
    minus_sell_price = Column(
        DECIMAL(20, 4),
        comment="minus_sell_prece"
    )
    is_change = Column(
        INTEGER,
        comment="价格是否跟随市场价格变化，0：不变，1：改变"
    )
    free_sell_price = Column(
        DECIMAL(20, 4),
        comment="从火币取回来的卖的价格"
    )
    update_time = Column(
        DATETIME,
    )
    extract_currency_rate = Column(
        DECIMAL(10, 4),
        comment="提币手续费"
    )
    min_extract_currency_count = Column(
        INTEGER,
        comment="最小提币数额"
    )
    max_extract_currency_count = Column(
        INTEGER,
        comment="最大提币数额"
    )
    transfer_curreny_rate = Column(
        DECIMAL(10, 4),
        comment="转币手续费"
    )
    min_transfer_curreny_count = Column(
        INTEGER,
        comment="最小转币数额"
    )
    max_transfer_curreny_count = Column(
        INTEGER,
        comment="最大转币数额"
    )
    is_fixed_buy_rate = Column(
        INTEGER,
        comment="1是"
    )
    fixed_buy_rate = Column(
        DECIMAL(10, 4),
        comment="固定购买汇率"
    )
    is_fixed_sell_rate = Column(
        INTEGER,
        comment="1是"
    )
    fixed_sell_rate = Column(
        DECIMAL(10, 4),
        comment=""
    )
    status = Column(
        INTEGER,
        comment="禁止"
    )
    minus_coin_pay_price = Column(
        DECIMAL(20, 4),
        comment="minus_coin_pay_price"
    )
    is_fixed_coin_pay_rate = Column(
        INTEGER,
        comment="1是"
    )
    fixed_coin_pay_rate = Column(
        DECIMAL(10, 4),
        comment=""
    )
    extract_currency_rate_trc = Column(
        DECIMAL(10, 4),
        comment="提币手续费"
    )
    withdraw_coin_price_diff = Column(
        DECIMAL(10, 4),
        comment="提币价格差"
    )
    is_withdraw_coin_pay_rate = Column(
        INTEGER,
        comment="1是"
    )
    withdraw_coin_rate = Column(
        DECIMAL(10, 4),
        comment=""
    )

    @classmethod
    async def get_coin(
            cls,
            coin_id: int,
            user_id: int = None,
            channel: str = None
    ) -> 'CoinInfo | None':
        if user_id:
            stmt = select(cls).where(
                cls.coin_id == coin_id,
                cls.channel == f'{user_id}'
            )
        elif channel:
            stmt = select(cls).where(
                cls.coin_id == coin_id,
                cls.channel == channel
            )
        else:
            return None
        # --
        results = await cls.select_execute(stmt) or []
        if len(results) > 0:
            return results[0]
        else:
            return None

    @classmethod
    async def find_price_within_channels(cls) -> 'list[CoinInfo] | None':
        stmt = select(cls).where(
            cls.channel != 'ALL'
        )
        results = await cls.select_execute(stmt) or []
        return results
