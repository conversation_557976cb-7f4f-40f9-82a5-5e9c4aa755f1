from datetime import datetime
from sqlalchemy import select, func, desc, and_, TIMESTAMP
from sqlalchemy import Column, VARCHAR, DECIMAL, INTEGER, BOOLEAN
from sqlalchemy.orm import load_only
from app.shared.schemas import BaseSchema, db


class LogUserDaily(BaseSchema):
    __tablename__ = 'log_user_daily'

    id = Column(
        INTEGER,
        primary_key=True,
        autoincrement=True,
    )
    userid = Column(
        INTEGER,
    )
    dateid = Column(
        VARCHAR(45),
        comment="时间id"
    )
    nickname = Column(
        VARCHAR(45),
    )
    channel = Column(
        VARCHAR(32),
    )
    regdate = Column(
        TIMESTAMP,
        default=datetime.now,
    )
    total_count_buy = Column(
        INTEGER,
        comment="总订单数(买)"
    )
    total_count_sell = Column(
        INTEGER,
        comment="总订单数(卖)"
    )
    finish_count_buy = Column(
        INTEGER,
        comment="完成数(买)"
    )
    finish_count_sell = Column(
        INTEGER,
        comment="完成数(卖)"
    )
    cancel_count_buy = Column(
        INTEGER,
        comment="取消订单数(买)"
    )
    cancel_count_sell = Column(
        INTEGER,
        comment="取消订单数(卖)"
    )
    appeal_count = Column(
        INTEGER,
        comment="申诉次数"
    )
    buy_amount = Column(
        DECIMAL(20, 10),
        comment="总购入，钱包内买币"
    )
    sell_amount = Column(
        DECIMAL(20, 10),
        comment="总出售，钱包内卖币"
    )
    recharge_amount = Column(
        DECIMAL(20, 10),
        comment="总充入，外面的币到钱包"
    )
    extract_amount = Column(
        DECIMAL(20, 10),
        comment="总提币，钱包的币到外面"
    )
    transfer_in_amount = Column(
        DECIMAL(20, 10),
        comment="总转入，钱包到钱包"
    )
    transfer_out_amount = Column(
        DECIMAL(20, 10),
        comment="总转出，钱包到钱包"
    )
    reward_amount = Column(
        DECIMAL(20, 10),
        comment="总奖励"
    )
    fee_amount = Column(
        DECIMAL(20, 10),
        comment="总手续费"
    )
    teamuser_num = Column(
        INTEGER,
        comment="团队的人数"
    )
    team_amount = Column(
        DECIMAL(20, 10),
        comment="团队佣金"
    )
    confirm_time = Column(
        INTEGER,
        comment="确认时间(秒)"
    )
    buy_money = Column(
        DECIMAL(11, 4),
        comment="购买币时成交的人民币数量累加"
    )
    sell_money = Column(
        DECIMAL(11, 4),
        comment="出售币时成交的人民币数量累加"
    )
    coin_id = Column(
        INTEGER,
        comment="币种ID"
    )
    team_amount_buy = Column(
        DECIMAL(20, 10),
        comment="团队购买币数量"
    )
    team_money_buy = Column(
        DECIMAL(20, 4),
        comment="团队购买法币数量"
    )
    personal_income_buy = Column(
        DECIMAL(20, 10),
        comment="个人的购买收益币数量"
    )
    team_count_buy = Column(
        INTEGER,
        comment="团队购买订单总数量"
    )
    team_complete_count_buy = Column(
        INTEGER,
        comment="团队购买完成订单数量"
    )
    team_amount_sell = Column(
        DECIMAL(20, 10),
        comment="团队出售币数量"
    )
    team_money_sell = Column(
        DECIMAL(20, 4),
        comment="团队出售法币数量"
    )
    personal_income_sell = Column(
        DECIMAL(20, 10),
        comment="个人的出售收益币数量"
    )
    team_count_sell = Column(
        INTEGER,
        comment="团队出售订单总数量"
    )
    team_complete_count_sell = Column(
        INTEGER,
        comment="团队出售完成订单数量"
    )
    team_recharge_amount = Column(
        DECIMAL(20, 10),
        comment="团队充值数字货币数量"
    )
    team_recharge_money = Column(
        DECIMAL(20, 4),
        comment="团队充值法币数量"
    )
    team_withdraw_amount = Column(
        DECIMAL(20, 10),
        comment="团队提现数字货币数量"
    )
    team_withdraw_money = Column(
        DECIMAL(20, 4),
        comment="团队提现法币数量"
    )
    team_income_amount = Column(
        DECIMAL(20, 10),
        comment="团队收益"
    )
    r_coin_pay_sys = Column(
        DECIMAL(20, 10),
        comment="币支付充值数量-系统地址"
    )
    r_coin_pay_shop = Column(
        DECIMAL(20, 10),
        comment="币支付充值数量-商户地址"
    )
    r_coin_pay_deal_num = Column(
        INTEGER,
        comment="币付笔数"
    )
    exchange_money_limit_unnamed = Column(
        DECIMAL(20, 4),
        comment="未实名单日兑汇限额默认人民币(其他换算成人民币)"
    )
    exchange_amount_limit_unnamed = Column(
        INTEGER,
        comment="未实名单日兑汇笔数"
    )

    @classmethod
    async def user_today_flow(cls, user_id: int) -> 'LogUserDaily | None':
        today = datetime.today().strftime('%Y-%m-%d')
        stmt = select(cls).where(
            cls.userid == user_id,
            cls.dateid == today
        )
        # --
        results = await cls.select_execute(stmt) or []
        if len(results) > 0:
            return results[0]
        else:
            return None
