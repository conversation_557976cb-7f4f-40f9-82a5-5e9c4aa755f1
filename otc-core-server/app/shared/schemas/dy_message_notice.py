from datetime import datetime
from sqlalchemy import Column, select
from sqlalchemy import VA<PERSON>HA<PERSON>, TIMESTAMP, INTEGER, DATETIME
from app.shared.schemas import BaseSchema


class MessageNotice(BaseSchema):
    __tablename__ = 'dy_message_notice'

    id = Column(
        INTEGER,
        primary_key=True,
        autoincrement=True,
    )
    type = Column(
        INTEGER,
        comment="类型(0.无)"
    )
    title = Column(
        VARCHAR(100),
        comment="标题"
    )
    content = Column(
        comment="内容"
    )
    user_id = Column(
        INTEGER,
        comment="消息发送的对象"
    )
    enable_status = Column(
        INTEGER,
        comment="启用-1、禁用-0"
    )
    remark = Column(
        VARCHAR(255),
        comment="备注"
    )
    is_read = Column(
        INTEGER,
        comment="是否已读(0未读，1已读)"
    )
    create_time = Column(
        DATETIME,
        comment="创建时间"
    )
    update_time = Column(
        TIMESTAMP,
        default=datetime.now,
        comment="更新时间"
    )
    channel = Column(
        VARCHAR(255),
        comment="渠道号"
    )

    @classmethod
    async def find_all(cls, user_id: int) -> 'list[MessageNotice]':
        stmt = select(cls).where(
            cls.enable_status == 1,
            cls.user_id == user_id,
        )
        results = await cls.select_execute(stmt) or []
        return results
