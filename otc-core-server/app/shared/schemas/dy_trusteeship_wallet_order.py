from datetime import datetime
from sqlalchemy import Column, select
from sqlalchemy import VARCHAR, DECIMAL, INTEGER
from sqlalchemy import TIMESTAMP
from sqlalchemy.orm import load_only
from app.shared.schemas import BaseSchema


class TrusteeshipWalletOrder(BaseSchema):
    __tablename__ = 'dy_trusteeship_wallet_order'

    id = Column(
        INTEGER,
        primary_key=True,
        autoincrement=True,
    )
    order_id = Column(
        VARCHAR(45),
        comment="订单号"
    )
    channel = Column(
        VARCHAR(45),
        comment="渠道号"
    )
    userid = Column(
        INTEGER,
        comment="用户id"
    )
    nickname = Column(
        VARCHAR(255),
        comment="用户昵称"
    )
    currency_type = Column(
        VARCHAR(255),
        comment="币种"
    )
    amount = Column(
        DECIMAL(20, 10),
        comment="币数量"
    )
    actual_amount = Column(
        DECIMAL(20, 10),
        comment="实际数量"
    )
    trusteeship = Column(
        VARCHAR(255),
        comment="冷钱包地址"
    )
    user_txid = Column(
        VARCHAR(255),
        comment="用户上传TXID"
    )
    create = Column(
        TIMESTAMP,
        default=datetime.now,
        comment="创建时间"
    )
    remarks = Column(
        VARCHAR(255),
        comment="备注"
    )
    status = Column(
        INTEGER,
        comment="2-已取消"
    )
    coin_id = Column(
        INTEGER,
        comment="币种ID"
    )
    addr_type = Column(
        INTEGER,
        comment="t20"
    )
    coin_name = Column(
        VARCHAR(255),
        comment="dy_block_chain_trans"
    )
    addr_name = Column(
        VARCHAR(255),
        comment="地址名字"
    )
    platform_id = Column(
        INTEGER,
        comment="平台编号"
    )

    @classmethod
    async def get_orders(
            cls,
            user_id: int,
            page_size: int,
            offset: int,
    ) -> 'list[TrusteeshipWalletOrder]':
        stmt = select(cls).where(
            cls.userid == user_id,
        ).order_by(
            cls.id.desc()
        ).limit(
            page_size
        ).offset(
            offset
        )
        # --
        results = await cls.select_execute(stmt) or []
        return results
