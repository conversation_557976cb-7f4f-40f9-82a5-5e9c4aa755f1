from sqlalchemy import Column, VARCHAR, INTEGER, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship
from app.shared.schemas import BaseSchema


class AdminConfigureLabelWithinShape(BaseSchema):
    __tablename__ = 'admin_configure_label_within_shapes'
    __table_args__ = (
        UniqueConstraint('idx', 'shape_id', name='uix_idx_shape_id'),
    )

    idx = Column(
        INTEGER,
        nullable=False,
        comment='label 在 shape 中的记号'
    )
    label = Column(
        VARCHAR(50),
        nullable=False,
        comment='笔画数'
    )
    shape_id = Column(
        INTEGER,
        ForeignKey('admin_configure_model_label_shapes.id')
    )
    shape = relationship(
        'AdminConfigureModelLabelShape',
        back_populates='labels'
    )
