from datetime import datetime
from sqlalchemy import Column, select
from sqlalchemy import VA<PERSON><PERSON><PERSON>, INTEGER, DATETIME
from sqlalchemy import TIMESTAMP
from app.shared.schemas import BaseSchema


class GroupMember(BaseSchema):
    __tablename__ = 'dy_group_member'

    id = Column(
        INTEGER,
        primary_key=True,
        autoincrement=True,
    )
    group_id = Column(
        INTEGER,
        comment="小组ID"
    )
    group_name = Column(
        VARCHAR(255),
        comment="小组名称"
    )
    group_user_id = Column(
        INTEGER,
        comment="小组成员的ID"
    )
    group_user_name = Column(
        VARCHAR(255),
        comment="小组成员的昵称"
    )
    status = Column(
        INTEGER,
        comment="2-删除"
    )
    create_time = Column(
        DATETIME,
        default=datetime.now,
    )
    update_time = Column(
        TIMESTAMP,
        onupdate=datetime.now,
        default=datetime.now,
    )
    group_recharge_switch = Column(
        INTEGER,
        default=1,
        comment="1-开启"
    )
    group_withdraw_switch = Column(
        INTEGER,
        default=1,
        comment="1-开启"
    )
    group_behalf_switch = Column(
        INTEGER,
        default=1,
        comment="1-开启"
    )
