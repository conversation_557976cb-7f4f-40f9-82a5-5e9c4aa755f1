from functools import wraps
from typing import Coroutine

from quart import request
from app.shared.libs.base_view import BaseView


def validator(required=BaseView):
    """
    装饰器: 接口参数验证
    @:param required:    必要参数
    @:param optional:    可选参数, 仅验证输入类型
    """

    def decorator(f):
        @wraps(f)
        async def wrapper(*args, **kwargs):
            data = await request.get_json() if request.method == 'POST' else request.args
            if required:
                model = required.validate(data)
                return await f(model, *args, **kwargs)
            return None

        return wrapper

    return decorator
