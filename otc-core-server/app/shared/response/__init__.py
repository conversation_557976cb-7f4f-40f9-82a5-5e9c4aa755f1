from quart import jsonify
from app.shared.response.code import ResponseCode, code_dict
from app.shared.response.error import ResponseError


class Response:
    code: int = 0
    body: dict | None = None
    message: str = ''

    @classmethod
    def to_dict(cls):
        if hasattr(cls, 'body'):
            return {
                'code': cls.code,
                'body': cls.body,
                'message': cls.message,
            }
        else:
            return {
                'code': cls.code,
                'message': cls.message,
            }

    @classmethod
    def success(cls, data=None):
        cls.code = ResponseCode.SUCCESS.value
        if data:
            cls.body = data.__dict__
        else:
            if hasattr(cls, 'body'):
                delattr(cls, 'body')
        cls.message = code_dict[ResponseCode.SUCCESS]

        return jsonify(cls.to_dict())

    @classmethod
    def fail(cls, data: ResponseError):
        cls.code = data.code
        if hasattr(cls, 'body'):
            delattr(cls, 'body')
        cls.message = data.msg

        return jsonify(cls.to_dict())
