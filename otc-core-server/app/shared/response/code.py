from enum import Enum


class ResponseCode(Enum):
    WS_SEND_MESSAGE = 0
    CUSTOM = 1

    # COMMON
    SUCCESS = 200
    NOT_FOUND = 404
    FAIL = 500
    HTTP_FAIL = 501
    PARAM_FAIL = 502
    PARAM_TYPE_FAIL = 503
    AUTH_FAIL = 504
    USER_NOT_EXIST = 506
    INVALID_PASSWORD = 507
    UCOIN_H5_EXCHANGE_RATE_NOT_SETTING = 508

    # WebSocket
    # 1000 - 1999
    WS_AUTHCODE_NOT_EXIST = 1001
    WS_USER_NOT_EXIST = 1002
    WS_PASS_ERR = 1003
    WS_NOT_SUPPORT = 1004
    WS_CREATE_USER_ERR = 1005
    WS_PHONE_EXIST = 1006
    WS_PARAMETER_ERROR = 1007
    WS_PHONE_NOT_EXIST = 1008
    WS_PWD_ERR_1 = 1009
    WS_PWD_ERR_2 = 1010
    WS_PWD_ERR_3 = 1011
    WS_FUNDPWD_ERR_1 = 1012
    WS_NICKNAME_ERR_1 = 1013
    WS_PAYINFO_EXIST = 1014
    WS_ADD_ERR_1 = 1015
    WS_PAYINFO_NOT_EXIST = 1016
    WS_INVITECODE_ERROR = 1017
    WS_FUNDPASSWORD_NOT_EXIST = 1018
    WS_ACCOUNT_LOCKED = 1019
    WS_FUND_PASS_ERR = 1020
    WS_AMOUNT_LIMIT_ERR = 1021
    WS_AMOUNT_CANNOT_LESS_THAN_LIMIT = 1022
    WS_NOT_ALLOW_DUPLICATE_ORDER = 1023
    WS_CHANNEL_NOT_EXISTS = 1024
    WS_BALANCE_NOT_ENOUGH = 1025
    WS_FORBID_ACCEPT_ORDER = 1026
    WS_TEAM_FORBID_ACCEPT_ORDER = 1027
    WS_MIN_LIMIT_SMALLER_THAN_SETTING = 1028
    WS_INPUT_PRICE = 1029
    WS_FORBID_HANG_SELL_ORDER = 1030
    WS_FORBID_HANG_BUY_ORDER = 1031
    WS_NO_AVAILABLE_PAYMENT = 1032
    WS_AMOUNT_MUST_MORE_THEN_ZERO = 1033
    WS_HANG_ORDER_FAILED = 1034
    WS_PAYMENT_NOT_SUPPORT = 1035
    WS_NO_PAY_ORDER_LIMIT = 1036
    WS_HANG_ORDER_EXPIRED = 1037
    WS_HANG_ORDER_NOT_EXISTS = 1038
    WS_HANG_ORDER_PRIVATE = 1039
    WS_HANG_ORDER_NOT_BELONG_YOUR_CHANNEL = 1040
    WS_HANG_ORDER_TRADE_NOT_YET = 1041
    WS_HANG_ORDER_MONEY_NOT_ENOUGH = 1042

    WS_USER_BANNED_FOREVER = 5001
    WS_USER_BANNED_LIMITED = 5002
    WS_USER_PROHIBIT_LOGIN = 5003
    WS_CHANNEL_CLOSED = 5004

    # Configure
    # 2000 - 2999

    # Foreign
    # 3000 - 3999
    FOREIGN_CUSTOM = 3000


# ----

code_dict = {
    ResponseCode.CUSTOM: '',
    # COMMON
    ResponseCode.SUCCESS: '成功',
    ResponseCode.NOT_FOUND: '未知请求',
    ResponseCode.FAIL: '服务错误',
    ResponseCode.HTTP_FAIL: '请求错误',
    ResponseCode.PARAM_FAIL: '参数错误',
    ResponseCode.PARAM_TYPE_FAIL: '参数类型错误',
    ResponseCode.AUTH_FAIL: '非法请求',
    ResponseCode.USER_NOT_EXIST: '用户不存在',
    ResponseCode.INVALID_PASSWORD: '密码错误',
    ResponseCode.UCOIN_H5_EXCHANGE_RATE_NOT_SETTING: '未设置汇率',

    # WebSocket
    # 1000 - 1999
    ResponseCode.WS_AUTHCODE_NOT_EXIST: '请输入正确验证码或手机',
    ResponseCode.WS_USER_NOT_EXIST: "用户不存在",
    ResponseCode.WS_PASS_ERR: "密码错误",
    ResponseCode.WS_NOT_SUPPORT: "暂不支持这种方式",
    ResponseCode.WS_CREATE_USER_ERR: "创建用户失败",
    ResponseCode.WS_PHONE_EXIST: "手机号码已被注册",
    ResponseCode.WS_PARAMETER_ERROR: "参数错误",
    ResponseCode.WS_PHONE_NOT_EXIST: "手机号码还未注册",
    ResponseCode.WS_PWD_ERR_1: "原密码错误",
    ResponseCode.WS_PWD_ERR_2: "新密码与源密码相同",
    ResponseCode.WS_PWD_ERR_3: "密码不能空",
    ResponseCode.WS_FUNDPWD_ERR_1: "资金密码已被设置",
    ResponseCode.WS_NICKNAME_ERR_1: "昵称不能为空",
    ResponseCode.WS_PAYINFO_EXIST: "该账号已经被绑定",
    ResponseCode.WS_ADD_ERR_1: "添加失败",
    ResponseCode.WS_PAYINFO_NOT_EXIST: "该账号未被绑定",
    ResponseCode.WS_INVITECODE_ERROR: "邀请码错误",
    ResponseCode.WS_FUNDPASSWORD_NOT_EXIST: "请先设置资金密码",
    ResponseCode.WS_ACCOUNT_LOCKED: "你已经被锁定无法进行该操作",
    ResponseCode.WS_FUND_PASS_ERR: "资金密码错误",
    ResponseCode.WS_AMOUNT_LIMIT_ERR: "限额有错误",
    ResponseCode.WS_AMOUNT_CANNOT_LESS_THAN_LIMIT: "挂单数量小于最小单笔最大限额",
    ResponseCode.WS_NOT_ALLOW_DUPLICATE_ORDER: "已经有挂单了，请勿重复挂单",
    ResponseCode.WS_CHANNEL_NOT_EXISTS: "未找到渠道配置",
    ResponseCode.WS_BALANCE_NOT_ENOUGH: "余额不足",
    ResponseCode.WS_FORBID_ACCEPT_ORDER: "您已被禁止接单",
    ResponseCode.WS_TEAM_FORBID_ACCEPT_ORDER: "您的团队已被禁止接单",
    ResponseCode.WS_MIN_LIMIT_SMALLER_THAN_SETTING: "最小限额小于交易设置",
    ResponseCode.WS_INPUT_PRICE: "请输入单价",
    ResponseCode.WS_FORBID_HANG_SELL_ORDER: "您被设置为不允许挂卖",
    ResponseCode.WS_FORBID_HANG_BUY_ORDER: "您被设置为不允许挂买",
    ResponseCode.WS_NO_AVAILABLE_PAYMENT: "请先绑定收款方式",
    ResponseCode.WS_AMOUNT_MUST_MORE_THEN_ZERO: "挂单数量必须大于0",
    ResponseCode.WS_HANG_ORDER_FAILED: "挂单失败",
    ResponseCode.WS_PAYMENT_NOT_SUPPORT: "你未开通该支付方式",
    ResponseCode.WS_NO_PAY_ORDER_LIMIT: "未付款订单已达上限，请先完成支付",
    ResponseCode.WS_HANG_ORDER_EXPIRED: "此挂单已失效",
    ResponseCode.WS_HANG_ORDER_NOT_EXISTS: "订单不存在",
    ResponseCode.WS_HANG_ORDER_PRIVATE: "该订单是私有的，您无法购买",
    ResponseCode.WS_HANG_ORDER_NOT_BELONG_YOUR_CHANNEL: "该订单不属于的您的渠道，您无法购买",
    ResponseCode.WS_HANG_ORDER_TRADE_NOT_YET: "该订单还不能购买",
    ResponseCode.WS_HANG_ORDER_MONEY_NOT_ENOUGH: "挂单余额不足",
    # Configure
    # 2000 - 2999
    ResponseCode.WS_USER_BANNED_FOREVER: "账号已被永久封禁",
    ResponseCode.WS_USER_BANNED_LIMITED: "账号已被封禁, 解封时间为: ",
    ResponseCode.WS_USER_PROHIBIT_LOGIN: "该账号已被禁止登陆",
    ResponseCode.WS_CHANNEL_CLOSED: "渠道已被关闭",

    # Foreign
    # 3000 - 3999
    ResponseCode.FOREIGN_CUSTOM: ''
}
