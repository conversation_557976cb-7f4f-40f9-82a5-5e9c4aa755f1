from werkzeug.exceptions import HTTPException
from app.shared.response.code import ResponseCode, code_dict
from enums.event import ResponseEventCode


class ResponseError(HTTPException):
    code: int = ResponseCode.FAIL.value
    """默认 <ResponseCode.FAIL> 500"""
    msg = code_dict[ResponseCode.FAIL]

    def __init__(self, code: ResponseCode = None, specify: str = None):
        if code:
            self.code = int(code.value)
            if specify:
                if code_dict[code]:
                    self.msg = f'{code_dict[code]}, {specify}'
                else:
                    self.msg = f'{specify}'
            else:
                self.msg = f'{code_dict[code]}'
        super(ResponseError, self).__init__(code)


class WSResponseError(BaseException):
    def __init__(
            self,
            code: ResponseCode,
            event_code: ResponseEventCode,
            proto,
            specify_msg: str | None = None
    ):
        super().__init__()
        from app.shared import response_binary_data
        proto.result = code.value
        proto.msg = code_dict[code]
        if specify_msg:
            proto.msg += specify_msg
        str_data = proto.SerializeToString()
        self.data = response_binary_data(event_code.value, str_data)
