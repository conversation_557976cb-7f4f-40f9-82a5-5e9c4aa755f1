import json
from decimal import Decimal
from quart import current_app
from redis import asyncio as aioredis
from app.shared import ResponseCode, md5_hash
from app.shared.libs.redis_key import *
from app.shared.response.error import WSResponseError
from app.shared.schemas.dy_user_info import UserInfo
from enums.event import ResponseEventCode


class BaseService:

    @property
    def redis(self):
        if hasattr(self.app, "redis"):
            if isinstance(self.app.redis, aioredis.Redis):
                return self.app.redis
        return None

    def __init__(self):
        self.app = current_app

    async def check_user_exists(
            self,
            user_id: int,
            code: ResponseEventCode,
            resp_pb
    ):
        user_json_str = await self.redis.get(RDS_USER % user_id)
        if user_json_str:
            user_dict = json.loads(user_json_str)
            user = UserInfo()
            user.from_dict(user_dict)
            # --
            user.erc_usdt_amount = Decimal(
                await self.redis.hget(
                    RDS_USER_USDT_AMOUNT,
                    str(user_id)
                )
            )
            user.erc_usdt_lock_amount = Decimal(
                await self.redis.hget(
                    RDS_USER_USDT_LOCK_AMOUNT,
                    str(user_id)
                )
            )
            user.erc_fc_amount = Decimal(
                await self.redis.hget(
                    RDS_USER_FC_AMOUNT,
                    str(user_id)
                )
            )
            user.erc_fc_lock_amount = Decimal(
                await self.redis.hget(
                    RDS_USER_FC_LOCK_AMOUNT,
                    str(user_id)
                )
            )
            user.coin_pay_usdt_amount = Decimal(
                await self.redis.hget(
                    RDS_USER_COIN_PAY_USDT_AMOUNT,
                    str(user_id)
                )
            )
            user.coin_pay_usdt_lock_amount = Decimal(
                await self.redis.hget(
                    RDS_USER_COIN_PAY_USDT_LOCK_AMOUNT,
                    str(user_id)
                )
            )
        else:
            user = await UserInfo.get_user_info_by_id(user_id)

        if not user:
            raise WSResponseError(
                ResponseCode.WS_USER_NOT_EXIST,
                code,
                resp_pb
            )
        return user

    @staticmethod
    def verify_fund_password(user: UserInfo, fund_password: str):
        return user.fundpassword == md5_hash(fund_password)
