from typing import get_args
from enum import EnumMeta

from app.shared.response import ResponseCode, ResponseError
from app.shared.schemas import BaseSchema


class BaseView:
    __abstract__ = True

    @property
    def schema_dict(self):
        __dict = {}
        for param, param_type in self.__annotations__.items():
            __dict[param] = self.__getattribute__(param)
        return __dict

    @property
    def __dict__(self):
        __dict = {}
        for param, param_type in self.__annotations__.items():
            if not hasattr(self, param):
                continue

            attr = getattr(self, param)
            if isinstance(attr, list):
                __dict[param] = []
                for item in attr:
                    if isinstance(item, BaseSchema):
                        __dict[param].append(item.to_dict())
                    else:
                        try:
                            __dict[param].append(item.__dict__)
                        except AttributeError:
                            __dict[param].append(item)
            else:
                if isinstance(attr, BaseSchema):
                    __dict[param] = attr.to_dict()
                else:
                    if type(getattr(self, param).__class__) is EnumMeta:
                        __dict[param] = getattr(self, param).value
                    else:
                        __dict[param] = attr

        return __dict

    def __init__(self, data: dict = None):
        if data is None:
            return
        for k, v in data.items():
            if type(self.__annotations__[k]) is EnumMeta:
                self.__setattr__(k, self.__annotations__[k](v))
            else:
                self.__setattr__(k, v)


    def dict(self, exclude=None):
        """
        返回对象的字典表示，支持排除指定字段

        注意：此方法名与Python内置dict()函数同名，但作用域不同，不会产生冲突。
        内置dict()用法：dict([('a', 1), ('b', 2)])
        此方法用法：instance.dict(exclude={'field'})

        :param exclude: 要排除的字段集合
        :return: 字典
        """
        if exclude is None:
            exclude = set()

        result = {}
        for param in self.__annotations__.keys():
            if param in exclude:
                continue
            if hasattr(self, param):
                attr = getattr(self, param)
                if isinstance(attr, list):
                    result[param] = []
                    for item in attr:
                        if hasattr(item, 'to_dict'):
                            result[param].append(item.to_dict())
                        elif hasattr(item, '__dict__'):
                            try:
                                result[param].append(item.__dict__)
                            except AttributeError:
                                result[param].append(item)
                        else:
                            result[param].append(item)
                else:
                    if hasattr(attr, 'to_dict'):
                        result[param] = attr.to_dict()
                    else:
                        if type(attr.__class__) is EnumMeta:
                            result[param] = attr.value
                        else:
                            result[param] = attr
        return result

    def __repr__(self):
        return f'<{self.__class__.__name__} {self.__dict__}>'

    @classmethod
    def validate(cls, data):
        """
        基类方法 - 验证表单字段和数据类型

        @update: 宽松验证

        :param data: Any model
        :return: None
        """
        for param, param_type in cls.__annotations__.items():
            if not hasattr(data, 'keys'):
                raise ResponseError(ResponseCode.PARAM_FAIL)
            if param not in data.keys():
                if type(None) in get_args(param_type):
                    continue
                raise ResponseError(ResponseCode.PARAM_FAIL)

        return cls(data)
