from datetime import datetime
from decimal import Decimal, ROUND_HALF_UP
from app.shared.response import ResponseError, ResponseCode
from enums import TradeType
from enums.event import RequestEventCode


def decimal_to_str(amount: Decimal | float, decimal_places=4):
    """
    格式化金额，移除不必要的末尾零

    Args:
        amount: Decimal | float 数值
        decimal_places: 最大保留小数位数

    Returns:
        格式化后的字符串
    """
    if isinstance(amount, float):
        amount = Decimal(amount)
    # 先四舍五入到指定位数
    rounded = amount.quantize(
        Decimal('0.' + '0' * decimal_places),
        rounding=ROUND_HALF_UP
    )

    # 移除末尾零
    s = str(rounded)
    if '.' in s:
        s = s.rstrip('0').rstrip('.')

    return s


def resolve_user_trade_type_within_order(
        user_id: int,
        order_type: TradeType,
        customer_user_id: int,
        vendor_user_id: int,
):
    if user_id == customer_user_id:
        # 用户是顾客
        if order_type == TradeType.BUY:
            # 买
            return order_type.BUY.value
        elif order_type == TradeType.SELL:
            # 卖
            return order_type.SELL.value
    elif user_id == vendor_user_id:
        # 用户是币商
        if order_type == TradeType.BUY:
            # 卖
            return order_type.SELL.value
        elif order_type == TradeType.SELL:
            # 买
            return order_type.BUY.value


def convert_to_proto(proto, data):
    """
    将映射表中的字段设置到 proto 的映射 message 中
    :param proto:
    :param data:
    :return:
    """
    from app.shared.schemas import BaseSchema
    if isinstance(data, BaseSchema):
        for attr_name, attr_value in data.attrs.items():
            if hasattr(proto, attr_name):
                if attr_value:
                    if isinstance(attr_value, datetime):
                        attr_value = str(attr_value)
                    elif isinstance(attr_value, list):
                        continue
                    elif isinstance(attr_value, Decimal):
                        attr_value = decimal_to_str(attr_value)
                    setattr(proto, attr_name, attr_value)
    elif isinstance(data, dict):
        for k, v in data.items():
            if not v:
                continue
            if hasattr(proto, k):
                setattr(proto, k, v)

    return proto


def get_timestamp_from_string(date_string, format_str="%Y-%m-%d %H:%M:%S"):
    """
    根据日期字符串获取时间戳

    Args:
        date_string: 日期字符串，如 "2023-01-01 12:00:00"
        format_str: 日期格式，默认为 "%Y-%m-%d %H:%M:%S"

    Returns:
        float: 时间戳（秒级）
    """
    try:
        dt = datetime.strptime(date_string, format_str)
        return dt.timestamp()
    except ValueError as e:
        raise ValueError(f"日期格式不匹配: {e}")


def md5_hash(text: str):
    import hashlib
    return hashlib.md5(text.encode('utf-8')).hexdigest()


def hash_password(password: str):
    import bcrypt
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')


def verify_password(password: str, hashed: str):
    import bcrypt
    return bcrypt.checkpw(
        password.encode('utf-8'),
        hashed.encode('utf-8')
    )


def process_binary_data(data):
    """
    :param data: socket 来自前端的消息
    :return: (code, pb_data) code:     操作码；pb_data:  proto 数据体
    """
    # 检查数据长度是否足够
    if len(data) < 12:
        raise ResponseError(ResponseCode.CUSTOM, '数据长度不足')

    # --
    offset = 0

    # 1. 解析前4字节的magic string "head"
    head_magic = bytes(data[offset:offset + 4]).decode('ascii')
    offset += 4

    if head_magic != "head":
        raise ResponseError(ResponseCode.CUSTOM, '无效的头部标识')

    # 2. 解析4字节的protobuf数据长度 (小端字节序)
    pb_length = 0
    for i in range(4):
        pb_length |= data[offset + i] << (i * 8)
    offset += 4

    # 验证protobuf数据长度
    if len(data) < 12 + pb_length:
        raise ResponseError(ResponseCode.CUSTOM, '数据长度不足')

    # 3. 解析2字节的code (小端字节序)
    code = 0
    for i in range(2):
        code |= data[offset + i] << (i * 8)
    offset += 2

    # 4. 解析2字节的校验码 (小端字节序)
    checksum = 0
    for i in range(2):
        checksum |= data[offset + i] << (i * 8)
    offset += 2

    # 验证校验码
    expected_checksum = code + 99
    if checksum != expected_checksum:
        raise ResponseError(ResponseCode.CUSTOM, '校验码错误')

    # 5. 提取protobuf数据
    pb_data = bytes(data[offset:offset + pb_length])
    return RequestEventCode(code), pb_data


def response_binary_data(code: int, data):
    """按照前端协议格式发送 protobuf 响应"""
    # 构建响应头部
    response_length = len(data)

    # 创建Uint8Array来存储完整响应
    send_data = bytearray(12 + response_length)
    offset = 0

    # 1. 写入magic string "head"
    head_str = "head"
    for i in range(4):
        send_data[offset + i] = ord(head_str[i])
    offset += 4

    # 2. 写入protobuf数据长度 (4字节，小端字节序)
    for i in range(4):
        send_data[offset + i] = (response_length >> (i * 8)) & 0xFF
    offset += 4

    # 3. 写入响应代码 (2字节，小端字节序)
    for i in range(2):
        send_data[offset + i] = (code >> (i * 8)) & 0xFF
    offset += 2

    # 4. 写入校验码 (2字节，code + 99，小端字节序)
    checksum = code + 99
    for i in range(2):
        send_data[offset + i] = (checksum >> (i * 8)) & 0xFF
    offset += 2

    # 5. 写入protobuf数据
    send_data[offset:offset + response_length] = data

    return bytes(send_data)
