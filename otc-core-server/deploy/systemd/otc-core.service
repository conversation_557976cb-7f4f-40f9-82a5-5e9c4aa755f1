[Unit]
Description=OTC Core Server - Quart Web Application
Documentation=https://github.com/your-org/otc-core-server
After=network.target mysql.service redis.service
Wants=mysql.service redis.service
Requires=network.target

[Service]
Type=simple
User=otc
Group=otc
WorkingDirectory=/opt/otc-core-server

# 环境变量
Environment=QUART_ENV=production
Environment=PYTHONPATH=/opt/otc-core-server
Environment=PYTHONUNBUFFERED=1

# 环境变量文件
EnvironmentFile=/opt/otc-core-server/.env

# 启动命令
ExecStart=/opt/otc-core-server/venv/bin/python run.py
ExecReload=/bin/kill -HUP $MAINPID

# 重启策略
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/otc-core-server/logs
ReadWritePaths=/opt/otc-core-server/uploads
ReadWritePaths=/tmp

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 日志设置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=otc-core

# 进程管理
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

[Install]
WantedBy=multi-user.target
