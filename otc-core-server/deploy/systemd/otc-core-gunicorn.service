[Unit]
Description=OTC Core Server - Quart Web Application with Gunicorn
Documentation=https://github.com/your-org/otc-core-server
After=network.target mysql.service redis.service
Wants=mysql.service redis.service
Requires=network.target

[Service]
Type=notify
User=otc
Group=otc
WorkingDirectory=/opt/otc-core-server

# 环境变量
Environment=QUART_ENV=production
Environment=PYTHONPATH=/opt/otc-core-server
Environment=PYTHONUNBUFFERED=1

# 环境变量文件
EnvironmentFile=/opt/otc-core-server/.env

# Gunicorn 启动命令
ExecStart=/opt/otc-core-server/venv/bin/gunicorn \
    --bind 127.0.0.1:6002 \
    --workers 4 \
    --worker-class quart.worker.GunicornWorker \
    --worker-connections 1000 \
    --max-requests 1000 \
    --max-requests-jitter 100 \
    --timeout 60 \
    --keep-alive 5 \
    --preload \
    --access-logfile /opt/otc-core-server/logs/gunicorn-access.log \
    --error-logfile /opt/otc-core-server/logs/gunicorn-error.log \
    --log-level info \
    --pid /opt/otc-core-server/logs/gunicorn.pid \
    run:app

ExecReload=/bin/kill -HUP $MAINPID

# 重启策略
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/otc-core-server/logs
ReadWritePaths=/opt/otc-core-server/uploads
ReadWritePaths=/tmp

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 日志设置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=otc-core-gunicorn

# 进程管理
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

[Install]
WantedBy=multi-user.target
