version: '3.8'

services:
  # OTC Core 应用
  otc-core:
    build:
      context: ../../
      dockerfile: deploy/docker/Dockerfile
    container_name: otc-core-app
    restart: unless-stopped
    ports:
      - "6002:6002"
    environment:
      - QUART_ENV=production
      - DB_HOST=mysql
      - REDIS_HOST=redis
    env_file:
      - .env
    volumes:
      - ../../logs:/app/logs
      - ../../uploads:/app/uploads
      - ../../static:/app/static
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - otc-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: otc-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./mysql/my.cnf:/etc/mysql/conf.d/my.cnf:ro
    ports:
      - "3306:3306"
    networks:
      - otc-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${DB_ROOT_PASSWORD}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: otc-redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD} --appendonly yes
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    ports:
      - "6379:6379"
    networks:
      - otc-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: otc-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ../../static:/var/www/static:ro
      - ../../uploads:/var/www/uploads:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - otc-core
    networks:
      - otc-network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  nginx_logs:
    driver: local

networks:
  otc-network:
    driver: bridge
