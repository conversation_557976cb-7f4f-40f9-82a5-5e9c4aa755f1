# Docker 环境配置文件
# 复制此文件为 .env 并修改相应的值

# ==================== 应用配置 ====================
QUART_ENV=production
SECRET_KEY=your-super-secret-key-change-this
API_PREFIX=/

# ==================== 数据库配置 ====================
DB_ROOT_PASSWORD=your-mysql-root-password
DB_HOST=mysql
DB_PORT=3306
DB_USER=otc_user
DB_PASSWORD=your-secure-database-password
DB_NAME=otc
DB_URL=mysql+aiomysql://otc_user:your-secure-database-password@mysql:3306/otc

# ==================== Redis 配置 ====================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your-redis-password

# ==================== 安全配置 ====================
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ACCESS_TOKEN_EXPIRES=3600
JWT_REFRESH_TOKEN_EXPIRES=86400

# ==================== 文件上传配置 ====================
UPLOAD_FOLDER=/app/uploads
MAX_CONTENT_LENGTH=52428800
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,pdf,doc,docx

# ==================== 邮件配置 ====================
MAIL_SERVER=smtp.your-domain.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-mail-password

# ==================== 监控配置 ====================
SENTRY_DSN=https://<EMAIL>/project-id
ENABLE_METRICS=true

# ==================== 其他配置 ====================
DEBUG=false
TIMEZONE=Asia/Shanghai
CORS_ORIGINS=https://your-frontend-domain.com
