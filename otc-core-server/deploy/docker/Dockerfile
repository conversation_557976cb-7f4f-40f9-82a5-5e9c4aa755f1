# OTC Core Server Dockerfile
# 基于 Python 3.9 的生产环境镜像

FROM python:3.9-slim

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    QUART_ENV=production

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    default-libmysqlclient-dev \
    pkg-config \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建非 root 用户
RUN groupadd -r otc && useradd -r -g otc otc

# 复制依赖文件
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir gunicorn

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p logs uploads static && \
    chown -R otc:otc /app

# 切换到非 root 用户
USER otc

# 暴露端口
EXPOSE 6002

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:6002/health || exit 1

# 启动命令
CMD ["gunicorn", "--bind", "0.0.0.0:6002", "--workers", "4", "--worker-class", "quart.worker.GunicornWorker", "--access-logfile", "-", "--error-logfile", "-", "run:app"]
