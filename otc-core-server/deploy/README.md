# OTC Core Server 部署指南

本文档提供了在 Linux 服务器上部署 OTC Core Server (Quart 应用) 的完整指南。

## 目录结构

```
deploy/
├── README.md                    # 本文档
├── nginx/
│   └── otc-core.conf           # Nginx 配置文件
├── systemd/
│   ├── otc-core.service        # 基础 systemd 服务
│   └── otc-core-gunicorn.service # Gunicorn systemd 服务
├── scripts/
│   ├── deploy.sh               # 自动部署脚本
│   └── update.sh               # 应用更新脚本
└── config/
    ├── .env.production         # 生产环境配置模板
    └── README.md               # 配置说明文档
```

## 系统要求

### 最低配置
- **操作系统**: Ubuntu 20.04 LTS 或 CentOS 8+
- **CPU**: 2 核心
- **内存**: 4GB RAM
- **存储**: 20GB 可用空间
- **网络**: 公网 IP 地址

### 推荐配置
- **操作系统**: Ubuntu 22.04 LTS
- **CPU**: 4 核心
- **内存**: 8GB RAM
- **存储**: 50GB SSD
- **网络**: 公网 IP + 域名

### 软件依赖
- Python 3.9+
- MySQL 8.0+
- Redis 6.0+
- Nginx 1.18+

## 快速部署

### 1. 自动部署（推荐）

```bash
# 下载部署脚本
wget https://raw.githubusercontent.com/your-org/otc-core-server/main/deploy/scripts/deploy.sh

# 给脚本执行权限
chmod +x deploy.sh

# 运行部署脚本
sudo ./deploy.sh
```

### 2. 手动部署

如果需要更精细的控制，请参考下面的手动部署步骤。

## 手动部署步骤

### 步骤 1: 准备服务器环境

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础依赖
sudo apt install -y python3 python3-pip python3-venv python3-dev \
    build-essential nginx mysql-server redis-server git curl wget \
    supervisor ufw fail2ban logrotate
```

### 步骤 2: 创建应用用户

```bash
# 创建专用用户
sudo useradd -r -s /bin/bash -d /opt/otc-core-server -m otc
sudo usermod -aG otc otc

# 创建目录结构
sudo mkdir -p /opt/otc-core-server/{logs,uploads,static,backups}
sudo chown -R otc:otc /opt/otc-core-server
sudo chmod -R 755 /opt/otc-core-server
```

### 步骤 3: 部署应用代码

```bash
# 克隆代码
sudo -u otc git clone https://github.com/your-org/otc-core-server.git /opt/otc-core-server
cd /opt/otc-core-server

# 设置 Python 虚拟环境
sudo -u otc python3 -m venv venv
sudo -u otc ./venv/bin/pip install --upgrade pip
sudo -u otc ./venv/bin/pip install -r requirements.txt
sudo -u otc ./venv/bin/pip install gunicorn
```

### 步骤 4: 配置数据库

```bash
# 配置 MySQL
sudo mysql_secure_installation

# 创建数据库和用户
sudo mysql -u root -p << EOF
CREATE USER 'otc_user'@'localhost' IDENTIFIED BY 'your-secure-password';
CREATE DATABASE otc CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
GRANT ALL PRIVILEGES ON otc.* TO 'otc_user'@'localhost';
FLUSH PRIVILEGES;
EOF

# 配置 Redis
sudo nano /etc/redis/redis.conf
# 设置密码: requirepass your-redis-password
sudo systemctl restart redis-server
sudo systemctl enable redis-server
```

### 步骤 5: 配置应用环境

```bash
# 复制配置文件
sudo cp deploy/config/.env.production /opt/otc-core-server/.env

# 编辑配置文件
sudo nano /opt/otc-core-server/.env
# 修改数据库密码、Redis密码、SECRET_KEY等

# 设置文件权限
sudo chown otc:otc /opt/otc-core-server/.env
sudo chmod 600 /opt/otc-core-server/.env
```

### 步骤 6: 配置 Nginx

```bash
# 复制 Nginx 配置
sudo cp deploy/nginx/otc-core.conf /etc/nginx/sites-available/

# 修改域名配置
sudo nano /etc/nginx/sites-available/otc-core.conf
# 将 your-domain.com 替换为实际域名

# 启用站点
sudo ln -s /etc/nginx/sites-available/otc-core.conf /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启 Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

### 步骤 7: 配置 SSL 证书

```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx

# 获取 SSL 证书
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 设置自动续期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

### 步骤 8: 配置 systemd 服务

```bash
# 复制服务文件
sudo cp deploy/systemd/otc-core-gunicorn.service /etc/systemd/system/

# 重新加载 systemd
sudo systemctl daemon-reload

# 启用并启动服务
sudo systemctl enable otc-core-gunicorn
sudo systemctl start otc-core-gunicorn
```

### 步骤 9: 配置防火墙

```bash
# 启用防火墙
sudo ufw enable

# 允许必要端口
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443

# 查看状态
sudo ufw status
```

### 步骤 10: 验证部署

```bash
# 检查服务状态
sudo systemctl status otc-core-gunicorn
sudo systemctl status nginx

# 检查端口监听
sudo netstat -tlnp | grep :6002
sudo netstat -tlnp | grep :80
sudo netstat -tlnp | grep :443

# 测试应用
curl -k https://your-domain.com/health
```

## 应用管理

### 启动/停止/重启服务

```bash
# 启动服务
sudo systemctl start otc-core-gunicorn

# 停止服务
sudo systemctl stop otc-core-gunicorn

# 重启服务
sudo systemctl restart otc-core-gunicorn

# 查看状态
sudo systemctl status otc-core-gunicorn

# 查看日志
sudo journalctl -u otc-core-gunicorn -f
```

### 应用更新

```bash
# 使用更新脚本
sudo /opt/otc-core-server/deploy/scripts/update.sh

# 或手动更新
cd /opt/otc-core-server
sudo systemctl stop otc-core-gunicorn
sudo -u otc git pull origin main
sudo -u otc ./venv/bin/pip install -r requirements.txt --upgrade
sudo systemctl start otc-core-gunicorn
```

### 日志管理

```bash
# 应用日志
sudo journalctl -u otc-core-gunicorn -f

# Nginx 日志
sudo tail -f /var/log/nginx/otc-core-access.log
sudo tail -f /var/log/nginx/otc-core-error.log

# 应用文件日志
sudo tail -f /opt/otc-core-server/logs/runtime.log
sudo tail -f /opt/otc-core-server/logs/access.log
```

## 监控和维护

### 性能监控

```bash
# 系统资源监控
htop
iotop
nethogs

# 应用进程监控
ps aux | grep gunicorn
ps aux | grep nginx

# 数据库监控
sudo mysql -u root -p -e "SHOW PROCESSLIST;"
```

### 备份策略

```bash
# 数据库备份
mysqldump -u otc_user -p otc | gzip > /opt/backups/otc_$(date +%Y%m%d).sql.gz

# 文件备份
tar -czf /opt/backups/uploads_$(date +%Y%m%d).tar.gz /opt/otc-core-server/uploads/

# 配置备份
cp /opt/otc-core-server/.env /opt/backups/config/.env.$(date +%Y%m%d)
```

### 安全维护

```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 检查失败登录
sudo fail2ban-client status sshd

# 查看防火墙状态
sudo ufw status verbose

# 检查 SSL 证书
sudo certbot certificates
```

## 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   sudo journalctl -u otc-core-gunicorn -n 50
   ```

2. **数据库连接失败**
   ```bash
   sudo mysql -u otc_user -p
   ```

3. **Redis 连接失败**
   ```bash
   redis-cli -a your-redis-password ping
   ```

4. **Nginx 配置错误**
   ```bash
   sudo nginx -t
   ```

5. **SSL 证书问题**
   ```bash
   sudo certbot certificates
   sudo certbot renew --dry-run
   ```

### 性能优化

1. **调整 Gunicorn worker 数量**
   ```bash
   # 编辑服务文件
   sudo nano /etc/systemd/system/otc-core-gunicorn.service
   # 修改 --workers 参数为 CPU核心数 * 2 + 1
   ```

2. **优化数据库配置**
   ```sql
   SET GLOBAL innodb_buffer_pool_size = 1073741824;
   SET GLOBAL max_connections = 200;
   ```

3. **配置 Redis 内存限制**
   ```
   maxmemory 512mb
   maxmemory-policy allkeys-lru
   ```

## 联系支持

如果在部署过程中遇到问题，请：

1. 查看相关日志文件
2. 检查配置文件设置
3. 参考故障排除部分
4. 联系技术支持团队

---

**注意**: 请确保在生产环境中修改所有默认密码和密钥，并定期进行安全更新。
