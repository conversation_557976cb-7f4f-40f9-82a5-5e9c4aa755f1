# OTC Core Server Nginx Configuration
# 适用于 Quart 异步 Web 框架的 Nginx 配置

# 上游服务器配置 - Quart 应用服务器
upstream otc_backend {
    # 主应用服务器
    server 127.0.0.1:6002 weight=1 max_fails=3 fail_timeout=30s;
    
    # 如果需要负载均衡，可以添加更多服务器实例
    # server 127.0.0.1:6003 weight=1 max_fails=3 fail_timeout=30s;
    # server 127.0.0.1:6004 weight=1 max_fails=3 fail_timeout=30s;
    
    # 保持连接
    keepalive 32;
}

# HTTP 服务器配置 - 重定向到 HTTPS
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # 强制重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

# HTTPS 主服务器配置
server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL 证书配置
    ssl_certificate /etc/ssl/certs/your-domain.com.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.com.key;
    
    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头设置
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 日志配置
    access_log /var/log/nginx/otc-core-access.log;
    error_log /var/log/nginx/otc-core-error.log;
    
    # 客户端上传限制
    client_max_body_size 50M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 静态文件服务
    location /static/ {
        alias /opt/otc-core-server/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # 上传文件服务
    location /uploads/ {
        alias /opt/otc-core-server/uploads/;
        expires 1d;
        add_header Cache-Control "public";
        access_log off;
    }
    
    # WebSocket 连接 - UCoin 模块
    location /ucoin {
        proxy_pass http://otc_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket 特殊配置
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
        proxy_connect_timeout 60s;
        
        # 禁用缓存
        proxy_buffering off;
        proxy_cache off;
    }
    
    # API 接口代理
    location / {
        # 限制请求方法
        limit_except GET POST PUT DELETE OPTIONS {
            deny all;
        }
        
        # CORS 预检请求处理
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
        
        # 代理到 Quart 应用
        proxy_pass http://otc_backend;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
        
        # 错误处理
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 3;
        proxy_next_upstream_timeout 30s;
    }
    
    # 健康检查端点
    location /health {
        proxy_pass http://otc_backend/health;
        access_log off;
    }
    
    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(py|pyc|pyo|pyd|log)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}

# 限流配置
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

# 针对特定接口的限流
server {
    listen 443 ssl http2;
    server_name api.your-domain.com;
    
    # SSL 配置同上...
    ssl_certificate /etc/ssl/certs/your-domain.com.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.com.key;
    
    # 登录接口限流
    location ~ ^/(user/login|user/register) {
        limit_req zone=login burst=5 nodelay;
        proxy_pass http://otc_backend;
        # 其他代理配置同上...
    }
    
    # 一般 API 限流
    location / {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://otc_backend;
        # 其他代理配置同上...
    }
}
