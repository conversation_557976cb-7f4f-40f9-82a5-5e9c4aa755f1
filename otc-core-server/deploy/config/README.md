# 生产环境配置说明

## 配置文件说明

### .env.production
生产环境的环境变量配置文件，包含所有必要的配置项。

## 重要配置项说明

### 1. 安全配置
- `SECRET_KEY`: 应用密钥，用于会话加密等，**必须修改**
- `JWT_SECRET_KEY`: JWT 令牌签名密钥，**必须修改**
- `ENCRYPTION_KEY`: 数据加密密钥，必须是32字符长度

### 2. 数据库配置
- `DB_HOST`: 数据库主机地址
- `DB_USER`: 数据库用户名
- `DB_PASSWORD`: 数据库密码，**必须修改**
- `DB_NAME`: 数据库名称

### 3. Redis 配置
- `REDIS_HOST`: Redis 主机地址
- `REDIS_PASSWORD`: Redis 密码，建议设置

### 4. 第三方服务
- `PAYMENT_API_KEY`: 支付服务 API 密钥
- `SMS_API_KEY`: 短信服务 API 密钥
- `SENTRY_DSN`: 错误监控服务配置

## 配置部署步骤

### 1. 复制配置文件
```bash
cp deploy/config/.env.production /opt/otc-core-server/.env
```

### 2. 修改配置
编辑 `/opt/otc-core-server/.env` 文件，修改以下必要配置：

```bash
# 生成随机密钥
openssl rand -hex 32

# 修改配置文件
nano /opt/otc-core-server/.env
```

### 3. 设置文件权限
```bash
chown otc:otc /opt/otc-core-server/.env
chmod 600 /opt/otc-core-server/.env
```

## 数据库初始化

### 1. 创建数据库用户
```sql
CREATE USER 'otc_user'@'localhost' IDENTIFIED BY 'your-secure-password';
CREATE DATABASE otc CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
GRANT ALL PRIVILEGES ON otc.* TO 'otc_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. 运行数据库迁移
```bash
cd /opt/otc-core-server
sudo -u otc ./venv/bin/python -c "
from app import create_app
from app.core.data_source.mysql import db
import asyncio

async def init_db():
    app = create_app('production')
    async with app.app_context():
        await db.init(app.config['SQLALCHEMY_DATABASE_URI'], False)
        # 这里可以添加初始化数据的逻辑

asyncio.run(init_db())
"
```

## Redis 配置

### 1. 安装 Redis
```bash
apt install redis-server
```

### 2. 配置 Redis
编辑 `/etc/redis/redis.conf`：
```
# 设置密码
requirepass your-redis-password

# 绑定地址
bind 127.0.0.1

# 持久化配置
save 900 1
save 300 10
save 60 10000
```

### 3. 重启 Redis
```bash
systemctl restart redis-server
systemctl enable redis-server
```

## SSL 证书配置

### 1. 使用 Let's Encrypt
```bash
# 安装 Certbot
apt install certbot python3-certbot-nginx

# 获取证书
certbot --nginx -d your-domain.com -d www.your-domain.com

# 自动续期
crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 2. 更新 Nginx 配置
修改 `/etc/nginx/sites-available/otc-core.conf` 中的证书路径：
```nginx
ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
```

## 监控配置

### 1. 日志监控
```bash
# 查看应用日志
journalctl -u otc-core-gunicorn -f

# 查看 Nginx 日志
tail -f /var/log/nginx/otc-core-access.log
tail -f /var/log/nginx/otc-core-error.log
```

### 2. 系统监控
安装系统监控工具：
```bash
apt install htop iotop nethogs
```

### 3. 应用监控
配置 Sentry 进行错误监控，在 `.env` 文件中设置 `SENTRY_DSN`。

## 备份策略

### 1. 数据库备份
创建备份脚本 `/opt/scripts/backup_db.sh`：
```bash
#!/bin/bash
BACKUP_DIR="/opt/backups/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

mysqldump -u otc_user -p otc > $BACKUP_DIR/otc_$DATE.sql
gzip $BACKUP_DIR/otc_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
```

### 2. 文件备份
```bash
# 备份上传文件
rsync -av /opt/otc-core-server/uploads/ /opt/backups/uploads/

# 备份配置文件
cp /opt/otc-core-server/.env /opt/backups/config/.env.$(date +%Y%m%d)
```

## 性能优化

### 1. Gunicorn 配置
根据服务器配置调整 worker 数量：
```bash
# CPU 核心数 * 2 + 1
workers = (2 * CPU_CORES) + 1
```

### 2. 数据库优化
```sql
-- 优化 MySQL 配置
SET GLOBAL innodb_buffer_pool_size = 1073741824;  -- 1GB
SET GLOBAL max_connections = 200;
```

### 3. Redis 优化
```
# 内存优化
maxmemory 512mb
maxmemory-policy allkeys-lru
```

## 安全检查清单

- [ ] 修改所有默认密码
- [ ] 设置防火墙规则
- [ ] 配置 SSL 证书
- [ ] 启用 fail2ban
- [ ] 定期更新系统包
- [ ] 配置日志监控
- [ ] 设置备份策略
- [ ] 限制文件上传大小
- [ ] 配置 CORS 策略
- [ ] 启用安全头设置
