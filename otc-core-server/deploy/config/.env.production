# OTC Core Server 生产环境配置
# 请根据实际环境修改以下配置

# ==================== 应用配置 ====================
QUART_ENV=production
SECRET_KEY=your-super-secret-key-change-this-in-production
API_PREFIX=/

# ==================== 数据库配置 ====================
# MySQL 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=otc_user
DB_PASSWORD=your-secure-database-password
DB_NAME=otc
DB_URL=mysql+aiomysql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}

# 数据库连接池配置
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# ==================== Redis 配置 ====================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your-redis-password
REDIS_MAX_CONNECTIONS=50

# ==================== 安全配置 ====================
# JWT 配置
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ACCESS_TOKEN_EXPIRES=3600
JWT_REFRESH_TOKEN_EXPIRES=86400

# 加密配置
ENCRYPTION_KEY=your-encryption-key-32-chars-long

# ==================== 日志配置 ====================
LOG_LEVEL=INFO
LOG_FILE_MAX_SIZE=50MB
LOG_FILE_BACKUP_COUNT=5
LOG_FORMAT=json

# ==================== 文件上传配置 ====================
UPLOAD_FOLDER=/opt/otc-core-server/uploads
MAX_CONTENT_LENGTH=52428800  # 50MB
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,pdf,doc,docx

# ==================== 邮件配置 ====================
MAIL_SERVER=smtp.your-domain.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-mail-password
MAIL_DEFAULT_SENDER=<EMAIL>

# ==================== 第三方服务配置 ====================
# 支付服务配置
PAYMENT_API_URL=https://api.payment-provider.com
PAYMENT_API_KEY=your-payment-api-key
PAYMENT_WEBHOOK_SECRET=your-webhook-secret

# 短信服务配置
SMS_API_URL=https://api.sms-provider.com
SMS_API_KEY=your-sms-api-key
SMS_TEMPLATE_ID=your-template-id

# ==================== 监控配置 ====================
# Sentry 错误监控
SENTRY_DSN=https://<EMAIL>/project-id

# 性能监控
ENABLE_METRICS=true
METRICS_PORT=9090

# ==================== 缓存配置 ====================
CACHE_TYPE=redis
CACHE_DEFAULT_TIMEOUT=300
CACHE_KEY_PREFIX=otc_core:

# ==================== 限流配置 ====================
RATE_LIMIT_STORAGE_URL=redis://localhost:6379/1
RATE_LIMIT_DEFAULT=100/hour
RATE_LIMIT_LOGIN=5/minute

# ==================== WebSocket 配置 ====================
WS_MAX_CONNECTIONS=1000
WS_HEARTBEAT_INTERVAL=30
WS_MESSAGE_MAX_SIZE=1048576  # 1MB

# ==================== 其他配置 ====================
# 时区设置
TIMEZONE=Asia/Shanghai

# 调试模式（生产环境应为 false）
DEBUG=false

# 跨域配置
CORS_ORIGINS=https://your-frontend-domain.com,https://admin.your-domain.com

# 健康检查配置
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/health
