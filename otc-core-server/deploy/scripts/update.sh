#!/bin/bash

# OTC Core Server 更新脚本
# 用于更新已部署的应用

set -e

# 配置变量
APP_NAME="otc-core"
APP_USER="otc"
APP_DIR="/opt/otc-core-server"
BACKUP_DIR="/opt/otc-core-server/backups"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为 root 用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要 root 权限运行"
        exit 1
    fi
}

# 创建备份
create_backup() {
    log_info "创建备份..."
    
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_FILE="$BACKUP_DIR/backup_$TIMESTAMP.tar.gz"
    
    mkdir -p $BACKUP_DIR
    
    cd $APP_DIR
    tar -czf $BACKUP_FILE \
        --exclude='venv' \
        --exclude='__pycache__' \
        --exclude='*.pyc' \
        --exclude='.git' \
        --exclude='logs/*.log' \
        .
    
    log_info "备份已保存到: $BACKUP_FILE"
}

# 停止服务
stop_service() {
    log_info "停止应用服务..."
    systemctl stop otc-core-gunicorn
}

# 更新代码
update_code() {
    log_info "更新应用代码..."
    cd $APP_DIR
    sudo -u $APP_USER git fetch origin
    sudo -u $APP_USER git reset --hard origin/main
}

# 更新依赖
update_dependencies() {
    log_info "更新 Python 依赖..."
    cd $APP_DIR
    sudo -u $APP_USER ./venv/bin/pip install --upgrade pip
    sudo -u $APP_USER ./venv/bin/pip install -r requirements.txt --upgrade
}

# 运行数据库迁移（如果有）
run_migrations() {
    log_info "检查数据库迁移..."
    # 这里可以添加数据库迁移逻辑
    # 例如：sudo -u $APP_USER ./venv/bin/python migrate.py
}

# 启动服务
start_service() {
    log_info "启动应用服务..."
    systemctl start otc-core-gunicorn
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 等待服务启动
    sleep 10
    
    if systemctl is-active --quiet otc-core-gunicorn; then
        log_info "服务启动成功"
        return 0
    else
        log_error "服务启动失败"
        return 1
    fi
}

# 回滚函数
rollback() {
    log_error "更新失败，开始回滚..."
    
    # 找到最新的备份文件
    LATEST_BACKUP=$(ls -t $BACKUP_DIR/backup_*.tar.gz | head -n1)
    
    if [ -n "$LATEST_BACKUP" ]; then
        log_info "使用备份文件: $LATEST_BACKUP"
        
        # 停止服务
        systemctl stop otc-core-gunicorn
        
        # 恢复备份
        cd $APP_DIR
        tar -xzf $LATEST_BACKUP
        
        # 启动服务
        systemctl start otc-core-gunicorn
        
        log_info "回滚完成"
    else
        log_error "未找到备份文件，无法回滚"
    fi
}

# 清理旧备份
cleanup_backups() {
    log_info "清理旧备份文件..."
    find $BACKUP_DIR -name "backup_*.tar.gz" -mtime +7 -delete
}

# 主函数
main() {
    log_info "开始更新 OTC Core Server..."
    
    check_root
    
    # 创建备份
    create_backup
    
    # 停止服务
    stop_service
    
    # 更新代码和依赖
    if update_code && update_dependencies; then
        log_info "代码更新成功"
    else
        log_error "代码更新失败"
        rollback
        exit 1
    fi
    
    # 运行迁移
    run_migrations
    
    # 启动服务
    start_service
    
    # 健康检查
    if health_check; then
        log_info "更新完成！"
        cleanup_backups
    else
        rollback
        exit 1
    fi
}

# 运行主函数
main "$@"
