#!/bin/bash

# OTC Core Server 部署脚本
# 用于在 Linux 服务器上部署 Quart 应用

set -e  # 遇到错误立即退出

# 配置变量
APP_NAME="otc-core"
APP_USER="otc"
APP_GROUP="otc"
APP_DIR="/opt/otc-core-server"
REPO_URL="https://github.com/your-org/otc-core-server.git"
PYTHON_VERSION="3.9"
NGINX_AVAILABLE="/etc/nginx/sites-available"
NGINX_ENABLED="/etc/nginx/sites-enabled"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为 root 用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要 root 权限运行"
        exit 1
    fi
}

# 更新系统包
update_system() {
    log_info "更新系统包..."
    apt update && apt upgrade -y
}

# 安装系统依赖
install_dependencies() {
    log_info "安装系统依赖..."
    apt install -y \
        python3 \
        python3-pip \
        python3-venv \
        python3-dev \
        build-essential \
        nginx \
        mysql-client \
        redis-tools \
        git \
        curl \
        wget \
        supervisor \
        ufw \
        fail2ban \
        logrotate
}

# 创建应用用户
create_app_user() {
    log_info "创建应用用户 $APP_USER..."
    if ! id "$APP_USER" &>/dev/null; then
        useradd -r -s /bin/bash -d $APP_DIR -m $APP_USER
        usermod -aG $APP_GROUP $APP_USER
        log_info "用户 $APP_USER 创建成功"
    else
        log_warn "用户 $APP_USER 已存在"
    fi
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."
    mkdir -p $APP_DIR/{logs,uploads,static,backups}
    chown -R $APP_USER:$APP_GROUP $APP_DIR
    chmod -R 755 $APP_DIR
}

# 克隆或更新代码
deploy_code() {
    log_info "部署应用代码..."
    if [ -d "$APP_DIR/.git" ]; then
        log_info "更新现有代码..."
        cd $APP_DIR
        sudo -u $APP_USER git pull origin main
    else
        log_info "克隆新代码..."
        sudo -u $APP_USER git clone $REPO_URL $APP_DIR
        cd $APP_DIR
    fi
}

# 设置 Python 虚拟环境
setup_python_env() {
    log_info "设置 Python 虚拟环境..."
    cd $APP_DIR
    
    if [ ! -d "venv" ]; then
        sudo -u $APP_USER python3 -m venv venv
    fi
    
    sudo -u $APP_USER ./venv/bin/pip install --upgrade pip
    sudo -u $APP_USER ./venv/bin/pip install -r requirements.txt
    
    # 安装 Gunicorn 用于生产环境
    sudo -u $APP_USER ./venv/bin/pip install gunicorn
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    if [ ! -f "$APP_DIR/.env" ]; then
        cat > $APP_DIR/.env << EOF
# 生产环境配置
QUART_ENV=production
SECRET_KEY=your-secret-key-here
API_PREFIX=/

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=otc_user
DB_PASSWORD=your-db-password
DB_URL=mysql+aiomysql://otc_user:your-db-password@localhost:3306/otc

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
EOF
        chown $APP_USER:$APP_GROUP $APP_DIR/.env
        chmod 600 $APP_DIR/.env
        log_warn "请编辑 $APP_DIR/.env 文件设置正确的配置"
    fi
}

# 配置 Nginx
setup_nginx() {
    log_info "配置 Nginx..."
    
    # 复制 Nginx 配置
    cp $APP_DIR/deploy/nginx/otc-core.conf $NGINX_AVAILABLE/
    
    # 启用站点
    if [ ! -L "$NGINX_ENABLED/otc-core.conf" ]; then
        ln -s $NGINX_AVAILABLE/otc-core.conf $NGINX_ENABLED/
    fi
    
    # 测试 Nginx 配置
    nginx -t
    
    # 重启 Nginx
    systemctl restart nginx
    systemctl enable nginx
}

# 配置 systemd 服务
setup_systemd() {
    log_info "配置 systemd 服务..."
    
    # 复制服务文件
    cp $APP_DIR/deploy/systemd/otc-core-gunicorn.service /etc/systemd/system/
    
    # 重新加载 systemd
    systemctl daemon-reload
    
    # 启用并启动服务
    systemctl enable otc-core-gunicorn
    systemctl start otc-core-gunicorn
}

# 配置防火墙
setup_firewall() {
    log_info "配置防火墙..."
    
    # 启用 UFW
    ufw --force enable
    
    # 允许 SSH
    ufw allow ssh
    
    # 允许 HTTP 和 HTTPS
    ufw allow 80
    ufw allow 443
    
    # 显示状态
    ufw status
}

# 配置日志轮转
setup_logrotate() {
    log_info "配置日志轮转..."
    
    cat > /etc/logrotate.d/otc-core << EOF
$APP_DIR/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $APP_USER $APP_GROUP
    postrotate
        systemctl reload otc-core-gunicorn
    endscript
}
EOF
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查服务状态
    if systemctl is-active --quiet otc-core-gunicorn; then
        log_info "OTC Core 服务运行正常"
    else
        log_error "OTC Core 服务未运行"
        systemctl status otc-core-gunicorn
        return 1
    fi
    
    # 检查端口
    if netstat -tlnp | grep :6002 > /dev/null; then
        log_info "应用端口 6002 正常监听"
    else
        log_error "应用端口 6002 未监听"
        return 1
    fi
    
    # 检查 Nginx
    if systemctl is-active --quiet nginx; then
        log_info "Nginx 服务运行正常"
    else
        log_error "Nginx 服务未运行"
        return 1
    fi
}

# 主函数
main() {
    log_info "开始部署 OTC Core Server..."
    
    check_root
    update_system
    install_dependencies
    create_app_user
    create_directories
    deploy_code
    setup_python_env
    setup_environment
    setup_nginx
    setup_systemd
    setup_firewall
    setup_logrotate
    
    log_info "等待服务启动..."
    sleep 10
    
    if health_check; then
        log_info "部署完成！"
        log_info "应用已在 https://your-domain.com 上运行"
        log_info "请记得："
        log_info "1. 编辑 $APP_DIR/.env 文件设置正确的配置"
        log_info "2. 配置 SSL 证书"
        log_info "3. 设置数据库"
        log_info "4. 更新 Nginx 配置中的域名"
    else
        log_error "部署失败，请检查日志"
        exit 1
    fi
}

# 运行主函数
main "$@"
