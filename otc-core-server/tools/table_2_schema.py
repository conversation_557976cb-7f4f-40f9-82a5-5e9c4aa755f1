sql = """
  `id` int NOT NULL AUTO_INCREMENT COMMENT '消息通知id',
  `type` int DEFAULT '0' COMMENT '类型(0.无)',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '内容',
  `user_id` int DEFAULT NULL COMMENT '消息发送的对象',
  `enable_status` int DEFAULT '0' COMMENT '启用状态 启用-1、禁用-0',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `is_read` int DEFAULT '0' COMMENT '是否已读(0未读，1已读)',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `channel` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '渠道号',
""".replace('  ', '')
sql = sql.split(',\n')

codes = []
for row in sql:
    contents = row.split(' ')
    col = f'    {contents[0].replace('`', '')} = Column(\n'
    if 'AUTO_INCREMENT' in contents:
        col += f'        INTEGER,\n'
        col += f'        primary_key=True,\n'
        col += f'        autoincrement=True,\n'
    else:
        try:
            if contents[1].startswith('varchar'):
                col += f'        {contents[1].upper()},\n'
            elif contents[1].startswith('decimal'):
                col += f'        {contents[1].upper()},\n'
            elif contents[1] == 'int':
                col += f'        INTEGER,\n'
            elif contents[1] == 'bigint':
                col += f'        BIGINT,\n'
            elif contents[1] == 'tinyint(1)':
                col += f'        BOOLEAN,\n'
            elif contents[1] == 'tinyint':
                col += f'        INTEGER,\n'
            elif contents[1] == 'datetime':
                col += f'        DATETIME,\n'
            elif contents[1] == 'timestamp':
                col += f'        TIMESTAMP,\n'
                if 'ON UPDATE CURRENT_TIMESTAMP' in row:
                    col += f'        onupdate=datetime.now,\n'
                col += f'        default=datetime.now,\n'

            if 'COMMENT' in contents:
                col += f'        comment="{contents[-1].replace("'", '')}"\n'
        except BaseException as error:
            print(error)
            continue
    col += f'    )\n'

    codes.append(col)

db_table_name = 'dy_message_notice'
table_name = db_table_name.replace('dy_', '').split('_')
class_name = ''.join([item.capitalize() for item in table_name])

print('from datetime import datetime')
print('from sqlalchemy import Column, select, func, desc, and_')
print('from sqlalchemy import VARCHAR, DECIMAL, INTEGER, BOOLEAN, DATETIME')
print('from sqlalchemy import BIGINT, TIMESTAMP')
print('from sqlalchemy.orm import load_only')
print('from app.shared.schemas import BaseSchema, db')
print('')
print('')
print(f'class {class_name}(BaseSchema):')
print(f"    __tablename__ = '{db_table_name}'")
print('')
print(f'    {''.join(codes)}')
