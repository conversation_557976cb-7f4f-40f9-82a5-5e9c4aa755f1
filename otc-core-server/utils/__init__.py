def md5(content: str):
    from hashlib import md5
    hash_obj = md5()
    hash_obj.update(content.encode('utf-8'))
    return hash_obj.hexdigest()


def root():
    from pathlib import Path
    """递归向上查找，直到找到项目根目录（如包含 README.md 或 .git 的目录）"""
    path = Path(__file__).absolute()  # 当前文件的绝对路径
    while True:
        # 检查是否包含项目根目录的标志文件
        if (path / "README.md").exists() or (path / "run.py").exists() or (path / ".git").exists():
            return path
        if path.parent == path:  # 已经到根目录，停止查找
            raise FileNotFoundError("无法找到项目根目录！")
        path = path.parent  # 继续向上查找
