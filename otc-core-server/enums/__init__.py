from enum import Enum


class FiatCurrencyType:
    CRYPTO = 0
    RMB = 1


class CoinOrderType(Enum):
    ALL = 0
    DEPOSIT = 1
    WITHDRAW = 2
    TRANS_IN = 3
    TRANS_OUT = 4


class FeeRateMode(Enum):
    BUY_FEE_RATE = 1
    """利用这个支付方式 ，买币需要支付的手续费"""
    SELL_FEE_RATE = 2
    """利用这个通道，卖币的时候，需要支付的手续费"""
    BUY_COMM_RATE = 3
    """用户当币商，利用这个通道，买币进来，赚取佣金的手续费"""
    SELL_COMM_RATE = 4
    """用户当币商，利用这个通道，卖币能够赚取的手续费"""
    BEHALF_SELL_FEE_RATE = 5
    """利用这个通道，代付卖币的时候，需要支付的手续费"""
    BEHALF_BUY_COMM_RATE = 6
    """用户当币商，利用这个通道，代付买币能够赚取的手续费"""


class OrderProcessStatusType(Enum):
    READY = 0
    """该订单未正式创建"""
    PENDING = 1
    """该订单开始，等待付款"""
    PENDING_TIMEOUT = 2
    """
    等待超时，这个订单会失效，不能在使用。不过可以提起申诉。
    因为有可能玩家已经付了款，但是没有点击确认
    """
    BUYER_PAYED = 3
    """订单已经付款，等待放行"""
    SELLER_DISCHARGED_TIMEOUT = 4
    """
    这个是币商没有及时放行，然后币会冻结2个小时。
    两个小时内，币商都可以点击行，如果币商还是没有处理，那么币会自动划转过去
    """
    FROZEN = 5
    """因为币商没有点击放行，该交易被冻结"""
    FINISHED_WITH_TIMEOUT = 6
    """该订单是超时完成的"""
    FINISHED_WITH_MANUAL = 7
    """订单已经完成，这个完成是人工点击完成的"""
    CANCELED = 8
    """订单被取消了。只有在状态1的情况下，才可能被取消"""
    APPEAL = 9
    """申诉中，在冻结的状态下，可以进行申诉"""
    APPEAL_REPLYING = 10
    """回复申诉中"""
    APPEAL_FINISH = 11
    """申诉完成"""
    FAILED = 12
    """冻结的钱不足，交易失败了"""
    REVIEWING = 13
    """审核中"""
    MATCHING = 14
    """匹配中"""
    REVIEW_NOT_PASS = 15
    """审核不通过"""
    APPEAL_CANCELED = 16
    """申诉取消"""


class CurrencyType(Enum):
    USDT = 2003


class HangOrderType(Enum):
    BUY = 0
    SELL = 1
    COIN_PAY = 2


class PriceType(Enum):
    NORMAL = 0
    """普通价格"""
    MARKET = 1
    """市场价格"""


class HangOrderAutoType(Enum):
    MANUAL = 0
    """非自动挂单"""
    AUTO = 1
    """自动挂单"""


class TradeType(Enum):
    BUY = 0
    """参考系为用户，即用户为 - 买；商户为卖"""
    SELL = 1
    """参考系为用户，即用户为 - 卖；商户为买"""
    ALL = 2


class OrderStatusType(Enum):
    ALL = 0
    """全部"""
    NOT_PAY = 1
    """未付款"""
    PAYED = 3
    """已付款"""
    FINISHED = 7
    """已完成"""
    CANCELED = 8
    """已取消"""
    APPEAL = 9
    """申诉中"""


class OrderType(Enum):
    DEPOSIT = '200'
    WITHDRAW = '300'


class WithdrawType(Enum):
    OTC = '0'
    EXCHANGE = '1'
    """代付"""


class PayType(Enum):
    ALIPAY = '8101'
    """支付宝原生"""
    ALIPAY_SCAN = '8102'
    """支付宝扫码"""
    ALIPAY_H5 = '8103'
    """支付宝H5"""
    C_ALIPAY = '8111'
    """企业支付宝app"""
    C_ALIPAY_H5 = '8112'
    """企业支付宝H5"""
    C_ALIPAY_HB = '8113'
    """企业支付宝红包"""

    BANK = '8201'
    """银行卡转卡"""
    UNION_PAY = '8202'
    """云闪付"""
    ALI_2_BANK = '8203'
    """支付宝转银行"""
    WECHAT_2_BANK = '8204'
    """微信转转银行"""

    WECHAT = '8301'
    """微信支付"""
    WECHAT_H5 = '8302'
    """微信H5"""

    BIT_PAY = '8401'
    """币支付"""
