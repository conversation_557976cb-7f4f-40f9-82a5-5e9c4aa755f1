from enum import Enum


class RequestEventCode(Enum):
    # 1000 - 1999
    LOGIN = 1003
    ADD_PAYMENT = 1017
    PAYMENTS = 1025
    HEARTBEAT = 1027
    RECONNECT = 1029
    NOTICES = 1101
    MESSAGE_LIST = 1105

    # 2000 - 2999
    HANG_ORDER = 2001
    MY_HANG_ORDER = 2007
    QUICK_DEAL = 2009
    DEAL = 2011
    TODAY_FLOW = 2035
    TRADE_RECORDS = 2037
    PENDING_ORDERS = 2041
    TRADING_ORDER_LIST = 2043
    WITHDRAW = 2045
    WITHDRAW_RECORDS = 2047
    EXCHANGE_RATE = 2049
    WALLET_TRANS_IN_RECORDS = 2057
    CHECK_ADDRESS = 2051
    WITHDRAW_FEE = 2059
    INCOME_AND_EXPENDITURE = 2065


class ResponseEventCode(Enum):
    # 1000 - 1999
    LOGIN = 1004
    ADD_PAYMENT = 1018
    PAYMENTS = 1026
    HEARTBEAT = 1028
    RECONNECT = 1030
    UPDATE_USER_INFO = 1032
    NOTICES = 1102
    MESSAGE_LIST = 1106

    # 2000 - 2999
    HANG_ORDER = 2002
    MY_HANG_ORDER = 2008
    QUICK_DEAL = 2010
    DEAL = 2012
    TODAY_FLOW = 2036
    TRADE_RECORDS = 2038
    PENDING_ORDERS = 2042
    TRADING_ORDER_LIST = 2044
    WITHDRAW = 2046
    WITHDRAW_RECORDS = 2048
    EXCHANGE_RATE = 2050
    WALLET_TRANS_IN_RECORDS = 2058
    CHECK_ADDRESS = 2051
    WITHDRAW_FEE = 2060
    UPDATE_NEW_ORDER = 2064
    INCOME_AND_EXPENDITURE = 2066
